import Tool from "@/common/tool";
import MarkdownIt from 'markdown-it';
import MdKatex from '@vscode/markdown-it-katex';
import MdLinkAttributes from 'markdown-it-link-attributes';
import MdMermaid from 'mermaid-it-markdown';
import hljs from 'highlight.js';
const service = require(`../module/${Tool.getCurrentModuleName()}/service/service`).default

// 优化: 提取为常量提高可维护性
const API_METHODS = {
    ASK: "aiConversation.ask"
};

const ERROR_MESSAGES = {
    UNKNOWN_ERROR: '未知错误',
    SERVER_ERROR: '服务器请求异常'
};

// 优化: 缓存大小限制
const MAX_CACHE_SIZE = 100;

/**
 * 获取基础URL
 * @returns {string} 基础URL
 */
const getBaseUrl = () => {
    let systemConfig = window.vm.$store.state.systemConfig;
    let ajaxServer = systemConfig.server_type.protocol + systemConfig.server_type.host + systemConfig.server_type.port;
    return ajaxServer;
}

/**
 * AI聊天服务类
 * 处理与AI对话相关的所有功能
 */
class AIChatService {
    constructor() {
        this.abortController = null;
        this.conversationID = null;
        this.renderedCache = new Map();

        // 用于内容缓冲和批量渲染
        this.contentBuffer = "";
        this.reasoningBuffer = "";
        this.renderInterval = null;
        this.renderDelay = 150; // 默认150ms渲染一次
        this.currentAnsweringCallback = null;
        this.currentThinkingCallback = null;
        this.isAborting = false; // 标记是否正在中止过程中

        // 初始化 MarkdownIt 实例
        this.mdi = new MarkdownIt({
            html: false,
            linkify: true,
            highlight: (code, language) => {
                const validLang = !!(language && hljs.getLanguage(language));
                if (validLang) {
                    const lang = language ?? '';
                    return this.highlightBlock(hljs.highlight(code, { language: lang }).value, lang);
                }
                return this.highlightBlock(hljs.highlightAuto(code).value, '');
            },
        });

        // 配置 MarkdownIt 插件
        this.mdi
            .use(MdKatex, {
                throwOnError: false,    // 遇到解析错误时不抛出异常
                errorColor: '#cc0000',  // 错误显示为红色
                output: 'html',         // 输出格式
            })
            .use(MdLinkAttributes, {
                attrs: {
                    target: '_self', // 不使用 _blank
                    class: 'custom-link',
                    onclick: 'return false;', // 阻止默认行为
                    'data-custom-link': 'true' // 添加自定义属性用于识别
                }
            })
            .use(MdMermaid);

        // 优化: 添加全局事件处理函数(如果不存在)
        if (!window.handleMarkdownLinkClick) {
            window.handleMarkdownLinkClick = (event) => {
                const link = event.target.closest('a[data-custom-link]');
                if (link) {
                    window.vm.$root.eventBus.$emit('ai-chat-link-click', link.href);
                }
            };
        }
    }

    /**
     * 高亮代码块
     * @param {string} str - 代码内容
     * @param {string} lang - 语言
     * @returns {string} 高亮后的HTML
     */
    highlightBlock(str, lang) {
        const localLang = window.vm.$store.state.language
        return `<pre class="code-block-wrapper">
            <div class="code-block-header">
                <span class="code-block-header__lang">${lang}</span>
                <button class="code-copy-btn" onclick="event.stopPropagation(); const content = this.parentElement.parentElement.querySelector('code').innerText; const textarea = document.createElement('textarea'); textarea.value = content; document.body.appendChild(textarea); textarea.select(); document.execCommand('copy'); document.body.removeChild(textarea); this.innerText='${localLang.copy_text_success}'; setTimeout(() => this.innerText='${localLang.copy}', 2000);">${localLang.copy}</button>
            </div>
            <code class="hljs code-block-body ${lang}">${str}</code>
        </pre>`;
    }

    /**
     * 处理LaTeX内容
     * @param {string} text - 原始文本
     * @returns {string} 处理后的文本
     */
    processLatexContent(text) {
        if (!text) {
            return '';
        }

        let result = text;

        // 处理块级数学公式 \[ \]
        result = result.replace(/\\\[([\s\S]*?)\\\]/g, (_, content) => {
            return '\n$$' + content.trim() + '$$\n';
        });

        // 处理行内数学公式 \( \)
        result = result.replace(/\\\(([\s\S]*?)\\\)/g, (_, content) => {
            return '$' + content.trim() + '$';
        });

        return result;
    }

    /**
     * 渲染Markdown内容
     * @param {string} content - Markdown内容
     * @returns {string} 渲染后的HTML
     */
    renderMarkdown(content) {
        if (!content) {
            return '';
        }

        const cacheKey = content.trim();
        if (this.renderedCache.has(cacheKey)) {
            return this.renderedCache.get(cacheKey);
        }

        try {
            // 优化: 添加内容长度限制
            if (content.length > 50000) {
                console.warn('Content too long for markdown rendering');
                return content;
            }

            // 处理 LaTeX 格式
            const processedContent = this.processLatexContent(content);

            // 渲染 markdown
            const rendered = this.mdi.render(processedContent);

            // 添加事件委托包装器
            const wrappedRendered = `<div class="markdown-content" onclick="handleMarkdownLinkClick(event)">${rendered}</div>`;

            // 优化: 限制缓存大小
            if (this.renderedCache.size >= MAX_CACHE_SIZE) {
                const firstKey = this.renderedCache.keys().next().value;
                this.renderedCache.delete(firstKey);
            }

            // 缓存结果
            this.renderedCache.set(cacheKey, wrappedRendered);
            return wrappedRendered;
        } catch (error) {
            console.error('Markdown rendering error:', error);
            return content;
        }
    }

    /**
     * 开始批量渲染计时器
     * @private
     */
    _startRenderInterval() {
        if (this.renderInterval) {
            return;
        }

        this.renderInterval = setInterval(() => {
            this._flushBuffers();
        }, this.renderDelay);
    }

    /**
     * 停止批量渲染计时器
     * @private
     */
    _stopRenderInterval() {
        if (this.renderInterval) {
            clearInterval(this.renderInterval);
            this.renderInterval = null;
        }
    }

    /**
     * 刷新缓冲区内容到回调函数
     * @private
     */
    _flushBuffers() {
        // 处理回答内容
        if (this.contentBuffer && this.currentAnsweringCallback) {
            this.currentAnsweringCallback(this.contentBuffer);
            this.contentBuffer = "";
        }

        // 处理思考内容
        if (this.reasoningBuffer && this.currentThinkingCallback) {
            this.currentThinkingCallback(this.reasoningBuffer);
            this.reasoningBuffer = "";
        }
    }

    /**
     * 处理错误数据
     * @private
     * @param {string} buffer - 包含错误信息的缓冲区
     * @param {Function} onError - 错误回调函数
     * @returns {boolean} 是否成功处理了错误
     */
    _handleErrorData(buffer, onError) {
        if (buffer.includes('"error_code"') && buffer.includes('"error_msg"')) {
            try {
                const errorMatch = buffer.match(/\{[\s\S]*"error_code"[\s\S]*\}/);
                if (errorMatch) {
                    const errorData = JSON.parse(errorMatch[0]);
                    if (errorData.error_code && errorData.error_msg) {
                        this._stopRenderInterval();
                        this._flushBuffers();

                        if (onError) {
                            onError(errorData.error_msg || errorData.error_zh || ERROR_MESSAGES.UNKNOWN_ERROR);
                            return true;
                        }
                    }
                }
            } catch (parseError) {
                console.warn('解析错误数据失败:', parseError);
            }
        }
        return false;
    }

    /**
     * 处理AI问答
     * @param {Object} options - 配置选项
     * @param {string} options.question - 问题内容
     * @param {string} options.conversationID - 会话ID
     * @param {boolean} options.deepThinking - 是否启用深度思考
     * @param {boolean} options.webSearch - 是否启用网络搜索
     * @param {Function} options.onThinking - 思考状态回调
     * @param {Function} options.onAnswering - 回答状态回调
     * @param {Function} options.onCompleted - 完成状态回调
     * @param {Function} options.onError - 错误状态回调
     * @param {Function} options.onAbort - 中止状态回调
     * @param {number} options.renderDelay - 渲染延迟时间(ms)，默认150ms
     * @returns {Promise<void>}
     */
    async askQuestion(options) {
        const {
            question,
            conversationID,
            deepThinking = false,
            webSearch = false,
            onThinking,
            onAnswering,
            onCompleted,
            onError,
            onAbort,
            renderDelay,
        } = options;

        // 如果存在之前的控制器，先中止之前的请求
        if (this.abortController) {
            await this._safeAbort();
        }

        // 重置状态
        this.isAborting = false;
        this.contentBuffer = "";
        this.reasoningBuffer = "";
        this.currentAnsweringCallback = onAnswering;
        this.currentThinkingCallback = onThinking;

        // 设置渲染延迟（如果提供）
        if (renderDelay && typeof renderDelay === 'number') {
            this.renderDelay = renderDelay;
        }

        // 创建新的 AbortController
        this.abortController = new AbortController();
        this.conversationID = conversationID;

        const bizContent = {
            question,
            conversationID,
            deepThinking: deepThinking ? 1 : 0,
            webSearch: webSearch ? 1 : 0
        };

        try {
            // 初始化思考状态
            if (onThinking) {
                onThinking();
            }

            // 启动渲染计时器
            this._startRenderInterval();

            const response = await fetch(`${getBaseUrl()}/v2/api`, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    "Accept": "text/event-stream",
                    "token": Tool.getToken(),
                    "Cache-Control": "no-cache",
                    "Connection": "keep-alive",
                },
                signal: this.abortController.signal,
                cache: 'no-store',
                credentials: 'same-origin',
                body: JSON.stringify({ method: API_METHODS.ASK, bizContent })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let buffer = '';

            while (true) {
                const { value, done } = await reader.read();
                if (done) {
                    break;
                }

                // 添加更安全的解码处理
                try {
                    buffer += decoder.decode(value, { stream: true });
                } catch (decodeError) {
                    console.warn('解码数据失败:', decodeError);
                    continue;
                }

                // 检查错误信息
                if (this._handleErrorData(buffer, onError)) {
                    return;
                }

                // 处理数据
                let lines = buffer.split('\n');
                buffer = lines.pop() || '';

                for (const line of lines) {
                    const trimmedLine = line.trim();
                    if (!trimmedLine) {
                        continue;
                    }

                    try {
                        // 检查数据格式
                        if (!trimmedLine.startsWith('{')) {
                            console.warn('跳过非JSON数据:', trimmedLine);
                            continue;
                        }

                        // 尝试修复不完整的JSON
                        let jsonStr = trimmedLine;
                        if (!trimmedLine.endsWith('}')) {
                            jsonStr = trimmedLine + '}';
                        }

                        const data = JSON.parse(jsonStr);

                        // 数据结构验证
                        if (!data || typeof data !== 'object') {
                            continue;
                        }

                        const choice = data.choices?.[0];
                        if (!choice) {
                            continue;
                        }

                        const delta = choice.delta || {};

                        if (choice.finish_reason === "stop") {
                            this._stopRenderInterval();
                            this._flushBuffers();

                            if (onCompleted) {
                                onCompleted();
                                return;
                            }
                            continue;
                        }

                        // 更新缓冲区
                        if (delta.reasoning_content) {
                            this.reasoningBuffer += delta.reasoning_content;
                        }

                        if (delta.content) {
                            this.contentBuffer += delta.content;
                        }
                    } catch (parseError) {
                        console.warn('解析数据失败:', parseError);
                        continue;
                    }
                }

                // 处理缓冲区中剩余的完整数据
                if (buffer.trim()) {
                    try {
                        if (buffer.startsWith('{') && buffer.endsWith('}')) {
                            const data = JSON.parse(buffer);
                            if (data.choices?.[0]?.finish_reason === 'stop') {
                                this._stopRenderInterval();
                                this._flushBuffers();

                                if (onCompleted) {
                                    onCompleted();
                                }
                            }
                        }
                    } catch (err) {
                        console.warn('处理最后的数据失败:', err);
                    }
                }
            }

            // 确保在流结束时停止渲染计时器并刷新缓冲区
            this._stopRenderInterval();
            this._flushBuffers();

            if (onCompleted) {
                onCompleted();
            }
        } catch (error) {
            // 停止渲染计时器
            this._stopRenderInterval();

            // 处理 AbortError - 只在非主动中止时报告错误
            if (error.name === 'AbortError') {
                this._flushBuffers();

                if (onAbort && !this.isAborting) {
                    onAbort();
                }
                return;
            }

            console.error("请求失败:", error);
            if (onError) {
                onError(ERROR_MESSAGES.SERVER_ERROR, error);
            }
        } finally {
            // 清理资源
            this.isAborting = false;
            this.abortController = null;
            this.currentAnsweringCallback = null;
            this.currentThinkingCallback = null;
        }
    }

    /**
     * 安全地中止控制器，防止多次触发abort事件
     * @private
     */
    async _safeAbort() {
        if (!this.abortController || this.isAborting) {
            return;
        }

        // 标记正在中止
        this.isAborting = true;

        // 创建一个临时变量引用当前的控制器
        const controller = this.abortController;
        this.abortController = null;

        // 中止控制器
        try {
            controller.abort();
        } catch (e) {
            console.warn('中止控制器时出错:', e);
        }

        // 等待一小段时间，让浏览器处理中止事件
        await new Promise(resolve => setTimeout(resolve, 50));
    }

    /**
     * 中止当前问答请求
     */
    async abort() {
        if (!this.abortController || this.isAborting) {
            return;
        }

        // 标记正在中止
        this.isAborting = true;

        // 停止渲染计时器和刷新缓冲区
        this._stopRenderInterval();
        this._flushBuffers();

        // 安全地中止控制器
        await this._safeAbort();

        try {
            // 通知后端停止处理
            const res = await service.stopAskAiQuestion({
                conversationID: this.conversationID
            });
            const data = res.data;
            if (data.error_code !== 0) {
                console.error('stopAskQuestion Fail', data);
            }
        } catch (error) {
            console.warn('通知后端停止处理时出错:', error);
        } finally {
            // 确保重置状态
            this.isAborting = false;
            this.abortController = null;
        }
    }

    // 优化: 统一异步服务调用处理
    /**
     * 调用服务方法并处理响应
     * @private
     * @param {Function} serviceMethod - 服务方法
     * @param {Object} params - 参数
     * @returns {Promise<Object>} 处理结果
     */
    async _callService(serviceMethod, params = {}) {
        try {
            const res = await serviceMethod(params);
            const data = res.data;
            if (data.error_code === 0) {
                return data.data;
            } else {
                throw data;
            }
        } catch (error) {
            console.error('Service call failed:', error);
            throw error;
        }
    }

    /**
     * 获取会话历史
     * @param {string} conversationID - 会话ID
     * @returns {Promise<Object>} 会话历史
     */
    async getConversationHistory(conversationID) {
        const params = {
            lastMessageId: '',
            limit: 999,
            conversationID
        };
        return this._callService(service.getConversationHistory, params);
    }

    /**
     * 获取最近会话列表
     * @returns {Promise<Object>} 最近会话列表
     */
    async getRecentConversationList() {
        return this._callService(service.getRecentConversationList);
    }

    /**
     * 创建AI会话
     * @returns {Promise<Object>} 创建的会话ID
     */
    async createAiConversation() {
        return this._callService(service.createAiConversation);
    }

    /**
     * 删除AI会话
     * @param {string} conversationID - 会话ID
     * @returns {Promise<Object>} 删除结果
     */
    async deleteAiConversation(conversationID) {
        return this._callService(service.deleteAiConversation, { conversationID });
    }
}

export default AIChatService;

{"name": "ultrasync", "version": "0.1.0", "private": true, "scripts": {"build": "node build/index.js", "lint": "vue-cli-service lint", "build:ce": "node build/index.js ce", "build:cn": "node build/index.js", "build:all": "npm-run-all build:ce build:cn", "dev": "vue-cli-service serve"}, "dependencies": {"@aliyun-sls/web-track-browser": "^0.3.9", "@netless/cursor-tool": "^0.1.1", "@panzoom/panzoom": "^4.5.1", "@vant/touch-emulator": "^1.4.0", "@vscode/markdown-it-katex": "^1.1.1", "@web-tracing/vue2": "^2.0.6", "aegis-web-sdk": "^1.36.9", "agora-rtc-sdk-ng": "^4.23.0", "agora-rtm": "^2.2.0-2", "ali-oss": "^6.19.0", "aws-sdk": "^2.1601.0", "axios": "^0.27.2", "callapp-lib": "^3.5.3", "cross-env": "^7.0.3", "crypto-js": "^4.2.0", "dexie": "^3.2.2", "echarts": "^5.4.3", "element-ui": "2.15.8", "highlight.js": "^11.11.1", "hls.js": "^1.5.17", "html2canvas": "^1.4.1", "idle-js": "^1.2.0", "js-base64": "^3.7.7", "jsqr": "^1.4.0", "katex": "^0.16.21", "log4javascript": "^1.4.16", "markdown-it": "^14.1.0", "markdown-it-link-attributes": "^4.0.1", "mermaid-it-markdown": "^1.0.8", "moment": "^2.29.4", "pdfh5": "^1.4.2", "plyr": "^3.7.8", "portal-vue": "^2.1.7", "register-service-worker": "^1.7.2", "socket.io-client": "^2.5.0", "swiper": "^4.5.1", "tasksfile": "^5.1.1", "v-contextmenu": "^2.9.2", "vant": "^2.13.2", "vconsole": "^3.14.6", "vue": "^2.6.14", "vue-awesome-swiper": "^3.1.3", "vue-cropper": "^0.4.9", "vue-json-viewer": "^2.2.22", "vue-lazyload": "1.2.3", "vue-router": "^3.5.1", "vue-runtime-helpers": "^1.1.2", "vue-touch": "^2.0.0-beta.4", "vue-virtual-scroller": "^1.0.10", "vue2-touch-events": "^3.2.3", "vuedraggable": "^2.24.3", "vuescroll": "^4.17.5", "vuex": "^3.6.2", "white-web-sdk": "^2.16.37", "worker-loader": "^3.0.8", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "babel-plugin-import": "^1.13.5", "eslint": "^7.32.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^8.0.3", "fs": "^0.0.1-security", "grunt": "^1.5.3", "grunt-contrib-clean": "^2.0.1", "grunt-contrib-concat": "^2.1.0", "grunt-contrib-cssmin": "^4.0.0", "grunt-contrib-uglify": "^5.2.2", "node-polyfill-webpack-plugin": "^2.0.1", "npm-run-all": "^4.1.5", "prettier": "^2.4.1", "sass": "^1.32.7", "sass-loader": "^12.0.0", "string-replace-loader": "^3.1.0", "svg-sprite-loader": "^6.0.11", "ts-loader": "^9.5.1", "typescript": "^5.6.3", "vue-template-compiler": "^2.6.14", "webpack-bundle-analyzer": "^4.10.2", "workbox-webpack-plugin": "^6.5.4"}}
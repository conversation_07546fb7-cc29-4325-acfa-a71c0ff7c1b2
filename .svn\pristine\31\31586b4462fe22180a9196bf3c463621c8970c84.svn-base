<template>
  <div class="app-container">
      <div class="title">{{ pageTitle }}</div>
      <div class="filter-container">
          <el-date-picker v-model="listQuery.timeScope" type="monthrange" align="right" unlink-panels :clearable="false"
              style="width: auto;margin-right: 10px;">
          </el-date-picker>
          <el-button type="success" @click="presetTimeScope('oneMonth')">
              {{ lang.statistic.time_map["1M"] }}
          </el-button>
          <el-button type="success" @click="presetTimeScope('threeMonth')">
              {{ lang.statistic.time_map["3M"] }}
          </el-button>
          <el-button type="success" @click="presetTimeScope('sixMonth')">
              {{ lang.statistic.time_map["6M"] }}
          </el-button>
          <el-button type="success" @click="presetTimeScope('oneYear')">
              {{ lang.statistic.time_map["1Y"] }}
          </el-button>
          <!-- <el-button type="primary" icon="el-icon-search" style="margin-left: 10px" @click="handleFilter">
              {{ lang.statistic.search_btn }}
          </el-button> -->
      </div>
      <el-row :gutter="20">
          <el-col :span="6">
              <div class="mainStyle block1">
                  <span class="block-title">
                      {{ lang.statistic.doppler_number }}
                  </span>
                  <span class="context">
                      {{ deviceCount }}
                  </span>
              </div>
          </el-col>
          <el-col :span="6">
              <div class="mainStyle block2">
                  <span class="block-title">
                      {{ lang.statistic.doppler_working_number }}
                  </span>
                  <span class="context">
                      {{ working }}
                  </span>
                  <span class="block-foot">
                      {{ (working/deviceCount * 100).toFixed(1) }} %
                  </span>
              </div>
          </el-col>
          <el-col :span="6">
              <div class="mainStyle block3">
                  <span class="block-title">
                      {{ lang.statistic.doppler_standby_number }}
                  </span>
                  <span class="context">
                      {{ standby }}
                  </span>
                  <span class="block-foot">
                      {{ (standby/deviceCount * 100).toFixed(1) }} %
                  </span>
              </div>
          </el-col>
          <el-col :span="6">
              <div class="mainStyle block4">
                  <span class="block-title">
                      {{ lang.statistic.doppler_offline_number }}
                  </span>
                  <span class="context">
                      {{ offline }}
                  </span>
                  <span class="block-foot">
                      {{ (offline/deviceCount * 100).toFixed(1) }} %
                  </span>
              </div>
          </el-col>
      </el-row>
      <el-row :gutter="20" style="padding: 20px 0;">
          <el-col :span="12">
              <div ref="bar_chart" class="barChart" />
          </el-col>
          <el-col :span="12">
              <div ref="pie_chart" class="barChart" />
          </el-col>
      </el-row>
      <div class="table-top">
          {{ lang.statistic.data_list }}
          <el-button type="primary" size="mini" @click="exportExcel" :disabled="buttonLoading" v-loading="buttonLoading">{{ lang.statistic.data_export }}</el-button>
      </div>
      <el-table :data="list" element-loading-text="Loading" border fit highlight-current-row>
          <el-table-column align="center" :label="lang.statistic.device_type_title">
              <template slot-scope="{row}">
                  {{ row.product_name }}
              </template>
          </el-table-column>
          <el-table-column align="center" :label="lang.statistic.table_series_number">
              <template slot-scope="{row}">
                  {{ row.series_number }}
              </template>
          </el-table-column>
          <el-table-column align="center" :label="lang.statistic.table_install_ts">
              <template slot-scope="{row}">
                  {{ row.install_ts|formatTimeToYMD }}
              </template>
          </el-table-column>
          <el-table-column align="center" :label="lang.statistic.table_hospital">
              <template slot-scope="{row}">
                  {{ row.hospital_name }}
              </template>
          </el-table-column>
          <el-table-column v-if="false" align="center" :label="lang.statistic.using_departments">
              <template slot-scope="{row}">
                  {{ row.using_department }}
              </template>
          </el-table-column>
          <el-table-column v-if="false" align="center" :label="lang.statistic.equipment_ownership">
              <template slot-scope="{row}">
                  {{ row.owner_department }}
              </template>
          </el-table-column>
          <el-table-column align="center" :label="lang.statistic.device_status">
              <template slot-scope="{row}">
                  {{ row.status|statusFilter }}
              </template>
          </el-table-column>
          <el-table-column align="center" :label="lang.statistic.group_joined_count">
              <template slot-scope="{row}">
                  {{ row.joinedGroupCount }}
              </template>
          </el-table-column>
          <el-table-column align="center" :label="lang.statistic.device_utilization_rate">
              <template slot-scope="{row}">
                  {{ row.workingTime > 0 && row.utilizationRate === 0 ? '< 0.01%' : row.utilizationRate+'%' }}
              </template>
          </el-table-column>
          <el-table-column align="center" :label="lang.statistic.table_total_startup_time">
              <template slot-scope="{row}">
                  {{ row.startupTime|secondToHours }}{{ lang.parsetime_hours_text }}
              </template>
          </el-table-column>
          <el-table-column align="center" :label="lang.statistic.table_total_working_time">
              <template slot-scope="{row}">
                  {{ row.workingTime|secondToHours }}{{ lang.parsetime_hours_text }}
              </template>
          </el-table-column>
          <el-table-column align="center" :label="lang.statistic.table_exam_type_count">
              <template slot-scope="{row}">
                  {{ row.checkTypeCount }}
              </template>
          </el-table-column>
          <el-table-column align="center" :label="lang.statistic.summary_user_count">
              <template slot-scope="{row}">
                  {{ row.summaryUserCount }}
              </template>
          </el-table-column>
          <el-table-column align="center" :label="lang.statistic.summary_exam_count">
              <template slot-scope="{row}">
                  {{ row.examCount }}
              </template>
          </el-table-column>
          <el-table-column v-show="listQuery.dataFrom!=='global'" align="center" :label="lang.statistic.summary_exam_count_ingroup">
              <template slot-scope="{row}">
                  {{ row.examGroupCount }}
              </template>
          </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.pagesize"
          @pagination="getList" />
      <a ref="link" style="display:none;"></a>
  </div>
</template>
<script>
import base from '../lib/base'
import Tool from '../lib/tool.js';
import request from '../service/service';
import download from '../service/export'
import moment from 'moment';
import Pagination from '../components/Pagination'
import examTypeMap from '../lib/examTypeMap.js';

export default {
    mixins: [base],
    name: 'statistic_index',
    components: { Pagination },
    filters: {
        minFilter(seconds) {
            return Math.round(seconds / 60)
        },
        statusFilter(status) {
            return window.vm.$store.state.language.statistic.device_status_map[status]
        }
    },
    data() {
        return {
            examCount: 0,
            imageCount: 0,
            videoCount: 0,
            buttonLoading: false,
            fullscreenLoading: false,
            list: [],
            total: 0,
            listQuery: {
                page: 1,
                pagesize: 10,
                timeScope: [Tool.getZeroTimeDateOfMonth(5), new Date(Tool.getZeroTimeDateOfMonth(-1)-1)],
                dataFrom: 'global',
                id: -1,
                type: 1,
            },
            deviceCount: 0,
            working: 0,
            standby: 0,
            offline: 0,
            bar_chart: {},
            pie_chart: {},
            xData: [],
            yData: [],
            overallDistribution: [
                { value: 0 ,key:4 },
                { value: 0 ,key:2},
                { value: 0 ,key:6},
                { value: 0 ,key:3},
                { value: 0 ,key:5},
                { value: 0 ,key:1},
                { value: 0 ,key:0},
                { value: 0 ,key:11},
                { value: 0 ,key:12},
                { value: 0 ,key:-1},
            ],
        }
    },
    computed: {
        pageTitle() {
            return this.$store.state.globalParams.targetInfo.subject || this.lang.statistic.statistic_device;
        }
    },
    async created() {
        if (Object.keys(this.$route.query).length) {
            this.$router.replace({ path: this.$route.path })
        }
        const query = JSON.parse(window.localStorage.getItem('stat_query'))
        if (query) {
            for (const item in query) {
                this.listQuery[item] = query[item]
            }
        }
        this.searchOneTimeOnCreated()
        this.handleFilter()
    },
    watch: {
        'listQuery.timeScope'(newValue) {
            if (newValue[1] - newValue[0] >= 60 * 60 * 24 * 365 * 1000) {
                this.$message.error(this.lang.statistic.time_range_tip);
            } else {
                newValue[1] = new Date(moment(newValue[1]).add(1, 'month').valueOf() - 1)
            }
            this.handleFilter()
        }
    },
    methods: {
        async searchOneTimeOnCreated() {
            this.$nextTick(() => {
                this.pie_chart = this.$echarts.init(this.$refs.pie_chart)
                this.pie_chart.clear()
                this.drawPieChart()
            })
        },
        async handleFilter() {
            this.fullscreenLoading = true
            const listData = await request.getDopplerExamListData(this.listQuery)
            const { deviceStatusMap, distributionMap, list, total } = listData.data.data;
            this.deviceCount = Object.values(deviceStatusMap).reduce((a,b) => a+b)
            this.working = deviceStatusMap[0]
            this.standby = deviceStatusMap[2]
            this.offline = deviceStatusMap[1]
            this.list = list;
            this.total = total;
            this.$nextTick(() => {
                this.bar_chart = this.$echarts.init(this.$refs.bar_chart)
                this.bar_chart.clear()
                this.drawLine(distributionMap)
            })
            this.fullscreenLoading = false
        },
        async getList() {
            this.fullscreenLoading = true
            const data = await request.getDopplerExamListData(this.listQuery)
            const {list, total} = data.data.data
            this.list = list
            this.total = total
            this.fullscreenLoading = false
        },
        async drawLine(distributionMap) {
            for(let item of this.overallDistribution){
                item.value = 0;
            }
            this.totalExamCount = 0;
            for(let key in distributionMap){
                const type = examTypeMap[key];
                if (type !== undefined) {
                    for(let item of this.overallDistribution){
                        if(item.key == type){
                            item.value += distributionMap[key];
                        }
                    }
                }else{
                    this.overallDistribution[9].value += distributionMap[key];
                }
                this.totalExamCount += distributionMap[key];
            }
            this.bar_chart.setOption(this.buildBarChartData())
        },
        async drawPieChart() {
            const pieChartData = await request.getDopplerInstallData(this.listQuery)
            this.pie_chart.setOption(this.buildPieChartData(pieChartData.data.data))
        },
        buildBarChartData() {
            const xData = this.overallDistribution.map((item) => this.lang.statistic.all_exam_types[item.key] || this.lang.statistic.other_exam_type)
            this.xData = xData
            const yData = this.overallDistribution.map((item) => item.value)
            const sum = this.totalExamCount
            const title = this.lang.statistic.examIncreased;
            const params = {
                title: { top: 10, left: 10, text: `${title}（${sum}）` },
                tooltip: { trigger: 'axis' },
                backgroundColor: '#f2f2f2',
                grid: {
                    left: '1%',
                    right: '1%',
                    bottom: '0%',
                    containLabel: true
                },
                xAxis: {
                    data: xData,
                    axisTick: {
                        show: false
                    }
                },
                yAxis: {
                    splitNumber: 4
                },
                series: [
                    {
                        barMaxWidth: '35px',
                        itemStyle: {
                            color: "#1E90FF"
                        },
                        label: {
                            show: true
                        },
                        type: 'bar',
                        data: yData
                    }
                ]
            };
            return Tool.oldAgeEdition(params)
        },
        buildPieChartData(data) {
            const legendData = Object.values(this.lang.statistic.year_range)
            const seriesData = Object.keys(data).map((key) => {
                return {name: this.lang.statistic.year_range[key], value: data[key]}
            })
            const params = {
                title: { top: 10, left: 10, text: this.lang.statistic.device_install_time_chart, textStyle: { fontSize: 30 }},
                tooltip: { // 提示
                    trigger: "item", // 触发方式
                    formatter: "{b}: {c} ({d}%)"  // 提示的格式
                },
                backgroundColor: '#f2f2f2',
                legend: {
                    orient: "vertical",
                    top: "30%",
                    right: "20%",
                    textStyle: {
                        color: "#4F4F4F",
                        fontSize: 30
                    },
                    data: legendData
                },
                series: [
                    {
                        type: 'pie',
                        radius: "65%",
                        center: ["40%", "50%"],
                        avoidLabelOverlap: true,
                        label: {normal: {show: false}},
                        labelLine: {
                            normal: {
                                show: false
                            },
                            fontSize: 30
                        },
                        data: seriesData,
                        animationEasing: 'elasticOut',
                    }
                ]
            };
            return params;
        },
        presetTimeScope(type) {
            this.listQuery.timeScope = Tool.presetTimeScope(type)
        },
        async exportExcel() {
            this.buttonLoading = true
            await this.doExportExcel();
            this.buttonLoading = false
        },
        async doExportExcel() {
            const statistic = this.lang.statistic;
            const res = await download.exportExcel(Object.assign({ chartData: [[statistic.time_txt, ...this.xData]] }, this.listQuery, { type: 'deviceExam' }))
            let blob = new Blob([res.data], { type: "application/xlsx" });
            const url = window.URL.createObjectURL(blob); // 设置路径
            const link = this.$refs.link;
            link.href = url;
            link.download = `${statistic.device_exam_report}-${moment().format("YYYY-MM-DD z")}.xlsx`; // 设置文件名
            link.click();
            URL.revokeObjectURL(url); // 释放内存
        },
    },
}
</script>
<style lang="scss">
.title {
  font-size: 30px;
  text-align: center;
  margin-bottom: 10px;
}

.app-container {
  padding: 20px;
}

.block-title {
  position: absolute;
  font-size: 30px;
  top: 20px;
  left: 30px;
}

.context {
  font-size: 66px;
  font-weight: 1000;
}

.block-foot {
  position: absolute;
  bottom: 20px;
  right: 30px;
  font-size: 30px;
}

.foot {
  position: absolute;
  bottom: 20px;
  right: 30px;
  font-size: 24px;
  font-weight: 500;
}

.mainStyle {
  color: #FFF;
  justify-content: center;
  display: flex;
  align-items: center;
  height: 200px;
  position: relative;
}

.block1 {
  background-color: #5CC9FA;
}

.block2 {
  background-color: #59C29F;
}

.block3 {
  background-color: #ED7163;
}
.block4 {
  background-color: #F3CA62;
}

.table-top {
  margin: 10px 0;
}

.groupSearch {
  width: 300px;
  margin: 0 10px;
}
.barChart {
  width: 100%;
  height: 500px;
}
</style>

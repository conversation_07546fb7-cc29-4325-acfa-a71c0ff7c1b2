var ConversationConfig = {
    //会话类型
    type: {
        Single:1,
        Group:2,
        GroupSet:3
    },

    //会话模式：群聊、单聊
    mode: {
        Group: 0,
        Single: 1
    },


    //设定群多大人数为会议模式
    group_mode_num: 10,

    //默认会议模式下，能有几个人发言
    //当前不控制，设置到特别大
    group_can_speek_num: 10000,

    //是否显示会话界面
    ui: {
        Hide: 0,
        Show: 1
    },

    //微信分享支持最大图片大小 800kb
    share_img_size: 800 * 1024,

    //会话启动类型
    start_type: {
        Default: 0,
        Gallery: 1,
        UltrasoundDesktop:2,
        VoiceSession:3,
        HistoryChatMessage:4,
        SendTo:5,
        TransmitConsultationFile:6,
        RequestMonitorWall:7,
        MonitorWall:8,
        Comment:9,
        RequestUltrasoundDesktop:10,
        RequestUltrasoundDesktopByMonitorWall:11,
        KickoutAttendee:12,
        NewChatMessage:13,
        RequestStartDeviceUltrasoundDesktop:14,
        RequestStopDeviceUltrasoundDesktop:15,
        RequestDeleteChatMessages:16,
        NotifyDeleteChatMessages:17
    },

    //消息类型
    msg_type: {
        Text: 0,
        Image: 1,
        File: 2,
        Frame: 3,
        Cine: 4,
        Sound: 5,
        RealTimeVideo: 6,
        Video:7,
        RealTimeVideoReview:8,
        MedicRealTimeVideoReview:9,//预留类型，实时回放入库后类型

        SYS_START_RT_VOICE:11,
        SYS_STOP_RT_VOICE:12,
        SYS_START_REALTIME_CONSULTATION:13,
        SYS_STOP_REALTIME_CONSULTATION:14,
        SYS_START_REALTIME_CONFERENCE:15,
        SYS_STOP_REALTIME_CONFERENCE:16,

        SYS_REQUEST_RT_CONSULTATION:17,
        SYS_JOIN_ATTENDEE:18,
        SYS_KICKOUT_ATTENDEE:19,

        SYS_CONFERENCE_PLAN:25,
        VIDEO_CLIP:26,
        COMMENT:30,
        TAG:31,

        IWORKS_PROTOCOL:41,
        ExamImages: 42, // 病例集合
        AI_ANALYZE:100
    },

    //画廊图像模式
    gallery_image_mode: {
        None: 0,
        Image: 1,
        Video: 2,
        Frame: 3,
        Cine: 4,
        RealTimeVideo:6
    },

    start_catch_type: {
        UltrasoundDesktop: 1,
        MonitorWall: 2,
        CameraVideoEx: 3,
        StorageConsultationFile: 4,
        LocalDesktop: 5,
        CameraDesktop: 6,
        MobileUltraSound:7 // 移动端发起的超声直播
    },

    voice_connect_type:{
        webrtc: 1,
        rtmp: 2
    },

    voice_identity: {
        start: 0,
        accept: 1,
    },

    voice_state_start:{
        none: 0,
        starting: 1,
        start: 2,
        closing: 3
    },

    voice_state_accept:{
        none: 0,
        unaccept: 1,
        accepting: 2,
        accept: 3,
        closing:4
    },

    start_ultrasound_desktop_timeout: 28800000,

    role: {
        Member: 0,       //普通成员
        Admin: 1,        //管理员
        SuperAdmin: 2,   //超级管理员
        TmpMember: 3    //临时成员
    },

    visual: {
        Private:0,
        Public:1
    },

    view_mode: {
        Common:0,
        ExamList:1
    },

    rt_video_state: {
        None: 0,
        Start: 1,
        Enter: 2,
        EnterSelf: 3
    },

    rt_voice_state: {
        None: 0,
        Start: 1,
        Enter: 2,
        EnterSelf: 3
    },

    comment_action: {
        Add:1,
        Delete:2,
        Edit:3
    },

    tag_action: {
        Add:1,
        Delete:2
    },

    conference_plan_tip_type:{
        New:1,
        Prepare:2,
        Cancel:3
    }
};
export default ConversationConfig

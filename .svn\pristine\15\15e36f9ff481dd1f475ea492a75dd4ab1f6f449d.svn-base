<template>
<div>
    <!-- <el-dialog
      class="version_info_page"
      :title="lang.system_info_text"
      :visible="true"
      :close-on-click-modal="false"
      width="480px"
      :modal="false"
      :before-close="back"
      v-click-n-times:7="handleShowVersion"> -->
    <CommonDialog class="version_info_page"
        :title="lang.system_info_text"
        :show.sync="visible"
        width="480px"
        :close-on-click-modal="false"
        :modal="false"
        @closed="back"
        :footShow="false"
        v-click-n-times:7="handleShowVersion"
    >
        <div class="wraper">
            <div class="main">
                <div class="fl info_left">
                    <img src="static/resource_pc/images/new_logo.png">
                    <p class="system_info" @click="showSystemInfo" v-if="!globalParams.isCE||showVersion">{{lang.app_and_server_build_version}}</p>
                    <p class="history_version" @click="openHistoryVersion" v-if="EnableHistoryVersion">{{lang.history_version}}</p>
                    <p class="faq" @click="openFaq" v-if="EnableFaq">{{lang.faq_title}}</p>
                    <p class="import_license" v-show="isSHowImportlicense" @click="importlicense">{{lang.import_license}}</p>
                    <p class="instruction_manual" @click="openInstructionManual">{{lang.instruction_manual}}</p>
                    <p class="privacy_policy" @click="openPrivacyPolicyPage">{{lang.privacy_policy}}</p>
                </div>

                <div class="fl info_right">
                    <template v-if="PROJECT_NOV==='CN'">
                        <p  class="title">{{lang.product_name}}：{{lang.product_name_value_cn}}</p>
                        <p>{{lang.software_version_number}}：{{serverInfo.REGISTER_VERSION}}</p>
                        <p>{{lang.registration_certificate_no}}：粤械注准20182211110</p>
                        <p class="des">{{lang.software_description_cn}}</p>
                    </template>
                    <template v-if="PROJECT_NOV==='CE'">
                        <p class="title">{{lang.product_name}}：{{lang.product_name_value_ce}}</p>
                        <p class="title">{{lang.product_model}}：UltraSync</p>
                        <p>{{lang.software_version_number}}：{{serverInfo.REGISTER_VERSION}}</p>

                        <p class="des">{{lang.software_description_ce}}
                        </p>
                    </template>
                    <!-- <p>{{lang.remote_ultrasonic_consultation_system}}{{serverInfo.REGISTER_VERSION}}</p> -->


                </div>
            </div>
            <div>
                <div class="clearfix">
                    <el-button @click="back" type="primary" size="mini" class="fr">{{lang.confirm_txt}}</el-button>
                </div>
            </div>
        </div>
        <!-- <el-dialog
        class="history_version_dialog"
        :title="lang.history_version"
        :visible="isShowHistory"
        :close-on-click-modal="false"
        width="640px"
        :modal="false"
        :append-to-body="true"
        :before-close="closeHistory"> -->
        <CommonDialog class="history_version_dialog"
            :title="lang.history_version"
            :show.sync="isShowHistory"
            width="640px"
            :close-on-click-modal="false"
            :modal="false"
            @closed="closeHistory"
            :footShow="false"
        >
        <!-- <vue-scroll v-loading="isHistoryVersionLoading"> -->
            <el-collapse v-model="activeNames">
                 <el-collapse-item v-for="(item,index) of list" :name="index" :key="index">
                    <template slot="title">
                      <span class="version_title">{{item.version}}</span>
                      <span class="version_title">{{item.time}}</span>
                    </template>
                    <div v-for="(chapter,chapterIndex) of item.content" :key="chapterIndex">
                        <div v-for="(section,sectionIndex) of chapter" :key="sectionIndex">
                            <div v-if="'text'==section.type" class="history_version_introduce_section_text">
                                <p>{{section.text}}</p>
                            </div>
                            <div v-else-if="'image'==section.type" class="history_version_introduce_section_images">
                                <div v-for="(image,imageIndex) of section.images" class="history_version_introduce_section_image" :key="imageIndex">
                                    <img :src="image.url"/>
                                </div>
                            </div>
                        </div>
                    </div>
                 </el-collapse-item>
            </el-collapse>
        <!-- </vue-scroll> -->
        <!-- </el-dialog> -->
        </CommonDialog>
        <!-- <el-dialog
        class="faq_dialog"
        :title="lang.faq_title"
        :visible="isShowFaq"
        :close-on-click-modal="false"
        width="840px"
        :modal="false"
        :append-to-body="true"
        :before-close="closeFaq"> -->
        <CommonDialog class="faq_dialog"
            :title="lang.faq_title"
            :show.sync="isShowFaq"
            width="840px"
            :close-on-click-modal="false"
            :modal="false"
            @closed="closeFaq"
            :footShow="false"
        >
        <!-- <vue-scroll v-loading="isFaqLoading"> -->
            <el-collapse v-model="activeNames">
                <el-collapse-item v-for="(item,itemIndex) of faq_list" :name="index" class="faq_model" :key="itemIndex">
                    <template slot="title">
                        <span class="faq_model_title">{{item.model_name}}</span>
                    </template>
                    <div class="faq_model_content">
                        <div class="faq_model_content_chapter" v-for="(chapter,chapterIndex) of item.content" :key="chapterIndex">
                            <div class="faq_question">{{chapter.question}}</div>
                            <div class="faq_answer">
                                <div v-for="(section,sectionIndex) of chapter.answer" :key="sectionIndex">
                                    <p class="faq_answer_section">{{section}}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </el-collapse-item>
            </el-collapse>
        <!-- </vue-scroll> -->
        <!-- </el-dialog> -->
        </CommonDialog>
        <InstructionManual :show="showInstructionManual" @closeInstructionManual="closeInstructionManual"></InstructionManual>
        <PersonalPrivacy :show="showPersonalPrivacy" @closePrivacyPolicyPage="closePrivacyPolicyPage"></PersonalPrivacy>
        <PrivacySettings :show="showPrivacySettings" @closePrivacySettings="closePrivacySettings"></PrivacySettings>
    <!-- </el-dialog> -->
    </CommonDialog>
</div>

</template>
<script>
import base from '../lib/base'
import service from '../service/service'
import Tool from '@/common/tool.js'
import PersonalPrivacy from '../components/personalPrivacy.vue'
import CommonDialog from '../MRComponents/commonDialog.vue'
export default {
    mixins: [base],
    name: 'versionInfo',
    components: {
        InstructionManual: () => import(/* webpackPrefetch: true */ '../components/instructionManual.vue'),
        PersonalPrivacy,
        CommonDialog,
        PrivacySettings: () => import(/* webpackPrefetch: true */ '../components/privacySettings.vue'),
    },
    data(){
        return {
            PROJECT_NOV:process.env.VUE_APP_PROJECT_NOV,
            serverInfo:{
                VERSION_INTRODUCTION:'',
                REGISTER_VERSION:'',
                client_version:''
            },
            isShowHistory:false,
            isHistoryVersionLoading:true,
            list:[],
            activeNames:[],

            isShowFaq:false,
            isFaqLoading:true,
            faq_list:[],
            visible:false,
            showInstructionManual:false,
            showVersion:false,
            showPersonalPrivacy:false,
            showPrivacySettings:false
        }
    },
    computed:{
        EnableHistoryVersion(){ // 是否允许查看历史版本记录
            return this.$store.state.systemConfig.serverInfo.system_infomation
            &&this.$store.state.systemConfig.serverInfo.system_infomation.enable_history_version
        },
        EnableFaq(){ // 是否允许查看问题解答
            return this.$store.state.systemConfig.serverInfo.system_infomation
            &&this.$store.state.systemConfig.serverInfo.system_infomation.enable_faq
        },
        isSHowImportlicense(){
            let is_trial = 0;
            if (window.license_info && 1 == window.license_info.is_trial) {
                is_trial = 1;
            }
            return Tool.ifAppWorkstationClientType(window.clientType) && is_trial;
        },
    },
    mounted(){
        this.$nextTick(()=>{
            let info;
            if (window.app_info) {
                info=window.app_info
            }else{
                info=window.server_info.WebIM
            }
            this.visible=true;
            this.serverInfo.VERSION_INTRODUCTION=info.VERSION_INTRODUCTION
            this.serverInfo.REGISTER_VERSION=info.REGISTER_VERSION
            this.serverInfo.client_version=`${info.MajorVersion}(Rev${info.MinorVersion})`
        })
    },
    methods:{
        showSystemInfo(){
            this.$MessageBox.alert(this.serverInfo.client_version,this.lang.app_and_server_build_version)
        },
        openHistoryVersion(){
            this.isShowHistory=true;
            this.queryHistoryVersion()
        },
        queryHistoryVersion(){
            let that = this;
            let language = window.localStorage.getItem('lang') || 'CN';
            let type = 'PC';
            service.query_history_version({
                type: type,
                language: language
            }).then((res)=>{
                //console.log(res);
                that.isHistoryVersionLoading = false;
                that.list = res.data.list;
                // that.$store.commit('historyVersion/initHistoryVersionList',list);
            }).catch(res=>{
                that.isHistoryVersionLoading = false;
                that.$message.error(that.lang.query_history_version_fail);
            })
        },
        closeHistory(){
            this.isShowHistory=false;
        },
        openFaq(){
            this.isShowFaq = true;
            if (this.isFaqLoading) {
                this.queryFaq();
            }
        },
        queryFaq(){
            let that = this;

            let language = window.localStorage.getItem('lang') || 'CN';
            let type = 'PC';
            window.main_screen.controller.emit("query_faq", {
                type: type,
                language:language
            }, function(err, result) {
                that.isFaqLoading = false;

                if (err) {
                    that.$message.error(that.lang.query_faq_fail);
                } else {
                    that.faq_list = result.content || [];
                }
            });
        },
        closeFaq(){
            this.isShowFaq=false;
        },
        importlicense(){
            window.CWorkstationCommunicationMng.importlicense();
        },
        openInstructionManual(){
            this.showInstructionManual = true
        },
        closeInstructionManual() {
            this.showInstructionManual = false;
        },
        handleShowVersion(){
            this.showVersion = true
        },
        openPrivacyPolicyPage(){
            this.showPrivacySettings = true;
        },
        closePrivacyPolicyPage(){
            this.showPersonalPrivacy = false
        },
        closePrivacySettings(){
            this.showPrivacySettings = false;
        }
    }
}
</script>
<style lang="scss">
.version_info_page{
    /* &.el-dialog__wraper{
        .el-dialog{
            height:340px !important;
            margin-top:calc(50vh - 160px) !important;
        }
    } */

    .wraper{
        height:100%;
        flex-direction: column;
        .main{
            .info_left{
                width:150px;
                img{
                    width:90%;
                    margin-right:10px;
                    margin-top: 10px;
                    margin-bottom:10px;
                }
            }
            height: 250px;
            overflow: auto;
            display: flex;

            .info_right{
                width:300px;
                .title{
                    line-height: 1.8;
                    font-size: 16px;
                    color: #000;
                }
                .des{
                    margin-top:10px;
                }
            }

        }
        .system_info,.history_version,.faq,.import_license,.instruction_manual,.privacy_policy{
            cursor:pointer;
        }
    }
}
.history_version_dialog{
    .version_title{
        flex:1;
    }
    .history_version_introduce_section_images{
        img{
            max-width:100%;
        }
    }
}

.faq_dialog{
    .faq_model_title {
        font-size:20px;
        flex:1;
    }
    .faq_model_content {
        .faq_model_content_chapter {
            margin-top:10px;
            border:1px solid gray;
            border-radius:4px;
            padding:0px 5px;
            .faq_question{
                font-size:18px;
                font-weight:bold;
                margin-bottom:5px;
            }
            .faq_answer_section {
                text-indent:1.3rem;
            }
        }
    }
}
</style>

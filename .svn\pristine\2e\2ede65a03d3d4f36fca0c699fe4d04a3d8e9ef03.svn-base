
import cloneDeep from 'lodash/cloneDeep'
import Vue from 'vue'

const initState = {
    /*"1":{
        first_init:true,//每次建立连接时的初始化标记
        isConnecting: true,
        client_type:7,
        name:null,
        cur_session_id:null,
        cur_session_type:null,
        cur_session_type:0,
        device_id:1,
        patient_id: "123",
        patient_name: "test_name",
        saved_list:[],
        open:false,
        isAnonymous: false,
        ftp_account: 'apollo',
        ftp_password: 'appolo_pwd',
        ftp_path: '/apollo_path',
        ftp_port: 22,
    },
    "2":{
        first_init:true,//每次建立连接时的初始化标记
        isConnecting: true,
        client_type:6,
        name:null,
        cur_session_id:null,
        cur_session_type:null,
        cur_session_type:0,
        device_id:2,
        patient_id: "123",
        patient_name: "test_name",
        saved_list:[],
        open:false,
        isAnonymous: false,
        ftp_account: 'apollo',
        ftp_password: 'appolo_pwd',
        ftp_path: '/apollo_path',
        ftp_port: 22,
    }, */
}
export default {
    state: cloneDeep(initState),
    mutations: {
        resetStore(state){
            if(window.vm.$store.state){
                Vue.set(window.vm.$store.state,'deviceCtrl',cloneDeep(initState))
            }
        },
        initDeviceCtrl(state, device_id){
            console.log("********************* initDeviceCtrl *************************");
            //初始化会话，在数据没返回前能进入聊天界面
            var defaultDeviceCtrl={
                first_init:true,//每次建立连接时的初始化标记
                isConnecting: false,
                client_type:0,
                name:null,
                cur_session_id:null,
                cur_session_type:null,
                cur_session_type:0,
                device_id:device_id,
                patient_id: "",
                patient_name: "",
                saved_list:[],
                open:false,
                isAnonymous: false,
                ftp_account: '',
                ftp_password: '',
                ftp_path: '',
                ftp_port: 22,
            }
            Vue.set(state, device_id, defaultDeviceCtrl);
        },
        updateDeviceCtrl(state, valObj) {
            if(valObj.device_id){
                let id = valObj.device_id + "";
                if(!state[id]){
                    this.commit("deviceCtrl/initDeviceCtrl", id);
                }

                for(let key in valObj){
                    Vue.set(state[id], key, valObj[key]);
                }
            }else{
                console.log("updateDeviceCtrl error !!!", valObj);
            }
        },
        deleteDeviceCtrl(state, device_id) {
            Vue.delete(state, device_id);
        },
        clearDeviceCtrl(state){//未见调用
            //清空所有会话
            for(let key in state){
                delete state[key]
            }
        },
        addSavedList(state,file){
            let id = file.device_id + "";
            state[id].saved_list.push(file)
        },
        updateSavedImage(state,obj){
            let id = obj.device_id + "";
            let imgObj=state[id].saved_list[obj.index];
            imgObj.url_local=obj.url_local;
            imgObj.realUrl=obj.url_local;
            state[id].saved_list.splice(obj.index,1,imgObj)
        }

    },
    actions: {},
    getters: {}
}

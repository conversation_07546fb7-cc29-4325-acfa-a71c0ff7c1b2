import cloneDeep from 'lodash/cloneDeep'
import Vue from 'vue'
const initState ={
    list:[],
    remarkMap:{},
}
export default {
    state: cloneDeep(initState),
    mutations: {
        resetStore(state){
            if(window.vm.$store.state){
                Vue.set(window.vm.$store.state,'friendList',cloneDeep(initState))
            }
        },
        initFriendList(state, list) {
            state.list.splice(0);
            for(let friend of list){
            	state.list.push(friend);
                if (friend.alias) {
                    Vue.set(state.remarkMap,friend.id,friend.alias);
                }
            }
        },
        addFriendList(state,obj){
            // obj.service_type=0
            if(obj.id){
                const res = state.list.some(item=>item.id === obj.id)
                if(!res){
                    state.list.push(obj)
                    if (obj.alias) {
                        Vue.set(state.remarkMap,obj.id,obj.alias);
                    }
                }
            }
        },
        clearFriendList(state){
            state.list.splice(0)
            state.remarkMap={}
        },
        updateFriendToFriendList(state,user){
            for(let i=0;i<state.list.length;i++){
                if (state.list[i].id==user.id) {
                    const temp=state.list[i];
                    user = Object.assign(temp,user);
                    state.list.splice(i,1,user)
                    break;
                }
            }
        },
        updateFriendAvatarLocalUrl(state,data){
            for(let i=0;i<state.list.length;i++){
                if (state.list[i].id==data.id) {
                    let user=state.list[i];
                    user.avatar_local=data.avatar_local
                    state.list.splice(i,1,user)
                    break;
                }
            }
        },
        updateRemark(state,data){
            Vue.set(state.remarkMap,data.fid,data.alias);
            // state.remarkMap[data.fid]=data.alias;
            for(let friend of state.list){
                if (friend.id===data.fid) {
                    friend.alias=data.alias;
                    break;
                }
            }
        },
    },
    actions: {},
    getters: {}
}

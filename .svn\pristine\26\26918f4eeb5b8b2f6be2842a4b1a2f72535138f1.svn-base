<!-- eslint-disable vue/no-mutating-props -->
<template>
    <div class="setting_module" :id="`RecursiveSettingItem_${node.id}`" v-if="showNode">
        <el-collapse v-model="activeCollapse">
            <el-collapse-item :title="node.title" :name="node.id" v-if="node.nodeType === 'node'">
                <template slot="title">
                    <div class="setting_module_title" @contextmenu.prevent="showContextMenu($event, node.id, 'node')">
                        <div v-if="!node.editingTitle" class="setting_module_title_text">
                            <p>{{ node.title }}</p>
                            <span v-if="node.field !== 'collectionGroup'">{{ lang.existing_items }}：{{ calcItemCount(node) }}</span>
                        </div>
                        <div v-else class="setting_module_title_edit">
                            <el-input v-model="node.newTitleValue" @focus.native.stop @click.native.stop></el-input>
                            <el-button @click.stop="setNewTitle(node)" type="primary" size="mini">{{
                                lang.confirm_txt
                            }}</el-button>
                            <el-button @click.stop="cancelSetNewTitle(node)" type="danger" size="mini">{{
                                lang.cancel_btn
                            }}</el-button>
                        </div>
                    </div>
                </template>
                <template v-if="node.field === 'collectionGroup'">
                    <div class="collection_group_tools">
                        <el-button @click="handleAddCollectionGroupOption(node)" type="primary" size="mini">{{
                            lang.edit_collection_group
                        }}</el-button>
                    </div>
                    <el-radio-group v-model="node.activeCollectionGroupOptionId" @change="handleCollectionGroupOptionChange" class="collection_group_radio_group">
                        <el-radio :label="item.id" v-for="item in node.collectionGroupOptions" :key="item.id" border size="mini">{{ item.label }}</el-radio>
                    </el-radio-group>
                </template>
                <template v-if="node.children && node.children.length">
                    <RecursiveSettingItem
                        v-for="child in node.children"
                        :key="child.id"
                        :node="child"
                        @showContextMenu="showContextMenu2"
                        @deleteElementByNodeId="deleteElementByNodeId"
                        @cancelEditElementByNodeId="cancelEditElementByNodeId"
                        @updateElementByNodeInfo="updateElementByNodeInfo"
                        @handleEditOptions="handleEditOptions"
                        @handleAddCollectionGroupOption="handleAddCollectionGroupOption"
                        @handleCollectionGroupOptionChange="handleCollectionGroupOptionChange"
                    />
                </template>
            </el-collapse-item>
        </el-collapse>
        <div
            class="item"
            v-if="node.nodeType === 'element'"
            @contextmenu.prevent="showContextMenu($event, node.id, 'element')"
        >
            <div v-if="(node.addingElement||node.editingElement) && node.controlType === 'string'" class="item_title_edit">
                <div class="item-edit-field">
                    <div class="name">
                        <el-input
                            :placeholder="lang.filed_name"
                            v-model.trim="node.title"
                            maxlength="50"
                            show-word-limit
                        ></el-input>
                    </div>
                    <div class="unit">
                        <el-input
                            :placeholder="lang.unit"
                            v-model.trim="node.unit"
                            maxlength="50"
                            show-word-limit
                        ></el-input>
                    </div>
                </div>
                <div class="item-edit-btns">
                    <el-button @click="addItem(node)" type="primary" size="mini">{{ lang.confirm_txt }}</el-button>
                    <el-button @click="cancelAddItem(node)" type="danger" size="mini" v-if="node.addingElement||node.editingElement">{{ lang.action_delete_text }}</el-button>
                </div>
            </div>
            <div v-else-if="(node.addingElement||node.editingElement) && node.controlType === 'radio'" class="item_title_edit">
                <div class="item-edit-field">
                    <div class="name">
                        <el-input
                            :placeholder="lang.filed_name"
                            v-model.trim="node.title"
                            maxlength="50"
                            show-word-limit
                        ></el-input>
                    </div>
                    <div class="radio_group">
                        <template v-for="option of node.options">
                            <div :key="option.id" class="radio_group_item">
                                <el-radio
                                    v-model="node.value"
                                    :label="option.id"
                                    @click.native.prevent="handleRadioClick(node, option.id)"
                                >{{ option.label }}</el-radio>
                                <template v-if="option.requiresRemark && node.value === option.id">
                                    <el-input
                                        v-model="option.remarkValue"
                                        size="mini"
                                        maxlength="100"
                                        show-word-limit
                                        :disabled="true"
                                    />
                                    <span v-if="option.remarkUnit" class="unit">{{ option.remarkUnit }}</span>
                                </template>
                            </div>
                        </template>
                    </div>
                </div>
                <div class="item-edit-btns">
                    <el-button @click="editOptions(node)" type="primary" size="mini">{{ lang.edit_options }}</el-button>
                    <el-button @click="addItem(node)" type="primary" size="mini">{{ lang.confirm_txt }}</el-button>
                    <el-button @click="cancelAddItem(node)" type="danger" size="mini" v-if="node.addingElement||node.editingElement">{{ lang.action_delete_text }}</el-button>
                </div>
            </div>
            <div v-else-if="(node.addingElement||node.editingElement) && (node.controlType === 'checkbox'||node.controlType === 'specialCheck')" class="item_title_edit">
                <div class="item-edit-radio">
                    <div class="name">
                        <el-input
                            :placeholder="lang.filed_name"
                            v-model.trim="node.title"
                            maxlength="50"
                            show-word-limit
                        ></el-input>
                    </div>
                    <div class="checkbox_group">
                        <template v-for="option of node.options">
                            <div :key="option.id" class="checkbox_group_item">
                                <el-checkbox v-model="node.value" :label="option.id">{{ option.label }}</el-checkbox>
                                <template v-if="option.requiresRemark && node.value.includes(option.id)">
                                    <el-input
                                        v-model="option.remarkValue"
                                        size="mini"
                                        maxlength="100"
                                        show-word-limit
                                    />
                                    <span v-if="option.remarkUnit" class="unit">{{ option.remarkUnit }}</span>
                                </template>
                            </div>
                        </template>
                    </div>
                </div>
                <div class="item-edit-btns">
                    <el-button @click="editOptions(node)" type="primary" size="mini">{{ lang.edit_options }}</el-button>
                    <el-button @click="addItem(node)" type="primary" size="mini">{{ lang.confirm_txt }}</el-button>
                    <el-button @click="cancelAddItem(node)" type="danger" size="mini" v-if="node.addingElement||node.editingElement">{{ lang.action_delete_text }}</el-button>
                </div>
            </div>
            <div v-else-if="(node.addingElement||node.editingElement) && node.controlType === 'imageTag'" class="item_title_edit">
                <div class="item-edit-radio">
                    <div class="checkbox_group">
                        <el-select v-model="node.value">
                            <el-option
                                v-for="option in node.options"
                                :key="option.id"
                                :label="option.label"
                                :value="option.id"
                            ></el-option>
                        </el-select>
                    </div>
                </div>
                <div class="item-edit-btns">
                    <el-button @click="editOptions(node)" type="primary" size="mini">{{ lang.edit_options }}</el-button>
                    <el-button @click="addItem(node)" type="primary" size="mini">{{ lang.confirm_txt }}</el-button>
                    <el-button @click="cancelAddItem(node)" type="danger" size="mini" v-if="node.addingElement||node.editingElement">{{ lang.action_delete_text }}</el-button>
                </div>
            </div>
            <div v-else-if="(node.addingElement||node.editingElement) && node.controlType === 'dateTime'" class="item_title_edit">
                <div class="item-edit-dateTime">
                    <el-row :gutter="10">
                        <el-col :span="12">
                            <el-input
                                :placeholder="lang.filed_name"
                                v-model.trim="node.title"
                                maxlength="50"
                                show-word-limit
                                style="width: 100%"
                                size="small"
                            ></el-input>
                        </el-col>
                        <el-col :span="12">
                            <el-select
                                v-model="node.format"
                                @change="handleFormatChange(node)"
                                size="small"
                                style="width: 100%; height: 30px"
                            >
                                <el-option
                                    v-for="option in node.options"
                                    :key="option"
                                    :label="lang.formatDateTime[option]"
                                    :value="option"
                                ></el-option>
                            </el-select>
                        </el-col>
                    </el-row>
                    <el-row :gutter="10" style="margin-top: 10px">
                        <el-col :span="12">
                            <el-date-picker
                                v-model.trim="node.value"
                                :type="
                                    node.format === 'yyyy' ? 'year' : node.format === 'yyyy-MM' ? 'month' : 'datetime'
                                "
                                :format="node.format"
                                :value-format="node.format"
                                @change="handleDateTimeChange(node)"
                                size="small"
                                style="width: 100%"
                            ></el-date-picker>
                        </el-col>
                    </el-row>
                </div>
                <div class="item-edit-btns">
                    <el-button @click="addItem(node)" type="primary" size="mini">{{ lang.confirm_txt }}</el-button>
                    <el-button @click="cancelAddItem(node)" type="danger" size="mini" v-if="node.addingElement||node.editingElement">{{ lang.action_delete_text }}</el-button>
                </div>
            </div>

            <div v-else-if="node.editingTitle" class="item_title_edit">
                <el-input v-model="node.newTitleValue" @focus.native.stop @click.native.stop></el-input>
                <el-button @click.stop="setNewTitle(node)" type="primary" size="mini">{{ lang.confirm_txt }}</el-button>
                <el-button @click.stop="cancelSetNewTitle(node)" type="danger" size="mini">{{
                    lang.cancel_btn
                }}</el-button>
            </div>
            <template v-else>
                <p class="name" v-if="node.title">{{ node.title }}</p>
                <template v-if="node.controlType === 'string'">
                    <el-input :placeholder="node.placeholder" />
                    <p class="unit">{{ node.unit }}</p>
                </template>
                <template v-else-if="node.controlType === 'date'">
                    <template v-if="node.field === 'examDate'">
                        <el-date-picker
                            v-model.trim="node.value"
                            type="date"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                            :disabled="true"
                        ></el-date-picker>
                    </template>
                    <template v-else>
                        <el-date-picker
                            v-model.trim="node.value"
                            type="date"
                            format="yyyy-MM-dd HH:mm"
                            value-format="yyyy-MM-dd HH:mm"
                            :disabled="true"
                        ></el-date-picker>
                    </template>
                    <p class="unit">{{ node.unit }}</p>
                </template>
                <template v-else-if="node.controlType === 'select'">
                    <el-select v-model="node.value">
                        <el-option value="-1" label=" " v-show="false"></el-option>
                        <el-option
                            v-for="option of node.options"
                            :key="option.id"
                            :value="option.id"
                            :label="option.label"
                        ></el-option>
                    </el-select>
                    <p class="unit">{{ node.unit }}</p>
                </template>
                <template v-else-if="node.controlType === 'radio'">
                    <div class="radio_group">
                        <template v-for="option of node.options">
                            <div :key="option.id" class="radio_group_item">
                                <el-radio
                                    v-model="node.value"
                                    :label="option.id"
                                    @click.native.prevent="handleRadioClick(node, option.id)"
                                >{{ option.label }}</el-radio>
                                <template v-if="option.requiresRemark && node.value === option.id">
                                    <el-input
                                        v-model="option.remarkValue"
                                        size="mini"
                                        maxlength="100"
                                        show-word-limit
                                        :disabled="true"
                                    />
                                    <span v-if="option.remarkUnit" class="unit">{{ option.remarkUnit }}</span>
                                </template>
                            </div>
                        </template>
                    </div>
                </template>
                <template v-else-if="node.controlType === 'checkbox'">
                    <div class="checkbox_group">
                        <template v-for="option of node.options">
                            <div :key="option.id" class="checkbox_group_item">
                                <el-checkbox v-model="node.value" :label="option.id">{{ option.label }}</el-checkbox>
                                <template v-if="option.requiresRemark && node.value.includes(option.id)">
                                    <el-input
                                        v-model="option.remarkValue"
                                        size="mini"
                                        maxlength="100"
                                        show-word-limit
                                        :disabled="true"
                                    />
                                    <span v-if="option.remarkUnit" class="unit">{{ option.remarkUnit }}</span>
                                </template>
                            </div>
                        </template>
                    </div>
                </template>
                <template v-else-if="node.controlType === 'specialCheck'">
                    <special-check :options="node.options" :value.sync="node.value"></special-check>
                </template>
                <template v-else-if="node.controlType === 'dateTime'">
                    <el-row :gutter="10">
                        <el-col :span="12">
                            <el-date-picker
                                v-model.trim="node.value"
                                :type="
                                    node.format === 'yyyy' ? 'year' : node.format === 'yyyy-MM' ? 'month' : 'datetime'
                                "
                                :format="node.format"
                                :value-format="node.format"
                                size="small"
                                style="width: 100%"
                                :disabled="true"
                            ></el-date-picker>
                        </el-col>
                    </el-row>
                </template>
                <template v-else-if="node.controlType === 'imageTag'">
                    <div class="checkbox_group">
                        <el-select v-model="node.value">
                            <el-option
                                v-for="option in node.options"
                                :key="option.id"
                                :label="option.label"
                                :value="option.id"
                            ></el-option>
                        </el-select>
                    </div>
                </template>
                <!-- <template v-else-if="node.controlType === 'imageTag'">
                    <div class="imageTagContainer">
                        <div v-if="node.addingElement" class="item_title_edit">
                            <div class="item-edit-field">
                                <div class="name">
                                    <el-input
                                        :placeholder="lang.filed_name"
                                        v-model.trim="node.title"
                                        maxlength="50"
                                        show-word-limit
                                    ></el-input>
                                </div>
                                <div class="select_group">
                                    <el-select v-model="node.value">
                                        <el-option v-show="false" :label="' '" :value="-1"></el-option>
                                        <el-option
                                            v-for="option in node.options"
                                            :key="option.id"
                                            :label="option.label"
                                            :value="option.id"
                                        ></el-option>
                                    </el-select>
                                </div>
                            </div>
                            <div class="item-edit-btns">
                                <el-button @click="editOptions(node)" type="primary" size="mini">{{ lang.edit_options }}</el-button>
                                <el-button @click="addItem(node)" type="primary" size="mini">{{ lang.confirm_txt }}</el-button>
                                <el-button @click="cancelAddItem(node)" type="danger" size="mini">{{ lang.cancel_btn }}</el-button>
                            </div>
                        </div>
                        <div v-else>
                            <el-select v-model="node.value">
                                <el-option v-show="false" :label="' '" :value="-1"></el-option>
                                <el-option
                                    v-for="option in node.options"
                                    :key="option.id"
                                    :label="option.label"
                                    :value="option.id"
                                ></el-option>
                            </el-select>
                        </div>
                    </div>
                </template> -->
            </template>
        </div>
    </div>
</template>
<script>
import base from "../../../lib/base";
import Tool from "@/common/tool";
import specialCheck from "../../../components/genericMulticenter/specialCheck.vue";

export default {
    mixins: [base],
    name: "RecursiveSettingItem",
    components: {
        specialCheck,
    },
    props: {
        node: {
            type: Object,
            required: true,
        },
    },
    data() {
        return {
            activeCollapse: [],
        };
    },
    computed:{
        showNode(){
            if(this.node.field === 'collectionGroupItem'){
                return this.node.nodeVisible||false
            }else{
                return true
            }
        }
    },
    created() {
        this.activeCollapse = [this.node.id];
    },
    methods: {
        addItem(setting) {
            if (setting.title === "" && setting.controlType !== 'imageTag') {
                this.$message.error(this.lang.please_fill_field_name_tips);
                return;
            } else {
                if (setting.controlType === "radio" || setting.controlType === "checkbox") {
                    if (setting.options.length === 0) {
                        this.$message.error(this.lang.number_options_greater_2);
                        return;
                    }
                }
                let params = {
                    ...setting,
                };
                delete params.addingElement;
                delete params.editingElement;
                this.$emit("updateElementByNodeInfo", params);
            }
        },
        cancelAddItem(setting) {
            this.$MessageBox.confirm(this.lang.delete_item_tips, this.lang.tip_title, {
                confirmButtonText: this.lang.confirm_txt,
                cancelButtonText:this.lang.cancel_text,
            }).then(() => {
                this.$emit("deleteElementByNodeId", setting.id);
            }).catch(() => {

            });
        },
        cancelEditItem(setting) {
            this.$emit("cancelEditElementByNodeId", setting.id);
        },
        deleteElementByNodeId(id) {
            this.$emit("deleteElementByNodeId", id);
        },
        cancelEditElementByNodeId(id) {
            this.$emit("cancelEditElementByNodeId", id);
        },
        updateElementByNodeInfo(node) {
            this.$emit("updateElementByNodeInfo", node);
        },
        handleEditOptions(node) {
            this.$emit("handleEditOptions", node);
        },
        setNewTitle(setting) {
            if (!setting.newTitleValue) {
                return;
            } else {
                setting.title = setting.newTitleValue;
                this.cancelSetNewTitle(setting);
            }
        },
        cancelSetNewTitle(setting) {
            setting.editingTitle = false;
            setting.newTitleValue = "";
        },
        showContextMenu(event, typeId, nodeType) {
            console.log(event, typeId, nodeType);
            this.$emit("showContextMenu", {
                event,
                typeId,
                nodeType,
            });
        },
        showContextMenu2(data) {
            const { event, typeId, nodeType } = data;
            this.$emit("showContextMenu", {
                event,
                typeId,
                nodeType,
            });
        },
        calcItemCount(setting) {
            let count = 0;
            if (setting.children) {
                setting.children.forEach((item) => {
                    if (item.nodeType === "element") {
                        count++;
                    }
                });
            }
            return count;
        },
        editOptions(node) {
            this.$emit("handleEditOptions", node);
        },
        handleFormatChange(node) {
            node.value = "";
        },
        handleDateTimeChange(node) {
            node.value = "";
        },
        handleAddCollectionGroupOption(node) {
            this.$emit("handleAddCollectionGroupOption", node);
        },
        handleCollectionGroupOptionChange(value){
            this.$emit("handleCollectionGroupOptionChange", {
                value,
                node:this.node
            });
        },
        handleRadioClick(node, optionId) {
            if (node.value === optionId) {
                node.value = -1;
            } else {
                node.value = optionId;
            }
        }
    },
};
</script>
<style lang="scss" scoped>
.setting_module {
    padding-top: 10px;
    padding-left: 10px;
    ::v-deep {
        .el-collapse-item__header {
            background: #bfd1d1;
            font-size: 20px;
            text-indent: 10px;
            color: #000 !important;
        }
    }

    .setting_module_title {
        display: flex;
        background: #bfd1d1;
        align-items: center;
        width: 100%;
        height: 100%;
        overflow: hidden;
        .setting_module_title_text {
            display: flex;
            justify-content: space-between;
            width: 100%;
            align-items: center;
            p {
                font-size: 20px;
                color: #000;
                margin-right: 50px;
            }
            span {
                font-size: 12px;
            }
        }
        .setting_module_title_edit {
            display: flex;
            justify-content: center;
            align-items: center;
            .el-input {
                margin-right: 10px;
                ::v-deep {
                    .el-input__inner {
                        height: 30px;
                        line-height: 30px;
                    }
                }
            }
            button {
                height: 30px;
                margin-left: 10px;
            }
        }
    }
    .collection_group_tools{
        display: flex;
        justify-content: flex-end;
        padding: 10px;
        button {
            height: 30px;
            margin-left: 10px;
        }
    }
    .collection_group_radio_group{
        .el-radio{
            margin-left: 0;
            margin-bottom: 10px;
        }
    }
}

.item {
    display: flex;
    align-items: center;
    min-height: 40px;
    background: #f3f2f2;
    padding: 10px;
    overflow-x: auto;
    .name {
        min-width: 120px;
        padding-right: 10px;
    }
    .unit {
        min-width: 60px;
        padding-left: 10px;
    }
    .el-input {
        flex: 1;
        min-width: 150px;
        ::v-deep {
            .el-input__inner {
                height: 30px;
                line-height: 30px;
            }
            .el-input__icon {
                line-height: 30px;
            }
        }
    }
    .el-checkbox {
        margin-right: 20px;
    }
    .btns {
        margin-left: 20px;
    }
    .imageTagContainer {
        padding: 10px;
        width: 100%;

        .select_group {
            flex: 1;
            margin-left: 10px;

            .el-select {
                width: 100%;
            }
        }

        .item-edit-field {
            display: flex;
            align-items: center;
            margin-bottom: 10px;

            .name {
                min-width: 200px;
            }
        }

        .item-edit-btns {
            display: flex;
            justify-content: flex-end;

            .el-button {
                margin-left: 10px;
            }
        }
    }
    .item_title_edit {
        width: 100%;
        .item-edit-field {
            display: flex;
            padding: 10px;
            .el-input {
                margin-right: 10px;
                ::v-deep {
                    .el-input__inner {
                        height: 30px;
                        line-height: 30px;
                    }
                }
            }
        }
        .item-edit-radio {
            width: 100%;
            padding: 10px;
        }
        .item-edit-btns {
            display: flex;
            justify-content: flex-end;
            margin-right: 10px;
            button {
                height: 30px;
                margin-left: 10px;
            }
        }
    }
    .radio_group {
        display: flex;
        flex-direction: column;
        .radio_group_item {
            display: flex;
            align-items: center;
            &:first-child {
                margin-top: 10px;
            }
            margin-bottom: 10px;
            .unit {
                min-width: 100px;
                max-width: 200px;
                white-space: pre-wrap;
                text-overflow: ellipsis;
                overflow: hidden;
                display: inline-block;
            }
            .el-radio {
                display: flex;
                justify-content: center;
                align-items: center;
                margin-right: 10px;
                ::v-deep {
                    .el-radio__label {
                        white-space: pre-wrap;
                        min-width: 120px;
                        max-width: 200px;
                        display: inline-block;
                    }
                }
            }
        }
    }
    .checkbox_group {
        display: flex;
        flex-direction: column;
        .checkbox_group_item {
            display: flex;
            align-items: center;
            &:first-child {
                margin-top: 10px;
            }
            margin-bottom: 10px;
            .unit {
                min-width: 100px;
                max-width: 200px;
                white-space: pre-wrap;
                text-overflow: ellipsis;
                overflow: hidden;
                display: inline-block;
            }
            .el-checkbox {
                display: flex;
                justify-content: center;
                align-items: center;
                margin-right: 10px;
                ::v-deep {
                    .el-checkbox__label {
                        white-space: pre-wrap;
                        min-width: 120px;
                        max-width: 200px;
                        display: inline-block;
                    }
                }
            }
        }
    }
}
</style>

import { Toast } from 'vant';
import {htmlEscape,
    checkIsCreator,
    checkIsManager,
    formatString,
    judgeIfCurrentYear,
} from '../lib/common_base'
import Tool from '@/common/tool'
import moment from 'moment'
export default {
    data(){
        return {
            conversationList:this.$store.state.conversationList,
            systemConfig:this.$store.state.systemConfig,
            gallery:this.$store.state.gallery,
            functionsStatus:this.$store.state.globalParams.functionsStatus,
            currentOpenConversationInfo:null
        }
    },
    computed:{
        lang(){
            return this.$store.state.language
        },
        user(){
            return this.$store.state.user
        },
        osName(){
            return this.$store.state.globalParams.osName
        },
        osVersion(){
            return this.$store.state.globalParams.osVersion
        },
        isCE(){
            return this.$store.state.globalParams.isCE
        },
        isApp(){
            return this.$store.state.systemConfig.client_type.AppMobile == this.$store.state.systemConfig.clientType || this.$store.state.systemConfig.client_type.UltraSoundMobile == this.$store.state.systemConfig.clientType
        },
        isUltraSoundMobile(){
            return this.$store.state.device.isUltraSoundMobile
        },
        isOutlineShow(){
            return this.systemConfig.server_type.enable_sms_identification;
        },
        isShowWechat(){
            return this.functionsStatus.wechat&&this.isApp&&!this.isCE&&!this.isUltraSoundMobile&&this.isOutlineShow;
        },
        globalParams(){
            return this.$store.state.globalParams
        },
        isInternalNetworkEnv(){
            return this.systemConfig.serverInfo.network_environment
        },
        currentLanguage() {
            return this.$store.state.language.currentLanguage;
        },
        consultationMode() {
            return Tool.getConsultationMode();
        },
    },
    beforeCreate(){
        // console.log("before create")
        // clearTimeout(window.loadPageTimer)
        // Indicator.close()

    },
    activated(){
        // console.log("activated");
        // clearTimeout(window.loadPageTimer);
        // Indicator.close()
    },
    methods:{
        loadPage(){
            //点击路由跳转时延时调用loading，beforeCreate/activated时关闭loading或者取消loading
            // console.log("load page")
            // var that=this
            // window.loadPageTimer=setTimeout(function(){
            //     Indicator.open(that.lang.page_loading_tip)
            // },300)
        },
        openConversation(id,open_type,callback,Fileitem){
            //open_type ,1:从会话列表打开 2:从群组列表打开  3:从好友列表打开
            //           4:点击画廊评论打开 5:新建群后自动打开 或 扫码加群后自动打开 6:单聊变群聊后自动打开
            //           7:云收藏,一键转发，检查浏览转发开启会话
            //           8:群成员扫群二维码打开
            //           9:对左下角最近文件进行重命名
            //           10:画廊点击打开会话按钮
            //           11:加好友成功自动打开会话发送提示消息 12:push的方式打开群 13:不打开会话
            //           14:群落列表里打开
            var that=this;
            this.currentOpenConversationInfo = {
                id,
                open_type,
                callback,
                Fileitem
            }
            if (!id||!this.$root.socket) {
                return ;
            }
            if(open_type==3){
                let fid=id
                let chatList=this.$store.state.chatList.list
                for(let chat of chatList){
                    if (chat.type==3) {
                        for(let member of chat.list){
                            if (member.fid==fid) {
                                this.openConversation(member.cid,1,callback);
                                return;
                            }
                        }
                    }
                    if (chat.fid==fid) {
                        //会话列表有则从会话列表打开会话
                        this.openConversation(chat.cid,1,callback)
                        return;
                    }
                }
                console.log('create conversation')
                //会话列表没有则新开一个会话
                this.$root.socket.emit("request_start_single_chat_conversation",{
                    list:[fid,this.user.uid],
                    start_type:undefined,
                    mode:this.systemConfig.ConversationConfig.mode.Single,
                    type:this.systemConfig.ConversationConfig.type.Single
                },async function(is_succ,data){
                    if (is_succ) {
                        let transmitQueue=that.$root.transmitQueue['f-'+fid];
                        let transferExamQueue=that.$root.transferExamQueue['f-'+fid];
                        let transferLocalQueue=that.$root.transferLocalQueue['f-'+fid];
                        that.$store.commit('conversationList/initConversation',data)
                        that.$store.commit('examList/initExamObj',data)
                        await Tool.handleAfterConversationCreated(data,'openConversation')
                        callback&&callback(is_succ,data)
                        //新开的会话，转发数据存在id下，应转移到cid下
                        if(transmitQueue) {
                            that.$root.transmitQueue[data]=transmitQueue;
                        }else if(transferExamQueue) {
                            transferExamQueue.SessionId=data;
                            that.$root.transferExamQueue[data]=transferExamQueue;
                        }else if(transferLocalQueue){
                            that.$root.transferLocalQueue[data]=transferLocalQueue;
                        }else{
                            that.$router.push(`/index/chat_window/${data}`)
                        }
                    }else{
                        callback&&callback(is_succ)
                        Toast(that.lang.start_conversation_error)
                    }
                })
            }else if(open_type==11){
                let fid=id
                this.$root.socket.emit("request_start_single_chat_conversation",{
                    list:[fid,this.user.uid],
                    start_type:undefined,
                    mode:this.systemConfig.ConversationConfig.mode.Single,
                    type:this.systemConfig.ConversationConfig.type.Single
                },async function(is_succ,cid){
                    if (is_succ) {
                        that.$store.commit('conversationList/initConversation',cid)
                        that.$store.commit('examList/initExamObj',cid)
                        await Tool.handleAfterConversationCreated(cid,'openConversation')
                        callback&&callback(is_succ,cid)
                    }else{
                        Toast(this.lang.start_conversation_error)
                        callback&&callback(is_succ,cid)

                    }
                })
            }else{
                //open_type1,2,4,5,6,7
                let cid=id
                if(this.conversationList[cid]&&this.conversationList[cid].hasOwnProperty('type')&&window.main_screen.conversation_list[cid]){
                    //会话已开启过
                    callback&&callback(true,this.conversationList[cid])
                }else{
                    this.$store.commit('conversationList/initConversation',cid)
                    that.$store.commit('examList/initExamObj',cid)
                    this.$root.socket.emit("request_start_conversation",cid,undefined, async(is_succ,data)=>{
                        if(is_succ){
                            await Tool.handleAfterConversationCreated(cid,'openConversation')
                            callback&&callback(is_succ,data)
                        }else{
                            callback&&callback(is_succ,data)
                        }


                    })
                }
                switch(open_type){
                case 1:
                case 8:
                case 12:
                    this.$router.push(`/index/chat_window/${cid}`);
                    break;
                case 2:
                case 5:
                    this.$router.replace(`/index/chat_window/${cid}`);
                    break;
                case 6:
                    history.go(-3);
                    setTimeout(function(){
                        that.$router.push(`/index/chat_window/${cid}`)
                    },100)
                    break;
                case 10:
                    this.$router.push({
                        name:'chat_window',
                        params:{
                            Fileitem:Fileitem,
                            cid:cid,
                            action:'gotoHistoryPage'
                        },
                    })
                    break
                    // this.$router.push(`/index/chat_window/${cid}?action=gotoHistoryPage`);
                case 13:
                    break;
                case 14:
                    // history.go(-2);
                    setTimeout(function(){
                        that.$router.push(`/index/chat_window/${cid}`)
                    },100)
                    break;
                }


            }
        },
        async openConversationByUserId(id,callback){
            let fid=id
            let chatList=this.$store.state.chatList.list
            let cid = 0
            for(let chat of chatList){
                if (chat.fid==fid) {
                    //会话列表有则从会话列表打开会话
                    cid = chat.cid
                    break;
                }
            }
            if(cid){
                if(this.conversationList[cid]&&this.conversationList[cid].hasOwnProperty('type')&&window.main_screen.conversation_list[cid]){
                    //会话已开启过
                    await Tool.handleAfterConversationCreated(cid,'openConversation')
                    callback&&callback(true,cid)
                }else{
                    this.$store.commit('conversationList/initConversation',cid)
                    this.$store.commit('examList/initExamObj',cid)
                    this.$root.socket.emit("request_start_conversation",cid,undefined,async (is_succ,data)=>{
                        if(is_succ){
                            await Tool.handleAfterConversationCreated(cid,'openConversation')
                            callback&&callback(is_succ,cid)
                        }else{
                            callback&&callback(is_succ,cid)
                        }

                    })

                }
            }else if(fid){
                this.$root.socket.emit("request_start_single_chat_conversation",{
                    list:[fid,this.user.uid],
                    start_type:undefined,
                    mode:this.systemConfig.ConversationConfig.mode.Single,
                    type:this.systemConfig.ConversationConfig.type.Single
                },async(is_succ,newCid)=>{
                    if (is_succ) {
                        let transmitQueue=this.$root.transmitQueue['f-'+fid];
                        let transferExamQueue=this.$root.transferExamQueue['f-'+fid];
                        let transferLocalQueue=this.$root.transferLocalQueue['f-'+fid];
                        this.$store.commit('conversationList/initConversation',newCid)
                        this.$store.commit('examList/initExamObj',newCid)
                        await Tool.handleAfterConversationCreated(newCid,'openConversation')
                        callback&&callback(is_succ,newCid)
                        //新开的会话，转发数据存在id下，应转移到cid下
                        if(transmitQueue) {
                            this.$root.transmitQueue[newCid]=transmitQueue;
                        }else if(transferExamQueue) {
                            transferExamQueue.SessionId=newCid;
                            this.$root.transferExamQueue[newCid]=transferExamQueue;
                        }else if(transferLocalQueue){
                            this.$root.transferLocalQueue[newCid]=transferLocalQueue;
                        }
                    }else{
                        callback&&callback(is_succ)
                        Toast(this.lang.start_conversation_error)
                    }
                })
            }
        },
        createConversation(id){
            if (!id) {
                return ;
            }
            return new Promise((resolve,reject)=>{
                let cid=id
                if(this.conversationList[cid]){
                    //会话已开启过
                    resolve(this.conversationList[cid])
                }else{
                    this.$store.commit('conversationList/initConversation',cid)
                    this.$store.commit('examList/initExamObj',cid)
                    this.$root.socket.emit("request_start_conversation",cid,undefined,(isSuc,data)=>{
                        if(isSuc){
                            resolve(data)
                        }else{
                            reject(data)
                        }

                    })

                }
            })

        },
        replaceChatWindow(cid){
            this.loadPage();
            this.$router.replace(`/index/chat_window/${cid}`)
        },
        back(length){
            if(typeof length === 'number'){
                this.$router.go(length*-1)
            }else{
                this.$router.back()
            }
            console.log('emit back')
            return this;
        },
        setDefaultImg(list=[]){
            for(let item of list){
                if(item.is_single_chat==0){
                    if (item.type==3) {
                        if (!item.avatar) {
                            item.avatar_local='static/resource/images/groupset.png'
                            item.avatar='static/resource/images/groupset.png'
                        }
                    }else if (!item.avatar) {
                        item.avatar_local='static/resource/images/b1.png'
                        item.avatar='static/resource/images/b1.png'
                    }
                }else{
                    if (item.service_type==this.systemConfig.ServiceConfig.type.FileTransferAssistant) {
                        item.avatar='static/resource/images/transfer.png'
                        item.avatar_local='static/resource/images/transfer.png'
                    }else{
                        //单聊
                        const userStatus=item.user_status||item.status
                        if (userStatus===this.systemConfig.userStatus.Destroy) {
                            item.avatar='static/resource/images/destroy.png'
                            item.avatar_local='static/resource/images/destroy.png'
                            item.sex=2;
                            if (item.nickname) {
                                item.nickname=item.nickname.replace('destroy',this.lang.destroy_replace_text);
                            }else if (item.subject) {
                                item.subject=item.subject.replace('destroy',this.lang.destroy_replace_text);
                            }
                            continue ;
                        }
                        if(item.sex==1 || item.sex==0){
                            if (!item.avatar||/user\/avatar\/default\/0\.png/.test(item.avatar)||/static\/resource\/images\//.test(item.avatar)) {
                                if(item.sex==1){
                                    item.avatar='static/resource/images/b3-1.png'
                                    item.avatar_local='static/resource/images/b3-1.png'
                                }else{
                                    item.avatar='static/resource/images/b2-1.png'
                                    item.avatar_local='static/resource/images/b2-1.png'
                                }
                            }
                        }else{
                            //给出默认头像
                            if(item.avatar){
                                item.avatar=item.avatar;
                            }else{
                                item.avatar='static/resource/images/b3-1.png';
                            }
                            if(item.avatar_local){
                                item.avatar_local=item.avatar_local;
                            }else{
                                item.avatar_local='static/resource/images/b3-1.png';
                            }
                        }
                    }

                }
            }
            return list;
        },
        changeDefaultImg(changeObj){
            if (changeObj.sex!=undefined) {
                if (/static\/resource/.test(this.user.avatar)) {
                    let item={};
                    if(changeObj.sex==1){
                        item.avatar='static/resource/images/b3-1.png'
                        item.avatar_local='static/resource/images/b3-1.png'
                    }else{
                        item.avatar='static/resource/images/b2-1.png'
                        item.avatar_local='static/resource/images/b2-1.png'
                    }
                    this.$store.commit('user/updateUser',item)
                }
            }
        },
        replaceUrlsWithLinks(oText) {
            const aRegex = /<a\b[^>]*>(.*?)<\/a>/g;
            let text = oText.replace(aRegex, function(match, url) {
                return url;
            });
            const regex = /(<a[^>]*>.*?<\/a>)|((https?|ftp|file):\/\/[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|])/gi;
            const wrappedText = text.replace(regex, (match, p1, p2) => {
                if (p1) {
                // 已经在 <a> 标签内的链接，直接返回
                    return match;
                } else {
                // 将链接包裹在 <a> 标签中
                    return `<a href="javascript:void(0)" style="text-decoration:underline;word-break: break-all;" class="openLinkByDefaultBrowser" data-url="${p2}">${p2}</a>`
                }
            });
            return wrappedText;
        },
        parseMessageBody(messageBody=''){
            //将[emo_xx]转化为html
            messageBody=htmlEscape(messageBody)
            messageBody=messageBody.replace(/\n/g,'<br/>')
            messageBody = this.replaceUrlsWithLinks(messageBody)
            return messageBody
        },
        parseObjToArr(data){
            var arr=[]
            for(let item in data){
                arr.push({...data[item],id:item})
            }
            return arr
        },
        resetStore(){
            //进入Index页凭借uid确认是否登录
            this.$store.commit('user/updateUser', {uid:''});
            this.$store.commit('chatList/clearChatList');
            this.$store.commit('consultationImageList/clearConsultationImages');
            this.$store.commit('conversationList/clearConversation');
            this.$store.commit('friendList/clearFriendList');
            this.$store.commit('groupList/clearGroupList');
            this.$store.commit('notifications/clearGroupApply');
            this.$store.commit('notifications/clearFriendApply');
            this.$store.commit('relationship/clearRelationship');
            this.$store.commit('loadingConfig/clearLoadingConfig');
            this.$store.commit('groupset/clearGroupset');
        },
        createConversationFail(){
            Toast(this.lang.init_conversation_err)
            if(this.currentOpenConversationInfo){
                this.$store.commit('conversationList/deleteConversationList',{cid:this.currentOpenConversationInfo.id})
            }

        },
        limitImageSize(url,size=70){
            if(url.includes('https')){
                url = Tool.addParamsToUrl(url,{'x-oss-process':`image/resize,w_${size}`})
            }
            return url
        },
        tryToDeleteMessages(cid, list, callback) {
            let that = this;
            if (0 == list.length) {
                return;
            }
            let uid = that.$store.state.user.uid;
            let conversation = that.$store.state.conversationList[cid];
            let total_count = 0;
            let sent_by_others_count = 0;
            let queue = that.$root.deleteQueue;
            let isCreator = checkIsCreator(cid);
            let isManager = checkIsManager(cid);
            //非群主和管理员无法删除其他人的数据
            for (let i in list) {
                let item = list[i];
                total_count++;
                let senderId = item.sender_id || item.creator_id;
                if (!isCreator && !isManager && senderId != uid) {
                    sent_by_others_count++;
                    continue;
                }

                if (!queue[item.group_id]) {
                    queue[item.group_id] = [];
                }

                var param = {};
                if (item.resource_id) {
                    param.resource_id = parseInt(item.resource_id);
                }
                if (item.gmsg_id) {
                    param.gmsg_id = parseInt(item.gmsg_id);
                }
                if (item.msg_type) {
                    param.msg_type = parseInt(item.msg_type);
                }

                queue[item.group_id].push(param);
            }
            if (0 < sent_by_others_count) {
                let tip = "";
                if (1 == total_count) {
                    tip = that.lang.delete_chat_message_fail_sended_by_others;
                } else {
                    tip = formatString(that.lang.delete_chat_message_warm_sended_by_others, {
                        1: sent_by_others_count,
                    });
                }
                Tool.openMobileDialog(
                    {
                        message: tip,
                        confirm:()=>{
                            that.deleteMessages(cid, callback);
                        }
                    }
                )
            } else {
                that.deleteMessages(cid, callback);
            }
        },
        deleteMessages(cid, callback) {
            let that = this;
            let queue = that.$root.deleteQueue;
            for (let i in queue) {
                if (cid && cid != i) {
                    continue;
                }

                if (0 == queue[i].length) {
                    continue;
                }

                let conversation = that.conversationList[i];
                if (conversation && conversation.socket) {
                    conversation.socket.emit("delete_chat_messages", queue[i], function (is_succ, data) {
                        if (!is_succ) {
                            Tool.openMobileDialog(
                                {
                                    message: that.lang.delete_chat_message_fail,
                                }
                            )
                        }
                    });
                    delete queue[i];
                    callback && callback(false);
                } else {
                    this.openConversation(i, 13, () => {
                        this.deleteMessages(i, callback);
                    });
                    // window.main_screen.controller.emit("request_start_conversation", i);
                }
            }
        },
        async deleteResourceByGroupId(file,gmsg_id = 0){
            return new Promise((resolve,reject)=>{
                console.log("deleteResourceByGroup",file);
                let params={
                    resource_id:file.resource_id,
                }
                if(gmsg_id){
                    params.gmsg_id = gmsg_id
                }
                window.main_screen.conversation_list[file.group_id].deleteResourceByGroup(params,(res)=>{
                    if(res.error_code){
                        Toast(this.lang[res.error_msg]||this.lang.delete_chat_message_fail)
                        reject(false)
                    }else{
                        Toast(this.lang.operate_success)
                        resolve(true)
                    }
                })
            })
        },
        checkFileExpired(item){
            if(item.resource_expired_at){
                // 创建一个moment对象表示当前时间
                const currentTime = new Date().getTime();
                // 创建一个moment对象表示要比较的时间
                const otherTime = new Date(item.resource_expired_at).getTime()
                // 比较两个时间的大小
                if (currentTime>otherTime) {
                    return true
                }
                return false
            }
            return false
        },
        parseTimestamp(time) {
            return moment(time).format("YYYY-MM-DD HH:mm:ss")
        },
        formatTime(time) {
            if(!time){
                return ""
            }
            return moment(time).format("YYYY-MM-DD HH:mm z");
        },
        formatTimeOrEllipsisYear(time) {
            if(!time){
                return ""
            }
            if (judgeIfCurrentYear(time)) {
                return moment(time).format("MM-DD HH:mm z");
            }
            return moment(time).format("YYYY-MM-DD HH:mm z");
        },
        tryToWithDrawMessages(cid, list, callback) {
            // 撤回前条件判断
            let that = this;
            if (0 == list.length) {
                return;
            }

            let uid = that.$store.state.user.uid;
            let total_count = 0;
            let sent_by_others_count = 0;
            let queue = that.$root.withDrawList;
            for (let i in list) {
                let item = list[i];
                total_count++;
                let senderId = item.sender_id || item.creator_id;
                if (senderId != uid) {
                    sent_by_others_count++;
                    continue;
                }

                if (!queue[item.group_id]) {
                    queue[item.group_id] = [];
                }

                let obj = { ...item };
                Object.keys(obj).map((ele) => {
                    let hasResourceId = ele === "resource_id" && obj[ele];
                    let hasGmsgId = ele === "gmsg_id" && obj[ele];
                    let hasMsgType = ele === "msg_type" && obj[ele];
                    if (hasResourceId || hasGmsgId || hasMsgType) {
                        obj[ele] = parseInt(obj[ele]);
                    }
                    return ele;
                });
                queue[item.group_id].push(obj);
            }

            if (0 < sent_by_others_count) {
                let tip = "";
                if (1 == total_count) {
                    tip = that.lang.withdraw_chat_message_fail_sended_by_others;
                } else {
                    tip = formatString(that.lang.withdraw_chat_message_fail_sended_by_others, {
                        1: sent_by_others_count,
                    });
                }
                Tool.openMobileDialog(
                    {
                        message: tip,
                    }
                )
            } else {
                that.withDrawMessages(cid, callback);
            }
        },
        withDrawMessages(cid, callback) {
            let that = this;
            let queue = that.$root.withDrawList;
            for (let i in queue) {
                if (cid && cid != i) {
                    continue;
                }

                if (0 == queue[i].length) {
                    continue;
                }

                let conversation = that.conversationList[i];
                if (conversation && conversation.socket) {
                    conversation.socket.emit("withdraw_chat_message", queue[i][0], function (is_succ, data) {
                        console.log(is_succ, data);
                        if (!is_succ) {
                            Tool.openMobileDialog(
                                {
                                    message: that.lang.withdraw_chat_message_fail,
                                }
                            )
                        }
                    });
                    delete queue[i];
                    callback && callback(false);
                } else {
                    window.main_screen.controller.emit("request_start_conversation", i);
                }
            }
        }
    }
}

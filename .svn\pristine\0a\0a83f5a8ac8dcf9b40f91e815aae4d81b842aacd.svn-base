import Tool from "@/common/tool.js";
import {
    findServiceId,
} from "../lib/common_base";

export default {
    data(){
        return {
            lastPcHeartbeatTime: null,
            pcHeartbeatTimer: null,
            fileTransferAssistantCid:null,
        }
    },
    methods: {
        initNotifyMsgFromOwnerListener(controller) {
            //同账号自己给自己发通知
            controller.on('notify_msg_from_owner', (data) => {
                console.error('notify_msg_from_owner', data)
                const action = data.action
                if (action === 'sendBindDeviceToULinker') {
                    this.sendBindDeviceToULinker(data.body)
                } else if (action === 'startLiveToFileTransferAssistant' && data.body.targetDeviceType === 'ULinker' && this.isUltraSoundMobile) {
                    this.startLiveToFileTransferAssistant(data.body)
                } else if (action === 'stopLiveToFileTransferAssistant' && data.body.targetDeviceType === 'ULinker' && this.isUltraSoundMobile) {
                    this.stopLiveToFileTransferAssistant(data.body)
                } else if (action === 'checkAntiCheatLiveStatus' && data.body.targetDeviceType === 'ULinker' && this.isUltraSoundMobile) {
                    // 记录最后一次收到 PC 心跳的时间
                    this.lastPcHeartbeatTime = Date.now();
                    // 启动或重置心跳监控定时器
                    if(!this.pcHeartbeatTimer){
                        this.startPcHeartbeatMonitor();
                    }
                    this.replyAntiCheatLiveStatusToPC()
                }
            });
        },
        sendBindDeviceToULinker(data){
            if(!this.isUltraSoundMobile){
                return
            }
            if(data.status === 1&&data.deviceId === this.deviceInfo.device_id){//绑定
                Tool.openMobileDialog({
                    message: this.lang.received_binding_from_pc_tips,
                    showRejectButton: true,
                    confirmButtonText:this.lang.agree_txt,
                    rejectButtonText:this.lang.disagree_txt,
                    confirm: () => {
                    // this.saveBindDeviceInfoToStorage(data)
                        this.ackBindULinkerFromPC()
                    },
                });
            }
        },
        ackBindULinkerFromPC(){
            window.main_screen.sendMsgOwner({
                action:'ackBindULinkerFromPC',
                body:{
                    deviceId:this.deviceInfo.device_id
                }
            },(res)=>{
                this.$message.success(this.lang.operate_success)
                console.error(res)
            })
        },
        sendMsgToOwnerByULinker(action,data = {}){
            window.main_screen.sendMsgOwner({
                action:action,
                body:{
                    deviceType:'ULinker',
                    targetDeviceType:'PC',
                    ...data
                }
            })
        },
        async startLiveToFileTransferAssistant(data){
            console.error('startLiveToFileTransferAssistant',data,this.isUltraSoundMobile)
            const trainingInfo = {
                subID:data.subID,
                testID:data.testID,
                topicID:data.topicID,
                trainingID:data.trainingID,
            }
            try{
                if(window.livingStatus > 0){
                    console.error('is living')
                    this.sendMsgToOwnerByULinker('NotifyStartLiveToFileTransferAssistant',{
                        error_code:-1,
                        errormsg:'is living'
                    })
                    return;
                }
                console.error('openFileTransfer')
                const cid = await this.openFileTransfer()
                this.fileTransferAssistantCid = cid;

                // 提取公共的joinRoom逻辑
                const executeJoinRoom = async () => {
                    await this.preHandleRecordMode(cid,true);
                    window.vm.$root.eventBus.$emit('chatWindowStartJoinRoom', {
                        main: 1,
                        aux: 1,
                        videoSource: 'doppler',
                        isSender: 1,
                        from: 'smart_tech_training',
                        trainingInfo,
                        joinRoomSuccessCallback:()=>{
                            this.sendMsgToOwnerByULinker('NotifyStartLiveToFileTransferAssistant',{
                                error_code: 0,
                                errormsg:'success',
                                cid:cid
                            })
                            // 启动来自 PC 的心跳监控
                            this.lastPcHeartbeatTime = Date.now();
                            if(!this.pcHeartbeatTimer){
                                this.startPcHeartbeatMonitor();
                            }
                        },
                        joinRoomFailedCallback:()=>{
                            this.sendMsgToOwnerByULinker('NotifyStartLiveToFileTransferAssistant',{
                                error_code: -1,
                                errormsg:'join room error',
                                cid:cid
                            })
                        },
                        leaveRoomCallback:()=>{
                            this.sendMsgToOwnerByULinker('NotifyStopLiveToFileTransferAssistant',{
                                error_code: 0,
                                errormsg:'success',
                                cid:cid,
                            })
                            this.stopPcHeartbeatMonitor();
                            this.fileTransferAssistantCid = null;
                        }
                    });
                };

                // 检查当前路由是否已经是期望的路由
                const targetRoute = `/index/chat_window/${cid}`;
                const currentRoute = this.$route.path;

                if (currentRoute === targetRoute) {
                    // 已经在期望的路由下，直接执行后续操作
                    console.log('已在目标路由，直接执行操作');
                    setTimeout(executeJoinRoom, 100);
                } else {
                    // 不在期望路由，需要进行路由跳转
                    const isMatched = await Tool.backToRoute('/index')
                    if(!isMatched){
                        this.$router.replace('/index')
                    }
                    this.$root.eventBus.$emit('toggleTab','chat')
                    setTimeout(() => {
                        window.vm.$router.push(targetRoute);
                    },500);
                    setTimeout(executeJoinRoom, 1000);
                }
            }catch(e){
                console.error('startLiveToFileTransferAssistant error',e)
                this.sendMsgToOwnerByULinker('NotifyStartLiveToFileTransferAssistant',{
                    error_code: -1,
                    errormsg:'error'
                })
            }
        },
        stopLiveToFileTransferAssistant(data) {
            window.vm.$root.eventBus.$emit('chatWindowLeaveRoom')
            // 停止 PC 心跳监控
            this.stopPcHeartbeatMonitor();
            this.fileTransferAssistantCid = null;
        },
        replyAntiCheatLiveStatusToPC(){
            // 检查是否正在直播
            const isLiving = window.livingStatus > 0;
            // 检查当前直播的 cid 是否是文件传输助手的 cid
            const isCorrectCid = String(window.vm.$root.currentLiveCid) === String(this.fileTransferAssistantCid);

            console.log('replyAntiCheatLiveStatusToPC - 直播状态检查:', {
                livingStatus: window.livingStatus,
                currentLiveCid: window.vm.$root.currentLiveCid,
                fileTransferAssistantCid: this.fileTransferAssistantCid,
                isLiving,
                isCorrectCid
            });

            // 判断防作弊直播状态：
            // 1. 必须正在直播 (livingStatus > 0)
            // 2. 如果有设置 fileTransferAssistantCid，则需要 cid 匹配
            // 3. 如果没有设置 fileTransferAssistantCid，只要在直播就认为有效
            let antiCheatLive = false;

            if (isLiving) {
                if (this.fileTransferAssistantCid) {
                    // 如果有设置文件传输助手 cid，需要匹配
                    antiCheatLive = isCorrectCid;
                } else {
                    // 如果没有设置文件传输助手 cid，只要在直播就认为有效
                    // 这种情况可能发生在刚开始直播时，cid 还没有完全同步
                    antiCheatLive = true;
                }
            }

            console.log('replyAntiCheatLiveStatusToPC - 最终判断结果:', { antiCheatLive });

            this.sendMsgToOwnerByULinker('NotifyAntiCheatLiveStatus',{
                antiCheatLive: antiCheatLive,
                error_code: antiCheatLive ? 0 : -1
            })
        },
        openFileTransfer() {
            return new Promise(async(resolve, reject) => {
                try {
                    const service_type = this.systemConfig.ServiceConfig.type.FileTransferAssistant;
                    let fileTransferAssistant = await findServiceId(service_type);
                    if (fileTransferAssistant.cid) {
                        this.openConversation(fileTransferAssistant.cid, 13, (is_succ, conversation) => {
                            console.error('openConversation',is_succ,conversation)
                            if(is_succ){
                                resolve(fileTransferAssistant.cid);
                            }else{
                                reject(this.lang.start_conversation_error);
                            }
                        });
                    } else {
                        this.openConversationByUserId(fileTransferAssistant.id,async(is_succ,cid)=>{
                            if(is_succ){
                                resolve(cid);
                            }else{
                                reject(this.lang.start_conversation_error);
                            }
                        })
                    }
                } catch (error) {
                    reject(error);
                }
            });
        },
        // 启动监控，检测 PC 端心跳是否中断
        startPcHeartbeatMonitor(){
            this.stopPcHeartbeatMonitor();
            this.pcHeartbeatTimer = setInterval(()=>{
                // 若超过 10 秒未收到 PC 端心跳，则主动结束采集
                const now = Date.now();
                if(!this.lastPcHeartbeatTime){
                    this.lastPcHeartbeatTime = now;
                }
                if(now - this.lastPcHeartbeatTime >= 10000){
                    console.warn('超过 10 秒未收到 PC 端心跳，自动停止采集');
                    this.stopPcHeartbeatMonitor();
                    this.stopLiveToFileTransferAssistant({});
                }
            },5000);
        },
        // 停止 PC 心跳监控
        stopPcHeartbeatMonitor(){
            if(this.pcHeartbeatTimer){
                clearInterval(this.pcHeartbeatTimer);
                this.pcHeartbeatTimer = null;
            }
        },
    }
}

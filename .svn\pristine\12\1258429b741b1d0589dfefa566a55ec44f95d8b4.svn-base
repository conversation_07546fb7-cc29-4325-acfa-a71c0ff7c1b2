<template>
    <div class="report_page" v-if="isShowReport">
        <div class="data_tree">
            <vue-scroll>
                <el-tree
                    :data="report.report_template"
                    node-key="id"
                    :props="defaultProps"
                    @node-click="handleNodeClick"
                    :default-expanded-keys="[1]"
                ></el-tree>
            </vue-scroll>
        </div>
        <div class="report_container">
            <div class="report_view" :class="{ is_a4: isA4 }">
                <vue-scroll>
                    <div class="title_bar">
                        <img src="static/resource_pc/images/hospital_logo.jpg" />
                        <div class="title">
                            <div v-if="!canEdit">{{ report_title }}</div>
                            <el-input v-else v-model="report_title"></el-input>
                            <div class="sub_title" v-if="!canEdit">{{ report_subtitle }}</div>
                            <el-input v-else v-model="report_subtitle"></el-input>
                        </div>
                    </div>
                    <div class="report_content">
                        <div class="ultrasound_info clearfix">
                            <div class="ultrasound_info_item">{{ lang.ultrasound_number }}：</div>
                            <div class="ultrasound_info_item">{{ lang.machine_info_name }}：</div>
                        </div>
                        <hr />
                        <div class="patient_info clearfix">
                            <el-row :gutter="10">
                                <el-col :span="6">
                                    <div class="patient_item">
                                        <div class="label">{{ lang.exam_patient_name }}:</div>
                                        <div v-if="!canEdit" class="content">{{ exam_patient_name }}</div>
                                        <el-input
                                            v-model="exam_patient_name"
                                            size="mini"
                                            v-else
                                            maxlength="10"
                                        ></el-input>
                                    </div>
                                </el-col>
                                <el-col :span="6">
                                    <div class="patient_item">
                                        <div class="label">{{ lang.exam_patient_age }}:</div>
                                        <div v-if="!canEdit">{{ exam_patient_age }}</div>
                                        <el-input
                                            v-model="exam_patient_age"
                                            size="mini"
                                            v-else
                                            type="number"
                                            max="120"
                                            min="0"
                                        ></el-input>
                                    </div>
                                </el-col>
                                <el-col :span="6">
                                    <div class="patient_item">
                                        <div class="label">{{ lang.exam_patient_sex }}:</div>
                                        <div v-if="!canEdit">{{ exam_patient_sex | sexFilter(this) }}</div>
                                        <el-select v-model="exam_patient_sex" size="mini" v-else>
                                            <el-option :label="lang.sex[0]" :value="0"></el-option>
                                            <el-option :label="lang.sex[1]" :value="1"></el-option>
                                            <el-option :label="lang.sex[2]" :value="2"></el-option>
                                        </el-select>
                                    </div>
                                </el-col>
                                <el-col :span="6">
                                    <div class="patient_item">{{ lang.department_title }}</div>
                                </el-col>
                            </el-row>
                            <el-row :gutter="10">
                                <el-col :span="6">
                                    <div class="patient_item">{{ lang.outpatient_number }}</div>
                                </el-col>
                                <el-col :span="6">
                                    <div class="patient_item">{{ lang.inpatient_number }}</div>
                                </el-col>
                                <el-col :span="6">
                                    <div class="patient_item">{{ lang.bed_number }}</div></el-col
                                >
                                <el-col :span="6">
                                    <div class="patient_item">{{ lang.sending_physician }}</div>
                                </el-col>
                            </el-row>
                            <el-row :gutter="10">
                                <el-col :span="12">
                                    <div class="patient_item">
                                        <div class="label2">{{ lang.exam_position }}:</div>
                                        <div v-if="!canEdit">{{ exam_type | examTypesFilter(this) }}</div>
                                        <el-select v-model="exam_type" size="mini" v-else>
                                            <el-option :label="lang.exam_types[0]" :value="0"></el-option>
                                            <el-option :label="lang.exam_types[1]" :value="1"></el-option>
                                            <el-option :label="lang.exam_types[2]" :value="2"></el-option>
                                            <el-option :label="lang.exam_types[3]" :value="3"></el-option>
                                            <el-option :label="lang.exam_types[4]" :value="4"></el-option>
                                            <el-option :label="lang.exam_types[5]" :value="5"></el-option>
                                            <el-option :label="lang.exam_types[6]" :value="6"></el-option>
                                            <el-option :label="lang.exam_types[7]" :value="7"></el-option>
                                            <el-option :label="lang.exam_types[8]" :value="8"></el-option>
                                            <el-option :label="lang.exam_types[9]" :value="9"></el-option>
                                            <el-option :label="lang.exam_types[10]" :value="10"></el-option>
                                            <el-option :label="lang.exam_types[-1]" :value="-1"></el-option>
                                        </el-select></div
                                ></el-col>
                                <el-col :span="12">
                                    <div class="patient_item">
                                        <div class="label2">{{ lang.exam_time }}:</div>
                                        <div v-if="!canEdit">{{ exam_time}}</div>
                                        <el-date-picker
                                            v-else
                                            v-model="exam_time"
                                            type="datetime"
                                            :placeholder="lang.choose_date_time"
                                            format="yyyy-MM-dd HH:mm"
                                            value-format="yyyy-MM-dd HH:mm"
                                            size="mini"
                                            default-time="12:00:00"
                                        >
                                        </el-date-picker></div
                                ></el-col>
                            </el-row>
                        </div>
                        <hr />
                        <div class="image_info">
                            <!-- <div>
                                <img :src="getResourceTempStatus(report.file.resource_id,'loaded')?getResourceTempStatus(report.file.resource_id,'realUrl'):report.file.url">
                            </div> -->
                            <el-row :gutter="20">
                                <el-col :span="12" v-for="item in currentImageList" :key="item.resource_id">
                                    <div class="exam_img_item">
                                        <img :src="getRealUrl(item)" class="exam_img" />
                                        <div v-if="canEdit" class="exam_img_action_tool">
                                            <el-button
                                                icon="el-icon-delete"
                                                type="text"
                                                @click.stop="handleImageRemove(item)"
                                                :title="lang.action_delete_text"
                                            ></el-button>
                                        </div>
                                    </div>
                                </el-col>
                                <template v-if="currentImageList.length < 4 && canEdit">
                                    <el-col
                                        :span="12"
                                        @click.native="openSelectImageDialog"
                                        v-loading="loadingGetImageList"
                                    >
                                        <div class="exam_img_item">
                                            <div class="add_exam_image">
                                                <i class="iconfont iconplus"></i>
                                            </div>
                                        </div>
                                    </el-col>
                                </template>
                            </el-row>
                        </div>
                        <div class="ultrasound_finding">
                            <span
                                ><b>{{ lang.ultrasonic_discovery }}</b></span
                            >
                            <el-input
                                v-model="report_finding"
                                :disabled="!canEdit"
                                type="textarea"
                                :rows="6"
                            ></el-input>
                        </div>
                        <div class="ultrasound_conclusion">
                            <span
                                ><b>{{ lang.ultrasonic_prompt }}</b></span
                            >
                            <el-input
                                v-model="report_conclusion"
                                :disabled="!canEdit"
                                type="textarea"
                                :rows="6"
                            ></el-input>
                        </div>
                        <hr />
                        <div class="doctor_info clearfix">
                            <div class="patient_item">{{ lang.report_time }}</div>
                            <div class="patient_item">{{ lang.examining_doctor }}</div>
                            <div class="patient_item">{{ lang.autograph }}</div>
                        </div>
                        <div class="report_operation clearfix">
                            <el-button class="fr operation" @click="closeReport">{{
                                lang.supply_case_close_btn
                            }}</el-button>
                            <el-button v-show="canEdit" class="fr operation" @click="saveReport">{{
                                lang.submit_btn
                            }}</el-button>
                            <el-button v-show="!canEdit" :disabled="isLock" class="fr operation" @click="clickEdit">{{
                                lang.edit_txt
                            }}</el-button>
                            <el-select class="fr operation" v-model="isA4">
                                <el-option label="A4" :value="true"></el-option>
                                <el-option label="100%" :value="false"></el-option>
                            </el-select>
                        </div>
                    </div>
                </vue-scroll>
                <div class="report_dialog" v-show="isShowDialog">
                    <div>
                        <div class="title">{{ lang.ultrasonic_discovery }}</div>
                        <div class="text" v-for="(findingStr, index) of dialog_template.finding" :key="index">
                            <el-checkbox
                                :disabled="!canEdit"
                                v-show="dialog_template.finding.length > 1"
                                v-model="findingStr.check"
                            ></el-checkbox>
                            <el-input
                                :disabled="!canEdit"
                                v-model="findingStr.label"
                                type="textarea"
                                :rows="2"
                            ></el-input>
                        </div>
                    </div>
                    <div>
                        <div class="title">{{ lang.ultrasonic_prompt }}</div>
                        <div class="text" v-for="(conclusionStr, index) of dialog_template.conclusion" :key="index">
                            <el-checkbox
                                :disabled="!canEdit"
                                v-show="dialog_template.conclusion.length > 1"
                                v-model="conclusionStr.check"
                            ></el-checkbox>
                            <el-input
                                :disabled="!canEdit"
                                v-model="conclusionStr.label"
                                type="textarea"
                                :rows="2"
                            ></el-input>
                        </div>
                    </div>
                    <div class="clearfix">
                        <el-button class="fr" @click="isShowDialog = false">{{ lang.supply_case_close_btn }}</el-button>
                        <el-button class="fr" :disabled="!canEdit" @click="write(1)">{{ lang.replace_btn }}</el-button>
                        <el-button class="fr" :disabled="!canEdit" @click="write(2)">{{ lang.add_btn }}</el-button>
                    </div>
                </div>
            </div>
        </div>
        <i class="iconfont iconsearchclose" @click="closeReport"></i>
        <SelectImageTransfer
            v-model="showSelectImageVisible"
            :file="report.file"
            :maxTransfer="maxExamImage"
            @submit="handleSelectedImage"
            :defaultTargetList="currentImageList"
            :defaultSourceList="sourceList"
        ></SelectImageTransfer>
    </div>
</template>
<script>
import base from "../lib/base";
import { getResourceTempStatus, getRealUrl } from "../lib/common_base";
import SelectImageTransfer from "../components/selectImageTransfer.vue";
import service from "../service/service";
import moment from "moment";
export default {
    mixins: [base],
    name: "MyReport",
    components: {
        SelectImageTransfer,
    },
    filters: {
        sexFilter(type, component) {
            const sexTypes = {
                0: component.lang.sex[0],
                1: component.lang.sex[1],
                2: component.lang.sex[2],
            };
            return sexTypes[type] || component.lang.sex[2];
        },
        examTypesFilter(type, component) {
            return component.lang.exam_types[type] || component.lang.exam_types[-1];
        }
    },
    data() {
        return {
            isShowReport:false,
            getResourceTempStatus,
            getRealUrl,
            report: this.$store.state.report,
            isA4: true,
            canEdit: false, //可以编辑
            isLock: false, //锁住图片
            report_finding: "",
            report_conclusion: "",
            report_title: "",
            report_subtitle: "",
            exam_patient_name: "",
            exam_patient_age: "",
            exam_patient_sex: "",
            exam_type: "",
            exam_time: "",
            list: [],
            defaultProps: {
                children: "children",
                label: "label",
            },
            id: 1,
            isShowDialog: false,
            dialog_template: {
                finding: [],
                conclusion: [],
            },
            maxExamImage: 4,
            currentImageList: [],
            showSelectImageVisible: false,
            sourceList: [],
            loadingGetImageList: false,
        };
    },
    mounted() {
        this.$nextTick(() => {
            this.$root.eventBus.$off("lockReport").$on("lockReport", this.lockReport);
            this.$root.eventBus.$off("initReportPage").$on("initReportPage", this.initReportInfo);
            this.$root.eventBus.$off("destroyReport").$on("destroyReport", this.destroyReport);
        });
    },
    destoryed() {
        // this.reportInfo=null
    },
    methods: {
        async initReportInfo() {
            this.isShowReport = true;
            let params = {
                img_id: this.report.file.img_id,
                resource_id: this.report.file.resource_id,
                group_id: this.report.file.group_id,
            };
            const res = await this.getReportInfo(params);
            console.log(res, "getReportInfo", this.report);
            if (res.data.error_code === 0) {
                if (res.data.data) {
                    this.report_finding = res.data.data.finding;
                    this.report_conclusion = res.data.data.conclusion;
                    this.isLock = res.data.data.lock;
                    this.currentImageList = this.filterTargetImageList(res.data.data.image_list);
                    let business_data = res.data.data.business_data;
                    if (business_data.hasOwnProperty("report_title")) {
                        this.report_title = business_data.report_title;
                    } else {
                        this.report_title = this.lang.report_title;
                    }
                    if (business_data.hasOwnProperty("report_subtitle")) {
                        this.report_subtitle = business_data.report_subtitle;
                    } else {
                        this.report_subtitle = this.lang.report_subtitle;
                    }
                    if (business_data.hasOwnProperty("patient_age")) {
                        this.exam_patient_age = business_data.patient_age;
                    } else {
                        this.exam_patient_age = Math.max(this.report.file.patient_age, 0);
                    }
                    if (business_data.hasOwnProperty("patient_name")) {
                        this.exam_patient_name = business_data.patient_name;
                    } else {
                        this.exam_patient_name = this.report.file.patient_name;
                    }
                    if (business_data.hasOwnProperty("patient_sex")) {
                        this.exam_patient_sex = business_data.patient_sex;
                    } else {
                        this.exam_patient_sex = this.report.file.patient_sex;
                    }
                    if (business_data.hasOwnProperty("exam_time")) {
                        this.exam_time = business_data.exam_time;
                    } else {
                        this.exam_time = this.report.file.patient_series_datetime;
                    }
                    if (business_data.hasOwnProperty("exam_type")) {
                        this.exam_type = business_data.exam_type;
                    } else {
                        this.exam_type = this.report.file.exam_type;
                    }
                } else {
                    this.currentImageList.push(this.report.file);
                    this.report_title = this.lang.report_title;
                    this.report_subtitle = this.lang.report_subtitle;
                    this.exam_patient_age = Math.max(this.report.file.patient_age, 0);
                    this.exam_patient_name = this.report.file.patient_name;
                    this.exam_patient_sex = this.report.file.patient_sex;
                    this.exam_time = this.report.file.patient_series_datetime;
                    this.exam_type = this.report.file.exam_type;
                }
            }
            // let cid = this.report.file.group_id;
            // var controller = this.conversationList[cid].socket;
            // controller.emit("get_report_info", params, (is_succ, report_info) => {
            //     console.log(is_succ, report_info, "get_report_info");
            //     this.report_finding = report_info.finding;
            //     this.report_conclusion = report_info.conclusion;
            //     this.isLock = report_info.lock;
            //     this.currentImageList.push(this.report.file);
            // });
            if (this.report.report_template.length == 0) {
                window.main_screen.controller.emit("get_common_report_template", (is_succ, data) => {
                    console.log(is_succ, data, "get_common_report_template");
                    if (is_succ) {
                        this.id = 1;
                        let list = this.setTemplate(data.Dir_);
                        this.$store.commit("report/setReportTemplate", list);
                    }
                });
            }
        },
        setTemplate(data) {
            let temp = [];
            for (let folder of data) {
                let obj = {};
                obj.label = folder.$.Name_;
                if (folder.Dir_) {
                    obj.id = this.id;
                    this.id++;
                    obj.children = this.setTemplate(folder.Dir_);
                } else if (folder.Item_) {
                    obj.children = [];
                    for (let item of folder.Item_) {
                        obj.children.push({
                            label: item.Name_[0],
                            finding: item.Finding_,
                            conclusion: item.Conclusion_,
                            lastNode: true,
                        });
                    }
                }
                temp.push(obj);
            }
            return temp;
        },
        handleNodeClick(obj) {
            if (obj.lastNode) {
                this.dialog_template = {
                    finding: [],
                    conclusion: [],
                };
                if (obj.finding) {
                    let finding = obj.finding[0].split("|");
                    for (let item of finding) {
                        if (item == "" || item == "$") {
                            continue;
                        }
                        var sub_item_list = item.split("@");
                        for (var i in sub_item_list) {
                            if (1 == i % 2 && -1 != sub_item_list[i].indexOf("^")) {
                                sub_item_list[i] = sub_item_list[i].split("^")[0];
                            }
                            sub_item_list[i] = sub_item_list[i].replace("|", "");
                            sub_item_list[i] = sub_item_list[i].replace(/\$/g, "\r\n");
                        }

                        this.dialog_template.finding.push({
                            check: false,
                            label: sub_item_list.join(""),
                        });
                    }
                }
                if (obj.conclusion) {
                    let conclusion = obj.conclusion[0].split("|");
                    for (let item of conclusion) {
                        if (item == "" || item == "$") {
                            continue;
                        }
                        var sub_item_list = item.split("@");
                        for (var i in sub_item_list) {
                            if (1 == i % 2 && -1 != sub_item_list[i].indexOf("^")) {
                                sub_item_list[i] = sub_item_list[i].split("^")[0];
                            }
                            sub_item_list[i] = sub_item_list[i].replace("|", "");
                            sub_item_list[i] = sub_item_list[i].replace(/\$/g, "\r\n");
                        }
                        this.dialog_template.conclusion.push({
                            check: false,
                            label: sub_item_list.join(""),
                        });
                    }
                }
                if (this.dialog_template.finding.length == 1) {
                    this.dialog_template.finding[0].check = true;
                }
                if (this.dialog_template.conclusion.length == 1) {
                    this.dialog_template.conclusion[0].check = true;
                }
                this.isShowDialog = true;
            }
        },
        write(type) {
            let finding = [];
            let conclusion = [];
            for (let item of this.dialog_template.finding) {
                if (item.check) {
                    finding.push(item.label);
                }
            }
            for (let item of this.dialog_template.conclusion) {
                if (item.check) {
                    conclusion.push(item.label);
                }
            }
            if (type == 1) {
                //替换
            } else {
                //添加
                finding.unshift(this.report_finding);
                conclusion.unshift(this.report_conclusion);
            }
            this.report_finding = finding.join("\n");
            this.report_conclusion = conclusion.join("\n");
            this.isShowDialog = false;
        },
        async clickEdit() {
            this.isLock = true;
            let params = {
                lock: 1,
                resource_id: this.report.file.resource_id,
                img_id: this.report.file.img_id,
                group_id: this.report.file.group_id,
            };
            const res = await this.editReportInfo(params);
            if (res.data.error_code === 0) {
                const data = res.data.data;
                if (data) {
                    if (
                        (data.finding && this.report_finding != data.finding) ||
                        (data.conclusion && this.report_conclusion != data.conclusion)
                    ) {
                        this.$confirm(this.lang.confirm_update_report, this.lang.tip_title, {
                            confirmButtonText: this.lang.confirm_txt,
                            cancelButtonText: this.lang.cancel_btn,
                            type: "waring",
                        })
                            .then(() => {
                                this.report_finding = data.finding;
                                this.report_conclusion = data.conclusion;
                                this.isLock = false;
                                this.canEdit = true;
                            })
                            .catch(async () => {
                                const res = await this.editReportInfo({
                                    lock: 0,
                                    img_id: this.report.file.img_id,
                                    resource_id: this.report.file.resource_id,
                                    group_id: this.report.file.group_id,
                                });

                                this.isLock = false;
                            });
                    } else {
                        this.isLock = false;
                        this.canEdit = true;
                    }
                } else {
                    this.isLock = false;
                    this.canEdit = true;
                }
            } else {
                this.isLock = false;
                this.$message.error(this.lang.edit_report_unenable_tips);
            }
            console.log(res, "edit");
            // controller.emit("edit_report_info", params, (is_succ, data) => {
            //     if (is_succ) {
            //         if (
            //             (data.finding && this.report_finding != data.finding) ||
            //             (data.conclusion && this.report_conclusion != data.conclusion)
            //         ) {
            //             this.$confirm(this.lang.confirm_update_report, this.lang.tip_title, {
            //                 confirmButtonText: this.lang.confirm_txt,
            //                 cancelButtonText: this.lang.cancel_btn,
            //                 type: "waring",
            //             })
            //                 .then(() => {
            //                     this.report_finding = data.finding;
            //                     this.report_conclusion = data.conclusion;
            //                     this.isLock = false;
            //                     this.canEdit = true;
            //                 })
            //                 .catch(() => {
            //                     controller.emit("edit_report_info", {
            //                         lock: false,
            //                         img_id: this.report.file.img_id,
            //                     });
            //                     this.isLock = false;
            //                 });
            //         } else {
            //             this.isLock = false;
            //             this.canEdit = true;
            //         }
            //     }
            // });
        },
        lockReport(data) {
            if (data.userId === this.user.uid) {
                return;
            }
            if (data.resource_id == this.report.file.resource_id) {
                if (data.lock) {
                    this.isLock = true;
                    this.canEdit = false;
                } else {
                    this.isLock = false;
                }
            }
        },
        async closeReport() {
            let cid = this.report.file.group_id;
            if (cid && this.conversationList[cid]) {
                const res = await this.editReportInfo({
                    lock: 0,
                    img_id: this.report.file.img_id,
                    resource_id: this.report.file.resource_id,
                    group_id: this.report.file.group_id,
                });
                this.$root.eventBus.$emit("showRealTimeVideo");
                this.back();
            } else {
                this.back();
            }
        },
        async saveReport() {
            let imageList = this.currentImageList.map((img) => img.resource_id);
            let params = {
                resource_id: this.report.file.resource_id,
                img_id: this.report.file.img_id,
                finding: this.report_finding,
                conclusion: this.report_conclusion,
                group_id: this.report.file.group_id,
                imageList,
                business_data: {
                    report_title: this.report_title,
                    report_subtitle: this.report_subtitle,
                    patient_age: this.exam_patient_age,
                    patient_name: this.exam_patient_name,
                    patient_sex: this.exam_patient_sex,
                    exam_time: this.exam_time,
                    exam_type: this.exam_type,
                },
            };
            const res = await this.setReportInfo(params);
            console.log(params,'params')
            if (res.data.error_code === 0) {
                this.closeReport();
            }
            // let cid = this.report.file.group_id;
            // var controller = this.conversationList[cid].socket;
            // controller.emit("save_report_info", params, (is_succ, data) => {
            //     this.closeReport();
            // });
        },
        async openSelectImageDialog() {
            if (this.sourceList.length === 0) {
                await this.getExamImageList();
            }

            this.showSelectImageVisible = true;
        },
        handleSelectedImage(targetList) {
            this.currentImageList = targetList;
        },
        handleImageRemove(item) {
            const index = this.currentImageList.findIndex((obj) => obj.resource_id === item.resource_id);
            if (index !== -1) {
                this.currentImageList.splice(index, 1);
            }
        },
        async getReportInfo(params) {
            return service.getReportInfo(params);
        },
        async setReportInfo(params) {
            return service.setReportInfo(params);
        },
        async editReportInfo(params) {
            return service.editReportInfo(params);
        },
        async getExamImageList() {
            return new Promise((resolve, reject) => {
                let timer = null;
                this.loadingGetImageList = true;
                let cid = this.report.file.group_id;
                this.controller = this.conversationList[cid].socket;
                this.controller.emit(
                    "get_exam_image_list",
                    {
                        exam_id: this.report.file.exam_id,
                    },
                    (is_succ, data) => {
                        this.loadingGetImageList = false;
                        if (is_succ) {
                            // this.setShowImageList(exam,data.image_list);
                            console.log(is_succ, data, "get_exam_image_list");
                            this.$set(this, "sourceList", this.filterSourceImageList(data.image_list));
                            resolve(true);
                        } else {
                            console.error(is_succ, data, "get_exam_image_list");
                            reject(this.lang.operate_err);
                        }
                        clearTimeout(timer);
                        timer = null;
                    }
                );
                timer = setTimeout(() => {
                    reject("loadImageList timeout");
                    this.loadingGetImageList = false;
                }, 10000);
            });
        },
        filterTargetImageList(imageList) {
            return imageList.filter((item) => item.url);
        },
        filterSourceImageList(imageList) {
            const filteredList = [];
            const resourceIdSet = new Set();

            for (let i = 0; i < imageList.length; i++) {
                const image = imageList[i];
                const resourceId = image.resource_id;
                const imgEncodeType = image.img_encode_type.toUpperCase();

                if (!resourceIdSet.has(resourceId) && (imgEncodeType === "PNG" || imgEncodeType === "JPG")) {
                    filteredList.push(image);
                    resourceIdSet.add(resourceId);
                }
            }
            return filteredList;
        },
        destroyReport(){
            this.isShowReport = false;
            this.canEdit= false //可以编辑
            this.isLock= false//锁住图片
            this.report_finding=  ""
            this.report_conclusion=  ""
            this.report_title=  ""
            this.report_subtitle=  ""
            this.exam_patient_name=  ""
            this.exam_patient_age=  ""
            this.exam_patient_sex = ""
            this.exam_type= ""
            this.exam_time= ""
            this.list= []
            this.defaultProps= {
                children: "children",
                label: "label",
            },
            this.isShowDialog= false
            this.dialog_template= {
                finding: [],
                conclusion: [],
            }
            this.currentImageList= []
            this.showSelectImageVisible= false
            this.sourceList= []
            this.loadingGetImageList= false
        }
    },
};
</script>
<style lang="scss" scoped>
.report_page {
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    right: 0;
    background: #85a5a2;
    z-index: 6002;
    display: flex;
    overflow: hidden;
    .data_tree {
        width: 300px;
        background: #fff;
        margin-right: 2px;
        .el-tree {
            color: #000;
            .el-tree-node__content {
                height: auto;
            }
            .el-tree-node__label {
                white-space: normal;
                font-size: 14px;
            }
        }
    }
    .report_container {
        flex: 1;
        position: relative;
        .report_view {
            width: 100%;
            height: 100%;
            background: #fff;
            margin: 0 auto;
            position: relative;
            &.is_a4 {
                width: 210mm;
            }
            .title_bar {
                text-align: center;
                img {
                    display: inline-block;
                    width: 60px;
                    margin-right: 10px;
                    margin-top: 10px;
                }
                .title {
                    display: inline-block;
                    vertical-align: text-bottom;
                    font-size: 26px;
                    .sub_title {
                        font-size: 20px;
                    }
                }
            }
            .report_content {
                margin: 20px;
                font-size: 20px;
                text-align: left;
                .ultrasound_info_item {
                    float: left;
                    width: 50%;
                }
                .ultrasound_finding {
                    textarea {
                    }
                }
                .patient_info {
                    .patient_item {
                        display: flex;
                        padding: 0 10px;
                        margin-bottom: 10px;
                        font-size: 16px;
                        align-items: center;
                        .content{
                            // text-wrap:nowrap
                        }
                        .label {
                            // width: 48px;
                            flex-shrink: 0;
                            font-size: 18px;
                        }
                        .label2 {
                            padding-right: 10px;
                            font-size: 18px;
                        }
                    }
                }
                .image_info {
                    .exam_img_item {
                        height: 270px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        margin-top: 20px;
                        border: 3px dashed #ccc;
                        position: relative;
                        .exam_img {
                            height: 100%;
                            object-fit: contain;
                            max-width: 100%;
                        }
                        .add_exam_image {
                            border-radius: 10px;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            cursor: pointer;
                            width: 100%;
                            height: 100%;
                            .iconplus {
                                font-size: 60px;
                            }
                        }
                        .exam_img_action_tool {
                            width: 100%;
                            height: 50px;
                            position: absolute;
                            width: 100%;
                            left: 0;
                            bottom: 0;
                            cursor: default;
                            text-align: center;
                            color: #fff;
                            font-size: 20px;
                            background-color: rgba(0, 0, 0, 0.5);
                            display: flex;
                            justify-content: flex-end;
                            align-items: center;
                            .el-button {
                                color: #fff;
                                font-size: 24px;
                                margin-right: 10px;
                            }
                        }
                    }
                }
                .doctor_info {
                    .patient_item {
                        float: left;
                        width: 33%;
                        margin-bottom: 10px;
                    }
                }
                .ultrasound_conclusion {
                    margin-top: 10px;
                }
                .operation {
                    margin: 10px;
                }
                hr {
                    border-top: 2px solid #000;
                    margin: 20px 0;
                }
                img {
                    width: 100%;
                }
            }
            .report_dialog {
                position: absolute;
                top: 80px;
                left: 50%;
                transform: translateX(-50%);
                background: #cfe5e3;
                width: 100%;
                padding: 20px;
                max-height: calc(100% - 100px);
                overflow: auto;
                .title {
                    margin: 12px 0;
                }
                .text {
                    display: flex;
                    margin: 10px 0;
                    .el-checkbox {
                        margin-right: 10px;
                    }
                }
                .el-button {
                    margin: 10px;
                }
            }
        }
    }
    .iconsearchclose {
        position: absolute;
        right: 10px;
        top: 4px;
        font-size: 20px;
        cursor: pointer;
    }
}
</style>

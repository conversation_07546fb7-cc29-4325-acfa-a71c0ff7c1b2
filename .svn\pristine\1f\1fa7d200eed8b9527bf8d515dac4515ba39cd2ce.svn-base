<template>
    <div class="report-table">
        <div class="table-header">
            <el-button type="primary" size="small" @click="handleExport" class="ai-theme-background export-button"
                >导出</el-button
            >
            <div class="statistics">
                <div class="statistics_item">
                    已质控报告<span class="count">{{ total }}</span
                    >份
                </div>
                <div class="statistics_item">
                    质控合格：<span class="count">{{ qualifiedCount }}</span
                    >份
                </div>
                <div class="statistics_item">
                    报告合格率 <span class="count">{{ (qualifiedRate * 100).toFixed(1) }}%</span>
                </div>
                <span type="primary" class="ai-operation-link" @click="handleViewUnqualified">查看不合格报告</span>
            </div>
        </div>

        <el-table
            :data="tableData"
            style="width: 100%"
            border
            stripe
            :header-cell-style="{ background: '#D7DFE1', color: '#000' }"
        >
            <el-table-column type="selection" min-width="50"></el-table-column>
            <el-table-column prop="examDoctor" label="检查医生" min-width="120"></el-table-column>
            <el-table-column prop="reportDoctor" label="报告医生" min-width="120"></el-table-column>
            <el-table-column prop="examItem" label="检查项目" min-width="200" show-overflow-tooltip></el-table-column>

            <el-table-column prop="result" label="质控结果" min-width="100">
                <template slot-scope="scope">
                    <div class="result-tag">
                        <template v-if="scope.row.result === '分析中'">
                            <span class="result-tag-item">
                                <span class="status-dot analyzing-dot"></span>
                                分析中
                            </span>
                        </template>
                        <template v-else-if="scope.row.result === '合格'">
                            <span class="result-tag-item">
                                <span class="status-dot qualified-dot"></span>
                                合格
                            </span>
                        </template>
                        <template v-else>
                            <span class="result-tag-item">
                                <span class="status-dot unqualified-dot"></span>
                                不合格
                            </span>
                        </template>
                    </div>
                </template>
            </el-table-column>

            <el-table-column
                prop="findings"
                label="超声所见/检查所见/可见"
                width="300"
                show-overflow-tooltip
            ></el-table-column>
            <el-table-column
                prop="suggestion"
                label="超声提示/检查结论/诊断意见"
                width="300"
                show-overflow-tooltip
            ></el-table-column>
            <el-table-column prop="reportUploadTime" label="报告上传时间" width="180"></el-table-column>
            <el-table-column prop="hospitalName" label="医院名称" width="150"></el-table-column>

            <el-table-column label="操作" min-width="150">
                <template slot-scope="scope">
                    <span class="ai-operation-link" @click="handleView(scope.row)">查看</span>
                    <span
                        class="ai-operation-link"
                        v-if="scope.row.result !== '分析中'"
                        @click="handleRecheck(scope.row)"
                        style="margin-left: 10px"
                    >
                        重新质控
                    </span>
                </template>
            </el-table-column>
        </el-table>

        <div class="pagination-container">
            <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="currentPage"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            ></el-pagination>
        </div>
    </div>
</template>

<script>
export default {
    name: "ultrasoundReportQCReportTable",
    data() {
        return {
            tableData: [
                {
                    id: "********",
                    examDoctor: "王武",
                    reportDoctor: "李四",
                    examItem: "乳腺及腋窝（双乳腺组织）",
                    result: "合格",
                    findings:
                        "双侧乳腺组织层次清晰，腺体结构紊乱，右侧乳腺外上象限可见一大小约1.2×0.8cm的低回声结节，边界尚清，形态欠规则，内部回声不均匀，可见点状强回声",
                    suggestion: "右侧乳腺实性结节伴粗大钙化，符合BI-RADS 4a类，建议进一步检查",
                    reportUploadTime: "2025-03-27 08:50:08",
                    hospitalName: "东莞市人民医院",
                },
                {
                    id: "R2024002",
                    examDoctor: "赵六",
                    reportDoctor: "李四",
                    examItem: "甲状腺及颈部淋巴结",
                    result: "分析中",
                    findings: "甲状腺右叶可见一大小约0.8×0.6cm的低回声结节，边界尚清，形态规则，内部回声均匀",
                    suggestion: "甲状腺右叶结节，考虑良性可能大，建议随访观察",
                    reportUploadTime: "2025-03-27 09:30:15",
                    hospitalName: "东莞市人民医院",
                },
                {
                    id: "R2024003",
                    examDoctor: "王武",
                    reportDoctor: "李四",
                    examItem: "肝胆胰脾",
                    result: "不合格",
                    findings:
                        "肝脏大小形态正常，包膜光滑，实质回声均匀，肝内血管走行清晰。胆囊大小正常，壁稍毛糙，腔内未见明显异常回声",
                    suggestion: "胆囊壁毛糙，考虑慢性胆囊炎可能",
                    reportUploadTime: "2025-03-27 10:15:22",
                    hospitalName: "东莞市人民医院",
                },
                {
                    id: "R2024003",
                    examDoctor: "王武",
                    reportDoctor: "李四",
                    examItem: "肝胆胰脾",
                    result: "不合格",
                    findings:
                        "肝脏大小形态正常，包膜光滑，实质回声均匀，肝内血管走行清晰。胆囊大小正常，壁稍毛糙，腔内未见明显异常回声",
                    suggestion: "胆囊壁毛糙，考虑慢性胆囊炎可能",
                    reportUploadTime: "2025-03-27 10:15:22",
                    hospitalName: "东莞市人民医院",
                },
                {
                    id: "R2024003",
                    examDoctor: "王武",
                    reportDoctor: "李四",
                    examItem: "肝胆胰脾",
                    result: "不合格",
                    findings:
                        "肝脏大小形态正常，包膜光滑，实质回声均匀，肝内血管走行清晰。胆囊大小正常，壁稍毛糙，腔内未见明显异常回声",
                    suggestion: "胆囊壁毛糙，考虑慢性胆囊炎可能",
                    reportUploadTime: "2025-03-27 10:15:22",
                    hospitalName: "东莞市人民医院",
                },
                {
                    id: "R2024003",
                    examDoctor: "王武",
                    reportDoctor: "李四",
                    examItem: "肝胆胰脾",
                    result: "不合格",
                    findings:
                        "肝脏大小形态正常，包膜光滑，实质回声均匀，肝内血管走行清晰。胆囊大小正常，壁稍毛糙，腔内未见明显异常回声",
                    suggestion: "胆囊壁毛糙，考虑慢性胆囊炎可能",
                    reportUploadTime: "2025-03-27 10:15:22",
                    hospitalName: "东莞市人民医院",
                },
                {
                    id: "R2024003",
                    examDoctor: "王武",
                    reportDoctor: "李四",
                    examItem: "肝胆胰脾",
                    result: "不合格",
                    findings:
                        "肝脏大小形态正常，包膜光滑，实质回声均匀，肝内血管走行清晰。胆囊大小正常，壁稍毛糙，腔内未见明显异常回声",
                    suggestion: "胆囊壁毛糙，考虑慢性胆囊炎可能",
                    reportUploadTime: "2025-03-27 10:15:22",
                    hospitalName: "东莞市人民医院",
                },
                {
                    id: "R2024003",
                    examDoctor: "王武",
                    reportDoctor: "李四",
                    examItem: "肝胆胰脾",
                    result: "不合格",
                    findings:
                        "肝脏大小形态正常，包膜光滑，实质回声均匀，肝内血管走行清晰。胆囊大小正常，壁稍毛糙，腔内未见明显异常回声",
                    suggestion: "胆囊壁毛糙，考虑慢性胆囊炎可能",
                    reportUploadTime: "2025-03-27 10:15:22",
                    hospitalName: "东莞市人民医院",
                },
                {
                    id: "R2024003",
                    examDoctor: "王武",
                    reportDoctor: "李四",
                    examItem: "肝胆胰脾",
                    result: "不合格",
                    findings:
                        "肝脏大小形态正常，包膜光滑，实质回声均匀，肝内血管走行清晰。胆囊大小正常，壁稍毛糙，腔内未见明显异常回声",
                    suggestion: "胆囊壁毛糙，考虑慢性胆囊炎可能",
                    reportUploadTime: "2025-03-27 10:15:22",
                    hospitalName: "东莞市人民医院",
                },
                {
                    id: "R2024003",
                    examDoctor: "王武",
                    reportDoctor: "李四",
                    examItem: "肝胆胰脾",
                    result: "不合格",
                    findings:
                        "肝脏大小形态正常，包膜光滑，实质回声均匀，肝内血管走行清晰。胆囊大小正常，壁稍毛糙，腔内未见明显异常回声",
                    suggestion: "胆囊壁毛糙，考虑慢性胆囊炎可能",
                    reportUploadTime: "2025-03-27 10:15:22",
                    hospitalName: "东莞市人民医院",
                },
            ],
            currentPage: 1,
            pageSize: 10,
            total: 400,
            qualifiedCount: 390,
            qualifiedRate: 0.975,
        };
    },
    methods: {
        handleSizeChange(val) {
            this.pageSize = val;
            // 重新加载数据
        },
        handleCurrentChange(val) {
            this.currentPage = val;
            // 重新加载数据
        },
        handleExport() {
            // 处理导出逻辑
        },
        handleView(row) {
            this.$router.push({
                name: 'ultrasoundReportQCDetail',
                params: { id: row.id }
            });
        },
        handleRecheck(row) {
            // 处理重新质控逻辑
        },
        handleViewUnqualified() {
            // 处理查看不合格报告逻辑
        },
    },
};
</script>

<style lang="scss" scoped>
@import "@/module/ultrasync_pc/style/aiChat.scss";
.report-table {
    background: #fff;
    padding: 20px;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    .table-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        .export-button {
            width: 120px;
            height: 36px;
            border-radius: 4px;
            font-size: 16px;
        }
        .statistics {
            background: rgba(22, 164, 233, 0.12);
            border: 1px solid rgba(22, 164, 233, 0.58);
            border-radius: 4px;
            font-size: 15px;
            color: #202226;
            flex: 1;
            display: flex;
            justify-content: flex-end;
            padding: 5px;
            margin-left: 20px;

            .statistics_item {
                line-height: 24px;
                margin-right: 10px;
                .count {
                    color: #007bff;
                    font-weight: 600;
                }
            }
        }
    }

    .result-tag {
        display: flex;
        align-items: center;
        justify-content: center;

        .result-tag-item {
            color: #000;
            display: flex;
            align-items: center;
            width: 100%;
            flex-wrap: wrap;
            .status-dot {
                display: inline-block;
                width: 16px;
                height: 16px;
                border-radius: 50%;
                margin-right: 5px;
                flex-shrink: 0;
            }
            .analyzing-dot {
                background: #fff;
                border: 2px solid #000;
            }
            .qualified-dot {
                background: #04cb00;
                border: 2px solid #009718;
            }
            .unqualified-dot {
                background: #ffbf00;
                border: 2px solid #ff5a00;
            }
        }
    }

    .pagination-container {
        margin-top: 20px;
        text-align: right;
    }

    :deep(.el-table) {
        @extend %ai-scrollbar-style;
        .el-table__header-wrapper {
            th {
                font-weight: bold;
                padding: 6px 0;
                color: #000;
            }
        }

        .el-table__body-wrapper {
            .el-table__row {
                td {
                    padding: 6px 0;
                    color: #000;
                }
            }
        }
        .el-table__body-wrapper {
            @extend %ai-scrollbar-style;
        }
        .el-checkbox__input.is-checked .el-checkbox__inner {
            background: $ai-theme-gradient;
            border-color: #6082e0;
        }

        .el-checkbox__input.is-indeterminate .el-checkbox__inner {
            background: $ai-theme-gradient;
            border-color: #6082e0;
        }
    }
}
</style>

/* eslint-disable no-console */
import { register } from "register-service-worker";
import service from "./service/service";
import moment from "moment";
import Tool from '@/common/tool'
let env = "build";
const isCE = window.location.href.indexOf("_ce") > -1;
let dir = isCE ? "/pc_ce/service-worker.js" : "/pc/service-worker.js";
let lastServiceBuildTime = null
const isProductEnv = window.location.href.indexOf('consult.mindray.com') > -1;
const intervalTime = isProductEnv?1*60*60*1000:5*60*1000
const isIP = function (ip) {
    const reg =
        /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
    return reg.test(ip);
};
const getBuildTime = function () {
    return new Promise((resolve, reject) => {
        service
            .getBuildTime({
                envPath:isCE?'pc_ce':'pc'
            })
            .then((res) => {
                resolve(res);
            })
            .catch((error) => {
                reject(error);
                console.error("error", error);
            });
    });
};
const formatLocalBuildTime = function (localBuildTime) {
    // 截取出第一个字符串中的时间部分
    return localBuildTime.split(" ")[0] + " " + localBuildTime.split(" ")[1];
};
const compareTimesNeedUpdate = function (localBuildTime, serviceBuildTime) {
    // 将两个时间字符串转为 moment 对象进行比较
    const localMoment = moment(localBuildTime, "YYYY-MM-DD HH:mm");
    const serviceMoment = moment(serviceBuildTime, "YYYY-MM-DD HH:mm");

    // 判断时间大小
    if (localMoment.isAfter(serviceMoment)) {
        return false;
    } else if (localMoment.isBefore(serviceMoment)) {
        //服务器时间大于 本地编译时间
        return true;
    } else {
        return false;
    }
};
const checkUpdate = async function () {
    if (process.env.NODE_ENV !== "production") {
        return;
    }
    if (window.livingStatus) {
        //没有在直播中
        return;
    }
    const res = await getBuildTime();
    const localBuildTime = formatLocalBuildTime(process.env.VUE_APP_BUILD_TIME);
    const serviceBuildTime = res.data.data.buildTime;
    const ignoreUpdateBuildTime = sessionStorage.getItem('ignoreUpdateBuildTime')

    if(ignoreUpdateBuildTime === serviceBuildTime){
        return
    }
    if (compareTimesNeedUpdate(localBuildTime, serviceBuildTime)) {
        if(lastServiceBuildTime=== serviceBuildTime){
            return
        }
        setTimeout(() => {
            let lang = window.vm.$store.state.language;
            let message = `<div>
                <p>${lang.update_ready_tip}</p>
                <p>${lang.cur_version}</p>
                <p>${localBuildTime}</p>
                <p>${lang.latest_version}：</p>
                <p>${serviceBuildTime}</p>
            </div>`;
            Tool.closeElementMessageBoxByClass('buildTimeDialog')
            window.vm.$MessageBox.alert(message, lang.tip_title, {
                confirmButtonText: lang.confirm_txt,
                cancelButtonText: lang.cancel_btn,
                type: "warning",
                dangerouslyUseHTMLString: true, // 允许渲染 HTML
                customClass:'buildTimeDialog'
            })
                .then(() => {
                    // window.CWorkstationCommunicationMng.ResetAllMedia();
                    window.location.reload(true);
                })
                .catch(() => {
                    sessionStorage.setItem('ignoreUpdateBuildTime',serviceBuildTime)
                });
            lastServiceBuildTime = serviceBuildTime
        }, 3000);
    }
};
checkUpdate();
setInterval(() => {
    checkUpdate();
}, intervalTime);
if (/localhost/.test(window.location.host) || isIP(window.location.hostname)) {
    env = "localhost";
    dir = "/service-worker.js";
}
if ("serviceWorker" in navigator) {
    navigator.serviceWorker.getRegistrations().then((registrations) => {
        registrations.forEach((registration) => {
            registration.unregister().then(() => {
                console.log("Service Worker unregistered temporarily.");
                // 清空所有的 Service Worker 缓存
                if (caches) {
                    caches.keys().then((cacheNames) => {
                        cacheNames.forEach((cacheName) => {
                            caches
                                .delete(cacheName)
                                .then(() => {
                                    console.log(`Cache ${cacheName} deleted.`);
                                })
                                .catch((err) => {
                                    console.error(`Failed to delete cache ${cacheName}:`, err);
                                });
                        });
                    });
                }
                setTimeout(() => {
                    let lang = window.vm.$store.state.language;
                    window.vm
                        .$confirm(lang.tip_title, lang.update_ready_tip, {
                            confirmButtonText: lang.confirm_txt,
                            cancelButtonText: lang.cancel_btn,
                            type: "warning",
                        })
                        .then(() => {
                            // window.CWorkstationCommunicationMng.ResetAllMedia();
                            window.location.reload(true);
                        })
                        .catch(() => {});
                }, 3000);
            });
        });
    });
}
if (env!='localhost') {
    // register(dir, {
    //     registered(registration) {
    //         console.log('Service worker has been registered.',registration);
    //     },

    //     updatefound() {
    //         console.log('SW:New content is downloading.')
    //     },
    //     cached() {
    //         console.log('SW:Content has been cached for offline use.')
    //     },
    //     ready() {
    //         console.log(
    //             'SW:App is being served from cache by a service worker.\n' +
    //       'For more details, visit https://goo.gl/AFskqB'
    //         )
    //     },
    //     async updated(registration) {
    //         console.log('SW:updated',registration)
    //         registration.update().then(()=>{
    //             console.log('SW:New content is available; please refresh.')
    //             let lang=window.vm.$store.state.language;
    //             window.vm.$confirm(lang.tip_title,lang.update_ready_tip,{
    //                 confirmButtonText:lang.confirm_txt,
    //                 cancelButtonText:lang.cancel_btn,
    //                 type:'warning'
    //             }).then(()=>{
    //                 // window.CWorkstationCommunicationMng.ResetAllMedia();
    //                 window.location.reload(true)
    //             }).catch(()=>{})
    //         })
    //     },
    //     offline() {
    //         console.log(
    //             'SW:No internet connection found. App is running in offline mode.'
    //         )
    //     },
    //     error(error) {
    //         console.error('SW:Error during service worker registration:', error)
    //     },
    // })
}

<template>
    <transition name="slide">
        <div class="my_device_page second_level_page">
            <mrHeader>
                <template #title>
                    {{ lang.ultrasound_device_title }}
                </template>
            </mrHeader>
            <div class="my_device_container">
                <div class="device_container_item">
                    <p class="sub_title">{{ lang.series_number }}</p>
                    <p class="device_id_number">{{ deviceInfo.device_id }}</p>
                </div>
                <div class="device_container_item">
                    <p class="sub_title">{{ lang.current_device }}</p>
                    <div class="my_device_list_group">
                        <div class="my_device_list_item">
                            <div class="left-item">
                                <i class="iconfont icon-mobile"></i>
                                <div class="left-item-text">
                                    <div class="left-item-title" @click="editDeviceName">
                                        {{ deviceInfo.device_name || lang.undefined_name }}
                                    </div>
                                    <div class="left-item-des" @click="editDeviceRemark">
                                        {{ deviceInfo.device_remark || lang.no_device_remark }}
                                    </div>
                                    <div class="bind_btn" @click="gotoDeviceBinding">{{ lang.device_binding }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <CommonDialog
                v-model="editNameDialogVisible"
                :title="lang.edit_device_name"
                :showRejectButton="true"
                :beforeClose="beforeCloseNameDialog"
            >
                <van-form ref="editNameForm" validate-trigger="onSubmit" label-width="0">
                    <van-field
                        v-model="editNameForm.newName"
                        :placeholder="lang.input_device_name"
                        name="newName"
                        :rules="[
                            { required: true, message: lang.input_device_name },
                            { validator: validatorNameLength, message: `${lang.length_limit_of}16` },
                        ]"
                        @blur.stop="editRemarkForm.newName = editRemarkForm.newName.trim()"
                    />
                </van-form>
            </CommonDialog>
            <CommonDialog
                v-model="editRemarkDialogVisible"
                :title="lang.edit_device_remark"
                :showRejectButton="true"
                :beforeClose="beforeCloseRemarkDialog"
            >
                <van-form ref="editRemarkForm" validate-trigger="onSubmit" label-width="0">
                    <van-field
                        v-model="editRemarkForm.newRemark"
                        :placeholder="lang.input_device_remark"
                        name="newRemark"
                        :rules="[
                            { required: true, message: lang.input_device_remark },
                            { validator: validatorRemarkLength, message: `${lang.length_limit_of}32` },
                        ]"
                        @blur.stop="editRemarkForm.newRemark = editRemarkForm.newRemark.trim()"
                    />
                </van-form>
            </CommonDialog>
            <transition name="slide">
                <router-view></router-view>
            </transition>
        </div>
    </transition>
</template>
<script>
import base from "../lib/base";
import { Dialog, Form, Field, Toast } from "vant";
import CommonDialog from '../MRComponents/commonDialog.vue'
export default {
    mixins: [base],
    name: "MyDevicePage",
    components: {
        // vanIcon:Icon
        CommonDialog,
        VanForm: Form,
        VanField: Field,
    },
    computed: {
        deviceInfo() {
            return this.$store.state.device;
        },
    },
    data() {
        return {
            editNameDialogVisible: false,
            editRemarkDialogVisible: false,
            editNameForm: {
                newName: "",
            },
            editRemarkForm: {
                newRemark: "",
            },
        };
    },
    created() {
        this.getDeviceNameById();
    },
    methods: {
        gotoDeviceDetail() {
            this.$router.push("/index/my_device/device_detail");
        },
        getDeviceNameById() {
            const params = {
                deviceId: this.deviceInfo.device_id,
            };
            window.main_screen.getDeviceNameById(params, (res) => {
                if (res.error_code === 0) {
                    this.$store.commit("device/updateDeviceInfo", {
                        device_name: res.data.name,
                        device_remark: res.data.remark,
                    });
                } else {
                }
            });
        },
        gotoDeviceBinding() {
            this.$router.push(
                `/index/my_device/device_binding/${this.deviceInfo.device_id}?client_type=${this.systemConfig.clientType}&device_name=${this.deviceInfo.device_name}`
            );
        },
        editDeviceName() {
            this.editNameDialogVisible = true;
        },
        editDeviceRemark() {
            this.editRemarkDialogVisible = true;
        },
        async beforeCloseNameDialog(action, done) {
            if (action === "confirm") {
                try {
                    await this.$refs.editNameForm.validate(Object.keys(this.editNameForm));
                    await this.saveDeviceInfoChange(1, this.editNameForm.newName);
                    this.clearEditNameForm();
                    done();
                } catch (error) {
                    done(false);
                }
            } else {
                this.clearEditNameForm();
                done();
            }
        },
        validatorNameLength(val) {
            if (val.length > 16) {
                return false;
            } else {
                return true;
            }
        },
        clearEditNameForm() {
            this.editNameForm = {
                newName: "",
            };
            this.$refs.editNameForm.resetValidation(Object.keys(this.editNameForm));
        },
        async beforeCloseRemarkDialog(action, done) {
            if (action === "confirm") {
                try {
                    await this.$refs.editRemarkForm.validate(Object.keys(this.editRemarkForm));
                    await this.saveDeviceInfoChange(2, this.editRemarkForm.newRemark);
                    this.clearEditRemarkForm();
                    done();
                } catch (error) {
                    done(false);
                }
            } else {
                this.clearEditRemarkForm();
                done();
            }
        },
        validatorRemarkLength(val) {
            if (val.length > 32) {
                return false;
            } else {
                return true;
            }
        },
        clearEditRemarkForm() {
            this.editRemarkForm = {
                newRemark: "",
            };
            this.$refs.editRemarkForm.resetValidation(Object.keys(this.editRemarkForm));
        },
        saveDeviceInfoChange(type, value) {
            return new Promise((resolve, reject) => {
                let params = {};
                if (type == 1) {
                    if (this.editNameForm.newName === this.deviceInfo.device_name) {
                        resolve(true);
                        return;
                    }
                    params = {
                        device_id: this.deviceInfo.device_id,
                        device_name: this.editNameForm.newName,
                        remark: this.deviceInfo.device_remark,
                        device_type: this.systemConfig.clientType,
                    };
                } else if (type == 2) {
                    if (this.editRemarkForm.newRemark === this.deviceInfo.device_remark) {
                        resolve(true);
                        return;
                    }
                    params = {
                        device_id: this.deviceInfo.device_id,
                        device_name: this.deviceInfo.device_name,
                        remark: this.editRemarkForm.newRemark,
                        device_type: this.systemConfig.clientType,
                    };
                }
                window.main_screen.reportDeviceInfo(params, (res) => {
                    if (res.error_code === 0) {
                        Toast(this.lang.operate_success);
                        this.$store.commit("device/updateDeviceInfo", {
                            device_name: params.device_name,
                            device_remark: params.remark,
                        });
                        resolve(true);
                    } else {
                        reject(false);
                    }
                });
            });
        },
        handleInputNewName() {
            this.editNameForm.newName = this.editNameForm.newName.trim();
        },
    },
};
</script>
<style lang="scss">
.my_device_page {
    .my_device_container {
        padding: 1rem;
        .device_container_item {
            margin-bottom: 1rem;
            .sub_title {
                font-size: 0.8rem;
            }
            .device_id_number {
                padding: 0.5rem 0;
            }
            .my_device_list_group {
                border-top: 1px solid #00c59d;
                margin-top: 0.5rem;
                .my_device_list_item {
                    font-size: 0.8rem;
                    border-bottom: 1px solid #eee;
                    .left-item {
                        display: flex;
                        padding: 1rem 0;
                        .iconfont {
                            font-size: 5rem;
                            color: #60b5ff;
                            line-height: 1;
                        }
                        .left-item-text {
                            flex: 1;
                            padding: 0.7rem 0 0.3rem;
                            position: relative;
                            .bind_btn {
                                position: absolute;
                                right: 0;
                                bottom: 0;
                                background: #00c59d;
                                color: #fff;
                                font-size: 0.6rem;
                                padding: 0.2rem 0.4rem;
                                border-radius: 1rem;
                            }
                        }
                        .left-item-title {
                            font-size: 0.7rem;
                            font-weight: bold;
                            background: #eee;
                            line-height: 1.4rem;
                            border-radius: 0.3rem;
                            padding: 0 0.5rem;
                            height: 1.4rem;
                            width: 100%;
                            box-sizing: border-box;
                        }
                        .left-item-des {
                            margin-top: 0.3rem;
                            font-size: 0.6rem;
                            color: #999;
                        }
                    }
                    .right-item {
                    }
                }
            }
        }
    }
}
</style>

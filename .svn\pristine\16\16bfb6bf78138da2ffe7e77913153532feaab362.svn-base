<template>
    <div class="external_link" style="font-size: 22px">
        <div v-if="loading" class="full_loading_spinner van_loading_spinner">
            <van-loading color="#00ad89"/>
        </div>
        <div class="container">
            <div class="external_all_file">
                <template v-for="(file, file_index) of list">
                    <div
                        class="file_item"
                        @click="clickItem(file_index, file)"
                        :key="file.resource_id"
                        :dataId="file.resource_id"
                    >
                        <div class="file_container">
                            <common-image :fileItem="file" :isMini="true"></common-image>
                        </div>
                    </div>
                </template>
            </div>
        </div>
        <gallery></gallery>
        <router-view></router-view>
    </div>
</template>
<script>
import base from "../lib/base";
import gallery from "../components/gallery.vue";
import service from "../service/service";
import { Loading } from 'vant';
export default {
    mixins: [base],
    name: "LoginPage",
    components: {
        gallery,
        VanLoading: Loading
    },
    data() {
        return {
            loading: false,
            list: [],
        };
    },
    computed: {},
    beforeCreate() {},
    created() {},
    mounted() {
        this.$nextTick(() => {
            this.shareId = this.$route.query.shareId;
            this.initData();
        });
    },
    destroyed() {},
    methods: {
        initData() {
            service
                .getResourceByShareId({
                    shareId: this.shareId.replace(/-/g, "\+"),
                })
                .then((res) => {
                    if (res.data.error_code == 0) {
                        this.list = res.data.data;
                    }
                });
        },
        clickItem(file_index) {
            this.$store.commit("gallery/setGallery", {
                list: this.list,
                index: file_index,
            });
            let target = `${this.$route.path}/gallery`;
            target += `?shareId=${encodeURIComponent(this.shareId)}`;
            this.$nextTick(() => {
                this.$router.push(target);
            });
        },
    },
};
</script>
<style lang="scss">
.external_link {
    height: 100%;
    width: 100%;
    .full_loading_spinner {
        position: absolute;
        width: 100%;
        top: 0;
    }

    .van_loading_spinner{
        display: flex;
        justify-content: center;
        align-items: center;

        .van-loading__spinner{
            width: 2.8rem;
            height: 2.8rem;
        }
    }

    .container {
        height: 100%;
        .external_all_file {
            height: 100%;
            position: relative;
            z-index: 1;
            transform: translate3d(0, 0, 0);
            overflow: auto;
            .file_item {
                float: left;
                width: 100%;
                border: 1px solid #fff;
                box-sizing: border-box;
                position: relative;
                width: 33.33%;
                .file_container {
                    position: relative;
                    padding-top: 100%;
                    height: 0;
                    background-color: #333;
                    overflow: hidden;
                }
            }
        }
    }
}
</style>

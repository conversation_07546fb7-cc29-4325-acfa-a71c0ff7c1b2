import cloneDeep from 'lodash/cloneDeep'
import Vue from 'vue'
const initState ={
    allExamList: [],
    hotExamList: [],
    condition: {},
    searchParams:{
        type:'text',//text,url,file
        content:''
    },
    defaultCondition:{
        //良恶性
        'benign_or_malignant': [],
        'bi_rads_feature': {
            //形状
            'shape': [],
            //方向
            'direction':[],
            //边缘
            'edge':[],
            //回声类型
            'echo_type':[],
            //后方回声
            'posterior_echo': [],
            //钙化
            'calcification': [],
        },
        //BI - RADS分类
        'bi_rads_type': [],
        //乳腺癌病理分类
        'pathological_classification_breast_cancer': {
            //非浸润性乳腺癌
            'noninvasive_breast_cancer': [],
            //浸润性乳腺癌
            'invasive_breast_cancer': []
        }
    },
    //lang.case_database_fliter['case_database_fliter']
    allCondition:{
        //良恶性
        'benign_or_malignant': [
            'benign', //'良性'
            'malignant', //'恶性'
        ],
        'bi_rads_feature': {
            //形状
            'shape': [
                'oval',// 椭圆形
                'circular',// 圆形
                'irregular',// 不规则形
            ],
            //方向
            'direction':[
                'parallel',// 与皮肤平行
                'unparallel',// 不平行
            ],
            //边缘
            'edge':[
                'finishing',// 光整
                'vague',// 模糊
                'angled',// 成角
                'microphylation',// 微小分叶
                'hairpin_like',// 毛刺状
            ],
            //回声类型
            'echo_type':[
                'anechoic',// 无回声
                'hypoechoic',// 低回声
                'hyperechoic',// 高回声
                'isoechoic',// 等回声
                'mixed_echo',// 混合回声
                'uneven_echo',// 不均回声
            ],
            //后方回声
            'posterior_echo': [
                'no_change',// 无改变
                'echo_enhancement',// 回声增强
                'acoustic_shadow',// 声影
                'mixed_change',// 混合性改变
            ],
            //钙化
            'calcification': [
                'no_calcification',// 无钙化
                'calcification_in_mass',// 肿块内钙化
                'calcification_out_mass',// 肿块外钙化
                'intraductal_calcification',// 导管内钙化
                'microcalcification',// 微钙化
                'coarse_calcification',// 粗大钙化
            ],
        },
        //BI - RADS分类
        'bi_rads_type': [
            'bi_rads_1',//'1类'
            'bi_rads_2',//'2类'
            'bi_rads_3',//'3类'
            'bi_rads_4a',//'4a类'
            'bi_rads_4b',//'4b类'
            'bi_rads_4c',//'4c类'
            'bi_rads_5',//'5类'
            'bi_rads_6',//'6类'
        ],
        //乳腺癌病理分类
        'pathological_classification_breast_cancer': {
            //非浸润性乳腺癌
            'noninvasive_breast_cancer': [
                'ductal_carcinoma_in_situ',//'导管原位癌'
                'lobular_carcinoma_in_situ',//'小叶原位癌'
            ],
            //浸润性乳腺癌
            'invasive_breast_cancer': [
                'infiltrating_ductal_carcinoma',//'浸润性导管癌'
                'infiltrating_lobular_carcinoma',//'浸润性小叶癌'
                'cephaloma',//'髓样癌'
                'mucinous_carcinoma',//'粘液癌'
                'papillary_carcinoma',//'乳头状癌'
            ]
        }
    },
}

export default {
    state: cloneDeep(initState),
    mutations: {
        resetStore(state){
            if(window.vm.$store.state){
                Vue.set(window.vm.$store.state,'caseDatabase',cloneDeep(initState))
            }
        },
        updateCondition(state, valObj) {
            Vue.set(state, 'condition', valObj)
        },
        updateAllExamList(state, valObj) {
            Vue.set(state, 'allExamList', valObj)
        },
        updateHotExamList(state, valObj) {
            Vue.set(state, 'hotExamList', valObj)
        },
        updateSearchParams(state, valObj) {
            Vue.set(state, 'searchParams', valObj)
        },
    },
    actions: {},
    getters: {},
}

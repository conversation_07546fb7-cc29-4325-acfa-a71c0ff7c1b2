<template>
    <div>
        <CommonDialog
            v-model="visible"
            :title="lang.rename"
            :showRejectButton="true"
            :beforeClose="beforeCloseDialog"
            :get-container="getRootContainer"
        >
            <van-form ref="renameForm" label-width="0">
                <van-field
                    v-model.trim="renameForm.file_name"
                    :placeholder="lang.download_image_name"
                    name="file_name"
                    show-word-limit
                    :rules="[
                        { required: true, message: `${lang.length_limit_of}24` },
                        { validator: validatorFileNameLength, message: `${lang.length_limit_of}24` },
                    ]"
                    @blur.stop
                />
            </van-form>
        </CommonDialog>
    </div>
</template>
<script>
import base from "../lib/base";
import { Form, Field } from "vant";
import { Toast } from 'vant';
import Tool from "@/common/tool";
import CommonDialog from "../MRComponents/commonDialog";
import { getResourceTempStatus } from "../lib/common_base.js";
export default {
    name: "imageRenameDialog",
    mixins: [base],

    model: {
        prop: "value",
        event: "change",
    },
    props: {
        message: {
            type: Object,
            default: () => {
                return {};
            },
        },
        value: {
            type: Boolean,
            default: false,
        },
    },
    components: {
        CommonDialog,
        VanField: Field,
        VanForm: Form,
    },
    watch: {
        value: {
            handler(val) {
                this.visible = val;
                if (val) {
                    let filename= getResourceTempStatus(this.message.resource_id,'custom_file_name')||this.message.custom_file_name||this.message.file_name;
                    let fileType = "";
                    if (filename) {
                        fileType = Tool.getFileType(filename);
                        filename = Tool.getFileName(filename)
                        this.renameForm.file_name = filename;
                        this.renameForm.file_type = fileType;
                    }
                }
            },
            immediate: true,
        },
        visible: {
            handler(val) {
                this.$emit("change", val);
            },
        },
    },
    data() {
        return {
            visible: false,
            renameForm: {
                file_name: "",
                file_type: "",
            },
            isSubmitting: false,
            rules: {},
        };
    },
    created() {},
    methods: {
        clearEditForm() {
            this.renameForm = {
                file_name: "",
                file_type: "",
            };
        },
        validatorFileNameLength(val) {
            if (val.length > 24) {
                return false;
            } else {
                return true;
            }
        },
        getRootContainer() {
            return document.querySelector(".main_container");
        },
        setImageFileName() {
            let custom_file_name = this.renameForm.file_name;
            if(this.renameForm.file_type){
                custom_file_name = `${custom_file_name}.${this.renameForm.file_type}`;
            }
            let params = {
                custom_file_name,
                resource_id: this.message.resource_id,
            };
            return new Promise((resolve, reject) => {
                window.main_screen.conversation_list[this.message.group_id].setImageFileName(params, (res) => {
                    if (res.error_code == 0) {
                        Toast(this.lang.operate_success);
                        resolve(true);
                    } else {
                        Toast(this.lang.operate_err);
                        reject(res.error_msg);
                    }
                });
            });
        },
        async beforeCloseDialog(action, done) {
            if (action === "confirm") {
                try {
                    await this.$refs.renameForm.validate(Object.keys(this.renameForm));
                    await this.setImageFileName();
                    this.clearEditForm();
                    done();
                } catch (error) {
                    done(false);
                }
            } else {
                this.clearEditForm();
                done();
            }
        },
    },
};
</script>
<style lang="scss"></style>

import cloneDeep from 'lodash/cloneDeep'
import Vue from 'vue'
const initState ={
    list:[],
    anonymous:{},
    type: {
        2:'hfr_multicenter',
        3:'thyroid_multicenter',
        4:'obstetric_qc_multicenter',
        5:'myocardial_strain_multicenter',
        1:'generic_multicenter',
    },
    name:{
        'hfr_multicenter': 2,
        'thyroid_multicenter': 3,
        'obstetric_qc_multicenter': 4,
        'myocardial_strain_multicenter': 5,
        'generic_multicenter':1,
    },
    config:{
        2:{
            exam_status:{
                all:-1,
                unsubmit:0,
                saved:1,
                submited:2,
                reject:3,
                assigned:4,
                reviewed:5,
                judgeSubmit:6,
            },
            roleRoute:{
                6:'normal_admin',
                5:'admin/data_view', // 超管页面
                4:'judge', // 仲裁页面
                3:'annotation', //批注页面
                2:'assignment', //分配页面
                1:'normal_admin' //普通者页面
            },
            role:{
                purchaser:6,
                admin:5,
                judge:4,
                review:3,
                assignment:2,
                normal:1
            },
            assignment_type: {
                A: 2,
                B: 3,
                Arbitrate: 4
            },
            search_other_options:{
                all:-1,
                complete_info:0,
                same_review:1,
                complete_info_and_same_review:2,
            },
        },
        3:{
            exam_status:{
                all:-1,
                saved:1,
                submited:2,
                reject:3,
                judgeSubmit:6,
            },
            roleRoute:{
                6:'normal_admin',
                5:'admin/data_view', // 超管页面
                2:'assignment', //分配页面
                1:'normal_admin' //普通者页面
            },
            role:{
                purchaser:6,   //采购者
                admin:5, //超级管理员
                assignment:2,  //审核者
                normal:1 //普通用户
            },
        },
        4:{
            exam_status:{
                all:-1,
                saved:1,
                submited:2,
                reject:3,
                judgeSubmit:6,
            },
            roleRoute:{
                6:'normal_admin',//质控负责人
                5:'admin/data_view', // 超管页面
                1:'admin/data_view', // 质控者
                0:'normal_admin', //从群进入者页面
            },
            role:{
                purchaser:6,   //质控负责人
                admin:5, //超级管理员
                normal:1 //普通用户
            },
        },
        5:{
            exam_status:{
                all:-1,
                saved:1,
                submited:2,
                reject:3,
                judgeSubmit:6,
            },
            roleRoute:{
                6:'normal_admin',
                5:'admin/data_view', // 超管页面
                2:'assignment', //分配页面
                1:'normal_admin' //普通者页面
            },
            role:{
                purchaser:6,   //采购者
                admin:5, //超级管理员
                assignment:2,  //审核者
                normal:1 //普通用户
            },
        },
        1:{
            exam_status:{
                all:-1,
                saved:1,
                submited:2,
                reject:3,
                judgeSubmit:6,
            },
            roleRoute:{
                6:'normal_admin',
                5:'admin/data_view', // 超管页面
                2:'assignment', //分配页面
                1:'normal_admin' //普通者页面
            },
            role:{
                purchaser:6,   //采购者
                admin:5, //超级管理员
                assignment:2,  //审核者
                normal:1 //普通用户
            },
        },
    },
    obstetricEarlyPregnancy:{
        mcOpId:1,
        examType:{
            obstetricExam:2, //表示中孕或晚孕检查模式
            fetalHeartrate:3, //3表示胎心模式；
        },
        colorType:{
            bImage: 1, //常规B图像
            colorImage: 2, //color模式图像
            powerImage: 3, //power模式图像，该参数值为
        },
        viewClass:{
            //0,1,20
            'base_view':0,
            'optional_view':1,
            'non_view':2
        },
    },//早孕 mc options id 
    currentConfig:null,
    currentMulticenter:null,
    optionList:[],//多中心options的列表
    enterByGroup:{
        cid:0,
        fid:0
    },//从群进入中心
    
}

export default {
    state: cloneDeep(initState),
    mutations: {
        resetStore(state){
            if(window.vm.$store.state){
                Vue.set(window.vm.$store.state,'multicenter',cloneDeep(initState))
            }
        },
        setMultiCenterList(state, list) {
            state.list=list;
        },
        setCurrentConfig(state,data){
            state.currentConfig=data;
        },
        setCurrentMulticenter(state,data){
            state.currentMulticenter=data;
        },
        updateMCOptionList(state,data){
            let list = Object.keys(state.optionList)
            if(list.length<1){
                state.optionList = data.reduce((h,v)=>{
                    h[v.id] = v
                    return h
                },{})
            }else{
                for(let i=0;i<(data||[]).length;i++){
                    let item=data[i]
                    if(list[item.id]){
                        state.optionList[item.id] = item
                        break;
                    }
                }
            }
        },
        updateEnterByGroup(state,data){
            state.enterByGroup=data;
        },
        setAnonymous(state,data){
            state.anonymous=data;
        }
    },
    actions: {},
    getters: {},
}

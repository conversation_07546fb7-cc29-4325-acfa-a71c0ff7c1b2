import cloneDeep from 'lodash/cloneDeep'
import Vue from 'vue'
const initState ={
    tv_wall_mode:false,//电视墙模式
    hospitals:[],//所有医院列表,
    token:null,//全局token
    isSafeAuth:false,//是否身份验证过,
    init_main_screen_time:0,
    safeKey:'',
    isWelcomeAnimationShown: false // 欢迎动画是否已显示
}

export default {
    state: cloneDeep(initState),
    mutations: {
        resetStore(state){
            if(window.vm.$store.state){
                Vue.set(window.vm.$store.state,'dynamicGlobalParams',cloneDeep(initState))
            }
        },
        updateDynamicGlobalParams(state, valObj) {
            for (let key in valObj) {
                Vue.set(state,key,valObj[key])
            }
        },
        updateToken(state,valobj) {
            state.token = valobj;
        },
        clearDynamicGlobalParams(state){
            state={
                tv_wall_mode:false,//电视墙模式
                hospitals:[],//所有医院列表,
                token:null,//全局token
                isSafeAuth:false,//是否身份验证过,
                init_main_screen_time:0,
                safeKey:''
            }
        },
        addHospital(state,valObj){
            state.hospitals.push(valObj);
        }
    },
    actions: {},
    getters: {}
}

<template>
    <div class="edit_organization_page third_level_page">
        <mrHeader @click-left="backHandle">
            <template #title>
                    {{lang.organization_name}}
            </template>
        </mrHeader>
        <div class="container">
            <div class="organization_content">
                <mr-organization ref="mrOrganization"></mr-organization>
            </div>

        </div>
    </div>
</template>
<script>
import base from '../lib/base'
import mrOrganization from '../MRComponents/mrOrganization'
export default {
    mixins: [base],
    name: 'edit_organization_page',
    components: {mrOrganization},
    computed:{
    },
    data(){
        return {
        }
    },
    mounted(){
        this.$refs.mrOrganization.searchKey=this.user.organizationName||''
    },
    methods:{
        backHandle(){
            let organizationId=this.$refs.mrOrganization.organizationId;
            let params={}
            if (!organizationId) {
                this.back();
                return ;
            }
            params.organization_id=organizationId;
            params.organizationName=this.$refs.mrOrganization.searchKey;
            window.main_screen.commitUserBasicInfoModify(params,(is_succ,result)=>{
                if (is_succ) {
                    this.$store.commit('user/updateUser',params)
                }
            })
            this.back();
        }
    }
}

</script>
<style lang="scss">
.edit_organization_page{
	.container{
        display: flex;
    }
    .mr_organization{
        height: 100%;
    }
    .organization_content{
        padding: 1rem;
        box-sizing:border-box;
        min-width: 0;
        width: 100%;
        height: 100%;
    }
}
</style>

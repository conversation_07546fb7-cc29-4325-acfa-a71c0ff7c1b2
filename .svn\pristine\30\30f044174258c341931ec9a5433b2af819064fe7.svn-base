<template>
    <div class="dr_data_display">
        <div class="data_header">
            <img class="logo" src="../static/images/logo.png" />
            <p>演示-区域DR影像中心</p>
            <div class="data_operate">
                <p>{{ time }}</p>
                <i class="el-icon-s-tools" @click="openSettingDialog"></i>
                <el-select
                    class="quick_modification_time"
                    size="mini"
                    v-model="quickModificationTime"
                    placeholder="-"
                    @change="handleQuickModificationTimeChange()"
                >
                    <el-option
                        v-for="item in quickModificationTimeList"
                        :key="item.value"
                        :label="item.value"
                        :value="item.param"
                    >
                    </el-option>
                </el-select>
            </div>
        </div>
        <div class="rainbow">
            <div class="red"></div>
            <div class="green"></div>
            <div class="camel"></div>
            <div class="light_blue"></div>
            <div class="purple"></div>
        </div>
        <div class="data_container">
            <div class="left">
                <section class="section1_3" v-loading="loadingLiveData">
                    <div class="count_panel">
                        <div class="count_item">
                            <p class="label">远程会诊及培训时长（分钟）</p>
                            <p class="value">{{ parseInt(liveDataObj.liveDuration/60) }}</p>
                        </div>
                        <div class="count_item">
                            <p class="label">远程会诊及培训场数</p>
                            <p class="value">{{ liveDataObj.liveCount }}</p>
                        </div>
                        <div class="count_item">
                            <p class="label">扫查规范使用次数</p>
                            <p class="value">{{ liveDataObj.iworksUseTimes }}</p>
                        </div>
                        <div class="count_item">
                            <p class="label">扫查规范使用人数</p>
                            <p class="value">{{ liveDataObj.iworksUserCount }}</p>
                        </div>
                    </div>
                </section>
                <section
                    id="draggableBlock1"
                    :class="{ section1_3: true, draggable: true, fake_empty: !statsList.block1 }"
                    draggable="true"
                    @dragstart="handleDragStart"
                    @dragover="handleDragOver"
                    @drop="handleDrop"
                    v-loading="loadingChart.block1"
                >
                    <div class="trend_chart_panel" v-if="statsList.block1">
                        <p class="title">{{ draggablePanelTitleGenerate(statsList.block1) }}</p>
                        <div class="chart_container">
                            <BIBarChart v-if="showTypes === 1" :data="statsList.block1.data" />
                            <BILineChart v-if="showTypes === 0" :data="statsList.block1.data" />
                        </div>
                    </div>
                    <!-- <el-empty v-else description="暂无数据" /> -->
                </section>

                <section
                    id="draggableBlock2"
                    :class="{ section1_3: true, draggable: true, fake_empty: !statsList.block2 }"
                    draggable="true"
                    @dragstart="handleDragStart"
                    @dragover="handleDragOver"
                    @drop="handleDrop"
                    v-loading="loadingChart.block2"
                >
                    <div class="trend_chart_panel" v-if="statsList.block2">
                        <p class="title">{{ draggablePanelTitleGenerate(statsList.block2) }}</p>
                        <div class="chart_container">
                            <BIBarChart v-if="showTypes === 1" :data="statsList.block2.data" />
                            <BILineChart v-if="showTypes === 0" :data="statsList.block2.data" />
                        </div>
                    </div>
                    <!-- <el-empty v-else description="暂无数据" /> -->
                </section>
            </div>
            <div class="center">
                <section class="section2_3">
                    <BIMapChart @getData="getMapData" ref="map_chart"/>
                </section>
                <section
                    id="draggableBlock3"
                    :class="{ section1_3: true, draggable: true, fake_empty: !statsList.block3 }"
                    draggable="true"
                    @dragstart="handleDragStart"
                    @dragover="handleDragOver"
                    @drop="handleDrop"
                    v-loading="loadingChart.block3"
                >
                    <div class="trend_chart_panel" v-if="statsList.block3">
                        <p class="title">{{ draggablePanelTitleGenerate(statsList.block3) }}</p>
                        <div class="chart_container">
                            <BIBarChart v-if="showTypes === 1" :data="statsList.block3.data" />
                            <BILineChart v-if="showTypes === 0" :data="statsList.block3.data" />
                        </div>
                    </div>
                    <!-- <el-empty v-else description="暂无数据" /> -->
                </section>
            </div>
            <div class="right">
                <section class="section2_3" v-loading="loadingPieData">
                    <div class="dr_exam_title">
                        <p>DR影像检查总体分布情况</p>
                    </div>
                    <BIPieChart :data="stats.overallDistribution" @togglePieData="handdleTogglePieData" :totalExamCount="totalExamCount" :totalImageCount="totalImageCount"/>
                </section>
                <section
                    id="draggableBlock4"
                    :class="{ section1_3: true, draggable: true, fake_empty: !statsList.block4 }"
                    draggable="true"
                    @dragstart="handleDragStart"
                    @dragover="handleDragOver"
                    @drop="handleDrop"
                    v-loading="loadingChart.block4"
                >
                    <div class="trend_chart_panel" v-if="statsList.block4">
                        <p class="title">{{ draggablePanelTitleGenerate(statsList.block4) }}</p>
                        <div class="chart_container">
                            <BIBarChart v-if="showTypes === 1" :data="statsList.block4.data" />
                            <BILineChart v-if="showTypes === 0" :data="statsList.block4.data" />
                        </div>
                    </div>
                    <!-- <el-empty v-else description="暂无数据" /> -->
                </section>
            </div>
        </div>
        <el-dialog
            class="setting"
            width="70%"
            :visible.sync="showSettingDialog"
            :modal-append-to-body="true"
            :show-close="false"
            :close-on-press-escape="false"
            :close-on-click-modal="false"
            @open="handleSettingOpen"
        >
            <section slot="title" class="setting_title">
                <span>BI统计看板配置</span>
            </section>
            <section class="setting_container">
                <section class="setting_header">
                    <section class="date_picker_box">
                        <label>统计时间</label>
                        <section class="date_picker_container">
                            <el-button
                                v-for="item in quickModificationTimeList"
                                :key="item.value"
                                type="primary"
                                size="small"
                                @click="adjustPeriod(item.param)"
                                >{{ item.value }}</el-button
                            >
                            <el-date-picker
                                class="date_picker"
                                v-model="basicOptions.period"
                                @change="handelChangeRange"
                                size="small"
                                :clearable="false"
                                type="monthrange"
                                range-separator="-"
                                start-placeholder="开始月份"
                                end-placeholder="结束月份"
                                :default-time="['00:00:00', '23:59:59']"
                            >
                            </el-date-picker>
                        </section>
                    </section>
                    <section class="group_search_box" v-if="isAdmin">
                        <el-autocomplete
                          v-model="subject"
                          clearable
                          :fetch-suggestions="querySearchAsync"
                          placeholder="请输入群名"
                          size="small"
                          @select="handleSelect"
                          @change="handleChange"
                        ></el-autocomplete>
                    </section>
                    <section class="type_selector_box">
                        <label>展示类型</label>
                        <el-select
                            class="type_selector"
                            size="small"
                            v-model="basicOptions.showTypes"
                            placeholder="请选择"
                        >
                            <el-option
                                v-for="item in allShowTypes"
                                :key="item.value"
                                :label="item.value"
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                    </section>
                    <section class="checked_count_box">
                        <span>已选择: </span>
                        <span :class="checkedOptionsCountClassName">
                            {{ checkedOptionsLength }}
                        </span>
                        <span> / {{ maxDetailOptions }}</span>
                    </section>
                </section>
                <section class="setting_content">
                    <section class="left">
                        <section class="setting_content_box">
                            <h2>用户相关</h2>
                            <el-checkbox
                                v-model="detailOptions.userActive"
                                :disabled="isOptionsMaxExceed(detailOptions.userActive)"
                                label="活跃用户"
                            />
                            <el-checkbox
                                v-model="detailOptions.userIncreased"
                                :disabled="isOptionsMaxExceed(detailOptions.userIncreased)"
                                label="新增用户"
                            />
                            <el-checkbox
                                v-model="detailOptions.userTotal"
                                :disabled="isOptionsMaxExceed(detailOptions.userTotal)"
                                label="累计用户"
                            />
                        </section>
                        <section class="setting_content_box">
                            <h2>内容相关</h2>
                            <el-checkbox
                                v-model="detailOptions.examIncreased"
                                :disabled="isOptionsMaxExceed(detailOptions.examIncreased)"
                                label="新增检查数"
                            />
                            <el-checkbox
                                v-model="detailOptions.imageIncreased"
                                :disabled="isOptionsMaxExceed(detailOptions.imageIncreased)"
                                label="新增检查图像数"
                            />
                            <el-checkbox
                                v-model="detailOptions.videoIncreased"
                                :disabled="isOptionsMaxExceed(detailOptions.videoIncreased)"
                                label="新增检查视频数"
                            />
                            <el-checkbox
                                v-model="detailOptions.breastAI"
                                :disabled="isOptionsMaxExceed(detailOptions.breastAI)"
                                label="佳佳老师使用量"
                            />
                            <el-checkbox
                                v-if="isAdmin"
                                v-model="detailOptions.library"
                                :disabled="isOptionsMaxExceed(detailOptions.library)"
                                label="图书馆浏览量"
                            />
                        </section>
                    </section>
                    <section class="center">
                        <section class="setting_content_box">
                            <h2>群/群落相关</h2>
                            <el-checkbox
                                v-model="detailOptions.groupActive"
                                :disabled="isOptionsMaxExceed(detailOptions.groupActive)"
                                label="活跃群数"
                            />
                            <el-checkbox
                                v-model="detailOptions.groupIncreased"
                                :disabled="isOptionsMaxExceed(detailOptions.groupIncreased)"
                                label="新增群数"
                            />
                            <el-checkbox
                                v-if="isAdmin"
                                v-model="detailOptions.groupTotal"
                                :disabled="isOptionsMaxExceed(detailOptions.groupTotal)"
                                label="累计群数"
                            />
                            <el-checkbox
                                v-model="detailOptions.liveCount"
                                :disabled="isOptionsMaxExceed(detailOptions.liveCount)"
                                label="远程会诊及培训场数"
                            />
                            <el-checkbox
                                v-model="detailOptions.liveUserTimes"
                                :disabled="isOptionsMaxExceed(detailOptions.liveUserTimes)"
                                label="远程会诊及培训参与人次"
                            />
                            <el-checkbox
                                v-model="detailOptions.liveDuration"
                                :disabled="isOptionsMaxExceed(detailOptions.liveDuration)"
                                label="远程会诊及培训时长 (分钟)"
                            />
                        </section>
                        <section class="setting_content_box">
                            <h2>iWorks协议</h2>
                            <el-checkbox
                                v-model="detailOptions.iworksUserCount"
                                :disabled="isOptionsMaxExceed(detailOptions.iworksUserCount)"
                                label="扫查规范使用人数"
                            />
                            <el-checkbox
                                v-model="detailOptions.iworksUseTimes"
                                :disabled="isOptionsMaxExceed(detailOptions.iworksUseTimes)"
                                label="扫查规范使用次数"
                            />
                            <!-- <el-checkbox
                                v-model="detailOptions.specCompliance"
                                :disabled="isOptionsMaxExceed(detailOptions.specCompliance)"
                                label="扫查规范遵从度"
                            /> -->
                        </section>
                    </section>
                    <section class="right">
                        <section class="setting_content_box">
                            <h2>设备相关</h2>
                            <el-checkbox
                                v-model="detailOptions.iSyncIncreased"
                                :disabled="isOptionsMaxExceed(detailOptions.iSyncIncreased)"
                                label="新增iSync设备数"
                            />
                            <el-checkbox
                                v-model="detailOptions.iSyncTotal"
                                :disabled="isOptionsMaxExceed(detailOptions.iSyncTotal)"
                                label="累计iSync设备数"
                            />
                            <el-checkbox
                                v-model="detailOptions.ulinkerIncreased"
                                :disabled="isOptionsMaxExceed(detailOptions.ulinkerIncreased)"
                                label="新增u-Linker设备数"
                            />
                            <el-checkbox
                                v-model="detailOptions.ulinkerTotal"
                                :disabled="isOptionsMaxExceed(detailOptions.ulinkerTotal)"
                                label="累计u-Linker设备数"
                            />
                            <el-checkbox
                                v-model="detailOptions.dopplerIncreased"
                                :disabled="isOptionsMaxExceed(detailOptions.dopplerIncreased)"
                                label="新增超声设备数"
                            />
                            <el-checkbox
                                v-model="detailOptions.dopplerTotal"
                                :disabled="isOptionsMaxExceed(detailOptions.dopplerTotal)"
                                label="累计超声设备数"
                            />
                        </section>
                    </section>
                </section>
            </section>
            <section slot="footer" class="setting_footer">
                <el-button @click="handleSettingCancel">取 消</el-button>
                <el-button type="primary" @click="handleSettingConfrim">确 定</el-button>
            </section>
        </el-dialog>
    </div>
</template>
<script>
import _ from "lodash";
import Tool from '../lib/tool.js';
import moment from 'moment';
import request from '../service/service'
import BIBarChart from "../components/BIBarChart";
import BILineChart from "../components/BILineChart";
import BIPieChart from "../components/BIPieChart";
import BIMapChart from "../components/BIMapChart";

export default {
    name: "data_display",
    components: { BIBarChart, BILineChart, BIPieChart,BIMapChart },
    filters: {},
    data() {
        return {
            time: "",
            subject:'',
            onDraggingElement: null,
            showSettingDialog: false,
            isAdmin:false,
            isAdminOrOwner:false,
            pieType:'examTypeMap',
            query:{
                dataFrom:'',
                id:'',
            },
            tempQuery:{
                dataFrom:'', //超管在全局进入云端统计，可以按群筛选
                id:'',
                value:'',
            },
            loadingLiveData:false,
            loadingPieData:false,
            loadingChart:{
                block1:false,
                block2:false,
                block3:false,
                block4:false,
            },
            liveDataObj:{
                iworksUserCount:7586,
                iworksUseTimes:12050,
                liveCount:9577,
                liveDuration:89665689,
            },
            allShowTypes: [
                {
                    id: 0,
                    value: "曲线图",
                },
                {
                    id: 1,
                    value: "柱状图",
                },
            ],
            quickModificationTime: "",
            quickModificationTimeList: [
                {
                    param: "3M",
                    value: "近3月",
                },
                {
                    param: "6M",
                    value: "近6月",
                },
                {
                    param: "1Y",
                    value: "近1年",
                },
                {
                    param: "TY",
                    value: "今年以来",
                },
            ],
            tempQuicktime:'',
            maxDetailOptions: 4,
            checkedOptions: [],
            statsList: {
                block1: null,
                block2: null,
                block3: null,
                block4: null,
            },
            period: [],
            showTypes: 1,
            basicOptions: {
                period: [], // 统计时间 [起(Date), 终(Date)]
                showTypes: 1, // 0 -> 曲线图 | 1 -> 柱状图
            },
            // 判断筛选条件用
            detailOptions: {
                // 用户相关
                userIncreased: false, // 新增用户
                userTotal: false, // 累计用户
                userActive: false, // 活跃用户
                // 群/群落
                groupActive: false, // 活跃群数
                groupIncreased: false, // 新增群数
                groupTotal: false, // 累计群数
                // 直播
                liveCount: true, // 直播场数
                liveUserTimes: true, // 直播参与人数
                liveDuration: false, // 直播时长
                // 设备
                iSyncIncreased: false, // 新增isync 数
                iSyncTotal: false, // 累计isync 数
                ulinkerIncreased: false, // 新增灵珂数
                ulinkerTotal: false, // 累计灵珂数
                dopplerIncreased: false, // 新增超声数
                dopplerTotal: false, // 累计超声数
                // 内容
                examIncreased: true, // 新增检查数
                imageIncreased: true, // 新增图像数
                videoIncreased: false, // 新增视频数
                breastAI: false, // 佳佳老师
                library: false, // 图书馆
                // iWorks协议（暂时定义）
                iworksUserCount: false, // 扫查规范使用人数
                iworksUseTimes: false, // 扫查规范使用次数
                // specCompliance: false, // 扫查规范遵从度
            },
            totalExamCount:0,
            totalImageCount:0,
            // 真实数据
            stats: {
                // 超声检查总体分布情况
                overallDistribution: [
                    { value: 0, fixedName: "头颅" ,key:1 },
                    { value: 0, fixedName: "骨盆" ,key:2},
                    { value: 0, fixedName: "脊柱" ,key:3},
                    { value: 0, fixedName: "胸部" ,key:4},
                    { value: 0, fixedName: "上肢" ,key:5},
                    { value: 0, fixedName: "下肢" ,key:6},
                ],
                // 用户相关
                userIncreased: { title: "新增用户", value: 0, data: undefined }, // 新增用户
                userTotal: { title: "累计用户", value: 0, data: undefined, hiddenCount:true }, // 累计用户
                userActive: { title: "活跃用户", value: 0, data: undefined }, // 活跃用户
                // 群/群落
                groupActive: { title: "活跃群数", value: 0, data: undefined }, // 活跃群数
                groupIncreased: { title: "新增群数", value: 0, data: undefined }, // 新增群数
                groupTotal: { title: "累计群数", value: 0, data: undefined, hiddenCount:true }, // 累计群数
                // 直播
                liveCount: { title: "远程会诊及培训场数", value: 0, data: undefined }, // 直播场数
                liveUserTimes: { title: "远程会诊及培训参与人次", value: 0, data: undefined }, // 直播参与人数
                liveDuration: { title: "远程会诊及培训时长(分钟)", value: 0, data: undefined }, // 直播时长
                // 设备
                iSyncIncreased: { title: "新增iSync设备数", value: 0, data: undefined }, // 新增isync 数
                iSyncTotal: { title: "累计iSync设备数", value: 0, data: undefined, hiddenCount:true }, // 累计isync 数
                ulinkerIncreased: { title: "新增u-Linker设备数", value: 0, data: undefined }, // 新增灵珂数
                ulinkerTotal: { title: "累计u-Linker设备数", value: 0, data: undefined, hiddenCount:true }, // 累计灵珂数
                dopplerIncreased: { title: "新增超声设备数", value: 0, data: undefined }, // 新增超声数
                dopplerTotal: { title: "累计超声设备数", value: 0, data: undefined, hiddenCount:true }, // 累计超声数
                // 内容
                examIncreased: { title: "新增检查数", value: 0, data: undefined }, // 新增检查数
                imageIncreased: { title: "新增检查图像数", value: 0, data: undefined }, // 新增图像数
                videoIncreased: { title: "新增检查视频数", value: 0, data: undefined }, // 新增视频数
                breastAI: { title: "佳佳老师使用量", value: 0, data: undefined }, // 佳佳老师
                library: { title: "图书馆浏览量", value: 0, data: undefined }, // 图书馆
                // iWorks协议（暂时定义）
                iworksUserCount: { title: "扫查规范使用人数", value: 0, data: undefined }, // 扫查规范使用人数
                iworksUseTimes: { title: "扫查规范使用次数", value: 0, data: undefined }, // 扫查规范使用次数
                //specCompliance: { title: "扫查规范遵从度", value: 0, data: undefined }, // 扫查规范遵从度
            },
        };
    },
    computed: {
        checkedOptionsLength() {
            let count = 0;
            for (const key in this.detailOptions) {
                if (this.detailOptions.hasOwnProperty(key) && this.detailOptions[key] === true) {
                    count++;
                }
            }
            return count;
        },
        checkedOptionsCountClassName() {
            return this.checkedOptionsLength >= this.maxDetailOptions ? "warning_count" : "count";
        },
        isOptionsMaxExceed() {
            return (self) => this.checkedOptionsLength >= this.maxDetailOptions && !self;
        },
    },
    watch: {
    },
    async created() {
        this.query = JSON.parse(window.localStorage.getItem('stat_query'))
        if(this.query.dataFrom === 'global') {
            this.isAdmin = true;
        }else{
            this.stats.library = null;
            this.stats.groupTotal = null;
        }
    },
    mounted() {
        //默认获取最近6月数据
        this.quickModificationTime = '6M';
        this.getTime();
        //初始化本地存储
        this.storageInitialize();
        this.$nextTick(() => {
            this.statsListInitialize();
            this.handleQuickModificationTimeChange();
        });
    },
    methods: {
        // 一般业务逻辑
        getTime() {
            const date = new Date();
            this.time = `${moment().format('YYYY/MM/DD HH:mm:ss z')} ${this.getWeek()}`;
            setInterval(() => {
                const date = new Date();
                this.time = `${moment().format('YYYY/MM/DD HH:mm:ss z')} ${this.getWeek()}`;
            }, 1000);
        },
        getWeek() {
            return `星期${"日一二三四五六".charAt(new Date().getDay())}`;
        },
        deepMerge(target, source) {
            const merged = { ...target };

            for (const key in source) {
                if (source.hasOwnProperty(key)) {
                    if (source[key] instanceof Object && key in target) {
                        // 如果当前属性是对象，并且在目标对象中已存在，递归合并
                        merged[key] = this.deepMerge(target[key], source[key]);
                    } else {
                        // 否则直接赋值
                        merged[key] = source[key];
                    }
                }
            }

            return merged;
        },
        adjustPeriod(opt) {
            const end = new Date();
            end.setHours(23, 59, 59, 0);
            let start = new Date();
            const [param1, param2] = opt.split('');
            this.tempQuicktime = opt;
            try {
                switch (opt) {
                case "TY": {
                    start.setUTCHours(0, 0, 0, 0);
                    start.setUTCMonth(0);
                    start.setUTCDate(1);
                    break;
                }
                case '3M': {
                    start = this.getDateOneOfMonth(3)
                    break;
                }
                case '6M': {
                    start = this.getDateOneOfMonth(6)
                    break;
                }
                case '1Y': {
                    start = this.getDateOneOfMonth(12)
                    break;
                }
                default: {
                    throw `Option error with an unknown parameter ${opt}`;
                }
                }
                // start.setHours(0, 0, 0, 0);
                this.basicOptions.period = [start, end];
            } catch (e) {
                console.error("An error occured. Details:", e);
                this.$message.error("错误日期参数，设置失败");
                return;
            }
        },
        // 获取几个月前的1号0点时间
        getDateOneOfMonth(num) {
            const time = moment().set('date', 1).subtract(num-1, 'month');
            return new Date(new Date(time).setHours(0,0,0,0));
        },
        adjustQuickModificationTime() {
            this.quickModificationTime = this.tempQuicktime;
        },
        handelChangeRange(value,value2){
            this.tempQuicktime = '';
            const startDate = new Date(this.basicOptions.period[0]);
            const endDate = new Date(this.basicOptions.period[1]);
            if (endDate.valueOf()-startDate.valueOf()>= 365*24*60*60*1000) {
                this.$message.error('日期范围不能超过1年')
                this.basicOptions.period = _.cloneDeep(this.period);
            }
        },
        storageInitialize() {
            if (localStorage.getItem("biShowTypesDRDemo")) {
                try {
                    const val = parseInt(localStorage.getItem("biShowTypesDRDemo"), 10);
                    if (Number.isNaN(val)) {
                        throw "Wrong option type of show types, expect Number but get NaN.";
                    }
                    this.showTypes = val;
                } catch (e) {
                    console.error("An error occured while watching show type options being modified. Detail: ", e);
                    localStorage.removeItem("biShowTypesDRDemo");
                    this.$message.error("缓存中图表类型有错误数据，已将对应数据初始化，请至配置项重新设置");
                    this.storageInitialize();
                }
            } else {
                localStorage.setItem("biShowTypesDRDemo", this.showTypes);
            }
            if (localStorage.getItem("biDetailOptionsDRDemo")) {
                try {
                    this.detailOptions = JSON.parse(localStorage.getItem("biDetailOptionsDRDemo"));
                } catch (e) {
                    console.error("An error occured while watching detail options being modified. Detail: ", e);
                    localStorage.removeItem("biDetailOptionsDRDemo");
                    this.$message.error("缓存中图表数据有错误数据，已将对应数据初始化，请至配置项重新设置");
                    this.storageInitialize();
                }
            } else {
                localStorage.setItem("biDetailOptionsDRDemo", JSON.stringify(this.detailOptions));
            }
        },
        statsListInitialize() {
            this.statsList = {
                block1: null,
                block2: null,
                block3: null,
                block4: null,
            };
            let blockSort = localStorage.getItem("biBlockSortDRDemo");
            if (blockSort) {
                blockSort = JSON.parse(blockSort)
                //本地缓存了顺序
                const checkedOptions = [];
                for (const block in blockSort) {
                    const key =blockSort[block]
                    if (this.stats[key]) {
                        // 非超管会隐藏部分条目
                        checkedOptions.push(key);
                        this.statsList[block] = {
                            ...this.stats[key],
                            source:key
                        };
                    }
                }
                this.checkedOptions = checkedOptions;
            }else{
                const checkedOptions = [];
                for (const key in this.detailOptions) {
                    if (this.detailOptions.hasOwnProperty(key) && this.detailOptions[key] === true) {
                        checkedOptions.push(key);
                    }
                }
                this.checkedOptions = checkedOptions;
                for (let i = 0; i < this.checkedOptions.length; i++) {
                    this.statsList[`block${i + 1}`] = {
                        ...this.stats[this.checkedOptions[i]],
                        source:this.checkedOptions[i]
                    };
                }
            }
        },
        draggablePanelTitleGenerate(item) {
            let title = void 0;
            let value = NaN;
            let data = null;
            if (item) {
                title = item.title ? item.title : "";
                value = item.value || item.value === 0 ? item.value : NaN;
                data = item.data ? item.data : null;
            }
            return title ? `${title} ${item.hiddenCount?'':`(${value})`}` : "";
        },

        // api请求

        // dialog操作
        openSettingDialog() {
            this.showSettingDialog = true;
        },
        closeSettingDialog() {
            this.showSettingDialog = false;
        },

        // handlers
        handleSettingOpen() {
            try {
                this.basicOptions = {
                    period: this.period,
                    showTypes: parseInt(localStorage.getItem("biShowTypesDRDemo"), 10),
                };
                this.detailOptions = JSON.parse(localStorage.getItem("biDetailOptionsDRDemo"));
                this.tempQuery = this.query;
                this.tempQuicktime = this.quickModificationTime
                this.subject = this.tempQuery.value || ''
                if (!this.isAdmin) {
                    this.detailOptions.groupTotal = false;
                    this.detailOptions.library = false;
                }
            } catch (e) {
                console.error("An error has occured while setting caches for settings. Details:", e);
                // localStorage.setItem("biPeriod", JSON.stringify(this.period));
                localStorage.setItem("biShowTypesDRDemo", JSON.stringify(this.showTypes));
                localStorage.setItem("biDetailOptionsDRDemo", JSON.stringify(this.detailOptions));
                this.$message.error("读取配置缓存失败，上次设置内容已丢失");
            }
        },
        handleSettingCancel() {
            this.closeSettingDialog();
        },
        handleSettingConfrim() {
            try {
                this.period = _.cloneDeep(this.basicOptions.period);
                this.showTypes = _.cloneDeep(this.basicOptions.showTypes);
                this.adjustQuickModificationTime(this.period);
                this.query = this.tempQuery;
                localStorage.setItem("biShowTypesDRDemo", this.basicOptions.showTypes);
                localStorage.setItem("biDetailOptionsDRDemo", JSON.stringify(this.detailOptions));
                localStorage.setItem("biBlockSortDRDemo", '');
            } catch (e) {
                console.error("An error has occured while setting cache for settings. Details:", e);
                this.$message.error("设置配置缓存失败，本次设置部分内容将不会在缓存中出现");
            } finally {
                this.statsListInitialize();
                this.closeSettingDialog();
                this.renderAll();
            }
        },
        handleDragStart(e) {
            this.onDraggingElement = e.currentTarget;
        },
        handleDragOver(e) {
            e.preventDefault();
        },
        handleDrop(e) {
            let dropElement = e.currentTarget;
            if (this.onDraggingElement != null && this.onDraggingElement != dropElement) {
                let dropId = dropElement.id.slice(-6).toLowerCase();
                let dragId = this.onDraggingElement.id.slice(-6).toLowerCase();
                let temp = this.statsList[dropId];
                this.statsList[dropId] = this.statsList[dragId];
                this.statsList[dragId] = temp;
            }
            //todo
            const statsList = {}
            for(let key in this.statsList){
                if (this.statsList[key]) {
                    statsList[key] = this.statsList[key].source;
                }
            }
            localStorage.setItem("biBlockSortDRDemo", JSON.stringify(statsList));
        },
        handleQuickModificationTimeChange() {
            if (this.quickModificationTime !== "") {
                this.adjustPeriod(this.quickModificationTime);
                this.period = _.cloneDeep(this.basicOptions.period);
            }
            this.renderAll();
        },
        renderAll(){
            // this.renderLiveData();
            this.renderChartData();
            this.renderPieData();
            this.renderMapData();
        },
        async renderLiveData(){
            this.loadingLiveData = true;
            const result = await request.getLiveAndIworksData({
                startTime:new Date(this.period[0]).valueOf(),
                endTime:new Date(this.period[1]).valueOf(),
                dataFrom:this.query.dataFrom,
                id:this.query.id
            })
            this.liveDataObj = result.data.data;
            this.loadingLiveData = false;
        },
        renderChartData(){
            for(let key in this.statsList){
                if (this.statsList[key]) {
                    this.renderChart(key);
                }
            }
        },
        renderChart(blockName){
            this.loadingChart[blockName] = true;
            const block = this.statsList[blockName];
            this.loadingChart[blockName] = false;
            let data = {
                xAxis: {
                    data: [],
                },
                yAxis: {},
                series: [
                    {
                        data: [],
                    },
                ],
            };
            let value = 0;
            let times = 1;//倍数，liveDuration是分钟要除以60
            if (block.source === 'liveDuration') {
                times = 60;
            }
            data.xAxis.data = Tool.buildHorizontalAxis(this.period[0],this.period[1]);
            for (let item of data.xAxis.data) {
                let randomData = parseInt(Math.random()*1000/times)
                value += randomData
                if (block.hiddenCount) {
                    data.series[0].data.push(value);
                }else{
                    data.series[0].data.push(randomData);
                }


            }
            block.data = data;
            block.value = value;
        },
        renderPieData(){
            this.loadingPieData = true;
            const result = {
                data:{
                    error_code:0,
                    data:{}
                }
            }
            this.loadingPieData = false;
            let data = result.data.data;
            result.data.data={
                "totalImageCount": 12718,
                "totalExamCount": 703,
                "totalVideoCount": 0,
                "examTypeMap": {
                    "0": 322,
                    "1": 225,
                    "2": 12,
                    "3": 142,
                    "4": 43,
                    "5": 192,
                    "6": 89,
                },
                "imageTypeMap": {
                    "0": 6899,
                    "1": 4552,
                    "2": 195,
                    "3": 2433,
                    "4": 963,
                    "5": 4210,
                    "6": 365,
                }
            }
            this.stats.overallDistribution
            this.tempPieData = result;
            this.setPieData();
        },
        handdleTogglePieData(type){
            this.pieType = type;
            this.setPieData();
        },
        setPieData(){
            const result = this.tempPieData;
            const typeArr = [1,2,3,4,5,6];
            for(let item of this.stats.overallDistribution){
                item.value = 0;
            }
            const map = result.data.data[this.pieType]
            for(let key in map){
                if (typeArr.indexOf(parseInt(key))>-1) {
                    for(let item of this.stats.overallDistribution){
                        if(item.key == key){
                            item.value = map[key];
                        }
                    }
                }else{
                    // this.stats.overallDistribution[8].value +=map[key];
                }
            }
            this.totalExamCount = result.data.data.totalExamCount;
            this.totalImageCount = result.data.data.totalImageCount + result.data.data.totalVideoCount;
        },
        async getMapData(data){
            if (data.regionCode === 'china') {
                data.regionCode = 86
            }
            const result = await request.getMapData({
                startTime:new Date(this.period[0]).valueOf(),
                endTime:new Date(this.period[1]).valueOf(),
                dataFrom:this.query.dataFrom,
                id:this.query.id,
                regionCode:data.regionCode,
                regionType:data.regionType
            })
            for(let key in result.data.data){
                let randomHasData = Math.random()>0.3?true:false;
                if (['110000','330000','350000','350400'].indexOf(key)>-1) {
                    // 北京，浙江，福建，福建三明必须有数据
                    randomHasData=true;
                }
                if (['710000'].indexOf(key)>-1) {
                    // 台湾不显示
                    randomHasData=false;
                }
                if (randomHasData) {
                    result.data.data[key]['2'] = parseInt(Math.random()*200)
                    result.data.data[key]['6'] = parseInt(Math.random()*200)
                    result.data.data[key]['9'] = parseInt(Math.random()*200)
                }
                console.log('key',key,result.data.data[key])
            }
            data.callback(result.data);
        },
        renderMapData(){
            this.$refs['map_chart'].renderMap();
        },
        async querySearchAsync(queryStr,cb){
            const arr=[]
            if (this.subject) {
                const result = await request.getSubjectData({
                    subject:this.subject
                })
                for(let item of result.data.data){
                    arr.push({
                        value:`${item.id}:${item.subject}(${item.adminInfo.nickname})`,
                        id:item.id,
                    })
                }
            }
            cb(arr)
        },
        handleSelect(item){
            this.tempQuery = {
                dataFrom: 'group',
                id:item.id,
                value:this.subject
            }
        },
        handleChange(value){
            if (!value) {
                this.tempQuery = {
                    dataFrom: 'global',
                    id:-1,
                    value:''
                }
            }
        }
    },
};
</script>
<style lang="scss" scoped>
$boxGap: 1em !default; // 每个盒子的间距
$columnGap: 1.3em !default; // 列间距
$rainbowHeight: 2px !default; // 彩虹盒子的高度
$svhOffset: 40px !default; // 针对于 100svh 去除 header 的偏移量

$value1_3: 100% / 3 !default; // 列 1/3 的数量值
$value2_3: calc($value1_3 * 2 + $columnGap) !default; // 列 2/3 的数量值，不同单位的运算需用 calc 原生css函数计算
$value3_3: 100% !default; // 列 3/3 的数量值
$rowValue1_3: calc(
    100vh / 3 - $svhOffset - $rainbowHeight
) !default; // 行 1/3 的数量值，用的 100svh 作为固定数值进行计算，使用 100% 进行计算将会因为百分比参照不同等繁琐的原因产生误差
$rowValue2_3: calc($rowValue1_3 * 2 + $boxGap) !default; // 行 2/3 的数量值
$rowValue3_3: 100% !default; // 行 3/3 的数量值

$optionsCheckedColor: #ffc062 !default; // 筛选条件中选中后的字体色及已选择的计数数字色
$warningColor: red !default; // 警告色
$optionsGeneralButtonColor: #169bd5 !default; // 筛选条件中一般按钮背景色
$optionsGeneralButtonHoverColor: rgb(93, 186, 226) !default; // 筛选条件中一般按钮hover/focus背景色
$optionsHollowButtonColor: #fff !default; // 筛选条件中镂空按钮背景色
$optionsHollowButtonHoverColor: #ecf5ff !default; // 筛选条件中镂空按钮hover/focus背景色
$basicBoxColor: #202938 !default; // 基础盒背景色
$darkgray: #666 !default; // 深灰（边框）
$lightgray: #c2c2c2 !default; // 淡灰（边框）

@mixin under-border($color: black, $size: 1px, $style: solid) {
    border-bottom: $style $size $color;
}

@mixin flex-between($flexWrap: nowrap) {
    display: flex;
    justify-content: space-between;
    flex-wrap: $flexWrap;
}

@mixin box-basic($occupy: 200px, $borderTop: none, $borderBottom: none, $borderLeft: none, $borderRight: none) {
    height: $occupy;
    padding: 1rem;
    background-color: $basicBoxColor;
    border-radius: 1rem;
    border-top: $borderTop;
    border-bottom: $borderBottom;
    border-left: $borderLeft;
    border-right: $borderRight;
    overflow: auto;
    box-sizing: border-box;
    .el-empty {
        padding: 0;
        :deep(.el-empty__description p) {
            margin: 0;
            font-size: 0.8rem;
            color: #fff;
        }
    }
}

@mixin rainbow-basic($backgroundColor, $flexBasis: 0%, $flexGrow: 1, $flexShrink: 1) {
    height: 100%;
    flex-grow: $flexGrow;
    flex-shrink: $flexShrink;
    flex-basis: $flexBasis;
    background-color: $backgroundColor;
}

.rainbow {
    display: flex;
    height: $rainbowHeight;
    .red {
        @include rainbow-basic(#91696d);
    }
    .green {
        @include rainbow-basic(#59857a);
    }
    .camel {
        @include rainbow-basic(#b19f8e, 7.5%);
    }
    .light_blue {
        @include rainbow-basic(#819aab, 11%);
    }
    .purple {
        @include rainbow-basic(#727493, 11%);
    }
}

.draggable {
    cursor: move;
}

.dr_data_display {
    height: 100%;
    background: #000;
    color: #fff;
    display: flex;
    flex-direction: column;
    user-select: none;
    .data_header {
        display: flex;
        justify-content: center;
        flex-wrap: nowrap;
        height: 60px;
        width: 100%;
        align-items: center;
        vertical-align: middle;
        position: relative;
        p {
            font-size: 1.4rem;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
        .logo{
            position: absolute;
            left: 0;
        }
        .data_operate {
            @include flex-between;
            align-items: center;
            right: 1rem;
            position: absolute;
            p {
                display: inline-block;
                font-size: 0.8rem;
            }
            i {
                margin-left: 1rem;
                display: inline-block;
                font-size: 1.5rem;
                cursor: pointer;
            }
        }
        .quick_modification_time {
            width: 10rem;
            margin-left: 1rem;
        }
    }

    .data_container {
        @include flex-between;
        flex: 1;
        padding: 1rem;
        gap: $columnGap;
        .left,
        .center,
        .right {
            display: flex;
            flex-direction: column;
            gap: $boxGap;
            height: $value3_3;
            flex:3;
        }
        .center{
            flex:4;
        }
        .section1_3 {
            // 1/3 区块
            @include box-basic($rowValue1_3);
            .count_panel {
                .count_item {
                    @include under-border($darkgray);
                    display: flex;
                    padding: 18px 8px 12px 0;
                    font-size: 1.2rem;
                    .label {
                        flex: 1;
                    }
                    .value {
                    }
                }
            }
            .trend_chart_panel {
                display: flex;
                flex-direction: column;
                height: 100%;
                .title {
                    @include under-border($darkgray);
                    font-size: 1rem;
                    font-weight: 600;
                    padding: 6px 0;
                    text-align: left;
                    color:#fff;
                }
                .chart_container {
                    flex: 1;
                }
            }
        }
        .section2_3 {
            // 2/3 区块
            @include box-basic($rowValue2_3);
            position: relative;
            .dr_exam_title{
                display: flex;
                justify-content: center;
                position: absolute;
                width: calc(100% - 2rem);
                &::after{
                    content: '';
                    display: block;
                    border-bottom: 1px solid #aaa;
                    width: 100%;
                    position: absolute;
                    top: 50%;
                    z-index: 1;
                }
                &>p{
                    color: #fff;
                    font-size: 18px;
                    text-align: center;
                    padding: 10px 40px;
                    border-radius: 8px;
                    border:2px solid #366;
                    z-index: 2;
                    background: #232937;
                }
            }
            .exam_title,.count_panel{
                display: none;
            }
        }
        .section3_3 {
            // 3/3 区块
            @include box-basic($rowValue3_3);
        }
    }
}
.setting {
    :deep(.el-dialog__header) {
        padding-bottom: 0;
    }
    :deep(.el-dialog__body) {
        padding-top: 0;
    }
    :deep(.el-button--primary) {
        background-color: $optionsGeneralButtonColor;
        &:hover,
        &:focus {
            background-color: $optionsGeneralButtonHoverColor;
        }
    }
    :deep(.el-button--default) {
        background-color: $optionsHollowButtonColor;
        &:hover,
        &:focus {
            background-color: $optionsHollowButtonHoverColor;
        }
    }
    .setting_title {
        @include under-border($darkgray);
        width: 100%;
        text-align: center;
        padding-bottom: 1rem;
        color: black;
        font-size: 1.2rem;
        font-weight: 600;
    }
    .setting_container {
        display: flex;
        flex-direction: column;
        .setting_header {
            @include flex-between(wrap);
            @include under-border($lightgray);
            align-items: center;
            width: 100%;
            min-height: 60px;
            margin-bottom: 1rem;
            padding: 1rem 0;

            * {
                margin-top: 2px;
                margin-bottom: 2px;
            }
            .count {
                color: $optionsCheckedColor;
            }
            .warning_count {
                color: $warningColor;
            }
            .date_picker_box {
                @include flex-between(wrap);
                align-items: center;
            }
            .date_picker_container > :nth-child(4),
            label {
                margin-right: 1rem;
            }
            :deep(.el-date-editor--daterange.el-input__inner) {
                width: 14rem;
            }
        }
        .setting_content {
            @include flex-between(wrap);
            padding: 0 2rem;
            :deep(.el-checkbox__input.is-checked + .el-checkbox__label) {
                color: $optionsCheckedColor;
            }
            .warning_count {
                color: $warningColor;
            }
            .setting_content_box {
                h2 {
                    @include under-border($darkgray);
                    text-align: center;
                    padding-bottom: 0.2rem;
                    margin-bottom: 0.8rem;
                }
                .el-checkbox {
                    display: block;
                    margin-bottom: 1rem;
                }
            }
        }
    }
    .setting_footer {
        width: 100%;
        display: flex;
        justify-content: space-around;
        .el-button {
            width: 12rem;
        }
    }
}
.fake_empty {
    background-color: #000 !important;
}
</style>

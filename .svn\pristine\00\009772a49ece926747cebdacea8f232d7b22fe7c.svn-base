import api from './api'

const service={
    exportExcel(data){
        // return v2InterfaceFactory('multicenter.exam.export.excel',data)
        // 需要加上responseType: 'blob'这个字段
        return api
            .post('/v2/api',{
                data:{
                    method:'statistic.export.excel',
                    bizContent:data,  
                },
            },{
                responseType: 'blob',
            })
            .then(res=>{
                return res
            },(error)=>{
                console.log('error',error)
                return error
            })
    },
}
export default service
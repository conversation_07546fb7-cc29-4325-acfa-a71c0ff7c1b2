/*
html5doctor.com Reset Stylesheet
v1.4.1
2010-03-01
Author: <PERSON> - http://richclarkdesign.com
*/

html,body{
    height: 100%;
    user-select: text;
    -webkit-user-select:text;
    background: #222;
    position: relative;
}

body{
    position: absolute;
    width: 100%;
    bottom:0;
}
html, body, div, span, object, iframe,
h1, h2, h3, h4, h5, h6, p, blockquote, pre,
abbr, address, cite, code,
del, dfn, em, img, ins, kbd, q, samp,
small, strong, sub, sup, var,
b, i,
dl, dt, dd, ol, ul, li,
fieldset, form, label, legend,
table, caption, tbody, tfoot, thead, tr, th, td,
article, aside, canvas, details, figcaption, figure,
footer, header, hgroup, menu, nav, section, summary,
time, mark, audio, video {
    margin:0;
    padding:0;
    border:0;
    outline:0;
    vertical-align:baseline;
    background:transparent;
}
body {
    line-height:1.4;
}
:focus {
	outline: none;
}
article,aside,canvas,details,figcaption,figure,
footer,header,hgroup,menu,nav,section,summary {
    display:block;
}
nav ul {
    list-style:none;
}
blockquote, q {
    quotes:none;
}
blockquote:before, blockquote:after,
q:before, q:after {
    content:'';
    content:none;
}
a {
    margin:0;
    padding:0;
    border:0;
    text-decoration: none;
    font-size:100%;
    vertical-align:baseline;
    background:transparent;
}
ins {
    background-color:#ff9;
    color:#000;
    text-decoration:none;
}
mark {
    background-color:#ff9;
    color:#000;
    font-style:italic;
    font-weight:bold;
}
del {
    text-decoration: line-through;
}
abbr[title], dfn[title] {
    border-bottom:1px dotted #000;
    cursor:help;
}
table {
    border-collapse:collapse;
    border-spacing:0;
}
hr {
    display:block;
    height:1px;
    border:0;
    border-top:1px solid #cccccc;
    margin:1em 0;
    padding:0;
}
input, select {
    vertical-align:middle;
}
input, textarea {
    caret-color: rgb(0, 197, 157);
}
/* rem init*/
/* @media only screen and (min-width: 320px){
    html,body{
        font-size: 16px !important;
    }
} */
@media only screen and (min-width: 375px){
    html,body{
        /* font-size: 20px !important; */
    }
}
@media only screen and (min-width: 640px){
    html,body{
        /* font-size: 30px !important; */
    }
}
@media only screen and (min-width: 750px){
    html,body{
        /* font-size: 40px !important; */
    }
}
@media only screen and (min-width: 1242px){
    html,body{
        /* font-size: 48px !important; */
        margin: 0 auto;
        border: 1px solid #eee;
        box-sizing: border-box;
        position: relative;
    }
}

/*base class*/
.fl{
    float: left;
}
.fr{
    float: right;
}
.clearfix:after{
    content: '';
    display: block;
    height: 0;
    clear: both;
}

.commont_input{
    width: 100%;
    border: none;
    font-size: 0.8rem;
    padding: .4rem 0;
    border-bottom: 1px solid #c8c7cc;
    margin: .5rem 0;
    display: block;
    color: #666;
    line-height: 1.2rem;
}
input:-webkit-autofill{
    box-shadow: 0 0 0 1000px white inset;
}
/* reset mint primary-color*/
.mint-msgbox-confirm{
    color: #00c59d;
}
.second_level_page,.third_level_page,.fourth_level_page,.fifth_level_page{
    transform:translateZ(0);
    /*box-shadow:-4px 0px 5px 2px rgba(216, 216, 216, 0.8);*/
    position:absolute;
    background-color:#fff;
    top:0;
    left:0;
    bottom:0;
    width:100%;
    height:100%;
    z-index:10;
    overflow: hidden;
    opacity: 1; /* 移动端动画加速*/
    user-select: none;
    -webkit-user-select:none;
    display: flex;
    flex-direction: column;
}
.second_level_page .second_level_content{
    height: calc(100% - 2.95rem);
    overflow: auto;
    box-sizing: border-box;
}
.third_level_page{
    z-index:100;
}
.fourth_level_page{
    z-index:200;
}
.fifth_level_page{
    z-index:300;
}
.slide-enter-active{
    animation:slide-in .2s;
    animation-timing-function:linear;
}
.slide-leave-active{
    animation:slide-in .2s reverse;
    animation-timing-function:linear;
}
.fade-enter-active{
    animation:fade-in .1s;
    animation-timing-function:linear;
}
.fade-leave-active{
    animation:fade-in .1s reverse;
    animation-timing-function:linear;
}
@keyframes slide-in{
    0%{
        transform: translateX(100%);
    }
    25%{
        transform: translateX(80%);
    }
    50%{
        transform: translateX(50%);
    }
    75%{
        transform: translateX(10%);
    }
    100%{
        transform: translateX(0);
    }
}
@keyframes fade-in{
    0%{
        opacity: 0;
    }
    100%{
        opacity: 1;
    }
}
.setting_list_group{
    margin: 0.8rem 0;
    background-color: #fff;
}
.setting_list_item{
    display: block;
    background-color: #fff;
    padding: .5rem .6rem;
    color: #444;
    margin: 0 0.6rem;
    border-bottom: 1px solid #eee;
    font-size:.8rem;
}
.setting_list_group .setting_list_item:last-child{
    border: none;
}
img.avatar{
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
}
img.offline{
    filter: grayscale(100%);
    -webkit-filter: grayscale(100%);
    -webkit-transform:translateZ(0);
}
/*.offline:before{
    display: block;
    position: absolute;
    content:'';
    width: 100%;
    height: 100%;
}*/
.full_loading_spinner,.full_img_loading_spinner{
    height: 100%;
    position: absolute;
    width: 100%;
    z-index: 9;
}
.full_loading_spinner > span,.full_img_loading_spinner>span{
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%);
    -webkit-transform: translate(-50%,-50%);
}
.full_loading_spinner > span >div{
    width: 2rem !important;
    height: 2rem !important;
}
.full_img_loading_spinner > span >div{
    width: 100px !important;
    height: 100px !important;
}
.full_img_loading_spinner>span>img{
    width: 80px;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%,-50%);
}

.rainbow_row > div{
    height:2px;
    width:20%;
}
.rainbow_row .block1{background-color:#FF6759;}
.rainbow_row .block2{background-color:#02C49E;}
.rainbow_row .block3{background-color:#FFB144;}
.rainbow_row .block4{background-color:#56C7FD;}
.rainbow_row .block5{background-color:#737FDE;}

/* mui-slider mui-zoom*/
.mui-slider {
    position: relative;
    z-index: 1;
    overflow: hidden;
    width: 100%;
}
.mui-slider .mui-slider-group {
    font-size: 0;
    position: relative;
    -webkit-transition: all 0s linear;
    transition: all 0s linear;
    white-space: nowrap;
}
.mui-slider .mui-slider-group .mui-slider-item {
    font-size: 14px;
    position: relative;
    display: inline-block;
    width: 100%;
    height: 100%;
    vertical-align: top;
    white-space: normal;
}
.longwrap{
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}
.common_btn{
    display: block;
    width: 100%;
    border: none;
    font-size: 0.8rem;
    line-height: 2rem;
    margin: 1rem 0 .6rem;
    border-radius: .2rem;
    color: #333;
}
.primary_color{
    color:#00c59d;
}
.primary_bg{
    background-color: #00c59d;
    color:#fff;
}

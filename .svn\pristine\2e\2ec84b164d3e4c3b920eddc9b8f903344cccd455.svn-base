<template>
	<div class="group_avatar">
		<canvas id="group_avatar"></canvas>
	</div>
</template>
<script>
import base from '../lib/base'
import {addRootToUrl,getLocalAvatar,getDefaultImg} from '../lib/common_base'
import Tool from '@/common/tool'
export default {
    mixins: [base],
    name: 'groupAvatar',
    components: {},
    data(){
        return {
            taskList:[],
            running:false,
            currentTask:{}
        }
    },
    mounted(){
        // this.$root.eventBus.$off('createGroupAvatarIfNeed').$on('createGroupAvatarIfNeed',this.createGroupAvatarIfNeed)
        this.$root.eventBus.$off('createGroupAvatar').$on('createGroupAvatar',this.createGroupAvatar)
    },
    methods:{
        // createGroupAvatarIfNeed({conversation,userList=[],callback=null}){
        //     if (conversation.is_single_chat==0&&conversation.avatar=='static/resource_pc/images/b1.png'||userList.length>0) {
        //         let newUserList = [...userList]
        //         this.taskList.push({conversation,newUserList});
        //         // if(userList.length>0){
        //         //     this.$root.updateGroupAvatarUserList.length=0
        //         // }
        //         if (!this.running) {
        //             this.run()
        //         }
        //     }
        // },
        createGroupAvatar({conversation,userList=[],callback=null}){
            this.taskList.push({conversation,userList,callback});
            if (!this.running) {
                this.run()
            }
        },
        run(){
            this.running=true;
            this.currentTask=this.taskList.pop()
            if (!this.currentTask) {
                this.running=false;
                return;
            }
            this.doingMakeGroupAvatar(this.currentTask)
        },
        getComputePosition({conversation,userList}){
            //获取群头像的组成图片及布局
            let attendeeArray=[]
            if(userList.length>0){
                userList.map(uitem=>{
                    attendeeArray.push({
                        avatar:getLocalAvatar(uitem),
                        sex:uitem.sex,
                    })
                })
            }else{
                for(let key in conversation.attendeeList){
                    if (conversation.attendeeList[key].attendeeState!=0) {
                        attendeeArray.push({
                            avatar:getLocalAvatar(conversation.attendeeList[key]),
                            sex:conversation.attendeeList[key].sex
                        })
                    }
                }
            }
            attendeeArray=attendeeArray.splice(0,9);
            let row=0,col=0
            switch(attendeeArray.length){
            case 1:
                attendeeArray[0].sx=0
                attendeeArray[0].sy=0
                attendeeArray[0].sw=1
                attendeeArray[0].sh=1
                attendeeArray[0].dx=75
                attendeeArray[0].dy=75
                attendeeArray[0].dw=150
                attendeeArray[0].dh=150
                break;
            case 2:
                attendeeArray[0].sx=1/4
                attendeeArray[0].sy=0
                attendeeArray[0].sw=1/2
                attendeeArray[0].sh=1
                attendeeArray[0].dx=0
                attendeeArray[0].dy=0
                attendeeArray[0].dw=145
                attendeeArray[0].dh=300
                attendeeArray[1].sx=1/4
                attendeeArray[1].sy=0
                attendeeArray[1].sw=1/2
                attendeeArray[1].sh=1
                attendeeArray[1].dx=155
                attendeeArray[1].dy=0
                attendeeArray[1].dw=145
                attendeeArray[1].dh=300
                break;
            case 3:
                attendeeArray[0].sx=1/4
                attendeeArray[0].sy=0
                attendeeArray[0].sw=1/2
                attendeeArray[0].sh=1
                attendeeArray[0].dx=0
                attendeeArray[0].dy=0
                attendeeArray[0].dw=145
                attendeeArray[0].dh=300
                attendeeArray[1].sx=0
                attendeeArray[1].sy=0
                attendeeArray[1].sw=1
                attendeeArray[1].sh=1
                attendeeArray[1].dx=155
                attendeeArray[1].dy=0
                attendeeArray[1].dw=145
                attendeeArray[1].dh=145
                attendeeArray[2].sx=0
                attendeeArray[2].sy=0
                attendeeArray[2].sw=1
                attendeeArray[2].sh=1
                attendeeArray[2].dx=155
                attendeeArray[2].dy=155
                attendeeArray[2].dw=145
                attendeeArray[2].dh=145
                break;
            case 4:
                for(let index=0;index<attendeeArray.length;index++){
                    let item=attendeeArray[index]
                    item.dw=145;
                    item.dh=145;
                    item.dx=col*155;
                    item.dy=row*155;
                    if (col<1) {
                        col++
                    }else{
                        row++;
                        col=0;
                    }
                }
                break;
            case 5:
                attendeeArray[0].dx=55
                attendeeArray[0].dy=55
                attendeeArray[1].dx=155
                attendeeArray[1].dy=55
                attendeeArray[2].dx=5
                attendeeArray[2].dy=155
                attendeeArray[3].dx=105
                attendeeArray[3].dy=155
                attendeeArray[4].dx=205
                attendeeArray[4].dy=155
                for(let item of attendeeArray){
                    item.dw=90;
                    item.dh=90;
                }
                break;
            case 6:
                attendeeArray[0].dx=5
                attendeeArray[0].dy=55
                attendeeArray[1].dx=105
                attendeeArray[1].dy=55
                attendeeArray[2].dx=205
                attendeeArray[2].dy=55
                attendeeArray[3].dx=5
                attendeeArray[3].dy=155
                attendeeArray[4].dx=105
                attendeeArray[4].dy=155
                attendeeArray[5].dx=205
                attendeeArray[5].dy=155
                for(let item of attendeeArray){
                    item.dw=90;
                    item.dh=90;
                }
                break;
            case 7:
                attendeeArray[0].dx=105
                attendeeArray[0].dy=5
                attendeeArray[0].dw=90
                attendeeArray[0].dh=90
                row=1;
                for(let index=1;index<attendeeArray.length;index++){
                    let item=attendeeArray[index]
                    item.dw=90;
                    item.dh=90;
                    item.dx=5+col*100;
                    item.dy=5+row*100;
                    if (col<2) {
                        col++
                    }else{
                        row++;
                        col=0;
                    }
                }
                break;
            case 8:
                attendeeArray[0].dx=55
                attendeeArray[0].dy=5
                attendeeArray[0].dw=90
                attendeeArray[0].dh=90
                attendeeArray[1].dx=155
                attendeeArray[1].dy=5
                attendeeArray[1].dw=90
                attendeeArray[1].dh=90
                row=1;
                for(let index=2;index<attendeeArray.length;index++){
                    let item=attendeeArray[index]
                    item.dw=90;
                    item.dh=90;
                    item.dx=5+col*100;
                    item.dy=5+row*100;
                    if (col<2) {
                        col++
                    }else{
                        row++;
                        col=0;
                    }
                }
                break;
            case 9:
                for(let item of attendeeArray){
                    item.dw=90;
                    item.dh=90;
                    item.dx=5+col*100;
                    item.dy=5+row*100;
                    if (col<2) {
                        col++
                    }else{
                        row++;
                        col=0;
                    }
                }
                break;
            }
            if (attendeeArray.length>=4) {
                for(let item of attendeeArray){
                    //大于4张头像固定设置
                    item.sx=0;
                    item.sy=0;
                    item.sw=1;
                    item.sh=1;
                }
            }
            return attendeeArray;
        },
        drawAvatar(attendeeArray,callback){
            let conversation=this.currentTask.conversation;
            var canvas=document.getElementById('group_avatar')
            var context=canvas.getContext('2d');
            canvas.width=300;
            canvas.height=300;
            context.fillStyle='#ededee'
            context.fillRect(0,0,300,300)
            console.log('attendeeArray',attendeeArray)
            for(let member of attendeeArray){
                let width=member.image.width;
                let height=member.image.height;
                // console.log(`${member.sx*width},${member.sy*height},${member.sw*width},${member.sh*height},${member.dx},${member.dy},${member.dw},${member.dy}`)
                context.drawImage(member.image,member.sx*width,member.sy*height,member.sw*width,member.sh*height,member.dx,member.dy,member.dw,member.dh)
            }
            let imageData=canvas.toDataURL('image/jepg');
            let controller=conversation.socket;
            controller.emit('set_group_portrait',{
                imageType:'image/jepg',
                fileName:'group_avatar_'+new Date().valueOf()+'.jpg',
                file:imageData
            },(err,result)=>{
                if(callback&&Object.prototype.toString.call(callback)==='[object Function]'){
                    callback()
                }
                this.run();
            })
        },
        doingMakeGroupAvatar({conversation,userList,callback}){
            // Toast('生成新头像')
            setTimeout(()=>{
                //不立即执行，防止抢占UI性能
                let attendeeArray=this.getComputePosition({conversation,userList})
                let doneNum=0;
                for(let member of attendeeArray){
                    let realImage=new Image();
                    realImage.onload=()=>{
                        //全部头像下载完才触发生成群头像
                        doneNum++
                        member.image=realImage;
                        if (doneNum==attendeeArray.length) {
                            this.drawAvatar(attendeeArray,callback)
                        }
                    }
                    realImage.onerror=()=>{
                        realImage.src=getDefaultImg(member);;
                    }
                    let avatar=member.avatar
                    if(this.systemConfig.serverInfo.network_environment === 1){
                        avatar = Tool.replaceInternalNetworkEnvImageHost(avatar)
                    }
                    realImage.setAttribute("crossOrigin",'anonymous');
                    realImage.src=avatar;
                }
            },200)
        }
    }
}
</script>
<style lang="scss">
.group_avatar{
	display:none
}
</style>

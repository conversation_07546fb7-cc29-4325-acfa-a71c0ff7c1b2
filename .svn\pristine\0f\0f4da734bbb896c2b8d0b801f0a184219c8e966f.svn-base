<template>
    <div class="education-skill-certification">
        <div class="coming-soon">
            <h1>技能认证</h1>
            <p>功能开发中，敬请期待...</p>
        </div>
    </div>
</template>

<script>
import base from "../../lib/base";

export default {
    mixins: [base],
    name: "EducationSkillCertification",
    components: {},
    data() {
        return {};
    },
    created() {
        console.log("技能认证页面已加载");
    },
    methods: {},
};
</script>

<style lang="scss" scoped>
@import '@/module/ultrasync_pc/style/education.scss';

.education-skill-certification {
    height: 100%;
    display: flex;
    flex-direction: column;
    background-color: #f7f9fc;
}

.coming-soon {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;

    h1 {
        font-size: 28px;
        color: #303133;
        font-weight: 600;
        margin-bottom: 20px;
    }

    p {
        font-size: 16px;
        color: #909399;
        font-weight: 400;
    }
}
</style>

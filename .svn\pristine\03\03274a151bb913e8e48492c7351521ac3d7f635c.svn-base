<template>
	<div class="exam_search_bar">
		<div class="sort_select">
            <el-select v-model="sortSelected" @change="changeSortType">
                <el-option v-for="item of sortTypes" :label="item.value" :value="item.id" :key="item.id"></el-option>
            </el-select>
        </div>
        <div  class="group_select">
            <el-select v-model="groupSelected" @change="changeGroupType">
                <el-option v-for="item of groupTypes" :label="item.value" :value="item.id" :key="item.id"></el-option>
            </el-select>
        </div>
        <div class="search_bar_btn" @click="showSearchBar=true">{{lang.search_text}}</div>
        <div v-show="showSearchBar" class="search_bar clearfix">
            <!-- <div class="search_item">
                <span class="title">{{lang.sender_nickname}}</span>
                <div class="search_item_right">
                    <el-input v-model="sender_nickname"></el-input>
                </div>
            </div> -->
            <div class="search_item long_item">
                <span class="title">{{lang.search_key}}：</span>
                <div class="search_item_right">
                    <el-input v-model="patient_name" :placeholder="lang.search_uploader_name"></el-input>
                </div>
            </div>
            <div class="search_item" v-if="sortSelected==1">
                <span class="title">{{lang.exam_type}}: </span>
                <div class="search_item_right">
                    <el-select v-model="optionSelected">
                        <el-option v-for="item of examTypes" :label="item.value" :value="item.id" :key="item.id"></el-option>
                    </el-select>
                </div>
            </div>
            <div class="search_item" v-if="showStatus">
                <span class="title">{{lang.case_status}}</span>
                <div class="search_item_right">
                    <el-select v-model="mcStatus">
                        <el-option :label="lang.exam_status['-1']" :value="-1"></el-option>
                        <el-option :label="lang.exam_status['1']" :value="1"></el-option>
                        <el-option :label="lang.exam_status['2']" :value="2"></el-option>
                        <el-option :label="lang.exam_status['3']" :value="3"></el-option>
                        <el-option :label="lang.exam_status['4']" v-if="isHFRMulticenter" :value="4"></el-option>
                        <el-option :label="lang.exam_status['5']" v-if="isHFRMulticenter" :value="5"></el-option>
                        <el-option :label="lang.exam_status['6']" :value="6"></el-option>
                    </el-select>
                </div>
            </div>
            <div class="search_item long_item">
                <span v-if="sortSelected==1" class="title">{{lang.exam_time}}: </span>
                <span v-if="sortSelected==2" class="title">{{lang.upload_date_text}}</span>
                <div class="search_item_right">
                    <el-date-picker
                      v-model="exam_range"
                      type="daterange"
                      align="left"
                      unlink-panels
                      :range-separator="lang.date_to"
                      :start-placeholder="lang.start_date"
                      :end-placeholder="lang.end_date"
                      value-format="yyyy-MM-dd"
                    style="width:100%"
                      >
                    </el-date-picker>
                </div>
            </div>
            <div class="search_item long_item">
                <span class="title">{{lang.label_txt}}:</span>
                <div class="search_item_right">
                    <el-popover
                        placement="bottom"
                        trigger="click"
                        popper-class="exam_mode_search_tags"
                        >
                        <div class="choose_tags_wrap">
                            <el-tabs v-model="tagType" type="border-card">
                                <el-tab-pane :label="lang.history_tag" name="1">
                                    <div class="add_custom_tag">
                                        <el-input v-model="tagText"></el-input>
                                        <el-button size="mini" type="primary" @click="addCustomTag">{{lang.admin_add}}</el-button>
                                    </div>
                                    <div class="tag_names clearfix">
                                        <div v-for="(item,index) in historyTag" @click="clickTag(item)" :class="{selected:item.choose}" :key="index">{{item.caption}}</div>
                                    </div>
                                </el-tab-pane>
                                <el-tab-pane :label="lang.custom_tag_top" name="2">
                                    <div class="tag_names clearfix">
                                        <div v-for="(item,index) in userTagTop" @click="clickTag(item)" :class="{selected:item.choose}" :key="index">{{item.caption}}</div>
                                    </div>
                                </el-tab-pane>
                                <!-- <el-tab-pane :label="lang.system_tag_top" name="3">
                                    <div class="tag_names clearfix">
                                        <div v-for="(item,index) in systemTagTop" @click="clickTag(item)" :class="{selected:item.choose}" :key="index">{{item.name}}</div>
                                    </div>
                                </el-tab-pane> -->
                            </el-tabs>
                        </div>
                        <div class="selected_tags clearfix" slot="reference">
                            <div v-for="(item,index) in selectTags" @click.stop="deleteChoose(item)" :key="index">{{item.caption}}
                                <i class="icon iconfont iconicon-"></i>
                            </div>
                        </div>
                    </el-popover>
                </div>
            </div>
            <div class="search_item btns_wrap">
                <div class="search_item_right">
                    <el-button type="primary" size="mini" class="fl" @click="search">{{lang.search}}</el-button>
                    <el-button type="default" size="mini" class="fl" @click="showSearchBar=false">{{lang.cancel_btn}}</el-button>
                </div>
            </div>
        </div>
	</div>
</template>
<script>
import base from '../lib/base'
// import Tool from '@/common/tool.js'
export default {
    mixins: [base],
    name: 'ExamSearchBar',
    components: {},
    data(){
        return {
            // sender_nickname:'',
            patient_name:'',
            optionSelected:"-1",

            showSearchBar:false,
            examTypes:[
                {id:"-1",value:''},
                {id:"4",value:''},
                {id:"0",value:''},
                {id:"1",value:''},
                {id:"2",value:''},
                {id:"3",value:''},
                {id:"5",value:''},
                {id:"6",value:''},
                {id:"7",value:''},
                {id:"8",value:''},
                //{id:"9",value:''},
                //{id:"10",value:''}
            ],
            exam_range:[],
            historyTag:[],
            systemTagTop:[],
            userTagTop:[],
            tagType:"1",
            selectTags:[],
            tagText:'',
            sortTypes:[
                {id:1,value:' '},
                {id:2,value:' '},
            ],
            sortSelected:2,
            groupTypes:[
                {id:1,value:' '},
                {id:2,value:' '},
                {id:3,value:' '},
            ],
            groupSelected:1,
            mcStatus:-1
        }
    },
    props:{
        searchCallback:{
            type:Function,
            default:()=>{}
        },
        changeCondition:{
            type:Function,
            default:()=>{}
        },
        showStatus:{
            type:Boolean,
            default:false
        },
        isHFRMulticenter:{
            type:Boolean,
            default:false
        }
    },
    computed:{
        allTags(){
            return this.$store.state.gallery.tagTopInfo.allTagTop||[];
        },
    },
    methods:{
        init(){
            for(let item of this.examTypes){
                item.value=this.lang.exam_types[item.id]
            }
            this.sortTypes[0].value=this.lang.sort_by_exam_ts
            this.sortTypes[1].value=this.lang.sort_by_upload_ts
            this.groupTypes[0].value=this.lang.group_by_day
            this.groupTypes[1].value=this.lang.group_by_week
            this.groupTypes[2].value=this.lang.group_by_month

            this.patient_name=''
            // this.sender_nickname='';
            this.optionSelected="-1";
            this.sortSelected=2;
            this.groupSelected=1;
            this.initDate();
            this.initTag();
        },
        initDate(){
            // let today=new Date();
            // let year=today.getFullYear();
            // let month=today.getMonth();
            // let day=today.getDate();
            // let lastMonth=new Date(year,month-3,day);
            // this.exam_range=[this.getPickerDate(lastMonth),this.getPickerDate(today)]
            this.exam_range=[]
        },
        initTag(){
            // let systemTagTop=this.$store.state.gallery.tagTopInfo.systemTagTop||[]
            let userTagTop=this.allTags;
            let historyTag=JSON.parse(window.localStorage.getItem('user_'+this.user.uid+'_historyTag')||'{"list":[]}')
            // this.systemTagTop=[];
            this.userTagTop=[];
            this.selectTags=[];
            this.historyTag=[];
            // for(let item of systemTagTop){
            //     this.systemTagTop.push(Object.assign({choose:false},item));
            // }
            for(let item of userTagTop){
                this.userTagTop.push(Object.assign({choose:false},item));
            }
            for(let item of historyTag.list){
                this.historyTag.push(Object.assign({choose:false},item));
            }
        },
        renderSearchCondition(examObj){
            this.patient_name=examObj.patient_name || ''
            // this.sender_nickname=examObj.sender_nickname
            this.optionSelected=examObj.exam_type || '-1'
            this.sortSelected=examObj.sortSelected||2
            this.groupSelected=examObj.groupSelected||1
            this.mcStatus=examObj.mcStatus||-1
            this.selectTags=examObj.selectTags||[]
            // for(let tag of this.selectTags){
            //     this.addChoose(tag)
            // }
            if (examObj.start_time && examObj.start_time!=0) {
                this.exam_range=[examObj.start_time,examObj.end_time]
            }
            var tag=[];
            for(let item of this.selectTags){
                tag.push(item.caption);
                // this.addHistoryTag(item);
            }
            let condition={
                patient_name:this.patient_name,
                // sender_nickname:this.sender_nickname,
                patient_id:'',//病人id，已废弃的搜索条件
                exam_type:this.optionSelected,
                tag:tag,
                selectTags:this.selectTags,
                start_time:(this.exam_range&&this.exam_range[0])||0,
                end_time:(this.exam_range&&this.exam_range[1])||0,
                sortSelected:this.sortSelected,
                groupSelected:this.groupSelected,
                mcStatus:this.mcStatus
            }
            this.changeCondition(condition)
        },
        clickTag(item){
            let choose=item.choose;
            if(choose){
                this.deleteChoose(item);

            }else{
                this.addChoose(item);
            }
        },
        deleteChoose(item){
            // item.choose=false;
            for(let i=0;i<this.selectTags.length;i++){
                if (item.id==this.selectTags[i].id) {
                    this.selectTags.splice(i,1)
                }
            }
            for(let tag of this.systemTagTop){
                if (item.id==tag.id) {
                    tag.choose=false;
                    break;
                }
            }
            for(let tag of this.userTagTop){
                if (item.id==tag.id) {
                    tag.choose=false;
                    break;
                }
            }
            for(let tag of this.historyTag){
                if (item.id==tag.id) {
                    tag.choose=false;
                    break;
                }
            }
        },
        addChoose(item){
            // item.choose=true;
            this.selectTags.push(item)
            for(let tag of this.systemTagTop){
                if (item.id==tag.id) {
                    tag.choose=true;
                    break;
                }
            }
            for(let tag of this.userTagTop){
                if (item.id==tag.id) {
                    tag.choose=true;
                    break;
                }
            }
            for(let tag of this.historyTag){
                if (item.id==tag.id) {
                    tag.choose=true;
                    break;
                }
            }
        },
        search(){
            this.showSearchBar=false
            var tag=[];
            for(let item of this.selectTags){
                tag.push(item.caption);
                this.addHistoryTag(item);
            }
            let condition={
                patient_name:this.patient_name,
                // sender_nickname:this.sender_nickname,
                patient_id:'',//病人id，已废弃的搜索条件
                exam_type:this.optionSelected,
                tag:tag,
                selectTags:this.selectTags,
                start_time:(this.exam_range&&this.exam_range[0])||0,
                end_time:(this.exam_range&&this.exam_range[1])||0,
                sortSelected:this.sortSelected,
                groupSelected:this.groupSelected,
                mcStatus:this.mcStatus
            }
            this.searchCallback&&this.searchCallback(condition)
        },
        addHistoryTag(item){
            let index=-1;
            item.choose=true;
            for(let i=0;i<this.historyTag.length;i++){
                if (this.historyTag[i].id==item.id) {
                    index=i;
                    break;
                }
            }
            if (index==-1) {
                //添加的标签不在历史记录里
                if (this.historyTag.length==10) {
                    //超过10个时删除最后一个记录
                    this.historyTag.pop();
                }
            }else{
                //添加的标签在历史记录里，删除该记录后推入首位
                this.historyTag.splice(index,1);
            }
            //将标签推入数组首位
            this.historyTag.unshift(item)
            let arr=[];
            for(let tag of this.historyTag){
                arr.push({
                    id:tag.id,
                    caption:tag.caption
                })
            }
            window.localStorage.setItem('user_'+this.user.uid+'_historyTag',JSON.stringify({list:arr}))
        },
        addCustomTag(){
            let str=this.tagText
            if (str.length==0) {
                this.$message.error(this.lang.tag_text_null);
                return;
            }else{
                this.selectTags.push({
                    caption:str,
                    id:'temp_'+new Date().valueOf()
                });
                this.tagText=''
            }
        },
        changeSortType(id){
            this.sortSelected=id;
            this.search();
        },
        changeGroupType(id){
            this.groupSelected=id;
            let condition={
                groupSelected:this.groupSelected
            }
            this.changeCondition(condition)
        },
    }
}
</script>
<style  lang="scss">
.exam_search_bar{
	display:flex;
    padding: 10px;
    background: #fff;
    position: relative;
    font-size:16px;
    .sort_select{
        margin-right:10px;
    }
    .group_select{
        margin-right:10px;
    }
    .search_bar_btn{
        line-height:28px;
    }
    .search_bar{
        background-color: #fff;
        padding-right:10px;
        position: absolute;
        left: 0;
        top: 50px;
        z-index: 11;
        border-bottom: 1px solid #ccc;
        padding: 20px;
        width: 100%;
        .search_item{
            width: 100%;
            // float: left;
            display: flex;
            margin: 10px 0;
            word-break: keep-all;
            .title{
                line-height:28px;
                width: 120px;
            }
            .search_item_right{
                flex: 1;
                height: 36px;
                .selected_tags{
                    border:1px solid #aaa;
                    border-radius: 3px;
                    min-height: 100%;
                    &>div{
                        color: #fff;
                        background: #83a5a1;
                        padding: 0 8px;
                        border-radius: 8px;
                        margin: 4px;
                        cursor: pointer;
                        position: relative;
                        font-size: 16px;
                        line-height: 1.6;
                        float:left;
                        word-break: break-all;
                    }
                }

            }
            &.long_item{
                // width: 500px;
            }
            &.btns_wrap{
                // width:auto;
            }
        }
    }
    .el-input,.el-select,.el-date-editor{
        height: 100%;
        font-size: 16px;
        .el-input__inner{
            height: 100%;

        }
        .el-input__icon,.el-range-separator{
            line-height:28px;
        }
    }
    // .el-range-editor{
    //     padding:0 10px;
    //     width: 320px !important;
    // }
}
</style>

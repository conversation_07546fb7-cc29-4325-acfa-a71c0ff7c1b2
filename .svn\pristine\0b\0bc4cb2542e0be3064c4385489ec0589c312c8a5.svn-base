export default {
    name: 'Name',
    tel: 'Telefon',
    save: '<PERSON><PERSON><PERSON><PERSON>',
    confirm: 'Bestätigen',
    cancel: 'Abbre<PERSON>',
    delete: '<PERSON>ö<PERSON>',
    complete: 'Fert<PERSON>',
    loading: 'Laden...',
    telEmpty: 'Bitte das Telefon ausfüllen',
    nameEmpty: 'Bitte den Name ausfüllen',
    nameInvalid: 'Ungültiger Name',
    confirmDelete: 'Löschen bestätigen',
    telInvalid: 'Ungültige Telefonnummer',
    vanCalendar: {
        end: 'Ende',
        start: 'Start',
        title: 'Kalender',
        startEnd: 'Start/Ende',
        weekdays: ['So', 'Mo', 'Di', 'Mi', 'Do', 'Fr', 'Sa'],
        monthTitle: (year, month) => `${year}/${month}`,
        rangePrompt: (maxRange) => `Wähle nicht mehr als ${maxRange} Tage`,
    },
    vanContactCard: {
        addText: 'Kontakt hinzufügen',
    },
    vanContactList: {
        addText: 'Neuen Kontakt hinzufügen',
    },
    vanPagination: {
        prev: 'Vorherige',
        next: 'Nächste',
    },
    vanPullRefresh: {
        pulling: 'Zum Aktualisieren ziehen...',
        loosing: 'Loslassen zum Aktualisieren...',
    },
    vanSubmitBar: {
        label: 'Total:',
    },
    vanCoupon: {
        unlimited: 'Unbegrenzt',
        discount: (discount) => `${discount * 10}% Rabatt`,
        condition: (condition) => `Mindestens ${condition}`,
    },
    vanCouponCell: {
        title: 'Gutschein',
        tips: 'Keine Gutscheine',
        count: (count) => `Du hast ${count} Gutscheine`,
    },
    vanCouponList: {
        empty: 'Keine Gutscheine',
        exchange: 'Umtauschen',
        close: 'Schließen',
        enable: 'Verfügbar',
        disabled: 'Nicht verfügbar',
        placeholder: 'Gutscheincode',
    },
    vanAddressEdit: {
        area: 'Standort',
        postal: 'PLZ',
        areaEmpty: 'Bitte Standort auswählen',
        addressEmpty: 'Adresse darf nicht leer sein',
        postalEmpty: 'Falsche Postleitzahl',
        defaultAddress: 'Als Standardadresse festlegen',
        telPlaceholder: 'Telefon',
        namePlaceholder: 'Name',
        areaPlaceholder: 'Standort auswählen',
    },
    vanAddressEditDetail: {
        label: 'Adresse',
        placeholder: 'Adresse',
    },
    vanAddressList: {
        add: 'Adresse hinzufügen',
    },
};

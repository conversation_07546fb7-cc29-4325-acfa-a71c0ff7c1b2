<template>
	<div class="image_viewer" v-show="isShow">
		<div class="viewer_wrapper">
            <div class="title_bar">
                <i class="iconfont iconsearchclose" @click="closeHandle"></i>
            </div>
            <div class="viewer_content">
                <div class="top" ref="topSwiper">
                    <template v-if="currentFile.msg_type === 3">
                        <img :src="currentFile.url" draggable="false" class="preview">
                    </template>
                    <template v-else-if="currentFile.msg_type === 4">
                        <video v-if="!isCef" class="main_video" :src="currentFile.url" controls @error="playVideoError()"></video>
                    </template>
                    <i v-if="imageList.length>2&&index!==0" @click="prevImage"  class="iconfont iconright1"></i>
                    <i v-if="imageList.length>2&&index<imageList.length-1" @click="nextImage" class="iconfont iconright2"></i>
                </div>
                <div class="thumb_wrap">
                    <div ref="thumb_scroll_wrap" class="thumb_scroll_wrap">
                        <vue-slide class="thumb_slide" ref="thumb_slide" v-if="isShow">
                            <div :style="{'width':imageList.length*157+'px','min-width':'100%'}" @mousewheel.prevent.stop class="clearfix">
                                <div v-for="(file,f_index) in imageList" class="thumb_item" :class="{'current_thumb':f_index==index}" @mousedown="mousedownThumb($event,f_index)" @mouseup="mouseupThumb($event,f_index)" :key="file.url+f_index"> 
                                    <template v-if="file.msg_type === 3">
                                        <img :src="file.url" class="preview" draggable="false">
                                        <i class="icon iconfont iconpicture"></i>
                                    </template>
                                    <template v-else-if="file.msg_type === 4">
                                        <i class="icon iconfont iconvideo_fill_light"></i>
                                    </template>
                                </div>
                            </div>
                        </vue-slide>
                    </div>
                    <i @click="lastPage" class="icon iconfont iconsanjiaoxing last_page"></i>
                    <i @click="nextPage" class="icon iconfont iconsanjiaoxing next_page"></i>
                </div>
            </div>
        </div>
	</div>
</template>
<script>
import base from '../lib/base';
import vueSlide from 'vuescroll/dist/vuescroll-slide'
import {initVideoPage} from '../lib/common_realtimeVideo'
import {cloneDeep} from 'lodash'
export default {
    name:'imageViewer',
    mixins:[base],
    components: { vueSlide },
    props:{
    },
    computed:{
        currentFile(){
            let file = cloneDeep(this.imageList[this.index]||{})
            // 兼容客户端播放器播放
            file.mainVideoSrc = file.url;
            return file
        },
    },
    data(){
        return {
            imageList:[],
            isShow:false,
            mousedownThumpPoint:null,
            index:-1,
        }
    },
    created(){
    },
    mounted(){
    },
    methods:{
        init(imageList,index){
            this.imageList = imageList;
            this.mousedownThumpPoint = null;
            this.index = -1;
            this.slideTop(index);
            this.isShow = true;
        },
        mousedownThumb(event,index){
            this.mousedownThumpPoint={
                x:event.x,
                y:event.y
            }
        },
        mouseupThumb(event,index){
            let offsetX=this.mousedownThumpPoint.x-event.x
            let offsetY=this.mousedownThumpPoint.y-event.y
            if (Math.abs(offsetX)<20&&Math.abs(offsetY)<20) {
                this.slideTop(index);
            }
        },
        slideTop(index){
            try {
                this.index=index;
                this.slideThumb(index)
                this.changeHandler(index);
            } catch (error) {
                console.error(error)
            }
        },
        slideThumb(index){
            console.log(index,'slideThumb')
            let thumb_slide=this.$refs.thumb_slide;
            let thumb_scroll_wrap=this.$refs.thumb_scroll_wrap;
            let scroll_width=thumb_scroll_wrap.clientWidth
            let left=index*157-(scroll_width/2)+78
            this.$nextTick(()=>{
                thumb_slide&&thumb_slide.scrollTo({x:left},150)
            })
        },
        prevImage(){
            if (this.index!=0) {
                this.slideTop(this.index-1)
            }
        },
        nextImage(){
            if (this.index<this.imageList.length) {
                this.slideTop(this.index+1)
            }
        },
        lastPage(){
            let thumb_slide=this.$refs.thumb_slide;
            let left=thumb_slide.getPosition().scrollLeft;
            let thumb_scroll_wrap=this.$refs.thumb_scroll_wrap;
            let scroll_width=thumb_scroll_wrap.clientWidth
            left-=scroll_width
            if(left === 0){
                left = -1
            }
            this.$nextTick(()=>{
                thumb_slide&&thumb_slide.scrollTo({x:left},150)
            })
        },
        nextPage(){
            let thumb_slide=this.$refs.thumb_slide;
            let left=thumb_slide.getPosition().scrollLeft;
            let thumb_scroll_wrap=this.$refs.thumb_scroll_wrap;
            let scroll_width=thumb_scroll_wrap.clientWidth
            left+=scroll_width
            if(left === 0){
                left = -1
            }
            this.$nextTick(()=>{
                thumb_slide&&thumb_slide.scrollTo({x:left},150)
            })
        },
        playVideoError(){
            this.showVideoErrorTips()
        },
        closeHandle(){
            this.isShow = false;
            this.index= -1;
            window.CWorkstationCommunicationMng.hideRealTimeVideo({})
        },
        changeHandler(index){
            let container=this.$refs.topSwiper
            initVideoPage({file:this.currentFile,container})
        }
    },
}
</script>
<style lang="scss" scoped>
.image_viewer{
    position: fixed;
    background: rgba(47, 47, 47, .7);
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    overflow: auto;
    margin: 0;
    z-index: 3001;
    display: flex;
    align-items: center;
    .viewer_wrapper{
        width: 90%;
        height: 90%;
        position: relative;
        box-shadow: 10px 8px 30px #666;
        border: 1px solid #fff;
        margin: 0 auto;
        display: flex;
        flex-direction: column;
        .title_bar{
            background: #212121;
            height: 36px;
            border-bottom: none;
            position: relative;
            .iconsearchclose{
                position: absolute;
                right: 4px;
                top: 4px;
                color: #fff;
                font-size: 20px;
                padding: 0 4px;
                cursor: pointer;
            }
        }
        .viewer_content{
            flex:1;
            background-color: #212121;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            .top{
                flex: 1;
                position: relative;
                padding: 10px;
                min-height: 0;
                .preview,.main_video{
                    max-width: 100%;
                    max-height: 100%;
                    position: absolute;
                    top: 0;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    margin: auto;
                }
                .iconright1{
                    font-size: 40px;
                    left: 20px;
                    top: 50%;
                    position: absolute;
                    color: #aaa;
                    cursor: pointer;
                    z-index: 3;
                    display: none;
                }
                .iconright2{
                    font-size: 40px;
                    right: 20px;
                    top: 50%;
                    position: absolute;
                    color: #aaa;
                    cursor: pointer;
                    z-index: 3;
                    display: none;
                }
                &:hover .iconfont{
                    display: block;
                }
            }
            .thumb_wrap{
                height: 120px;
                padding: 0 20px 4px;
                position: relative;
                .thumb_scroll_wrap{
                    width:100%;
                    height:100%;
                    user-select:none;
                    .__rail-is-horizontal{
                        height:0 !important;
                    }
                    .thumb_slide{
                        position:relative;
                        width:100%;
                        height:100%;
                        z-index: 1;
                        .thumb_item{
                            float:left;
                            width: 156px;
                            height: 116px;
                            background: #000;
                            position: relative;
                            margin-right: 1px;
                            cursor:pointer;
                            &.current_thumb{
                                border:3px solid #599592;
                            }
                            .preview{
                                max-width:100%;
                                max-height:100%;
                                position: absolute;
                                top: 0;
                                bottom:0;
                                left: 0;
                                right:0;
                                margin:auto;
                            }
                            .iconpicture{
                                position: absolute;
                                bottom: 0;
                                color: #fff;
                                font-size: 24px;
                                line-height: 1;
                                z-index: 2;
                            }
                            .iconvideo_fill_light{
                                position: absolute;
                                top: 44px;
                                left: 60px;
                                font-size: 34px;
                                color: #fff;
                                line-height: 1;
                                z-index: 2;
                            }
                        }
                        .__bar-is-horizontal,.__bar-is-vertical{
                            display:none;
                        }
                    }
                }
                .last_page{
                    transform:rotate(90deg) scaleX(1.5);
                    position: absolute;
                    left: 0px;
                    top: 50px;
                    color: #fff;
                    font-size: 18px;
                    line-height: 16px;
                    cursor: pointer;
                }
                .next_page{
                    transform:rotate(270deg) scaleX(1.5);
                    position: absolute;
                    right: 0px;
                    top: 50px;
                    color: #fff;
                    font-size: 18px;
                    line-height: 16px;
                    cursor: pointer;
                }
            }
        }
    }
}
</style>

/**
 * 自动登录管理器
 * 负责处理自动登录相关的逻辑，包括登录状态管理、token处理、设备ID管理等
 */
import service from '../service/service'
import { handleAfterLogin } from './common_base'

class AutoLoginManager {
    constructor(vueInstance) {
        this.vm = vueInstance
        this.isAutoLogging = false
        this.autoLoginTime = 0
        this.autoLogonTimer = null
    }

    /**
     * 自动登录主方法
     * @param {Function} callback 登录成功后的回调函数
     */
    async autoLogin(callback) {
        console.log('[event] autoLoginManager autoLogin')

        if (this.isAutoLogging) {
            console.log('repeat auto login reject')
            return
        }

        this.isAutoLogging = true

        // 关闭现有的socket连接
        if (window.main_screen && window.main_screen.gateway) {
            window.main_screen.CloseSocket()
        }

        // 解绑控制器事件
        this.vm.unBindControllerEvent()

        // 获取登录相关信息
        const loginInfo = this._getLoginInfo()

        if (loginInfo.loginToken !== '') {
            await this._autoLoginAction({
                action: 'loginByToken',
                loginToken: loginInfo.loginToken,
                callback: callback,
            })
        } else if (loginInfo.account !== '' && loginInfo.password !== '') {
            await this._autoLoginAction({
                action: 'getLoginToken',
                account: loginInfo.account,
                password: loginInfo.password,
                callback: callback,
            })
        } else if (loginInfo.needChangeDeviceId || loginInfo.token === '') {
            console.log('[event] autoLogin - login')
            this._redirectToLogin()
        } else {
            await this._loginWithToken(loginInfo.token, loginInfo.device_id, callback)
        }
    }

    /**
     * 获取登录信息
     * @returns {Object} 登录相关信息
     */
    _getLoginInfo() {
        let loginToken = window.localStorage.getItem('loginToken') || ''
        let account = window.localStorage.getItem('account') || ''
        let password = window.localStorage.getItem('password') || ''
        let token = this.vm.user.new_token || ''
        let device_id = this.vm.$store.state.device.device_id
        let needChangeDeviceId = false

        // 处理第三方登录信息
        if (window.g_extend_info) {
            const info = window.g_extend_info
            if (info.token && info.token !== '') {
                token = info.token
            }
            if (info.loginToken && info.loginToken !== '') {
                loginToken = info.loginToken
            }
        }

        console.log("################### autoLoginManager device_id ###################", device_id)

        return {
            loginToken,
            account,
            password,
            token,
            device_id,
            needChangeDeviceId
        }
    }

    /**
     * 自动登录操作分发
     * @param {Object} params 登录参数
     */
    async _autoLoginAction(params) {
        if (params.action === 'loginByToken') {
            await this._autoLoginByToken(params.loginToken, params.callback)
        } else if (params.action === 'getLoginToken') {
            // 兼容旧版本的本地密码登录，后替换为Token
            const tokenParams = {
                login_name: params.account,
                password: params.password,
                language: window.localStorage.getItem('lang') || 'CN',
                clientType: this.vm.systemConfig.clientType,
                deviceId: this.vm.$store.state.device.device_id
            }

            try {
                const res = await service.getLoginToken(tokenParams)
                if (res.data.error_code === 0) {
                    await this._autoLoginByToken(res.data.data.token, params.callback)
                } else {
                    this.isAutoLogging = false
                    this.resetStartupOption()
                    this.vm.clearAndDirectToLogin(this.vm.lang[res.data.key])
                }
            } catch (error) {
                this.isAutoLogging = false
                console.error('getLoginToken error:', error)
            }
        }
    }

    /**
     * 通过Token自动登录
     * @param {string} loginToken 登录Token
     * @param {Function} callback 回调函数
     */
    async _autoLoginByToken(loginToken, callback) {
        try {
            const res = await service.loginByToken({
                token: loginToken,
                deviceInfo: {
                    device_id: this.vm.$store.state.device.device_id,
                    client_type: this.vm.systemConfig.clientType
                }
            })

            window.Logger.save({
                message: 'autoLoginByToken',
                eventType: 'socket_event',
                data: { res },
            })

            if (res.data.error_code === 0) {
                setTimeout(() => {
                    this.vm.loginLoading = false
                }, 300)

                const user = res.data.data
                handleAfterLogin(user, true)
                callback && callback()
            } else {
                await this._handleLoginError(res.data, loginToken, callback)
            }
        } catch (error) {
            console.error('autoLoginByToken error:', error)
            setTimeout(() => {
                this._autoLoginByToken(loginToken, callback)
            }, 5000)
        }
    }

    /**
     * 处理登录错误
     * @param {Object} data 错误数据
     * @param {string} loginToken 登录Token
     * @param {Function} callback 回调函数
     */
    async _handleLoginError(data, loginToken, callback) {
        if (data.key === 'userChangePwdNeedLogin' || data.key === 'codeMustRequired') {
            this.isAutoLogging = false
            this.resetStartupOption()
            this.vm.clearAndDirectToLogin(this.vm.lang[data.key])
        } else if (data.key === 'userTokenError') {
            this.isAutoLogging = false
            this.resetStartupOption()
        } else if (data.key === 'userOutOfTrail') {
            // 试用期到跳转到输入推荐码界面
            this.isAutoLogging = false
            this.resetStartupOption()
            this.vm.clearAndDirectToLogin(this.vm.lang.userOutOfTrail)
            setTimeout(() => {
                this.vm.$router.push(`/login/referral_code?token=${loginToken}&isAutoLogin=1&isRemember=1`)
            }, 300)
        } else {
            setTimeout(() => {
                this._autoLoginByToken(loginToken, callback)
            }, 5000)
        }
    }

    /**
     * 使用Token登录（旧版本兼容）
     * @param {string} token Token
     * @param {string} device_id 设备ID
     * @param {Function} callback 回调函数
     */
    async _loginWithToken(token, device_id, callback) {
        console.log('autoLogin--', (new Date()).toLocaleString())

        try {
            const res = await service.login({
                action_form: 'login_action',
                user: {
                    name: '',
                    pwd: '',
                    device_id: device_id,
                    token: token,
                    extend_info: "",
                    type: this.vm.systemConfig.clientType,
                }
            })

            if (res.data && res.data.uid) {
                setTimeout(() => {
                    this.vm.loginLoading = false
                }, 300)

                this.vm.$store.commit('loadingConfig/updateLoaded', {
                    key: 'networkUnavailable',
                    loaded: false
                })

                this.vm.$store.commit('user/updateUser', res.data)
                callback(res)

                if (res.data.probationary_expiry) {
                    let msg = this.vm.lang.nls_probationary_expiry_tip.replace('{1}', res.data.probationary_expiry)
                    this.vm.$MessageBox.alert(msg)
                }
            } else {
                this._handleLoginFailure(res.data)
            }
        } catch (error) {
            console.error('loginWithToken error:', error)
            this._handleNetworkError()
        }
    }

    /**
     * 处理登录失败
     * @param {Object} data 响应数据
     */
    _handleLoginFailure(data) {
        this.isAutoLogging = false
        this.resetStartupOption()
        let msg = this.vm.lang.unknown_error
        if (data.msg && data.msg) {
            msg = data.msg
        }
        this.vm.clearAndDirectToLogin(msg)
    }

    /**
     * 处理网络错误
     */
    _handleNetworkError() {
        this.isAutoLogging = false
        let message = this.vm.lang.network_error_tip
        this.vm.$store.commit('loadingConfig/updateLoaded', {
            key: 'networkUnavailable',
            loaded: true
        })
        setTimeout(() => {
            this.autoLogin()
        }, 5000)
    }

    /**
     * 重定向到登录页面
     */
    _redirectToLogin() {
        this.vm.$router.replace('/login')
        this.vm.$root.eventBus.$emit('reloadRouter')
        window.localStorage.setItem('password', '')
        window.localStorage.setItem('loginToken', '')
        this.vm.$store.commit('user/updateUser', {
            new_token: ''
        })
    }

    /**
     * Socket连接成功处理
     */
    onSocketConnectSuccess() {
        this.autoLoginTime = 0
    }

    /**
     * Socket错误处理
     * @param {string} error 错误信息
     */
    onSocketError(error) {
        if (error === 'Invalid namespace') {
            if (this.autoLoginTime > 2) {
                this.vm.$router.replace('/login')
            } else {
                this.autoLoginTime++
                this.autoLogin(() => {
                    this.vm.initNetworkData()
                    if (this.autoLogonTimer) {
                        clearTimeout(this.autoLogonTimer)
                        this.autoLogonTimer = null
                    }
                })
            }
        }
    }

    /**
     * Socket重连失败处理
     */
    onSocketReconnectFail() {
        this.autoLogin(() => {
            this.vm.initNetworkData()
        })
    }

    /**
     * Socket断开连接处理
     * @param {string} reason 断开原因
     */
    onSocketDisconnect(reason) {
        if (reason !== 'io client disconnect') {
            this.autoLogin(() => {
                this.vm.initNetworkData()
            })
        }
    }

    /**
     * 异常通知处理
     */
    onNotifyException() {
        this.autoLogin(() => {
            this.vm.initNetworkData()
        })
    }
    resetStartupOption() {
        window.CWorkstationCommunicationMng.ClearStartupOption();
        window.g_extend_info = null;
    }
    /**
     * 清理资源
     */
    destroy() {
        if (this.autoLogonTimer) {
            clearTimeout(this.autoLogonTimer)
            this.autoLogonTimer = null
        }
        this.isAutoLogging = false
        this.autoLoginTime = 0
    }
}

export default AutoLoginManager

<template>
    <transition name="slide">
    	<two-factor-authentication
        :cellphone="cellphone"
        :email="email"
        :token="token"
        :isLoading="isLoading"
        verifyCodeType="destroyUser"
        :verifyCallback="logoffAction"
        ref="twoFactorAuthentication"
        ></two-factor-authentication>
	</transition>
</template>
<script>
import base from '../lib/base'
import service from '../service/service'
import twoFactorAuthentication from '../components/twoFactorAuthentication.vue'
export default {
    mixins: [base],
    name: 'DestroyAuthentication',
    components: {twoFactorAuthentication},
    computed:{

    },
    data(){
        return {
            cellphone:'',
            email:'',
            token:'',
            isLoading:false
        }
    },
    mounted(){
        if (this.user.mobile_phone) {
            this.cellphone=`+${this.user.international_code} ${this.user.mobile_phone}`;
        }
        this.email=this.user.email;
        this.token=this.user.new_token;
        this.$nextTick(()=>{
            this.$refs.twoFactorAuthentication.init();
        })
    },
    methods:{
        logoffAction(verifyCode,type){
            let params={
                token:this.user.new_token,
                accountType:type,
                code:verifyCode,
            }
            this.isLoading=true;
            service.logoff(params).then(res => {
                this.isLoading=false;
                if (res.data.error_code===0) {
                    //notity_user_destroy
                }
            })
        },
    }
}

</script>
<style lang="scss">
</style>

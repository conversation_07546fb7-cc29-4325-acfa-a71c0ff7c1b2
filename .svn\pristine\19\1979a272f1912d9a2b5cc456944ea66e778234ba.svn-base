<template>
    <div>
        <section class="h1_box">
            <h1>版本已更新，跳转到新页面中...</h1>
            <h1>This page has been updated, redirecting...</h1>
        </section>
    </div>
</template>
<script>
import Tool from '@/common/tool.js';
export default {
    // 该页面已经被迁移

    name: 'WebLive_deprecated',
    created(){
        setTimeout(() => {
            try {
                // 重定向至新版页面，本页面为兼容旧版
                if (window.location.href.includes('localhost')){
                    window.location.href = `activity.html${window.location.hash}`; // 本地开发的情况
                } else {
                    window.location.href = Tool.transferLocationToCe(`${window.location.origin}/activity/activity.html${window.location.hash}`); // 部署的情况
                }
            } catch (e) {
                alert(`跳转失败，请检查系统设置`);
                console.error(e);
            }
        }, 2000)
    },
    beforeDestroy(){
        document.querySelector('meta[name="viewport"]').setAttribute("content","")
    }
}
</script>
<style scoped>
    div {
        height: 100vh;
        overflow: hidden;
    }

    h1 {
        margin: 0 auto;
        text-align: center;
    }

    .h1_box{
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translateY(-50%) translateX(-50%);
    }
</style>

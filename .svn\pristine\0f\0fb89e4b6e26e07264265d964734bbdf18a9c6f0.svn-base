<template>
    <transition name="fade" appear>
    	<div class="apply_friend_page fourth_level_page">
            <mrHeader>
                <template #title>
                    {{lang.apply_add_friend}}
                </template>
            </mrHeader>
            <div class="container">
            	<p class="modify_tip">{{lang.send_apply_add_friend}}</p>
				<input type="text" class="commont_input" v-model="description" maxlength="16">
				<button class="primary_bg modify_remark_btn" @tap="requestAdd">{{lang.send_message_txt}}</button>
            </div>
    	</div>
    </transition>
</template>
<script>
import base from '../lib/base'
import { Toast } from 'vant';
import { cloneDeep } from 'lodash'
export default {
    mixins: [base],
    name: 'apply_friend',
    components: {},
    data(){
        return {
            description:''
        }
    },
    computed:{
        personalObj(){
            let obj=cloneDeep(this.$store.state.relationship.personalObj);
            if (!obj.userid) {
                //attendeeList的id被占用，只能用userid
                obj.userid=obj.id;
            }
            return obj
        },
    },
    mounted(){
    },
    methods:{
        requestAdd(index){
            //申请添加好友
            var user=this.personalObj
            this.$root.socket.emit("request_add_friend",{id:user.id,description:this.description})
            this.$store.commit('relationship/addApplyFriend',user)
        },
    }
}
</script>
<style lang="scss">
.apply_friend_page{
	.container{
		margin:.8rem;
		.modify_tip{
			font-size:.8rem;
			color:#707070;
			margin:.1rem 0;
		}
		input{
			background-color:transparent;
			margin:0;
			color:#333;
			transform: translateZ(0px);
		}
		.modify_remark_btn{
			display: block;
		    width: 100%;
		    border: none;
		    font-size: 1rem;
		    line-height: 2rem;
		    margin: 1rem 0 .6rem;
		    border-radius: .2rem;
            transform: translateZ(0px);
		}
	}
}
</style>

<template>
    <div ref="main_live_container">
        <div class="main_live_container">
            <div class="main_live_top_container" v-if="!isLeaveChannel">
                <div class="top_tools"></div>
                <div class="top_tools_right">
                    <el-button plain readonly>
                        <IntervalTime :diffTime="living_time" class="inter_time"></IntervalTime>
                    </el-button>
                    <el-button plain @click="maxOrMin">
                        <i class="el-icon-full-screen"></i>
                        全屏
                    </el-button>
                </div>
            </div>
            <div class="main_live_content_container">
                <div class="live_player_left">
                    <div id="main-stream"></div>
                </div>
                <div class="live_player_right" v-show="LivingData.showAuxVideoDomList.length > 0">
                    <div class="aux_stream_container" v-show="findVideoStreamDomShow('video-stream-dom1')">
                        <div id="video-stream-dom1" class="aux-stream"></div>
                    </div>
                    <div class="aux_stream_container" v-show="findVideoStreamDomShow('video-stream-dom2')">
                        <div id="video-stream-dom2" class="aux-stream"></div>
                    </div>
                    <div class="aux_stream_container" v-show="findVideoStreamDomShow('video-stream-dom3')">
                        <div id="video-stream-dom3" class="aux-stream"></div>
                    </div>
                </div>
                <div class="live_player_extra" v-show="showMemberList">
                    <i @click="closeExtra" class="iconfont iconsearchclose"></i>
                    <ManageVideoMemberDialog
                        v-model="showMemberList"
                        :liveRoom="liveRoom"
                        class="managerDialogList"
                        :channelId="channelId"
                    ></ManageVideoMemberDialog>
                </div>
            </div>
            <div class="main_live_bottom_container" v-if="LivingData.joinedAux">
                <div class="bottom_tools">
                    <div
                        class="menu-button-arrow-left"
                        @click="openCamera(false)"
                        v-show="LivingData.localVideoStream === 1"
                        v-loading="isHandlingCamera"
                        element-loading-spinner="el-icon-loading"
                    >
                        <div class="icon icon-camera"></div>
                        <div class="text longwrap">摄像头</div>
                    </div>
                    <div
                        class="menu-button-arrow-left"
                        @click="openCamera(true)"
                        v-show="LivingData.localVideoStream < 1"
                        v-loading="isHandlingCamera"
                        element-loading-spinner="el-icon-loading"
                    >
                        <div class="icon icon-camera-off"></div>
                        <div class="text longwrap">摄像头</div>
                    </div>
                    <el-popover
                        placement="top"
                        width="400"
                        trigger="manual"
                        v-model="showCamerasList"
                        ref="camerasPopover"
                        popper-class="live_tools_menu-popper"
                    >
                        <i class="el-icon-arrow-down" slot="reference" @click="handleShowCamerasList"></i>
                        <ul class="dropdown-menu-ul">
                            <li
                                v-for="device in camerasList"
                                :key="device.deviceId"
                                :class="['dropdown-item', { active: selectedCamera.deviceId === device.deviceId }]"
                                @click="switchCamera(device.deviceId)"
                            >
                                <span class="indicator" v-if="selectedCamera.deviceId === device.deviceId">●</span>
                                {{ device.label }}
                            </li>
                        </ul>
                    </el-popover>
                    <div
                        class="menu-button-arrow-left"
                        @click="openMicrophone(false)"
                        v-show="LivingData.localAudioStream === 1"
                    >
                        <div class="icon icon-microphone"></div>
                        <div class="text longwrap">麦克风</div>
                    </div>
                    <div
                        class="menu-button-arrow-left"
                        @click="openMicrophone(true)"
                        v-show="LivingData.localAudioStream < 1"
                    >
                        <div class="icon icon-microphone-off"></div>
                        <div class="text longwrap">麦克风</div>
                    </div>
                    <el-popover
                        placement="top"
                        width="400"
                        trigger="manual"
                        v-model="showMicrophonesList"
                        ref="microphonesPopover"
                        popper-class="live_tools_menu-popper"
                    >
                        <i class="el-icon-arrow-down" slot="reference" @click="handleShowMicrophonesList"></i>
                        <ul class="dropdown-menu-ul">
                            <li
                                v-for="device in microphonesList"
                                :key="device.deviceId"
                                :class="['dropdown-item', { active: selectedMicrophone.deviceId === device.deviceId }]"
                                @click="switchMicrophone(device.deviceId)"
                            >
                                <span class="indicator" v-if="selectedMicrophone.deviceId === device.deviceId">●</span>
                                {{ device.label }}
                            </li>
                        </ul>
                    </el-popover>
                    <div class="menu-button" @click="openMemberList()">
                        <div class="icon icon-users"></div>
                        <div class="text longwrap">成员列表</div>
                    </div>
                    <div class="menu-button" @click="openSetting()">
                        <div class="icon icon-setting"></div>
                        <div class="text longwrap">设置</div>
                    </div>
                </div>
                <div class="bottom_tools_right">
                    <el-button type="danger" plain @click="leaveChannel">离开直播</el-button>
                </div>
            </div>
        </div>
        <div
            v-loading.fullscreen="isConferenceJoining"
            element-loading-text="直播准备中"
            class="chatWindow_isConferenceJoining"
        ></div>
        <div
            v-loading.fullscreen="isReconnectChannel"
            element-loading-text="重新连接中"
            class="chatWindow_isReconnectChannel"
        ></div>
        <LiveRoomSetting v-model="showLiveSetting" :liveRoom="liveRoom"></LiveRoomSetting>
        <!-- <div v-show="false">{{ isSelectCheckBoxList }}</div> -->
    </div>
</template>
<script>
import CLiveRoomWeb from "@/common/CLiveConferencePUMCH/CLiveRoomWeb";
import Tool from "@/common/tool";
import ManageVideoMemberDialog from "./manageVideoMemberDialog.vue";
import LiveRoomSetting from "./liveRoomSetting.vue";
import IntervalTime from "../../components/intervalTime.vue";
import { cloneDeep } from "lodash";
import axios from 'axios';

export default {
    name: "LiveRoomWebComponent",
    components: {
        ManageVideoMemberDialog,
        IntervalTime,
        LiveRoomSetting,
    },
    data() {
        return {
            title: "",
            loading: false,
            agoraClient: null,
            remoteUsers: {},
            isConferenceJoining: false,
            isReconnectChannel: false,
            isCleaningUp: false,
            liveRoom: {},
            showMemberList: false,
            living_time: 0,
            isShowCloseConferenceBtn: false,
            isShowLeaveConferenceBtn: false,
            showLiveSetting: false,
            microphonesList: [],
            selectedMicrophone: {},
            camerasList: [],
            selectedCamera: {},
            showMicrophonesList: false,
            showCamerasList: false,
            isHandlingCamera: false,
            isHandlingMic: false,
            mainStreamCheckBox: true,
            videoStreamDomCheckBox1: true,
            videoStreamDomCheckBox2: true,
            videoStreamDomCheckBox3: true,
            channelId: "16018938683",
            appId: '453c50bcbbe64a07834bb8af77e489b5',
            mainInfo:{},
            auxInfo:{
                "uid":10783,
                "token":"006453c50bcbbe64a07834bb8af77e489b5IAAKYUWtg1Bnb+QgqH405ybpdWvzEeIDmmSJ6SEbaBQbGfmvlmNysCMjIgBvHg5tWbtgaAQAAQBZu2BoAgBZu2BoAwBZu2BoBABZu2Bo",
                "isHost":false
            },
            isFullScreen: false,
            isLeaveChannel:false,
            urlToken: "", // 新增：存储从URL获取的token
            nickname:"",
            socketAddress:"https://www.baidu.com"

        };
    },
    computed: {
        LivingData() {
            const data =
                (this.$store.state.livingData[this.channelId] &&this.$store.state.livingData[this.channelId].LivingData) ||
                {
                    showAuxVideoDomList:[]
                };
            console.error(data);
            return data;
        }
    },
    mounted() {
        // 获取URL参数中的token
        this.getTokenFromUrl();

        // 监听全局点击事件
        document.addEventListener("click", this.handleClickOutside);
        // 监听全屏状态变化
        document.addEventListener("fullscreenchange", this.handleFullscreenChange);
        document.addEventListener("webkitfullscreenchange", this.handleFullscreenChange);
        document.addEventListener("mozfullscreenchange", this.handleFullscreenChange);
        document.addEventListener("MSFullscreenChange", this.handleFullscreenChange);
    },
    beforeDestroy() {
        // 销毁前移除事件监听
        document.removeEventListener("click", this.handleClickOutside);
        document.removeEventListener("fullscreenchange", this.handleFullscreenChange);
        document.removeEventListener("webkitfullscreenchange", this.handleFullscreenChange);
        document.removeEventListener("mozfullscreenchange", this.handleFullscreenChange);
        document.removeEventListener("MSFullscreenChange", this.handleFullscreenChange);
    },
    methods: {
        // 获取URL参数中的token
        getTokenFromUrl() {
            // 获取URL查询参数中的token
            const token = this.$route.query.token;
            const nickname = this.$route.query.nickname || '';
            if (token) {
                this.urlToken = token;
                this.nickname = nickname || '';
                console.log('从URL获取到的token:', token);
                // 获取到token后立即请求直播信息
                this.getLiveInfo();
            } else {
                console.warn('URL中未找到token参数');
            }
        },

        // 请求直播信息接口
        async getLiveInfo() {
            if (!this.urlToken) {
                console.error('urlToken为空，无法请求直播信息');
                return;
            }
            let port = window.location.port ? ':' + window.location.port : '';
            let server_type = {
                hostname: window.location.hostname,
                protocol: window.location.protocol + '//',
                host: window.location.hostname,
                port: port,
                enable_sms_identification: true,
                websocket_protocol: window.location.protocol === 'https:' ? 'wss://' : 'ws://',
                websocket_prot: window.location.protocol === 'https:' ? ':443' : port,
            };
            try {
                const res = await axios.post(`${server_type.protocol}${server_type.host}${server_type.port}/qclive`, {
                    method: "qclive.get.live.info",
                    bizContent: {
                        "mr_token": this.urlToken,
                    }
                });

                console.log('直播信息接口响应:', res.data);

                if (res.data && res.data.error_code === 0) {
                    // 处理成功响应
                    this.appId = res.data.data.appId;
                    this.channelId = res.data.data.channelId;
                    this.auxInfo = res.data.data.auxInfo || {};
                    this.mainInfo = res.data.data.mainInfo || {};
                    this.socketAddress = server_type.websocket_protocol+server_type.host+server_type.websocket_prot+res.data.data.socketAddress;
                    this.$nextTick(() => {
                        this.startJoinRoom();
                    });
                } else {
                    console.error('获取直播信息失败:', res.data);
                    this.$message.error('获取直播信息失败');
                }
            } catch (error) {
                console.error('请求直播信息接口出错:', error);
                this.$message.error('请求直播信息接口出错');
            }
        },
        async initLiveRoomObj(channelId) {
            if (window.CLiveRoomWeb) {
                if (!window.CLiveRoomWeb[channelId]) {
                    window.CLiveRoomWeb[channelId] = new CLiveRoomWeb({
                        main_dom: "main-stream",
                        aux_dom_list: ["video-stream-dom1", "video-stream-dom2", "video-stream-dom3"],
                        channelId,
                    });
                }
            } else {
                window.CLiveRoomWeb = {};
                window.CLiveRoomWeb[channelId] = new CLiveRoomWeb({
                    main_dom: "main-stream",
                    aux_dom_list: ["video-stream-dom1", "video-stream-dom2", "video-stream-dom3"],
                    channelId,
                });
            }
            this.liveRoom = window.CLiveRoomWeb[channelId];
            console.error(this.liveRoom);
            // 监听 liveRoom.data 的变化
            this.observeDataChanges();
            this.liveRoom.event.off("HandleNotifyLeaveChannelAux");
            this.liveRoom.event.on("HandleNotifyLeaveChannelAux", this.HandleNotifyLeaveChannelAux);

            this.liveRoom.event.off("HandleNotifyJoinChannelAux");
            this.liveRoom.event.on("HandleNotifyJoinChannelAux", this.HandleNotifyJoinChannelAux);

            this.liveRoom.event.off("HandleDisconnectAux");
            this.liveRoom.event.on("HandleDisconnectAux", this.HandleDisconnectAux);
            this.$root.eventBus.$off("startJoinRoom").$on("startJoinRoom", this.startJoinRoom);
        },
        //禁用或启用摄像头
        openCamera: Tool.throttle(
            async function (isSwitch) {
                this.isHandlingCamera = true;
                try {
                    await this.liveRoom.MuteLocalVideoStream({
                        isMute: !isSwitch,
                    });
                } catch (error) {
                    console.error(error);
                } finally {
                    this.isHandlingCamera = false;
                }
            },
            1000,
            true
        ),
        //启用或禁用麦克风
        openMicrophone: Tool.throttle(
            async function (isSwitch) {
                this.isHandlingMic = true;
                try {
                    await this.liveRoom.MuteLocalAudioStream({
                        isMute: !isSwitch,
                    });
                } catch (error) {
                    console.error(error);
                } finally {
                    this.isHandlingMic = false;
                }
            },
            800,
            true
        ),
        // 客户离开信道
        async leaveChannel() {
            this.liveRoom.LeaveChannelAux();
        },
        forceLeaveChannel() {
            this.liveRoom.LeaveChannelAux("sender");
        },
        startJoinRoom: Tool.debounce(
            async function (callback) {
                try {
                    this.isConferenceJoining = true;
                    await this.initLiveRoomObj(this.channelId);
                    await this.joinChannel();
                    this.isConferenceJoining = false;
                    callback && callback(true);
                } catch (error) {
                    console.error(error);
                    this.isConferenceJoining = false;
                    callback && callback(false, error);
                }
            },
            800,
            true
        ),
        // 清理直播房间的统一方法
        cleanupLiveRoom(data) {
            console.error('cleanupLiveRoom',data)
            // 防止重复清理
            if (this.isCleaningUp) {
                return;
            }
            this.isCleaningUp = true;
            this.isLeaveChannel = true
            console.log("开始清理直播房间资源");
            if(data.action !== 'error'){
                this.$message.success("直播已经结束，请关闭网页");
            }else{
                this.$message.error("直播异常结束，请尝试重新刷新页面");
            }

            // 重置状态
            this.isConferenceJoining = false;
            this.isShowCloseConferenceBtn = false;
            this.isShowLeaveConferenceBtn = false;

            // 发出离开频道事件
            this.$emit("leaveChannelAux");

            // 异步清理 DOM 和数据绑定
            setTimeout(() => {
                this.unbindDataChanges();

                // 重置清理标志
                this.isCleaningUp = false;
                console.log("直播房间资源清理完成");
            }, 0);
        },

        HandleNotifyLeaveChannelAux(data) {
            this.cleanupLiveRoom(data);
        },
        HandleNotifyJoinChannelAux() {
            console.log("HandleNotifyJoinChannelAux");
        },
        HandleDisconnectAux() {
            console.error("HandleDisconnectAux", 1);
            this.isConferenceJoining = false;
        },
        showVideoMember(value) {
            this.speechPanelVisible = value;
        },
        openMemberList() {
            if (!this.showMemberList) {
                this.showMemberList = true;
            } else {
                this.showMemberList = false;
            }
        },
        closeExtra() {
            this.showMemberList = false;
        },
        maxOrMin() {
            const dom = document.querySelector(".main_live_content_container");
            this.fullScreen(dom);
        },
        fullScreen(dom) {
            // 检查是否全屏
            const isCurrentlyFullScreen =
                document.fullscreenElement ||
                document.webkitFullscreenElement ||
                document.mozFullScreenElement ||
                document.msFullscreenElement;

            if (isCurrentlyFullScreen) {
                // 退出全屏
                if (document.exitFullscreen) {
                    document.exitFullscreen(); // 标准写法
                } else if (document.webkitExitFullscreen) {
                    document.webkitExitFullscreen(); // Webkit 浏览器（Chrome、Safari）
                } else if (document.mozCancelFullScreen) {
                    document.mozCancelFullScreen(); // Firefox
                } else if (document.msExitFullscreen) {
                    document.msExitFullscreen(); // IE 浏览器
                } else {
                    console.warn("退出全屏失败：不支持该浏览器的退出全屏 API");
                }
                this.isFullScreen = false;
            } else {
                // 进入全屏
                if (dom.requestFullscreen) {
                    dom.requestFullscreen(); // 标准写法
                } else if (dom.webkitRequestFullscreen) {
                    dom.webkitRequestFullscreen(); // Webkit 浏览器（Chrome、Safari）
                } else if (dom.mozRequestFullScreen) {
                    dom.mozRequestFullScreen(); // Firefox
                } else if (dom.msRequestFullscreen) {
                    dom.msRequestFullscreen(); // IE 浏览器
                } else {
                    console.warn("进入全屏失败：不支持该浏览器的全屏 API");
                }
                this.isFullScreen = true;
            }
        },
        observeDataChanges() {
            // 防抖函数用于批量替换 roomUserMap
            const debouncedUpdateRoomUserMap = Tool.debounce(() => {
                // 防抖函数内部执行深拷贝，确保拷贝最新的 roomUserMap
                // const newRoomUserMap = cloneDeep(this.liveRoom.data.roomUserMap);
                this.$store.commit("livingData/setRoomUserMapData", {
                    channelId: this.channelId,
                    data: { roomUserMap: this.liveRoom.data.roomUserMap },
                });
            }, 150); // 设置防抖时间为 50ms，可以根据需求调整

            // 代理 liveRoom.data 对象
            const originData = cloneDeep(this.liveRoom.data);
            this.liveRoom.data = Tool.deepReactive(originData, (target, key, value, action, path) => {
                // 检测是否是 roomUserMap 的更新
                if (path.includes("roomUserMap")) {
                    if (action === "set" || action === "delete") {
                        // 使用防抖函数更新 roomUserMap
                        debouncedUpdateRoomUserMap();
                    }
                } else {
                    // 处理其他路径的更新
                    if (action === "set") {
                        this.$store.commit("livingData/setLiveRoomData", { channelId: this.channelId, data: { path, value } });
                    } else if (action === "delete") {
                        this.$store.commit("livingData/deleteLiveRoomData", { channelId: this.channelId, data: { path } });
                    }
                }

                // 更新本地状态
                if (this.liveRoom.data) {
                    this.living_time = this.liveRoom.data.living_time;
                    if (this.liveRoom.data.joinedAux && this.liveRoom.data.losing_connect_server) {
                        this.isReconnectChannel = true;
                    } else {
                        this.isReconnectChannel = false;
                    }
                    if (!this.liveRoom.data.joinedAux) {
                        this.showMemberList = false;
                    }
                }
            });
            // 初始同步一次数据
            this.$store.commit("livingData/replaceLiveRoomData", {
                channelId: this.channelId,
                data: cloneDeep(this.liveRoom.data),
            });
        },
        unbindDataChanges() {
            this.liveRoom.data = cloneDeep(this.liveRoom.data);
        },
        findVideoStreamDomShow(dom) {
            // 查找 showVideoDomList 中 id 匹配的对象及其索引
            const index = this.LivingData.showAuxVideoDomList.findIndex((item) => item.dom === dom);
            if (index !== -1) {
                return true;
            }

            return false;
        },
        getCurrentVideoStreamNum() {
            const videoDomNum1 = this.findVideoStreamDomShow("video-stream-dom1") ? 1 : 0;
            const videoDomNum2 = this.findVideoStreamDomShow("video-stream-dom2") ? 1 : 0;
            const videoDomNum3 = this.findVideoStreamDomShow("video-stream-dom3") ? 1 : 0;
            return videoDomNum1 + videoDomNum2 + videoDomNum3;
        },
        openSetting() {
            this.showLiveSetting = true;
        },
        handleShowMicrophonesList() {
            if (!this.showMicrophonesList) {
                this.liveRoom
                    .getMicrophonesList()
                    .then((res) => {
                        this.microphonesList = res;
                        this.selectedMicrophone = this.getSelectedMicrophone();
                        if (this.microphonesList && this.microphonesList.length > 0) {
                            this.$nextTick(() => {
                                this.showMicrophonesList = true;
                            });
                        }
                    })
                    .catch((error) => {
                        this.microphonesLis = [];
                        this.selectedMicrophone = {};
                    });
            } else {
                this.showMicrophonesList = false;
            }
        },
        async switchMicrophone(deviceId) {
            this.showMicrophonesList = false;
            await this.liveRoom.switchMicrophone(deviceId);
            this.selectedMicrophone = this.getSelectedMicrophone();
        },
        handleShowCamerasList() {
            if (!this.showCamerasList) {
                this.liveRoom
                    .getCamerasList()
                    .then((res) => {
                        this.camerasList = res;
                        this.selectedCamera = this.getSelectedCamera();
                        if (this.camerasList && this.camerasList.length > 0) {
                            this.$nextTick(() => {
                                this.showCamerasList = true;
                            });
                        }
                    })
                    .catch((error) => {
                        this.camerasList = [];
                        this.selectedCamera = {};
                    });
            } else {
                this.showCamerasList = false;
            }
        },
        getSelectedMicrophone() {
            if (this.liveRoom.getCurrentMicrophoneInfo()) {
                return this.liveRoom.getCurrentMicrophoneInfo();
            } else if (this.liveRoom.getMicrophoneSettingFromLocalStorage()) {
                const info = this.liveRoom.getMicrophoneSettingFromLocalStorage();
                if (this.microphonesList.find((item) => item.deviceId === info.deviceId)) {
                    return info;
                }
            }
            return {};
        },
        getSelectedCamera() {
            if (this.liveRoom.getCurrentCameraInfo()) {
                return this.liveRoom.getCurrentCameraInfo();
            } else if (this.liveRoom.getCameraSettingFromLocalStorage()) {
                const info = this.liveRoom.getCameraSettingFromLocalStorage();
                if (this.camerasList.find((item) => item.deviceId === info.deviceId)) {
                    return info;
                }
            }
            return {};
        },
        async switchCamera(deviceId) {
            this.showCamerasList = false;
            await this.liveRoom.switchCamera(deviceId);
            this.selectedCamera = this.getSelectedCamera();
        },
        handleClickOutside(event) {
            if (this.$refs.microphonesPopover) {
                const popover = this.$refs.microphonesPopover.$el; // 获取 Popover 元素
                if (popover && !popover.contains(event.target)) {
                    this.showMicrophonesList = false; // 点击外部时关闭弹层
                }
            }
            if (this.$refs.camerasPopover) {
                const popover = this.$refs.camerasPopover.$el; // 获取 Popover 元素
                if (popover && !popover.contains(event.target)) {
                    this.showCamerasList = false; // 点击外部时关闭弹层
                }
            }
        },
        handleFullscreenChange() {
            // 检查当前全屏状态
            const isCurrentlyFullScreen =
                document.fullscreenElement ||
                document.webkitFullscreenElement ||
                document.mozFullScreenElement ||
                document.msFullscreenElement;

            this.isFullScreen = !!isCurrentlyFullScreen;
        },
        joinChannel(){
            return new Promise(async(resolve,reject)=>{
                try {
                    if(this.mainInfo.uid&&this.auxInfo.uid){ //同时有主辅流，先让辅流进
                        this.liveRoom.JoinChannelAux({ //辅流先进房间
                            channelId:this.channelId,//房间名
                            uid:this.auxInfo.uid,
                            token:this.auxInfo.token,
                            appId:this.appId,
                            socketAddress:this.socketAddress,
                            nickname:this.nickname
                        }).then((joinAuxRes)=>{
                            console.error(joinAuxRes,this.mainInfo.uid)
                            resolve(true)
                            if(joinAuxRes&&this.mainInfo.uid){
                                this.liveRoom.JoinChannelMain({// 后主流加入
                                    channelId:this.channelId,//房间名
                                    uid:this.mainInfo.uid,
                                    token:this.mainInfo.token,
                                    appId:this.appId,
                                }).then(()=>{

                                }).catch((error)=>{


                                })

                            }
                        }).catch((error)=>{
                            console.error(error)
                            return reject(error)
                        })
                    }else{
                        if(this.auxInfo.uid){
                            this.liveRoom.JoinChannelAux({
                                channelId:this.channelId,//房间名
                                uid:this.auxInfo.uid,
                                token:this.auxInfo.token,
                                appId:this.appId,
                                socketAddress:this.socketAddress,
                                nickname:this.nickname
                            }).then(()=>{
                                return resolve(true)
                            }).catch((error)=>{
                                return reject(error)
                            })
                        }
                        if(this.mainInfo.uid){
                            this.liveRoom.JoinChannelMain({
                                channelId:this.channelId,//房间名
                                uid:this.mainInfo.uid,
                                token:this.mainInfo.token,
                                appId:this.appId,
                            }).then(()=>{
                                return resolve(true)
                            }).catch((error)=>{
                                return reject(error)
                            })
                        }
                        //
                    }
                } catch(error) {

                    return reject(error)

                }

            })


        }
    },
};
</script>
<style lang="scss">
.main_live_container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    z-index: 999;
    user-select: none;

    .main_live_top_container {
        height: 60px;
        background: #fff;
        position: relative;

        .top_tools_select_window {
            position: absolute;
            right: 400px;
            top: 50%;
            transform: translate(-50%, -50%);
            display: flex;

            .select_window_span {
                font-size: 18px;
                font-weight: 900;
            }

            .el-button {
                margin-left: 60px;
                padding: 8px 40px;

                &.confirm_btn {
                    color: #fff;
                    background-color: rgb(22, 155, 213);

                    &:hover {
                        background-color: rgb(86, 175, 213);
                    }

                    &:active {
                        border-color: rgb(21, 118, 160);
                        background-color: rgb(21, 118, 160);
                    }
                }
            }
        }
        .top_tools_right {
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            i{
                color: #000;
            }
        }
    }
    .main_live_content_container {
        flex: 1;
        display: flex;
        background: #000;
        overflow: hidden;
        .live_player_left {
            flex: 5;
            width: 100%;
            height: 100%;
            border-left: 1px solid #ddd;
            border-right: 1px solid #ddd;
            position: relative;
            #main-stream {
                width: 100%;
                height: 100%;
                background: transparent url("../../../../../static/resource_activity/images/allmute.png") center no-repeat;
                background-color: #000;
            }
        }
        .live_player_right {
            display: flex;
            flex-direction: column;
            flex: 2;
            background: #000;
            .aux_stream_container {
                position: relative;
                flex: 1;
                border-bottom: 1px solid #ddd;
                .aux-stream {
                    width: 100%;
                    height: 100%;
                    background: transparent url("../../../../../static/resource_activity/images/icon-camera-off.png") center
                        no-repeat;
                    background-color: #000;
                    background-size: 40px 40px;
                }
            }
        }
        .live_player_extra {
            flex: 2;
            background: #000;
            position: relative;
            .chat_history {
                background: #ebeef5;
            }
            .iconsearchclose {
                position: absolute;
                right: 10px;
                top: 10px;
                cursor: pointer;
            }
            .managerDialogList {
                width: 100%;
                height: 100%;
                background: #fff;
                padding-top: 40px;
            }
        }
        .plyr--video {
            width: 100%;
            height: 100%;
            .plyr__controls {
                .plyr__controls__item {
                    &:first-child {
                        margin-right: unset;
                    }
                }
            }
        }
    }
    .main_live_bottom_container {
        height: 60px;
        background: #fff;
        display: flex;
        align-items: center;
        position: relative;
        .bottom_tools {
            display: flex;
            width: 100%;
            justify-content: center;
            align-items: center;
            .menu-button {
                width: 65px;
                height: 50px;
                justify-content: center;
                align-items: center;
                display: flex;
                flex-direction: column;
                cursor: pointer;
                margin: 0 10px;
                user-select: none;
                &:hover {
                    background: #ddd;
                }
                .text {
                    width: 65px;
                    height: 15px;
                    font-size: 12px;
                    text-align: center;
                }
            }
            .menu-button-arrow {
                display: flex;
                width: 75px;
                height: 50px;
                justify-content: center;
                display: flex;
                cursor: pointer;
                margin: 0 10px;
                user-select: none;
                align-items: center;
            }
            .menu-button-arrow-left {
                width: 65px;
                height: 100%;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                margin-left: 10px;
                cursor: pointer;
                height: 50px;
                .text {
                    width: 65px;
                    height: 15px;
                    font-size: 12px;
                    text-align: center;
                }
                .icon {
                    width: 65px;
                    height: 25px;
                }
                &:hover {
                    background: #ddd;
                }
            }
            .el-icon-arrow-down {
                height: 50px;
                display: flex;
                justify-content: center;
                align-items: center;
                cursor: pointer;
                color: #333; /* 设置图标颜色为深灰色，确保在白色背景下可见 */

                &:hover {
                    background: #ddd;
                }
            }
            .icon {
                background-size: contain; /* 设置背景图按比例缩放以完全显示 */
                width: 24px; /* 设置容器宽度 */
                height: 24px; /* 设置容器高度 */
            }
            .icon-camera-off {
                background: url("../../../../../static/resource_activity/images/icon-camera-off.png") center no-repeat;
            }
            .icon-camera {
                background: url("../../../../../static/resource_activity/images/icon-camera.png") center no-repeat;
            }
            .icon-microphone {
                background: url("../../../../../static/resource_activity/images/icon-microphone.png") center no-repeat;
            }
            .icon-microphone-off {
                background: url("../../../../../static/resource_activity/images/icon-microphone-off.png") center no-repeat;
            }
            .icon-users {
                background: url("../../../../../static/resource_activity/images/icon-users.png") center no-repeat;
            }
            .icon-desktop {
                background: url("../../../../../static/resource_activity/images/icon-desktop.png") center no-repeat;
            }
            .icon-desktop-off {
                background: url("../../../../../static/resource_activity/images/icon-desktop-off.png") center no-repeat;
            }
            .icon-setting {
                background: url("../../../../../static/resource_activity/images/icon-setting.png") center no-repeat;
                background-size: contain;
            }
        }
        .bottom_tools_right {
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
        }
    }
}
#live_player_main {
    background: none !important;
    position: absolute !important;
    width: 0 !important;
    height: 0 !important;
    right: 0;
    top: 0;
    .el-dialog {
        width: 80vw;
        height: 80vh;
        position: fixed !important;
        margin: 0 !important;
        .el-dialog__header {
            position: relative;
        }
    }
    #main-stream-menu {
        width: 100%;
        height: 50px;
        background-color: #ffffff;
        display: flex;
        justify-content: center;
        border-bottom: 1px solid #ccc;
    }
}

#live_player_remote_aux {
    background: none !important;
    position: absolute !important;
    width: 0 !important;
    height: 0 !important;
    left: 0;
    top: 0;
    .el-dialog {
        width: 300px;
        height: 300px;
        position: fixed !important;
        margin: 0 !important;
        right: 0;
        left: auto;

        .el-dialog__header {
            position: relative;
        }
    }
}
#live_player_local_aux {
    background: none !important;
    position: absolute !important;
    width: 0 !important;
    height: 0 !important;
    left: 0;
    top: 0;
    .el-dialog {
        width: 300px;
        height: 300px;
        position: fixed !important;
        margin: 0 !important;
        right: 0;
        top: 350px;
        left: auto;

        .el-dialog__header {
            position: relative;
        }
    }
    .aux-stream {
        width: 100%;
        height: 100%;
        background: transparent url("../../../../../static/resource_activity/images/icon-camera-off.png") center no-repeat;
        background-color: #000;
    }
}
.live_tools_menu-popper {
    user-select: none;
    .dropdown-menu-ul {
        .dropdown-item {
            line-height: 30px;
            cursor: pointer;
            &.active {
                background: #ccc;
            }
            &:hover {
                background: #ccc;
            }
        }
    }
}
</style>

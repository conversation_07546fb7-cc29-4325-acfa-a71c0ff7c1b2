import cloneDeep from 'lodash/cloneDeep'
import Vue from 'vue'
import enLocale from 'element-ui/lib/locale/lang/en'
import cnLocale from 'element-ui/lib/locale/lang/zh-CN'
import esLocale from 'element-ui/lib/locale/lang/es'
import ptLocale from 'element-ui/lib/locale/lang/pt'
import ruLocale from 'element-ui/lib/locale/lang/ru-RU'
import deLocale from 'element-ui/lib/locale/lang/de'
import frLocale from 'element-ui/lib/locale/lang/fr'
import itLocale from 'element-ui/lib/locale/lang/it'
import elementLocale from 'element-ui/lib/locale'
import enUS from 'vant/es/locale/lang/en-US';
import zhCN from 'vant/es/locale/lang/zh-CN';
import esES from 'vant/es/locale/lang/es-ES';
import ptBR from '@/common/language/vant/pt-BR';
import ruRU from '@/common/language/vant/ru-RU';
import deDE from '@/common/language/vant/de-DE';
import frFR from '@/common/language/vant/fr-FR';
import itIT from '@/common/language/vant/it-IT';
import { Locale } from 'vant';
const initState ={
    statistic:{
    }
}

export default {
    state: cloneDeep(initState),
    mutations: {
        resetStore(state){
            if(window.vm.$store.state){
                Vue.set(window.vm.$store.state,'language',cloneDeep(initState))
            }
        },
        setLanguage(state, valObj) {
            for (let key in valObj) {
                //没有在state初始化的属性需要用set强更新
                Vue.set(state,key,valObj[key])
                if(key === 'currentLanguage'){
                    if(valObj[key] === 'EN'){
                        elementLocale.use(enLocale)
                        Locale.use('en-US', enUS);
                    }else if(valObj[key] === 'CN'){
                        elementLocale.use(cnLocale)
                        Locale.use('zh-CN',zhCN);
                    } else if(valObj[key] === 'ES'){
                        elementLocale.use(esLocale)
                        Locale.use('es-ES', esES);
                    }else if(valObj[key] === 'PTBR'){
                        elementLocale.use(ptLocale)
                        Locale.use('pt-BR', ptBR);
                    }else if(valObj[key] === 'RU'){
                        elementLocale.use(ruLocale)
                        Locale.use('ru-RU', ruRU);
                    }else if(valObj[key] === 'DE'){
                        elementLocale.use(deLocale)
                        Locale.use('de-DE', deDE);
                    }else if(valObj[key] === 'FR'){
                        elementLocale.use(frLocale)
                        Locale.use('fr-FR', frFR);
                    }else if(valObj[key] === 'IT'){
                        elementLocale.use(itLocale)
                        Locale.use('it-IT', itIT);
                    }else{
                        elementLocale.use(enLocale)
                        Locale.use('en-US', enUS);
                    }
                }
            }
        }
    },
    actions: {},
    getters: {}
}

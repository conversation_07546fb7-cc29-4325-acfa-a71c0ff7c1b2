<template>
    <div class="caseInfoItem" v-if="showNode">
        <el-collapse v-model="activeNames" class="caseItemCollapse">
            <el-collapse-item :name="node.id" :key="node.id" v-if="node.nodeType === 'node' && node.id !== '12'">
                <template slot="title">
                    {{ node.title }}
                </template>
                <template v-if="node.field === 'collectionGroup'">
                    <el-radio-group
                        class="collection_group_radio_group"
                        v-model="localActiveOptionId"
                        @change="(value) => handleCollectionGroupOptionChange(value, node)"
                    >
                        <el-radio :label="item.id" v-for="item in node.collectionGroupOptions" :key="item.id" border size="mini">{{
                            item.label
                        }}</el-radio>
                    </el-radio-group>
                </template>
                <template v-if="node.children && node.children.length">
                    <RecursiveCaseItem v-for="child in node.children" :key="child.id" :node="child" @handleCollectionGroupOptionChange="onChildCollectionGroupOptionChange" />
                </template>
            </el-collapse-item>
        </el-collapse>
        <div class="patient_info_item" v-if="node.nodeType === 'element'">
            <div class="item_title">{{ node.title }}：</div>
            <div class="item_value" v-html="renderMeasureItem(node)"></div>
        </div>
    </div>
</template>

<script>
import base from "../../../lib/base";
import { cloneDeep } from "lodash";
import moment from "moment"
export default {
    name: "RecursiveCaseItem",
    mixins: [base],
    props: {
        node: {
            type: Object,
            required: true,
        },
    },
    data() {
        return {
            activeNames: [],
            submitData: {},
            defaultData: {},
            showData: [],
            localState: {},
            localActiveOptionId: null
        };
    },
    computed: {
        showNode() {
            if (this.node.field === 'collectionGroupItem') {
                return this.node.nodeVisible || false;
            } else {
                return true;
            }
        }
    },
    watch: {
        'node.activeCollectionGroupOptionId': {
            immediate: true,
            handler(val) {
                this.localActiveOptionId = val;
            }
        }
    },
    created() {
        this.activeNames = [this.node.id];
    },
    mounted() {},
    methods: {
        updateToView(caseData) {
            this.activeNames = [];
            this.showData = [];
            let index = 1;
            for (let item of caseData) {
                this.activeNames.push(index++);
                this.showData.push(item);
            }
            console.log("showData", this.showData);
        },
        clearAllData() {},
        renderMeasureItem(obj) {
            let result = "";
            const lang = this.lang;
            if (obj.value === "" || obj.value === -1 || obj.value === null) {
                result = `<span>${lang.not_filled}</span>`;
            } else {
                if (obj.controlType === "radio") {
                    for (let option of obj.options) {
                        if (option.id === obj.value) {
                            result = option.label;
                            if (option.requiresRemark && option.remarkValue) {
                                result += `: ${option.remarkValue}`;
                                if (option.remarkUnit) {
                                    result += ` ${option.remarkUnit}`;
                                }
                            }
                            break;
                        }
                    }
                } else if (obj.controlType === "specialCheck"||obj.controlType === "checkbox") {
                    let arr = [];
                    for (let value of obj.value) {
                        for (let option of obj.options) {
                            if (option.id === value) {
                                let optionText = option.label;
                                if (option.requiresRemark && option.remarkValue) {
                                    optionText += `: ${option.remarkValue}`;
                                    if (option.remarkUnit) {
                                        optionText += ` ${option.remarkUnit}`;
                                    }
                                }
                                arr.push(optionText);
                            }
                        }
                    }
                    if (arr.length === 0) {
                        result = `<span>${lang.not_filled}</span>`;
                    } else {
                        result = arr.join(", ");
                    }
                } else if (obj.controlType === "date") {
                    if(obj.field === 'examTime'){
                        result  = moment(obj.value).format("YYYY-MM-DD HH:mm")
                    }else{
                        result  = moment(obj.value).format("YYYY-MM-DD")
                    }
                } else if (obj.controlType === "dateTime") {
                    if (obj.format === 'yyyy') {
                        result = moment(obj.value).format("YYYY");
                    } else if (obj.format === 'yyyy-MM') {
                        result = moment(obj.value).format("YYYY-MM");
                    } else {
                        result = moment(obj.value).format("YYYY-MM-DD HH:mm");
                    }
                } else if (obj.controlType === "select") {
                    for (let option of obj.options) {
                        if (option.id === obj.value) {
                            result = option.label;
                            break;
                        }
                    }
                    if (result === "") {
                        result = `<span>${lang.not_filled}</span>`;
                    }
                } else if (obj.controlType === "string") {
                    result = `${obj.value} ${obj.unit || ''}`;
                } else {
                    result = `${obj.value} ${obj.unit || ''}`;
                }
            }
            return result;
        },
        handleCollectionGroupOptionChange(value, node) {
            console.log("handleCollectionGroupOptionChange", value, node);
            this.$emit("handleCollectionGroupOptionChange", {
                value,
                node,
            });

            this.updateCollectionGroupItemVisibility(node, value);
        },
        onChildCollectionGroupOptionChange(data) {
            this.$emit("handleCollectionGroupOptionChange", data);
        },
        updateCollectionGroupItemVisibility(node, activeId) {
            if (node.children && node.children.length) {
                const newChildren = [...node.children];

                newChildren.forEach(child => {
                    if (child.field === 'collectionGroupItem') {
                        child.nodeVisible = false;
                    }
                });

                const activeChild = newChildren.find(child =>
                    child.field === 'collectionGroupItem' &&
                    child.collectionGroupOptionId === activeId
                );

                if (activeChild) {
                    activeChild.nodeVisible = true;
                }
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.caseInfoItem {
    .collection_group_radio_group {
        margin-top: 20px;
        margin-bottom: 20px;
        :deep(.el-radio) {
            margin-left: 0 !important;
            margin-bottom: 10px !important;
        }
    }

    .caseItemCollapse {
        ::v-deep {
            .is-active {
                .el-collapse-item__header {
                    background-color: #bfd1d1;
                }
            }
            .el-collapse-item {
                user-select: none;
                .patient_info_item {
                    position: relative;
                    display: flex;
                    align-items: center;
                    margin: 10px 0;
                    .item_title {
                        // flex:4;
                        min-width: 240px;
                        font-size: 16px;
                    }
                    .item_value {
                        flex: 3;
                        display: flex;
                        align-items: center;
                        & > span {
                            color: #f00;
                        }
                        .year_unit {
                            color: #000;
                        }
                        i {
                            color: #f3ce3c;
                            font-size: 20px;
                            margin-left: 6px;
                        }
                    }
                    .item_unit {
                        flex: 3;
                        padding-left: 6px;
                        font-size: 16px;
                    }
                }
                .iconright {
                    color: green;
                    margin-left: 10px;
                }
                .el-collapse-item__header {
                    position: relative;
                    font-size: 18px;
                    padding-left: 10px;
                    background-color: #bfd1d1;
                    .ecg_radios {
                        margin-left: 20px;
                    }
                    .iconright {
                        color: green;
                        position: absolute;
                        right: 36px;
                    }
                    .el-button--primary {
                        background-color: #fff;
                        border-color: #fff;
                        color: #606266;
                    }
                }
                .el-collapse-item__content {
                    padding-left: 16px;
                    .el-collapse-item__content {
                        padding-bottom: 0px;
                    }
                    .el-collapse-item__header {
                        padding-left: 0;
                    }
                }
            }
        }
    }
}
</style>

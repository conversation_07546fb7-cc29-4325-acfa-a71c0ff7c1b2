<template>
    <CommonDialog v-model="renameDialog" :title="lang.input_new_name" :showRejectButton="true" :beforeClose="beforeCloseNewNameDialog">
        <van-form  ref="renameDialogForm" validate-trigger="onSubmit" label-width="0">
            <van-field
                v-model="renameDialogForm.renameValue"
                :placeholder="`${lang.input_info_check_err}${lang.length_limit_info}`"
                name="renameValue"
                :rules="[
                { validator:validatorRenameValue, message: `${lang.input_info_check_tip}`}
                ]"
                @blur.stop
            />
        </van-form>
    </CommonDialog>
</template>
<script>
import base from '../lib/base'
import { Toast } from 'vant';
import {Dialog,Form,Field } from 'vant';
import CommonDialog from '../MRComponents/commonDialog.vue'
export default {
    model:{
        prop:"value",
        event:'change'
    },
    props:{
        value:{
            type:<PERSON><PERSON><PERSON>,
            default:false
        },
        resource_id:{
            type: [Number,String],
            default:'0'
        },
        renameCid:{
            type:[Number,String],
            default:'0'
        }
    },
    mixins: [base],
    name: 'rename_dialog',
    components: {
        CommonDialog,
        VanForm:Form,
        VanField:Field,
    },
    watch:{
        value: {
            handler(val) {
                this.renameDialog = val
            },
            immediate:true
        },
    },
    data(){
        return {
            cid:this.$route.params.cid,
            renameDialogForm:{
                renameValue:''
            },
            renameDialog:false
        }
    },
    beforeDestroy(){
    },
    created(){
        this.cid=this.$route.params.cid
    },
    mounted(){
        this.$nextTick(()=>{
        })
    },
    computed:{

    },
    methods:{
        updateResourceDes(){
            return new Promise((resolve,reject)=>{
                let cid = '0'
                if(String(this.renameCid)!=='0'){
                    cid = this.renameCid
                }else{
                    cid = this.cid
                }
                var params = {cid:cid, resource_id:this.resource_id, des:this.renameDialogForm.renameValue};
                console.log("params ", params)
                this.conversationList[cid].socket.emit("update_resource_des", params, (is_succ, data)=> {
                    if (is_succ) {
                        this.$emit('success')
                        resolve(true)
                        Toast(this.lang.update_success_text);
                        console.log("update success ")

                    }  else {
                        this.$emit('failed')
                        reject(this.lang.update_failed_text)
                        Toast(this.lang.update_failed_text);
                        console.log("update failed ",  data)

                    }
                });

            })
        },
        validatorRenameValue(val){
            let reg = /^[A-Za-z0-9\u4e00-\u9fa5]{1,20}$/
            if(reg.test(val)){
                return true
            }
            return false
        },
        async beforeCloseNewNameDialog(action,done){
            if(action ==='confirm'){
                try {
                    await this.$refs.renameDialogForm.validate(Object.keys(this.renameDialogForm))
                    await this.updateResourceDes()
                    this.renameDialogForm.renameValue = ''
                    this.$refs.renameDialogForm.resetValidation(Object.keys(this.renameDialogForm))
                    this.$emit("change",false)
                    done()
                } catch (error) {
                    done(false)
                }
            }else{
                this.renameDialogForm.renameValue = ''
                this.$refs.renameDialogForm.resetValidation(Object.keys(this.renameDialogForm))
                this.$emit("change",false)
                done()
            }
        },
        renameImageDes(){
            this.renameDialog = true
        },
    }
}
</script>


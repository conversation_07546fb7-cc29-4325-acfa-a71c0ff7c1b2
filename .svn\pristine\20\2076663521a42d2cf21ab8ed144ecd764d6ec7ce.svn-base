<template>
    <div class="download_manager">
        <el-button
            icon="el-icon-download"
            circle
            type="primary"
            class="downLoadButton"
            size="large"
            @click="showDownloadTaskVisible"
            v-if="downloadTasksArray.length > 0 && !downloadTaskVisible"
        >
            <!-- <span class="down_num">{{ getUnSuccessfulTaskLength }}</span> -->
        </el-button>
        <CommonDialog
            :title="lang.download_task_manager"
            :show.sync="downloadTaskVisible"
            @closed="closeDownloadTaskVisible"
            :modal="false"
            :close-on-click-modal="false"
            class="downloadTaskDialog"
            style="z-index: 2001"
            :foot-show="false"
        >
            <div class="table-tools">
                <el-popconfirm
                    :title="lang.continue_downloading_ask"
                    @confirm="resumeAllTask"
                    v-if="hasErrorTask"
                    class="table-tools-item"
                >
                    <el-button size="small" slot="reference">{{ lang.batch_continue_downloading }}</el-button>
                </el-popconfirm>
                <el-popconfirm
                    icon="el-icon-info"
                    icon-color="red"
                    :title="lang.cancel_tasks_ask"
                    @confirm="cancelAllTask"
                    v-if="hasUnSuccessfulTask"
                    class="table-tools-item"
                >
                    <el-button slot="reference" size="small">{{ lang.batch_cancel_download }}</el-button>
                </el-popconfirm>
            </div>
            <el-table :data="downloadTasksArray" border row-key="id" height="380">
                <el-table-column type="index"> </el-table-column>
                <el-table-column
                    :label="lang.file_name"
                    show-overflow-tooltip
                    prop="filename"
                ></el-table-column>
                <el-table-column :label="lang.file_download_progress">
                    <template slot-scope="{ row }">
                        <el-progress :percentage="row.percent" :stroke-width="18" text-inside></el-progress>
                    </template>
                </el-table-column>
                <el-table-column :label="lang.save_directory" show-overflow-tooltip>
                    <template slot-scope="{ row }">
                        <p class="longwrap clickLink" @click="openDir(row.dest)">{{ row.dest }}</p>
                    </template>
                </el-table-column>
                <el-table-column :label="lang.status" show-overflow-tooltip>
                    <template slot-scope="{ row }">
                        <span v-if="row.paused">{{ lang.paused }}</span>
                        <span v-if="row.error">{{ lang.failed }}</span>
                        <span v-if="row.finished">{{ lang.success }}</span>
                    </template>
                </el-table-column>
                <el-table-column :label="lang.operation">
                    <template slot-scope="{ row }">
                        <el-button
                            type="text"
                            size="small"
                            @click="pauseTask(row)"
                            v-if="!row.finished && !row.error && !row.paused"
                            >{{ lang.pause_download }}</el-button
                        >
                        <el-button type="text" size="small" @click="resumeTask(row)" v-if="row.error || row.paused">{{
                            lang.continue_downloading
                        }}</el-button>
                        <el-button type="text" size="small" @click="cancelTask(row)" v-if="!row.finished">{{
                            lang.cancel_btn
                        }}</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </CommonDialog>
        <CommonDialog
            class="download_image_dialog"
            :title="lang.download_title"
            :show.sync="directoryVisible"
            :close-on-click-modal="false"
            width="40%"
            :append-to-body="true"
            :modal="false"
            @closed="handleAfterCloseDirectoryVisible"
            @open="handleAfterOpenDirectoryVisible"
            @submit="addDownloadTask"
            :submitText="lang.download_title"
        >
            <div class="container clearfix">
                <div class="item" v-if="needsInputFileName">
                    <div class="title">{{ lang.download_name }}</div>
                    <div class="form_item">
                        <el-input v-model="downloadFileName" @blur="validateFileName">
                            <template slot="append">{{ downLoadFileType }}</template>
                        </el-input>
                    </div>
                </div>
                <div class="item">
                    <div class="title">{{ lang.download_directory }}</div>
                    <div class="form_item">
                        <el-input v-model.trim="downloadDirectory" :disabled="true"></el-input>
                    </div>
                    <el-button @click="openChooseDirectory" class="choose_directory" type="success" size="small">{{
                        lang.download_choose_btn
                    }}</el-button>
                </div>
            </div>
        </CommonDialog>
    </div>
</template>
<script>
import base from "../lib/base";
import Tool from "@/common/tool";
import CommonDialog from "../MRComponents/commonDialog.vue"
export default {
    name: "DownLoadManager",
    mixins: [base],
    inheritAttrs: true,
    props: {},
    components:{
        CommonDialog
    },
    data() {
        return {
            downloadDirectory: "",
            downloadFileName: "",
            downLoadFileType:"",
            taskMap: {},
            downloading: false,
            downloadTaskVisible: false,
            directoryVisible: false,
            needsInputFileName: false,
            currentDownloadingArray: [],
            addTaskArray: [],
            maxDownloadLength: 5,
            hasErrorTask: false,
            hasUnSuccessfulTask: false,
        };
    },
    computed: {
        // getUnSuccessfulTaskLength() {
        //     let length = 0;
        //     Object.keys(this.taskMap).forEach((id) => {
        //         if (this.taskMap[id].percent !== 100) {
        //             length++;
        //         }
        //     });
        //     return length
        // },
        downloadTasksArray() {
            let errorTaskNum = 0;
            let unSuccessfulTaskNum = 0;
            let downloadTasksArray = [];
            downloadTasksArray = Object.entries(this.taskMap).map(([key, value]) => {
                if (value.error === true || value.paused === true) {
                    errorTaskNum++;
                }
                if (!value.finished) {
                    unSuccessfulTaskNum++;
                }
                return {
                    id: key,
                    ...value,
                };
            });
            if (errorTaskNum > 0) {
                this.hasErrorTask = true;
            } else {
                this.hasErrorTask = false;
            }

            if (unSuccessfulTaskNum > 0) {
                this.hasUnSuccessfulTask = true;
            } else {
                this.hasUnSuccessfulTask = false;
            }
            return downloadTasksArray;
        },
    },
    created() {},
    mounted() {
        this.$root.eventBus.$off("NotifyExportFileList").$on("NotifyExportFileList", this.NotifyExportFileList);
        this.$root.eventBus.$off("createDownLoadTask").$on("createDownLoadTask", this.createDownLoadTask);
        this.$root.eventBus.$off("cancelDownLoadTask").$on("cancelDownLoadTask", this.cancelAllTask);
        this.$root.eventBus.$off("closeDownloadTaskVisible").$on("closeDownloadTaskVisible", this.closeDownloadTaskVisible);
    },
    methods: {
        openChooseDirectory() {
            Tool.createCWorkstationCommunicationMng({
                name: "openDirectory",
                emitName: "NotifyOpenDirectory",
                params: {},
                timeout: null,
            }).then((res) => {
                this.downloadDirectory = res;
            });
        },
        createDownLoadTask({ downLoadUrlList = [], needsInputFileName = false } = {}) {
            this.directoryVisible = true;
            this.needsInputFileName = needsInputFileName;
            this.addTaskArray = []
            if (needsInputFileName && downLoadUrlList.length === 1) {
                let fileType = Tool.getFileType(downLoadUrlList[0].src)
                if (fileType === "m3u8") {
                    //m3u8需要让原生转换成mp4下载
                    fileType = "mp4";
                }
                this.downLoadFileType = `.${fileType}`
                if(downLoadUrlList[0].filename){
                    this.downloadFileName = downLoadUrlList[0].filename.replace(this.downLoadFileType,'');
                }
                this.addTaskArray = [{...downLoadUrlList[0],filename:this.downloadFileName}]
            }else{
                this.addTaskArray =downLoadUrlList.map((item)=>{
                    let fileType = Tool.getFileType(item.src)
                    let fileTypeStr = `.${fileType}`
                    if(item.filename){
                        item.filename = item.filename.replace(fileTypeStr,'');
                    }

                    return item
                })
            }
        },
        validateFileName(){
            if (!this.downloadFileName) {
                return;
            }
            if (!Tool.validateIllegalCharacters(this.downloadFileName) && this.needsInputFileName) {
                this.$message.error(this.lang.filename_illegal_characters);
                return;
            }
        },
        async addDownloadTask() {
            if (this.needsInputFileName && this.downloadFileName == "") {
                this.$message.error(this.lang.download_image_name);
                return;
            }
            if (this.downloadDirectory == "") {
                this.$message.error(this.lang.download_directory_empty);
                return;
            }
            if (!Tool.validateIllegalCharacters(this.downloadFileName) && this.needsInputFileName) {
                this.$message.error(this.lang.filename_illegal_characters);
                return;
            }
            let repeatMap={};
            await Promise.all(this.addTaskArray.map(async(item) => {
                console.log('addDownloadTask')
                let sourceType = Tool.getFileType(item.src);
                let targetType = sourceType;
                let exportId = Tool.genID();
                let filename = this.downloadFileName||item.filename || exportId;
                //同标签名文件加下标
                if (repeatMap[filename+targetType] > 0) {
                    repeatMap[filename+targetType] += 1;
                    filename = filename +'_' + repeatMap[filename+targetType];
                }else{
                    repeatMap[filename+targetType] = 1;
                }
                if (sourceType === "m3u8") {
                    //m3u8需要让原生转换成mp4下载
                    targetType = "mp4";
                }
                // if (sourceType === "ai"&& item.msg_type==this.systemConfig.msg_type.OBAI) {
                //     //ai需要让原生转换成jpg下载
                //     targetType = "jpg";
                // }
                let fullFileName = `${filename}.${targetType}`
                if(this.addTaskArray.length === 1 && fullFileName.indexOf('no_safe_name') === -1){ //单个文件下载时，获取安全的下载文件名
                    const res = await Tool.createCWorkstationCommunicationMng({
                        name: "GetSafeDownloadName",
                        emitName: "NotifyGetSafeDownloadName",
                        params: {dest:this.downloadDirectory,file_name:fullFileName},
                        timeout: null,
                    })
                    fullFileName = res.file_name
                }
                let dest = `${this.downloadDirectory}/${fullFileName}`;
                if (item.dest_name) {
                    dest = `${this.downloadDirectory}/${item.dest_name}/${fullFileName}`;
                }
                let downLoadUrl = item.src
                if(this.systemConfig.serverInfo.network_environment === 1){
                    downLoadUrl = Tool.replaceInternalNetworkEnvImageHost(downLoadUrl)
                }
                downLoadUrl = Tool.encodeURLPath(downLoadUrl)
                let params = {
                    src: downLoadUrl,
                    dest,
                    exportId: exportId,
                    targetType,
                    sourceType,
                };
                this.$set(this.taskMap, params.exportId, {});
                this.$set(this.taskMap[params.exportId], "percent", 0);
                this.$set(this.taskMap[params.exportId], "finished", false);
                this.$set(this.taskMap[params.exportId], "error", false);
                this.$set(this.taskMap[params.exportId], "paused", false);
                this.$set(this.taskMap[params.exportId], "dest", dest);
                this.$set(this.taskMap[params.exportId], "filename", fullFileName);
                this.$set(this.taskMap[params.exportId], "downloadParams", params);
            }));
            this.directoryVisible = false;
            this.downloadTaskVisible = true;
            this.checkDownLoading();
        },
        checkDownLoading(fromTop = false) {
            this.currentDownloadingArray = this.currentDownloadingArray.filter(
                (item) =>
                    this.taskMap[item.id] &&
                    !this.taskMap[item.id].finished &&
                    !this.taskMap[item.id].error &&
                    !this.taskMap[item.id].paused
            );
            const tasksToAdd = this.maxDownloadLength - this.currentDownloadingArray.length;
            if (tasksToAdd > 0) {
                const tasksToAddArray = [];
                let count = 0;
                for (const task of this.downloadTasksArray) {
                    if (!task.error && !task.finished && !task.paused) {
                        if (fromTop) {
                            //是否要从头开始判断下载
                            tasksToAddArray.push(task);
                            count++;
                        } else {
                            if (task.percent === 0) {
                                tasksToAddArray.push(task);
                                count++;
                            }
                        }
                        if (count === tasksToAdd) {
                            break;
                        }
                    }
                }
                if (tasksToAddArray.length > 0) {
                    const urlList = tasksToAddArray.map((item) => item.downloadParams);
                    window.CWorkstationCommunicationMng.ExportFileList({ fileList: urlList });
                }
                this.currentDownloadingArray.push(...tasksToAddArray);
            }
        },
        NotifyExportFileList(res) {
            const fileList = res.fileList;
            if (Array.isArray(fileList)) {
                fileList.forEach((item) => {
                    if (item.error_code === 0) {
                        if (this.taskMap.hasOwnProperty(item.data.exportId)) {
                            this.taskMap[item.data.exportId].percent = item.data.progress;
                            this.taskMap[item.data.exportId].error = false;
                            this.taskMap[item.data.exportId].paused = false;
                        }
                    } else if (item.error_code === 1) {
                        if (this.taskMap.hasOwnProperty(item.data.exportId)) {
                            this.taskMap[item.data.exportId].error = true;
                            this.taskMap[item.data.exportId].paused = false;
                        }
                    } else if (item.error_code === 4) {
                        if (this.taskMap.hasOwnProperty(item.data.exportId)) {
                            this.taskMap[item.data.exportId].error = false;
                            this.taskMap[item.data.exportId].paused = true;
                        }
                    } else if (item.error_code === 200) {
                        this.taskMap[item.data.exportId].percent = item.data.progress;
                        this.taskMap[item.data.exportId].error = false;
                        this.taskMap[item.data.exportId].paused = false;
                        this.taskMap[item.data.exportId].finished = true;
                    }
                });
            }
            this.checkDownLoading();
        },
        handleAfterOpenDirectoryVisible() {
            // if (this.needsInputFileName && this.addTaskArray.length > 0) {
            //     this.downloadFileName = this.addTaskArray[0].filename;
            // }
        },
        handleAfterCloseDirectoryVisible() {
            this.downloadDirectory = "";
            this.downloadFileName = "";
            this.directoryVisible = false;
            this.$emit("close");
        },
        cancelTask(row) {
            this.$confirm(this.lang.cancel_download_confirm, this.lang.tip_title, {
                confirmButtonText: this.lang.confirm_button_text,
                cancelButtonText: this.lang.cancel_button_text,
                type: "warning",
            })
                .then(() => {
                    if (!row.finished) {
                        let cancelIds = [row.id];
                        window.CWorkstationCommunicationMng.CancelExportFileList({ fileList: cancelIds });
                        this.$delete(this.taskMap, row.id);
                    }
                })
                .catch(() => {});
        },
        showDownloadTaskVisible() {
            this.downloadTaskVisible = true;
        },
        closeDownloadTaskVisible() {
            this.downloadTaskVisible = false;
        },
        openDir(dest) {
            if (dest) {
                Tool.createCWorkstationCommunicationMng({
                    name: "RequestOpenLocalDirectory",
                    emitName: "NotifyRequestOpenLocalDirectory",
                    params: { path: dest },
                    timeout: null,
                }).then((res) => {
                    if (res.error_code === 1) {
                        this.$message.error(this.lang.open_directory_failed_tips);
                    }
                });
            }
        },
        resumeTask(row) {
            this.taskMap[row.id].error = false;
            this.taskMap[row.id].paused = false;
            // row.error = false;
            // row.paused = false
            window.CWorkstationCommunicationMng.ExportFileList({ fileList: [row.downloadParams] });
        },
        pauseTask(row) {
            if (!row.finished) {
                let cancelIds = [row.id];
                window.CWorkstationCommunicationMng.PauseExportFileList({ fileList: cancelIds });
            }
        },
        resumeAllTask() {
            Object.keys(this.taskMap).forEach((id) => {
                if (!this.taskMap[id].finished) {
                    this.taskMap[id].error = false;
                    this.taskMap[id].paused = false;
                }
            });
            this.checkDownLoading(true);
        },
        cancelAllTask() {
            let cancelIds = [];
            Object.keys(this.taskMap).forEach((id) => {
                if (!this.taskMap[id].finished) {
                    cancelIds.push(id);
                    this.$delete(this.taskMap, id);
                }
            });
            if (cancelIds.length === 0) {
                return;
            }
            window.CWorkstationCommunicationMng.CancelExportFileList({ fileList: cancelIds });
        },
    },
};
</script>
<style lang="scss" scoped>
.download_manager {

    .downLoadButton {
        position: fixed;
        bottom: 30px;
        right: 30px;
        z-index: 10;
        width: 80px;
        height: 80px;
        ::v-deep {
            .el-icon-download {
                font-size: 26px;
            }
            span {
                margin-left: 0;
            }
        }
        .down_num {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: red;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #fff;
            position: absolute;
            font-size: 12px;
            top: -15px;
            right: 0;
        }
    }
}
</style>
<style lang="scss">
    .downloadTaskDialog {
        background: unset;
        position: fixed;
        bottom: 20px;
        right: 20px;
        width: 640px;
        height: 500px;
        top: auto !important;
        left: auto !important;
        border: 2px solid #eee;
        .el-dialog{
            margin: 0 !important;
            bottom: auto !important;
            right: auto !important;
            top: auto !important;
            left: auto !important;
            position: absolute;
            height: 100% !important;
            width: 100%;
            border-radius: 0;
        }
        .table-tools {
            margin: 10px 0;
            .table-tools-item {
                margin-right: 10px;
            }
        }
        ::v-deep {
            .el-dialog {

            }
            .el-table .cell{
                word-break: break-word;
                padding: 0 6px;
                .el-button{
                    white-space: normal;
                }
            }
        }
        .clickLink {
            text-decoration: underline;
            color: #0095ff;
            cursor: pointer;
        }
    }
.download_image_dialog {
    .el-dialog {
        height: auto !important;
    }
    .container {
        .item {
            display: flex;
            margin: 10px 0;
            .title {
                line-height: 40px;
                min-width: 120px;
            }
            .form_item {
                flex: 1;
            }
            .choose_directory {
                margin-left: 10px;
            }
        }
    }
    .process_modal {
        position: absolute;
        z-index: 2;
        background: rgba(256, 256, 256, 0.7);
        width: 100%;
        top: -36px;
        left: 0;
        bottom: 0;
        padding: 20px;
        .el-progress {
            top: 50%;
        }
    }
}
</style>

// Worker 加载 OpenCV.js 库
self.importScripts("./OpencvQr.js");

let opencvQr;
let hostUrl = ''; // 存储主机地址

// 初始化 OpenCV QR 检测器
async function initQrDetector() {
    // 根据主机地址构建模型URL
    const baseUrl = hostUrl;
    
    opencvQr = new OpencvQr({
        dw: `${baseUrl}/javascripts/models/detect.caffemodel`,
        sw: `${baseUrl}/javascripts/models/sr.caffemodel`
    });
    
    await opencvQr.ready;
    
    // 发送就绪消息
    self.postMessage({
        type: "ready",
    });
}

// 延迟初始化，等待主线程发送host信息
let initTimer = null;

// 监听消息，处理图像数据的二维码检测
self.addEventListener("message", (event) => {
    const data = event.data;
    
    // 处理初始化消息
    if (data.type === 'init') {
        hostUrl = data.host;
        // console.log('Worker收到host地址:', hostUrl);
        
        // 清除之前的定时器
        if (initTimer) {
            clearTimeout(initTimer);
        }
        
        initQrDetector().catch(error => {
            self.postMessage({
                type: "error",
                error: "OpenCV 初始化失败: " + error.message,
            });
        });
        
        return;
    }
    
    const { imageData, id } = data;

    if (!imageData || !imageData.data || !imageData.width || !imageData.height) {
        self.postMessage({
            type: "error",
            id,
            error: "无效的图像数据",
        });
        return;
    }

    try {
        if (!opencvQr) {
            throw new Error("OpenCV QR 检测器尚未初始化");
        }

        // 使用 OpenCV 进行检测
        opencvQr.load(imageData);
        const qrCodeDataArray = opencvQr.getInfos();
        
        // 返回结果
        self.postMessage({
            type: "result",
            id,
            hasQRCode: qrCodeDataArray.length > 0,
            qrCodeData: qrCodeDataArray.length > 0 ? qrCodeDataArray[0] : null,
            allQrCodes: qrCodeDataArray, // 返回所有检测到的二维码数据
        });
    } catch (error) {
        console.error("QR码检测错误:", error);
        self.postMessage({
            type: "error",
            id,
            error: error.message || "二维码识别失败",
        });
    }
});

// 设置一个默认的延迟初始化，如果没有收到init消息，仍能工作
initTimer = setTimeout(() => {
    if (!hostUrl) {
        console.log('Worker没有收到host地址，使用默认地址初始化');
        initQrDetector().catch(error => {
            self.postMessage({
                type: "error",
                error: "OpenCV 默认初始化失败: " + error.message,
            });
        });
    }
}, 3000);

<template>
    <div class="push_stream_setting_page second_level_page">
        <mrHeader>
            <template #title>
                {{lang.push_stream_setting}}
            </template>
        </mrHeader>
        <div class="container">
        <van-field name="switch" :label="lang.whether_last_stream_pushing_action" label-width="250" input-align="right" class="setting_item">
            <template #input>
                <van-switch v-model="autoPushStream" size="20" @change="changeAutoPushStreamStatus"/>
            </template>
        </van-field>
        <van-field name="switch" :label="lang.whether_enable_automatic_recording" label-width="250" input-align="right" v-if="autoPushStream" class="setting_item">
            <template #input>
                <van-switch v-model="recordMode" size="20" @change="changeRecordMode"/>
            </template>
        </van-field>
        <van-field name="switch" label="断网重连增强" label-width="250" input-align="right" v-if="$root.isInitVConsole" class="setting_item">
            <template #input>
                <van-switch v-model="networkReconnectEnhanced" size="20" @change="changeNetworkReconnectEnhanced"/>
            </template>
        </van-field>
        <van-cell :title="lang.network_camera_setting" @click="openNetWorkCameraSetting" is-link class="setting_item"></van-cell>
        </div>
    </div>
</template>
<script>
import base from '../lib/base'
import { Field,Switch,Cell } from 'vant';
export default {
    mixins: [base],
    name: 'push_stream_setting',
    components: {
        VanField:Field,
        VanSwitch:Switch,
        VanCell:Cell
    },
    data(){
        return {
            autoPushStream:false,
            recordMode:false,
            networkReconnectEnhanced:false
        }
    },
    created(){
        let isAutoPushStream = window.localStorage.getItem(`isAutoPushStream_${this.user.uid}`)
        let isAutoPushRecord = window.localStorage.getItem(`isAutoPushRecord_${this.user.uid}`)
        let isPushStreamNetworkReconnectEnhanced = window.localStorage.getItem(`isPushStreamNetworkReconnectEnhanced`)
        this.autoPushStream = !!Number(isAutoPushStream)
        this.recordMode = !!Number(isAutoPushRecord)
        this.networkReconnectEnhanced = !!Number(isPushStreamNetworkReconnectEnhanced)
    },
    mounted(){
    },
    methods:{
        changeAutoPushStreamStatus(val){
            window.localStorage.setItem(`isAutoPushStream_${this.user.uid}`,val?1:0)
        },
        changeRecordMode(val){
            window.localStorage.setItem(`isAutoPushRecord_${this.user.uid}`,val?1:0)
        },
        changeNetworkReconnectEnhanced(val){
            window.localStorage.setItem(`isPushStreamNetworkReconnectEnhanced`,val?1:0)
        },
        openNetWorkCameraSetting(){
            window.CWorkstationCommunicationMng.openNetWorkCameraSetting()
        }
    }
}
</script>
<style lang="scss">
.push_stream_setting_page{
    background-color:#fff;
    min-height:26rem;
    .container{
        .setting_item{
            font-size: 0.8rem;
            padding: 0.5rem 0.8rem;
            line-height: 1.2rem;
            color: #323233;
            .van-cell__title{
                color: #323233;
            }
        }
    }
}
</style>

<template>
<div>
	<CommonDialog
      class="invite_registration"
      :title="lang.invite_registration"
      :show.sync="visible"
      :close-on-click-modal="false"
      width="40%"
      :modal="false"
      @closed="back"
      :footShow = "false"
      >
        <div class="referral_code_body">
            <div class="download_app_tip">
                <p class="download_app_tip_text">{{lang.download_app_tip}}</p>
                <div class="qrcode_container">
                  <div class="qrcode" ref="qrCode"></div>
                </div>
            </div>
            <div class="referral_code_content">
                <span class="referral_code_content_left">{{lang.referral_code}}:</span>
                <span class="referral_code_content_right">{{referral_code}}</span>
            </div>
            <div class="referral_code_tip">
                <span>{{lang.referral_code_tip}}</span>
                <span v-show="isShowReferralCodeTipExpire" class="referral_code_tip_expire">{{referral_code_tip_expire}}</span>
            </div>
            <div class="referral_code_tool">
                <el-button
                  :disabled="!enable_generate_referral_code"
                  @click="generateReferralCode"
                  type="primary"
                  size="middle"
                  class="generate_btn">
                  {{lang.referral_code_generate}}
                </el-button>
            </div>
        </div>
    </CommonDialog>
</div>

</template>
<script>
import base from '../lib/base'
import {formatString} from '../lib/common_base'
import Tool from '@/common/tool.js'
import CommonDialog from "../MRComponents/commonDialog.vue"; //3rd change

export default {
    mixins: [base],
    name: 'InviteRegistration',
    components: {CommonDialog},
    data(){
        return {
            PROJECT_NOV:process.env.VUE_APP_PROJECT_NOV,
            referral_code:"",
            referral_code_tip_expire:"",
            enable_generate_referral_code:true,
            visible: false,
            // download_app_qr_image:""
        }
    },
    computed:{
        isShowReferralCodeTipExpire(){
            return 0 < this.referral_code.length;
        }
    },
    mounted(){
        this.$nextTick(()=>{
            // if (this.user.build_version) {
            //     this.download_app_qr_image='static/resource_pc/images/zcm_' + this.user.build_version + '.bmp';
            // } else {
            //     this.download_app_qr_image='static/resource_pc/images/zcm_prod.bmp';
            // }
            this.visible = true;
            this.generateReferralCode();
        })
    },
    methods:{
        generateReferralCode() {
            var that = this;

            that.enable_generate_referral_code = false;

            var time_out = setTimeout(function () {
                that.generateReferralCodeError("time_out");
                that.enable_generate_referral_code = true;
            }, 10000);

            window.main_screen.controller.emit("request_generate_referral_code",{},function (err, result){
                console.log("callback in request_generate_referral_code", err, result);
                clearTimeout(time_out);
                that.enable_generate_referral_code = true;
                if (err) {
                    //失败
                    var error_code = "unknown_err";
                    if ("none_referral_code" == err) {
                        error_code = err;
                    } else if ("database_err" == err) {
                        error_code = err;
                    }
                    that.generateReferralCodeError(error_code);
                } else {
                    //成功
                    if (result && result.referral_code && result.creation_ts) {
                        that.referral_code = result ? result.referral_code : "";
                        that.referral_code_tip_expire = formatString(that.lang.referral_code_tip_expire, {
                            1:result.creation_ts
                        });
                        that.initQrCode();
                    } else {
                        that.generateReferralCodeError("unknown_err");
                    }
                }
            })
        },
        generateReferralCodeError: function (error_code) {
            this.referral_code = "";
            this.referral_code_tip_expire = "";
            this.$message.error(this.lang.referral_code_generate_err[error_code]);
        },
        initQrCode(){
            let user = this.$store.state.user
            this.$refs.qrCode.innerHTML = '';
            let timestamp = Date.parse(new Date())
            let ajaxServer=this.systemConfig.server_type.protocol+this.systemConfig.server_type.host+this.systemConfig.server_type.port;
            let surl = this.systemConfig.server_type.protocol+this.systemConfig.server_type.host+this.systemConfig.server_type.port + '/';
            let url = Tool.transferLocationToCe(surl + `activity/activity.html#/qr_install_app?act=add_friend&uid=${user.uid}&timestamp=${timestamp}&uname=${user.username}&referral_code=${this.referral_code}`);
            // let url = `${ajaxServer}/activity/activity.html#/activity/activity.html#/qr_install_app?act=add_friend&uid=${user.uid}&timestamp=${timestamp}&uname=${user.username}&referral_code=${this.referral_code}`
            var qrcode = new window.QRCode(this.$refs.qrCode,{
                text:url,
                width:142,
                height:142
            });
        },
    }
}
</script>
<style lang="scss">
.invite_registration{
    .el-dialog__body{
        display:flex;
        flex-direction: column;
        padding: 32px 48px 28px 48px;
        background: #f4f6fa;
        border-radius: 16px;
        box-shadow: 0 6px 32px rgba(0,0,0,0.08);

        .referral_code_body{
            font-size:16px;
            overflow:auto;
            max-width: 420px;
            margin: 0 auto;

            .download_app_tip {
                margin-top: 0;
                text-align: center;

                .download_app_tip_text {
                    margin-bottom: 18px;
                    color: #333;
                    font-size: 16px;
                    font-weight: 500;
                    letter-spacing: 1px;
                }

                .qrcode_container {
                    display: flex;
                    justify-content: center;
                    margin-bottom: 18px;

                    .qrcode {
                        padding: 12px;
                        background: #fff;
                        border-radius: 12px;
                        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.10);
                        display: inline-block;
                        border: 1.5px solid #e3e6ed;

                        canvas {
                            display: block;
                        }
                    }
                }
            }

            .referral_code_content{
                text-align: center;
                margin: 28px 0 18px 0;
                padding: 24px 0 18px 0;
                background: linear-gradient(90deg, #fafdff 0%, #f3f7fa 100%);
                border-radius: 12px;
                border: 1.5px dashed #b5c9e2;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                box-shadow: 0 2px 8px rgba(0,0,0,0.04);

                .referral_code_content_left{
                    color: #2d3a4b;
                    font-weight: 600;
                    margin-bottom: 8px;
                    font-size: 15px;
                    letter-spacing: 1px;
                }

                .referral_code_content_right{
                    font-size: 38px;
                    font-weight: 800;
                    color: #1a73e8;
                    letter-spacing: 4px;
                    font-family: 'Courier New', monospace;
                    text-align: center;
                    background: linear-gradient(90deg, #e3f0ff 0%, #fafdff 100%);
                    border-radius: 6px;
                    padding: 6px 18px;
                    margin-top: 2px;
                }
            }

            .referral_code_tip{
                margin-top: 8px;
                text-align: center;
                color: #6c757d;
                font-size: 14px;
                line-height: 1.7;
                letter-spacing: 0.5px;

                span {
                    display: block;
                    text-align: center;
                }

                .referral_code_tip_expire {
                    display: block;
                    margin-top: 8px;
                    color: #e53935;
                    font-weight: 600;
                    text-align: center;
                    font-size: 15px;
                }
            }

            .referral_code_tool{
                margin-top: 32px;
                text-align: center;

                .generate_btn {
                    padding: 14px 38px;
                    font-size: 18px;
                    border-radius: 32px;
                    background: linear-gradient(90deg, #4f8cff 0%, #1a73e8 100%);
                    color: #fff;
                    font-weight: 700;
                    border: none;
                    box-shadow: 0 2px 8px rgba(26,115,232,0.10);
                    transition: all 0.2s cubic-bezier(.4,0,.2,1);

                    &:hover:not(:disabled) {
                        transform: translateY(-2px) scale(1.04);
                        box-shadow: 0 6px 18px rgba(26,115,232,0.18);
                        background: linear-gradient(90deg, #1a73e8 0%, #4f8cff 100%);
                    }

                    &:disabled {
                        opacity: 0.6;
                        cursor: not-allowed;
                        background: #b5c9e2;
                        color: #fff;
                    }
                }
            }
        }
    }
}
</style>

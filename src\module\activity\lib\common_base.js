import { Toast } from 'vant';
import store from '../store'
import moment  from 'moment'
import Tool from '@/common/tool.js';

// const store=window.vm.$store;
const systemConfig = store.state.systemConfig
const lang = store.state.language
let root = null
setTimeout(()=>{
    root = window.vm.$root
},0)
export function back(length){
    if(typeof length === 'number'){
        window.vm.$router.go(length*-1)
    }else{
        window.vm.$router.go(-1)
    }
    return window.vm;
}

export function formatString(text, param){
    return text.replace(/\{(\d+)\}/gm, function(ms, p1){
        return typeof(param[p1]) == 'undefined' ? ms : param[p1]
    });
}
export function getThumb(msg_type){
    let width=window.screen.width;
    let thumb=''
    if (width>1200) {
        thumb='_large'
    }else if (width>=768) {
        thumb='_mid'
    }else{
        return ''
    }
    switch(msg_type){
    case systemConfig.msg_type.Image:
    case systemConfig.msg_type.Video:
        thumb="_thumb"+thumb+".jpg"
        break;
    case systemConfig.msg_type.OBAI:
    case systemConfig.msg_type.Frame:
    case systemConfig.msg_type.Cine:
        thumb="thumbnail"+thumb+".jpg"
        break;
    default:
        break;
    }
    return thumb
}
export function getImgObjUrl(imgObj, type) {
    var img_src=''
    type = parseInt(type);
    switch(type){
    case 1:
    case 4:
    case 8:
    case 12:
    case 13:
    case 14:
    case 15:
    case 16:
    case 17:
    case 19:
    case 30:
        img_src=imgObj.url;
        let target=getThumb(imgObj.msg_type);
        if (target&&imgObj.url_local&&imgObj.url_local.indexOf(target)>-1) {
            img_src=img_src.replace("_thumb.jpg",target)
            img_src=img_src.replace("thumbnail.jpg",target)
        }
        break;
    case 0:
    case 2:
        img_src=imgObj.tempRealUrl
        break;
    case 3:
    case 5:
    case 6:
    case 9:
    case 10:
    case 11:
    case 18:
    case 21:
    case 22:
    case 23:
    case 24:
    case 25:
    case 26:
    case 27:
    case 28:
        img_src=imgObj.avatar
        break;
    case 7:
        img_src=imgObj.param.avatar;
        break;
    case 20:
        img_src=imgObj.sender_nickname[0].avatar;
        break;
    default:
        console.log("imgObj type Error ############################")
        console.log(type)
    }
    return img_src;
}

export function htmlEscape(text = '') {
    var map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };

    // 这次将所有的 & 和 < 都转义，而不排除任何标签
    return text.replace(/[&<>"]/g, function (m) {
        return map[m] || m;
    });
}
export function htmlUnescape(str=''){
    str=str.replace(/&amp;/g,'&')
    str=str.replace(/&lt;/g,'<')
    str=str.replace(/&gt;/g,'>')
    str=str.replace(/&quot;/g,'"')
    str=str.replace(/&#x27;/g,"'")
    str=str.replace(/&#x2F;/g,'/')
    return str
}
export function getFullServerResourceUrl(img_src){ // 获取完整的服务器资源地址
    let socketServer=systemConfig.server_type.protocol+systemConfig.server_type.host+systemConfig.server_type.port
    var full_url = socketServer + "/" + img_src;//完整的服务器资源地址
    let abs_url=img_src//请求生成缩略图的地址
    if (img_src.match(/^http:\/\//i) || img_src.match(/^https:\/\//i)){//单帧会诊文件
        full_url = img_src;
    }else{
        abs_url="/"+img_src
    }
    return {
        full_url,
        abs_url
    }
}
export function getLocalImgUrl(img_src='',forceDownload) {
    const user=window.vm.$store.state.user;
    const osName=window.vm.$store.state.globalParams.osName;
    const isCE=window.vm.$store.state.globalParams.isCE;
    var local_http = 'file://';
    var local_path = '';//相对路径
    var img_src_http = img_src;
    let isApp = systemConfig.client_type.AppMobile == window.clientType || systemConfig.client_type.UltraSoundMobile == window.clientType
    const noDownloadImage = window.vm.$store.state.globalParams.noDownloadImage
    if((isApp&&!noDownloadImage)||forceDownload) {//mobile app.
        if (img_src.match(/^file:\/\//i)) {//如果已经是本地地址
            img_src_http = img_src;
        }else{
            if (img_src.match(/^http:\/\//i) || img_src.match(/^https:\/\//i)){//单帧会诊文件
                var img_srcArr = img_src.split("mindray.com:443/");
                if(img_srcArr.length < 2){
                    img_srcArr = img_src.split("aliyuncs.com/");
                }
                if(img_srcArr.length < 2){
                    img_srcArr = img_src.split("mindray.com:8081/");
                }
                if(img_srcArr.length < 2){
                    img_srcArr = img_src.split("mindray.com:80/");
                }
                if(img_srcArr.length < 2){
                    img_srcArr = img_src.split("mindray.com/");
                }
                local_path = img_srcArr.pop();
            } else {
                local_path = img_src;
            }

            var sd_path_cell1 = '_documents/Ruitong/'+systemConfig.server_type.host+'/'+user.uid+'/images/'+local_path;
            var sd_path_cell2 = ''
            if(true){
                try {
                    Tool.createCWorkstationCommunicationMng({
                        name: "convertLocalFileSystemURL",
                        emitName: 'NotifyConvertedLocalFileSystemURL',
                        params:{url: sd_path_cell1},
                        timeout:1500
                    }).then((res)=>{
                        sd_path_cell2 = res.url
                        img_src_http = local_http + sd_path_cell2;
                    })
                }catch(error){
                    Toast(error);
                }
            }
            // img_src_http = local_http + sd_path_cell2;
        }
    }else{
        img_src_http = getFullServerResourceUrl(img_src).full_url
    }
    // console.log('user.uid',user.uid)
    // console.log('getLocalImgUrl',img_src_http)
    return img_src_http;
}
export function getThumbnailLocalImgUrl(img_src='',msg_type){
    let target=getThumb(msg_type)
    if (target) {
        img_src=img_src.replace("_thumb.jpg",target)
        img_src=img_src.replace("thumbnail.jpg",target)
    }
    return getLocalImgUrl(img_src);
}
export function getRealUrl(imageObj){
    let localRealUrl=''
    let serverRealUrl=''
    let url_local=getLocalImgUrl(imageObj.url)
    // let socketServer=systemConfig.server_type.protocol+systemConfig.server_type.host+systemConfig.server_type.port
    let isApp = systemConfig.client_type.AppMobile == window.clientType || systemConfig.client_type.UltraSoundMobile == window.clientType
    if (isApp){
        //生成大缩略图在内网下upload/xxx地址需加上/
        if (!/https?:\/\//.test(imageObj.url)) {
            serverRealUrl='/'
        }
    }
    let msg_type = imageObj.img_type_ex||imageObj.msg_type
    switch(msg_type){
    case systemConfig.msg_type.Image:
        localRealUrl=url_local.replace(imageObj.thumb,"");
        serverRealUrl=serverRealUrl +imageObj.url.replace(imageObj.thumb,"");
        break;
    case systemConfig.msg_type.Video:
        localRealUrl=url_local.replace(imageObj.thumb,imageObj.poster);
        serverRealUrl=serverRealUrl +imageObj.url.replace(imageObj.thumb,imageObj.poster);
        break;
    case systemConfig.msg_type.OBAI:
        localRealUrl=url_local.replace("thumbnail.jpg","ScreenShot.jpg");
        serverRealUrl=imageObj.url.replace("thumbnail.jpg","ScreenShot.jpg");
        break;
    case systemConfig.msg_type.Frame:
        localRealUrl=url_local.replace("thumbnail.jpg","SingleFrame.jpg");
        serverRealUrl=imageObj.url.replace("thumbnail.jpg","SingleFrame.jpg");
        break;
    case systemConfig.msg_type.Cine:
        localRealUrl=url_local.replace("thumbnail.jpg","DevicePoster.jpg");
        serverRealUrl=imageObj.url.replace("thumbnail.jpg","DevicePoster.jpg");
        break;
    default:
        break;
    }
    return {
        localRealUrl:localRealUrl,
        serverRealUrl:serverRealUrl
    }
}

export function addRootToUrl(img_src){
    let isApp = systemConfig.client_type.AppMobile == window.clientType || systemConfig.client_type.UltraSoundMobile == window.clientType
    if (isApp) {
        if (window.location.protocol.indexOf('file:')>=0&&!/http/.test(img_src)&&img_src[0]!='/') {
            img_src='/'+img_src
        }
    }
    return img_src;
}

export function getRecordSubject(message){
    if(message.live_record_data && message.live_record_data.subject){
        return message.live_record_data.subject
    }else if (message.live_record_data && message.live_record_data.creator_name) {
        return `${message.live_record_data.creator_name}${lang.initiated_live_broadcast}`;
    }else{
        return ''
    }
}

export function judgeIfCurrentYear(start, end){
    const currentYear = moment(start).toDate().getFullYear()
    const targetYear = moment(end).toDate().getFullYear()
    return currentYear === targetYear
}

export function formatDurationTime(duration){
    let start_time = new Date().getTime() - (duration || 0)*1000
    let millisecond = new Date() - start_time
    let h = Math.floor(millisecond / (60 * 60 * 1000))
    h = h < 10 ? '0' + h : h
    let min = Math.floor((millisecond % (60 * 60 * 1000)) / (60 * 1000))
    min = min < 10 ? '0' + min : min
    let sec = Math.floor(((millisecond % (60 * 60 * 1000)) % (60 * 1000)) / 1000)
    sec = sec < 10 ? '0' + sec : sec
    let Str = `${sec}${lang.live_replay_second}`
    if(min>0){
        Str = `${min}${lang.live_replay_minute}${Str}`
    }
    if(h>0){
        Str = `${h}${lang.live_replay_hour}${min}${lang.live_replay_minute}`
    }
    return Str;
    // _this.count_time = h + ':' + min + ':' + sec
}


export function getReviewVideoSubject(message){
    if (message && message.live_record_data && message.live_record_data.creator_name) {
        return `${message.live_record_data.creator_name}${lang.initiated_live_broadcast}`;
    }
}

export function generateGalleryFileId(galleryItem){
    return galleryItem.resource_id + galleryItem.file_id + galleryItem.gmsg_id
}

export function getResourceTempStatus(resource_id,key){
    const storeState = window.vm&&window.vm.$store&&window.vm.$store.state
    const resourceTempStatus = storeState.resourceTempStatus;
    if (resourceTempStatus.hasOwnProperty(resource_id)) {
        if (resourceTempStatus[resource_id].hasOwnProperty(key)) {
            return resourceTempStatus[resource_id][key]
        }else{
            return null
        }
    }else{
        return null
    }
}
export function checkResourceType(currentFile){
    if(currentFile.msg_type==systemConfig.msg_type.Image||
    currentFile.img_type_ex==systemConfig.msg_type.Image||
    currentFile.img_type_ex==systemConfig.msg_type.Frame||
    currentFile.msg_type==systemConfig.msg_type.Frame   ||
    currentFile.img_type_ex==systemConfig.msg_type.OBAI ||
    currentFile.msg_type==systemConfig.msg_type.OBAI){
        return 'image'
    }else if(currentFile.msg_type==systemConfig.msg_type.Cine||
    currentFile.img_type_ex==systemConfig.msg_type.Cine||
    currentFile.msg_type==systemConfig.msg_type.Video||
    currentFile.img_type_ex==systemConfig.msg_type.Video){
        return 'video'
    }else if(currentFile.msg_type==systemConfig.msg_type.RealTimeVideoReview||
    currentFile.img_type_ex==systemConfig.msg_type.RealTimeVideoReview||
    currentFile.img_type_ex==systemConfig.msg_type.VIDEO_CLIP||
    currentFile.msg_type==systemConfig.msg_type.VIDEO_CLIP){
        return 'review_video'
    }
}
export function getClipSubject(message){
    return `${message.nickname}${lang.generated_video_clips}`
}

export function goPrivacyPolicy(){
    const server_type = window.vm.$store.state.systemConfig.server_type;
    // 根据CN/CE环境判断隐私协议语言，而不是根据当前语言设置
    // CN环境使用中文隐私协议，CE环境使用英文隐私协议
    const env = process.env.VUE_APP_PROJECT_NOV || 'CN';
    let privacy_version = window.vm.$store.state.systemConfig.envConfig && window.vm.$store.state.systemConfig.envConfig.privacy_agreement_version
    let host = server_type.protocol + server_type.host + server_type.port;
    if (host.indexOf("localhost") > -1 || host.indexOf("192.168") > -1) {
        host = `https://${Tool.getHostConfig().dev}`;
    }
    const url = host + `/privacyPolicyPage/${env}/pravicyPolicy${env}_${privacy_version}.html`;
    window.open(url, "blank");
}

<template>
<div>
	<el-dialog
      class="group_seeding_setting"
      :title="lang.setting_title"
      :visible="true"
      :close-on-click-modal="false"
      width="40%"
      top="20%"
      :modal="false"
      v-loading="loading"
      :before-close="back">
        <template>
            <div class="setting_item" >
                <span>{{lang.group_setting_is_live_record}}：</span>
                <el-radio v-model="record_mode" :label="0" :disabled="!!conferenceState">{{lang.cancel_button_text}}</el-radio>
                <el-radio v-model="record_mode" :label="1" :disabled="!!conferenceState">{{lang.confirm_button_text}}</el-radio>
            </div>
        </template>
        <el-button type="primary" size="medium" class="submit_btn" @click="submit">{{lang.confirm_txt}}</el-button>
        <router-view></router-view>
    </el-dialog>
</div>

</template>
<script>
import base from '../lib/base'
import Tool from '@/common/tool.js'
export default {
    mixins: [base],
    name: 'GroupSeedingSettingPage',
    components: {},
    data(){
        return {
            record_mode:0,
            conversation:{},
            cid:0,
            loading:false,
        }
    },
    computed:{
        conferenceState(){
            return this.$store.state.liveConference[this.cid]&&this.$store.state.liveConference[this.cid].conferenceState
        },
    },
    mounted(){
        this.$nextTick(()=>{
            this.initDate()
        })
    },
    methods:{
        initDate(){
            this.cid=this.$route.params.cid
            this.conversation=this.conversationList[this.cid]||{}
            this.record_mode=0; //this.conversation.record_mode
        },
        submit(){
            var that=this;
            var data={
                gid:that.cid,
                record_mode:that.record_mode?1:0
            }
            that.conversation.socket.emit('edit_record_mode',data,function(is_succ,data){
                if(is_succ){
                    //修改成功
                    that.$store.commit('conversationList/updateIsLiveRecord',{
                        cid:that.cid,
                        record_mode:that.record_mode
                    });
                }else{//修改失败
                    that.record_mode = !that.record_mode;
                }
                that.$root.eventBus.$emit('chatStartConferenceFn')
            })
            this.back();
        }
    }
}
</script>
<style lang="scss">
.group_seeding_setting{
    .el-dialog{
        height:150px !important;
    }
    .setting_item{
        line-height: 40px;
        font-size: 16px;
    }
    .submit_btn{
        position: absolute;
        right: 10px;
        bottom: 10px;
    }
    .el-radio__inner::after{
        transition:none;
    }
}
</style>

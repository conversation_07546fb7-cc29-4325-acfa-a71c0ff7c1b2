<svg xmlns="http://www.w3.org/2000/svg">
	<symbol id="search_down" viewBox="0 0 28 20">
		<path d="M15.33,19.32,27.69,2.65A1.65,1.65,0,0,0,26.36,0H1.64A1.65,1.65,0,0,0,.31,2.65L12.67,19.32A1.66,1.66,0,0,0,15.33,19.32Z"/>
	</symbol>
	<symbol id="search_up" viewBox="0 0 28 20">
		<path d="M12.67.68.31,17.35A1.65,1.65,0,0,0,1.64,20H26.36a1.65,1.65,0,0,0,1.33-2.64L15.33.68A1.66,1.66,0,0,0,12.67.68Z"/>
	</symbol>

	<symbol id="filter"  viewBox="0 0 45 50">
		<path d="M.34,2.75,16,24.16A5.49,5.49,0,0,1,17,27.41V48.6a1.35,1.35,0,0,0,2.11,1.16l7.54-5.17A3.1,3.1,0,0,0,28,42V27.41a5.49,5.49,0,0,1,1.06-3.25L44.66,2.75A1.72,1.72,0,0,0,43.33,0H1.67A1.72,1.72,0,0,0,.34,2.75Z"/>
	</symbol>
	<symbol id="iworks"  viewBox="0 0 50 50">
		<rect x="11.49" y="11.49" width="11.79" height="11.79" rx="1.12"/>
		<rect  x="26.71" y="11.49" width="11.79" height="11.79" rx="1.12"/>
		<rect x="11.49" y="26.71" width="11.79" height="11.79" rx="1.12"/>
		<rect x="26.71" y="26.71" width="11.79" height="11.79" rx="1.12"/>
		<polyline points="35.47 31.04 31.82 34.69 29.74 32.61"/>
	</symbol>
	<symbol id="exam_down"  viewBox="0 0 42 42">
		<path fill="#656d70" d="M21,42A21,21,0,1,1,42,21,21,21,0,0,1,21,42ZM21,1A20,20,0,1,0,41,21,20,20,0,0,0,21,1Z"/><path fill="#7c8589" d="M22.6,33,35,16.7a1.64,1.64,0,0,0-1.3-2.64H8.87A1.65,1.65,0,0,0,7.56,16.7L20,33A1.64,1.64,0,0,0,22.6,33Z"/>
	</symbol>
	<symbol id="exam_up"  viewBox="0 0 42 42">
		<path fill="#656d70" d="M21,0A21,21,0,1,1,0,21,21,21,0,0,1,21,0Zm0,41A20,20,0,1,0,1,21,20,20,0,0,0,21,41Z"/><path fill="#7c8589" d="M19.4,9.05,7,25.3a1.64,1.64,0,0,0,1.3,2.64H33.13a1.65,1.65,0,0,0,1.31-2.64L22,9.05A1.64,1.64,0,0,0,19.4,9.05Z"/>
	</symbol>
	<symbol id="magnifier"  viewBox="0 0 46 46">
		<path d="M18.63,37.25A18.63,18.63,0,1,1,37.25,18.62,18.65,18.65,0,0,1,18.63,37.25ZM18.63,4A14.63,14.63,0,1,0,33.25,18.62,14.64,14.64,0,0,0,18.63,4Z"/>
		<path d="M44,46a2,2,0,0,1-1.41-.59L29,31.79A2,2,0,1,1,31.79,29L45.41,42.59A2,2,0,0,1,44,46Z"/>
	</symbol>
	<symbol id="video"  viewBox="0 0 28 28">
		<path fill="#00c59d" d="M14,0A14,14,0,1,0,28,14,14,14,0,0,0,14,0Zm8.06,14.82L10.89,21.26a.94.94,0,0,1-1.42-.81V7.55a.94.94,0,0,1,1.42-.81l11.17,6.44A1,1,0,0,1,22.06,14.82Z"/>
	</symbol>
	<symbol id="transmit"  viewBox="0 0 38 38">
		<path d="M32.77,38H5.23A5.23,5.23,0,0,1,0,32.77V5.22A5.23,5.23,0,0,1,5.23,0h12.2a1,1,0,0,1,0,2H5.23A3.23,3.23,0,0,0,2,5.22V32.77A3.23,3.23,0,0,0,5.23,36H32.77A3.23,3.23,0,0,0,36,32.77V20.56a1,1,0,0,1,2,0V32.77A5.23,5.23,0,0,1,32.77,38Z"/>
		<path d="M37,13.94a1,1,0,0,1-1-1V2H25.06a1,1,0,0,1,0-2H37a1,1,0,0,1,1,1V12.94A1,1,0,0,1,37,13.94Z"/>
		<path d="M12.94,26.06a1,1,0,0,1-.71-.29,1,1,0,0,1,0-1.42L36.29.29a1,1,0,1,1,1.42,1.42L13.65,25.77A1,1,0,0,1,12.94,26.06Z"/>
	</symbol>
	<symbol id="comment"  viewBox="0 0 38 38">
		<path d="M19,38h0a1,1,0,0,1-.71-.29l-5.44-5.45H5.23A5.23,5.23,0,0,1,0,27V5.23A5.23,5.23,0,0,1,5.23,0H32.77A5.23,5.23,0,0,1,38,5.23V27a5.23,5.23,0,0,1-5.23,5.22H25.15l-5.44,5.45A1,1,0,0,1,19,38ZM5.23,2A3.23,3.23,0,0,0,2,5.23V27a3.23,3.23,0,0,0,3.23,3.22h8a1,1,0,0,1,.71.29l5,5,5-5a1,1,0,0,1,.71-.29h8A3.23,3.23,0,0,0,36,27V5.23A3.23,3.23,0,0,0,32.77,2Z"/>
		<path d="M10.44,17.13h-5a1,1,0,0,1,0-2h5.05a1,1,0,0,1,0,2Z"/><path class="cls-1" d="M21.52,17.13h-5a1,1,0,0,1,0-2h5a1,1,0,1,1,0,2Z"/>
		<path d="M32.61,17.13h-5a1,1,0,0,1,0-2h5a1,1,0,1,1,0,2Z"/>
	</symbol>
	<symbol id="apply"  viewBox="0 0 38 38">
		<path d="M32.78,38H5.23A5.23,5.23,0,0,1,0,32.77V5.23A5.23,5.23,0,0,1,5.23,0h8.42a1,1,0,0,1,0,2H5.23A3.23,3.23,0,0,0,2,5.23V32.77A3.23,3.23,0,0,0,5.23,36H32.78A3.23,3.23,0,0,0,36,32.77v-16a1,1,0,0,1,2,0v16A5.23,5.23,0,0,1,32.78,38Z"/>
		<path d="M26.45,32.88c-3.55,0-8.68-1.32-14.35-7h0C2,15.81,5.7,7.42,6.09,6.6A1.94,1.94,0,0,1,7.83,5.43h5.7A1.61,1.61,0,0,1,15.14,7v8.19a1.75,1.75,0,0,1-1.44,1.7L13,17c0,.35.32,2,3.13,4.81S20.53,24.93,21,25l.11-.67a1.76,1.76,0,0,1,1.7-1.45H31a1.62,1.62,0,0,1,1.61,1.62v5.7a1.94,1.94,0,0,1-1.17,1.74A11.94,11.94,0,0,1,26.45,32.88Zm-12.93-8.4c9.31,9.31,16.71,5.77,17,5.62l0,0V24.85H23l-.13.8A1.61,1.61,0,0,1,21.17,27c-.72,0-2.92-.2-6.43-3.72S11,17.54,11,16.82a1.61,1.61,0,0,1,.28-1,1.57,1.57,0,0,1,1-.66l.79-.13V7.43H7.83c-.08.34-3.62,7.74,5.69,17.05Zm-.84-7.38h0Zm.7-2.15h0Z"/>
		<path d="M32.89,11.94H22.2a1.74,1.74,0,0,1-1.74-1.74V1.75A1.74,1.74,0,0,1,22.2,0H32.89a1.74,1.74,0,0,1,1.74,1.75v.92l1.2-.79a1.41,1.41,0,0,1,1.46-.12A1.43,1.43,0,0,1,38,3.05V8.79a1.41,1.41,0,0,1-.71,1.29A1.39,1.39,0,0,1,35.83,10l-1.2-.79v1A1.74,1.74,0,0,1,32.89,11.94Zm-10.43-2H32.63V7.31a1,1,0,0,1,1.55-.84L36,7.68V4.17l-1.82,1.2a1,1,0,0,1-1.55-.83V2H22.46Z"/>
	</symbol>
	<symbol id="right"  viewBox="0 0 36 24">
		<path d="M14.28,24h0a1.54,1.54,0,0,1-1.1-.47L.44,10.35a1.57,1.57,0,0,1,0-2.21,1.52,1.52,0,0,1,2.18,0l11.64,12L33.36.47a1.52,1.52,0,0,1,2.18,0,1.57,1.57,0,0,1,0,2.21L15.39,23.53A1.54,1.54,0,0,1,14.28,24Z"/>
	</symbol>
	<symbol id="transfer"  viewBox="0 0 48 48">
		<path d="M37.77,20.73H10.22a1,1,0,0,1,0-2H35.36l-4.94-5a1,1,0,0,1,1.41-1.41L38.48,19a1,1,0,0,1,.22,1.09A1,1,0,0,1,37.77,20.73Z"/>
		<path d="M16.88,35.92a1,1,0,0,1-.71-.29L9.52,29a1,1,0,0,1-.22-1.09,1,1,0,0,1,.92-.62H37.77a1,1,0,0,1,0,2H12.64l4.94,4.95a1,1,0,0,1,0,1.41A1,1,0,0,1,16.88,35.92Z"/>
		<path d="M24,48A24,24,0,1,1,48,24,24,24,0,0,1,24,48ZM24,2A22,22,0,1,0,46,24,22,22,0,0,0,24,2Z"/>
	</symbol>
	<symbol id="more"  viewBox="0 0 38 8">
		<circle cx="4" cy="4" r="4"/>
		<circle cx="19" cy="4" r="4"/>
		<circle cx="34" cy="4" r="4"/>
	</symbol>
	<symbol id="back"  viewBox="0 0 30 54">
		<polygon points="27.82 54 0 27 27.82 0 30 2.12 4.36 27 30 51.88 27.82 54"/>
	</symbol>
	<symbol id="app_title_CN"  viewBox="0 0 235 64">
		<path d="M.84,20.66v-6.5h16v6.5H12.32V32.14h4.09v6.44H12.32v11.7l5-.84q0,3.87,0,6.78Q10,57.56,1.12,59.52L0,52.24l5.66-.84V38.58H1.18V32.14H5.66V20.66Zm17,14.84V29.62H53V35.5H37.69l-.9,3.75H51.52V57q.23,6.33-5.6,6.44-1.12.06-5.49.06L40,60.75H36.74V45.07H33.26V61H27.94v-16H24.47V63.66H18.65V39.25H29.74l.72-3.75Zm1.46-9.36V13.71h6.55v6.55h5.77V11h6.78v9.24h5.76V13.71h6.56V26.14ZM45.64,45.07h-3.7V57.62h1.35c1.6,0,2.39-.69,2.35-2.19Z"/>
		<path d="M59.25,51.18V39.53H85.46V51.18H76.22v6.21c.11,3.92-1.67,6-5.32,6.16l-2.3.11H65.18q-.39-2-1.12-4.87-1.5,1.58-4.31,4.26l-4.09-4.26a84.17,84.17,0,0,0,7.45-7.11l4.54,3.25a2.75,2.75,0,0,0-.28.33c-1.27,1.39-2.24,2.41-2.91,3.08,1,0,2.11.06,3.3.06,1.53,0,2.28-.69,2.24-2.18v-5Zm-.56-21.56V12.76H86V29.62H75.54l.62,2.68H88.54v4.88H56.22V32.3H69.44l-.78-2.68ZM65,17.18v2.47H79.69V17.18Zm0,5.88v2.47H79.69V23.06Zm.62,20.72V47h13.6v-3.2ZM84,61.76q-2.91-2.91-6.61-6.44l4.09-3.58c.48.44,1.27,1.1,2.35,2l3.36,2.91a62.07,62.07,0,0,0,17.36-14.17l4.82,4.87A73.79,73.79,0,0,1,88.48,64L87.3,62.32,85.68,60Zm7.45-32.7Q90.16,27,87.58,23.34a63.47,63.47,0,0,0,15-11.59l4.87,4.65A86.84,86.84,0,0,1,91.45,29.06Zm0,16.18q-1.68-2.75-3.87-5.71a58.17,58.17,0,0,0,16.19-12.66l4.59,4.87A79.31,79.31,0,0,1,91.45,45.24Z"/>
		<path d="M112.56,37.4V30.18h52.19V37.4H138.94q-5.88,8.24-12.1,15.34,7,0,22.12-.67-1.85-2.46-5.66-7.28c-.37-.48-.65-.84-.84-1.06l6.11-3.7q8.85,10.36,15.12,18.43L157.08,63l-.9-1.23c-.41-.6-1.14-1.61-2.18-3Q143.41,59,127,60a63,63,0,0,0-8.45,1l-3.59-7.56a25.64,25.64,0,0,0,6.22-5.32,81.18,81.18,0,0,0,8-10.75Zm4.54-16.07V14h42.78v7.34Z"/>
		<path d="M204.33,22H191.39V35.06h-5.77V22h-13V16.3h13V3.36h5.77V16.3h12.94Z"/>
		<path d="M235,14h-9.7v9.78H221V14h-9.78V9.7H221V0h4.33V9.7H235Z"/>
	</symbol>
	<symbol id="app_title_EN2" viewBox="0 0 92 72">
	 <g>
	  <text font-weight="bold" stroke="#000" transform="matrix(1.3429596837572018,0,0,1.7258058144054647,-86.91745976277525,-34.45372682630882) " xml:space="preserve" text-anchor="start" font-family="Oswald, sans-serif" font-size="20" id="svg_1" y="50.170455" x="65.863636" stroke-width="0" fill="#fff">MiCo+</text>
	 </g>
	</symbol>
	<symbol id="app_title_CE" viewBox="0 0 92 72">
		  <text stroke="#000" font-weight="normal" xml:space="preserve" text-anchor="start" font-family="Helvetica, Arial, sans-serif" font-size="57" id="svg_1" y="42" x="0" stroke-width="0" fill="#fff">MiCo</text>
		  <text stroke="#000" style="vector-effect: non-scaling-stroke;" transform="matrix(2.122301379677083,0,0,1.104858160018921,-151.3528626248891,-1.7301596403121948) " xml:space="preserve" text-anchor="start" font-family="Helvetica, Arial, sans-serif" font-size="57" id="svg_2" y="69.125" x="134.863636" stroke-width="0" fill="#fff"/>
		  <text stroke="null" style="vector-effect: non-scaling-stroke;" transform="translate(111.66194152832031,-7.982524871826172) scale(1.1345059871673584,1) translate(-111.66194152832031,7.982524871826172) translate(111.1298599243164,-8.054752349853516) scale(1.1773582696914673,1) translate(-111.1298599243164,8.054752349853516) translate(110.41072082519531,-8.054752349853516) scale(1.23971426486969,1) translate(-110.41072082519531,8.054752349853516) translate(109.859375,-8.054752349853516) scale(1.1837817430496216,1) translate(-109.859375,8.054752349853516) matrix(1.6180589199066162,0,0,1.5408450365066526,-53.5731542147696,-57.73239181190729) " xml:space="preserve" text-anchor="start" font-family="Helvetica, Arial, sans-serif" font-size="29" id="svg_4" y="61.125" x="102.863636" fill-opacity="null" stroke-opacity="null" stroke-width="0" fill="#fff"/>
		  <text stroke="null" style="vector-effect: non-scaling-stroke;" transform="rotate(117 284.9377746582031,-141.58015441894534) translate(159.38917541503906,35.32931900024414) scale(-7.276382923126221,1) translate(-159.38917541503906,-35.32931900024414) translate(194.9281005859375,11.375831604003906) scale(0.06747308373451233,1) translate(-194.9281005859375,-11.375831604003906) translate(125.2520980834961,7.770261287689209) scale(1.2408456802368164,1) translate(-125.2520980834961,-7.770261287689209) translate(124.44731140136719,7.764004707336426) scale(1.2682276964187622,1.0195859670639038) translate(-124.44731140136719,-7.764004707336426) translate(124.96382141113281,7.472506523132324) scale(1.3103744983673096,1.0986735820770264) translate(-124.96382141113281,-7.472506523132324) matrix(1.201284408569336,0,0,1,-25.5348142683506,0) " xml:space="preserve" text-anchor="start" font-family="Helvetica, Arial, sans-serif" font-size="35" id="svg_5" y="46.426368" x="132.118424" stroke-opacity="null" stroke-width="0" fill="#fff">+</text>
		  <text stroke="null" style="vector-effect: non-scaling-stroke;" transform="matrix(3.697015548063405,0,0,1,-436.00871495291904,0) " xml:space="preserve" text-anchor="start" font-family="Helvetica, Arial, sans-serif" font-size="35" id="svg_6" y="63.130489" x="139.871527" fill-opacity="null" stroke-opacity="null" stroke-width="0" fill="#fff"/>
		  <text stroke="null" transform="translate(133.4494171142578,-14.355960845947266) scale(0.7365599870681763,1) translate(-133.4494171142578,14.355960845947266) translate(170.82386779785156,-14.153303146362305) scale(0.8034065365791321,1) translate(-170.82386779785156,14.153303146362305) translate(124.42279052734375,-14.153303146362305) scale(1.205074667930603,1) translate(-124.42279052734375,14.153303146362305) translate(123.68394470214844,-14.153303146362305) scale(1.2462472915649414,1) translate(-123.68394470214844,14.153303146362305) translate(122.6290054321289,-14.153303146362305) scale(1.3515998125076294,1) translate(-122.6290054321289,14.153303146362305) translate(123.51608276367188,-14.630145072937012) scale(0.7043462991714478,1) translate(-123.51608276367188,14.630145072937012) translate(123.6026840209961,42.02883529663086) scale(0.9711401462554932,0.8230994939804077) translate(-123.6026840209961,-42.02883529663086) translate(124.23992156982422,-25.68450355529785) scale(0.7876150012016296,1) translate(-124.23992156982422,25.68450355529785) translate(124.23992156982422,-25.698904037475586) scale(1,0.8937770128250122) translate(-124.23992156982422,25.698904037475586) translate(124.23992156982422,50.508277893066406) scale(1,1.2622253894805908) translate(-124.23992156982422,-50.508277893066406) translate(123.66490936279297,-10.515982627868652) scale(1.191645622253418,1) translate(-123.66490936279297,10.515982627868652) translate(122.61663055419922,-10.515982627868652) scale(1.3493809700012207,1) translate(-122.61663055419922,10.515982627868652) translate(122.99321746826172,-10.515982627868652) scale(0.8744860291481018,1) translate(-122.99321746826172,10.515982627868652) translate(122.99321746826172,-10.515982627868652) scale(1) translate(-122.99321746826172,10.515982627868652) translate(122.89662170410156,-10.670037269592285) scale(1.0321935415267944,1) translate(-122.89662170410156,10.670037269592285) translate(122.99075317382812,49.92174530029297) scale(0.9686263799667358,0.8216290473937988) translate(-122.99075317382812,-49.92174530029297) translate(123.67427062988281,-22.96967315673828) scale(0.7721924781799316,1) translate(-123.67427062988281,22.96967315673828) translate(124.29593658447266,-22.71967124938965) scale(0.7928056716918945,1) translate(-124.29593658447266,22.71967124938965) translate(172.09605407714844,-22.71875) scale(0.8402146697044373,1) translate(-172.09605407714844,22.71875) translate(-12.001582145690918,-4.000527381896973) matrix(1.5554133819663039,0,0,1,-101.78034962426811,0) " xml:space="preserve" text-anchor="start" font-family="Helvetica, Arial, sans-serif" font-size="57" id="svg_8" y="36.85429" x="149.515714" fill-opacity="null" stroke-opacity="null" stroke-width="0" fill="#fff">+</text>
	</symbol>
	<symbol id="camera"  viewBox="0 0 62 62">
		<path d="M31,62A31,31,0,1,1,62,31,31,31,0,0,1,31,62ZM31,2A29,29,0,1,0,60,31,29,29,0,0,0,31,2Z"/>
		<path d="M18.62,36.33h-4a2.17,2.17,0,0,1-2.17-2.17V20.24a2.17,2.17,0,0,1,2.17-2.17H36.44v2H14.67a.18.18,0,0,0-.17.17V34.16a.18.18,0,0,0,.17.17h4Z"/>
		<path d="M47.31,27.11,42,30.61V25.24a1.43,1.43,0,0,0-1.43-1.43H20.05a1.43,1.43,0,0,0-1.43,1.43V41.49a1.44,1.44,0,0,0,1.43,1.44H40.59A1.44,1.44,0,0,0,42,41.49V35.93l5.29,3.51c.65.43,1.19.14,1.19-.64V27.74C48.5,27,48,26.67,47.31,27.11Zm-17.86,3.6H23.68v-2h5.77Z"/>
	</symbol>
	<symbol id="chat"  viewBox="0 0 75 75">
		<path d="M64.48,75c-6.08,0-11-1.56-14.69-4.64l0,0h0a38.36,38.36,0,0,1-12.1,1.94h-.21C16.73,72.2,0,55.91,0,36S16.81,0,37.4,0h.17C58.27.09,75.06,16.39,75,36.32A35.53,35.53,0,0,1,62.87,62.83c.55,4.52,1.87,7.71,3.88,9.53A1.5,1.5,0,0,1,65.81,75C65.38,75,64.93,75,64.48,75ZM49.74,67.34a3.08,3.08,0,0,1,2,.72,18.28,18.28,0,0,0,10.88,3.88,22.15,22.15,0,0,1-2.72-8.67,3.08,3.08,0,0,1,1-2.7A32.5,32.5,0,0,0,72,36.32C72.06,18,56.61,3.09,37.56,3H37.4C18.46,3,3,17.8,3,36S18.4,69.21,37.43,69.29h.2A35.27,35.27,0,0,0,48.78,67.5,3,3,0,0,1,49.74,67.34Z"/>
	</symbol>
	<symbol id="friend"  viewBox="0 0 75 75">
		<path d="M57.5,57.5A17.5,17.5,0,1,1,75,40,17.52,17.52,0,0,1,57.5,57.5Zm0-32A14.5,14.5,0,1,0,72,40,14.51,14.51,0,0,0,57.5,25.5Z"/>
		<path d="M68.29,53.75a17,17,0,0,1-2.73,1.76C69.7,58.22,72,61.39,72,64.43l-.21,4.18c-.14,1.55-3.89,3.39-21,3.39-2.22,0-4.51,0-6.84,0h-.11l-6.33,0H31.28l-4.62,0c-5.75,0-23.25,0-23.53-3.4L3,64.38C3,56,19.07,48.57,37.32,48.57h.23c1.63,0,3.24.08,4.83.19A17.07,17.07,0,0,1,41,45.66c-1.13,0-2.26-.09-3.41-.09h-.23C16.78,45.57,0,54,0,64.42l.14,4.29c.51,6.37,15,6.28,31.15,6.21H37.5l6.3,0h.12c2.33,0,4.63,0,6.86,0,11.87,0,23.5-.47,24-6.18L75,64.51C75,60.63,72.66,56.9,68.29,53.75Z"/>
		<rect x="48.68" y="32" width="17.64" height="3"/>
		<rect x="48.68" y="38.5" width="17.64" height="3"/>
		<rect x="48.68" y="45" width="17.64" height="3"/>
		<path d="M40.57,35.65a16.84,16.84,0,0,1-3.12.3A16.48,16.48,0,1,1,54,19.53,16.14,16.14,0,0,1,53.63,23a16.64,16.64,0,0,1,3.11-.41,19.8,19.8,0,0,0,.26-3A19.48,19.48,0,0,0,37.57,0h0A19.56,19.56,0,0,0,18,19.42,19.48,19.48,0,0,0,37.5,39a19.65,19.65,0,0,0,2.56-.18A17.82,17.82,0,0,1,40.57,35.65Z"/>
	</symbol>
	<symbol id="library"  viewBox="0 0 75 75">
		<rect x="14.8" y="7" width="10" height="3"/>
		<rect x="14.8" y="13" width="10" height="3"/>
		<rect x="32.5" y="7" width="10" height="3"/>
		<rect x="32.5" y="13" width="10" height="3"/>
		<rect x="50.2" y="15.25" width="10" height="3"/>
		<path d="M58.75,75.5H12.12a12.13,12.13,0,0,1,0-24.25H13A22.15,22.15,0,0,1,55.3,43h3.45a16.26,16.26,0,0,1,0,32.51ZM12.12,54.25a9.13,9.13,0,0,0,0,18.25H58.75a13.26,13.26,0,0,0,0-26.51H54.31a1.5,1.5,0,0,1-1.4-.94A19.15,19.15,0,0,0,16,52.16a1.94,1.94,0,0,0,0,.24c0,.1,0,.2,0,.31a1.49,1.49,0,0,1-1.5,1.54Z"/>
		<path d="M47.85,34V11.75h13.2a1.5,1.5,0,0,1,1.5,1.5V43.46a16.37,16.37,0,0,1,3,1V13.25a4.51,4.51,0,0,0-4.5-4.5H46.35a1.5,1.5,0,0,0-1.5,1.5v22A22.5,22.5,0,0,1,47.85,34Z"/>
		<path d="M30.15,30.6V5a1.5,1.5,0,0,1,1.5-1.5h11.7A1.5,1.5,0,0,1,44.85,5V32.24a22.5,22.5,0,0,1,3,1.77V5A4.51,4.51,0,0,0,43.35.5H31.65A4.51,4.51,0,0,0,27.15,5V31.52A20.42,20.42,0,0,1,30.15,30.6Z"/>
		<path d="M12.12,51.25h.33V5A1.5,1.5,0,0,1,14,3.5h11.7A1.5,1.5,0,0,1,27.15,5V31.52a20.42,20.42,0,0,1,3-.92V5A4.51,4.51,0,0,0,25.65.5H14A4.51,4.51,0,0,0,9.45,5V51.56A12.34,12.34,0,0,1,12.12,51.25Z"/>
	</symbol>
	<symbol id="mine"  viewBox="0 0 75 75">
		<path d="M37.5,75A37.5,37.5,0,1,1,75,37.5,37.54,37.54,0,0,1,37.5,75Zm0-72A34.5,34.5,0,1,0,72,37.5,34.54,34.54,0,0,0,37.5,3Z"/>
		<path class="cls-1" d="M37.31,54.43a16.88,16.88,0,0,1-9.47-2.89,1.5,1.5,0,0,1,1.68-2.49,13.93,13.93,0,0,0,15.92-.24,1.5,1.5,0,0,1,1.75,2.43A16.76,16.76,0,0,1,37.31,54.43Z"/>
		<path d="M16.14,29.43a1.5,1.5,0,0,1-.88-2.72,17,17,0,0,1,14-2.69,1.5,1.5,0,1,1-.72,2.91A14.06,14.06,0,0,0,17,29.14,1.42,1.42,0,0,1,16.14,29.43Z"/>
		<path d="M58.86,29.43a1.42,1.42,0,0,1-.87-.29A14.06,14.06,0,0,0,46.5,26.93,1.5,1.5,0,1,1,45.78,24a17,17,0,0,1,14,2.69,1.5,1.5,0,0,1-.88,2.72Z"/>
	</symbol>
	<symbol id="chat_active"  viewBox="0 0 75 75">
		<path d="M64.48,75c-6.08,0-11-1.56-14.69-4.64l0,0h0a38.36,38.36,0,0,1-12.1,1.94h-.21C16.73,72.2,0,55.91,0,36S16.81,0,37.4,0h.17C58.27.09,75.06,16.39,75,36.32A35.53,35.53,0,0,1,62.87,62.83c.55,4.52,1.87,7.71,3.88,9.53A1.52,1.52,0,0,1,67.16,74a1.49,1.49,0,0,1-1.35,1C65.38,75,64.93,75,64.48,75Z"/>
	</symbol>
	<symbol id="friend_active"  viewBox="0 0 75 75">
		<path d="M68.29,53.75a17,17,0,0,1-2.73,1.76C69.7,58.22,72,61.39,72,64.43l-.21,4.18c-.14,1.55-3.89,3.39-21,3.39-2.22,0-4.51,0-6.84,0h-.11l-6.33,0H31.28l-4.62,0c-5.75,0-23.25,0-23.53-3.4L3,64.38C3,56,19.07,48.57,37.32,48.57h.23c1.63,0,3.24.08,4.83.19A17.07,17.07,0,0,1,41,45.66c-1.13,0-2.26-.09-3.41-.09h-.23C16.78,45.57,0,54,0,64.42l.14,4.29c.51,6.37,15,6.28,31.15,6.21H37.5l6.3,0h.12c2.33,0,4.63,0,6.86,0,11.87,0,23.5-.47,24-6.18L75,64.51C75,60.63,72.66,56.9,68.29,53.75Z"/>
		<path d="M57.5,22.5A17.5,17.5,0,1,0,75,40,17.52,17.52,0,0,0,57.5,22.5ZM66.32,48H48.68V45H66.32Zm0-6.5H48.68v-3H66.32Zm0-6.5H48.68V32H66.32Z"/>
		<path d="M40.57,35.65a16.84,16.84,0,0,1-3.12.3A16.48,16.48,0,1,1,54,19.53,16.14,16.14,0,0,1,53.63,23a16.64,16.64,0,0,1,3.11-.41,19.8,19.8,0,0,0,.26-3A19.48,19.48,0,0,0,37.57,0h0A19.56,19.56,0,0,0,18,19.42,19.48,19.48,0,0,0,37.5,39a19.65,19.65,0,0,0,2.56-.18A17.82,17.82,0,0,1,40.57,35.65Z"/>
	</symbol>
	<symbol id="library_active"  viewBox="0 0 75 75">
		<rect x="14.8" y="6.5" width="10" height="3"/>
		<rect x="14.8" y="12.5" width="10" height="3"/>
		<rect x="32.5" y="6.5" width="10" height="3"/>
		<rect x="32.5" y="12.5" width="10" height="3"/>
		<rect x="50.2" y="14.75" width="10" height="3"/>
		<path d="M58.75,75H12.12a12.13,12.13,0,0,1,0-24.25H13a22.15,22.15,0,0,1,42.3-8.26h3.45a16.26,16.26,0,0,1,0,32.51Z"/>
		<path d="M47.85,33.51V11.25h13.2a1.5,1.5,0,0,1,1.5,1.5V43a16.37,16.37,0,0,1,3,1V12.75a4.51,4.51,0,0,0-4.5-4.5H46.35a1.5,1.5,0,0,0-1.5,1.5v22A22.5,22.5,0,0,1,47.85,33.51Z"/>
		<path d="M30.15,30.1V4.5A1.5,1.5,0,0,1,31.65,3h11.7a1.5,1.5,0,0,1,1.5,1.5V31.74a22.5,22.5,0,0,1,3,1.77V4.5A4.51,4.51,0,0,0,43.35,0H31.65a4.51,4.51,0,0,0-4.5,4.5V31A20.42,20.42,0,0,1,30.15,30.1Z"/>
		<path d="M12.12,50.75h.33V4.5A1.5,1.5,0,0,1,14,3h11.7a1.5,1.5,0,0,1,1.5,1.5V31a20.42,20.42,0,0,1,3-.92V4.5A4.51,4.51,0,0,0,25.65,0H14a4.51,4.51,0,0,0-4.5,4.5V51.06A12.34,12.34,0,0,1,12.12,50.75Z"/>
	</symbol>
	<symbol id="mine_active"  viewBox="0 0 75 75">
		<path d="M37.5,0A37.5,37.5,0,1,0,75,37.5,37.54,37.54,0,0,0,37.5,0ZM17,29.14a1.42,1.42,0,0,1-.87.29,1.5,1.5,0,0,1-.88-2.72,17,17,0,0,1,14-2.69,1.5,1.5,0,1,1-.72,2.91A14.06,14.06,0,0,0,17,29.14Zm30.18,22.1a16.93,16.93,0,0,1-19.35.3,1.5,1.5,0,0,1,1.68-2.49,13.93,13.93,0,0,0,15.92-.24,1.5,1.5,0,0,1,1.75,2.43ZM60.08,28.8a1.49,1.49,0,0,1-1.22.63,1.42,1.42,0,0,1-.87-.29A14.06,14.06,0,0,0,46.5,26.93,1.5,1.5,0,1,1,45.78,24a17,17,0,0,1,14,2.69A1.5,1.5,0,0,1,60.08,28.8Z"/>
	</symbol>
	<symbol id="mobile"  viewBox="0 0 42 42">
		<path d="M21,42A21,21,0,1,1,42,21,21,21,0,0,1,21,42ZM21,1.35A19.65,19.65,0,1,0,40.65,21,19.68,19.68,0,0,0,21,1.35Z"/>
		<path d="M15.77,26.23c8.65,8.64,15.73,5,15.73,5a.8.8,0,0,0,.5-.71V25.6a.53.53,0,0,0-.53-.53H24.38a.65.65,0,0,0-.61.53l-.14.89a.51.51,0,0,1-.61.41s-1.73.29-5-2.95-3-5-3-5a.51.51,0,0,1,.41-.61l.89-.14a.65.65,0,0,0,.53-.61V10.53A.53.53,0,0,0,16.4,10H11.46a.78.78,0,0,0-.7.5S7.13,17.58,15.77,26.23Z"/>
	</symbol>
	<symbol id="reject"  viewBox="0 0 256 256">
		<circle cx="128" cy="128" r="128"/>
		<path d="M128,97.3c-61.75,0-74.09,38.26-74.09,38.26a4.08,4.08,0,0,0,.75,4.32l17.61,17.61a2.69,2.69,0,0,0,3.79,0l25.31-25.3a3.26,3.26,0,0,0,.32-4.06L99,124.43a2.57,2.57,0,0,1,.66-3.65s5.13-7.21,28.31-7.21,28.31,7.22,28.31,7.22a2.55,2.55,0,0,1,.66,3.64l-2.65,3.69a3.26,3.26,0,0,0,.31,4.06l25.31,25.31a2.68,2.68,0,0,0,3.79,0l17.61-17.6a4.1,4.1,0,0,0,.75-4.32S189.75,97.3,128,97.3Z"/>
	</symbol>
	<symbol id="circle_plus"  viewBox="0 0 56 56">
		<rect x="11.71" y="26.47" width="32.58" height="3.05"/>
		<rect x="26.47" y="11.71" width="3.05" height="32.58"/>
		<path d="M28,56A28,28,0,1,1,56,28,28,28,0,0,1,28,56ZM28,3.05A25,25,0,1,0,53,28,25,25,0,0,0,28,3.05Z"/>
	</symbol>
	<symbol id="ellipsis"  viewBox="0 0 48 48">
		<path d="M16.34,24h0a2.13,2.13,0,0,1-2.13,2.13H14.1A2.13,2.13,0,0,1,12,24h0a2.13,2.13,0,0,1,2.13-2.13h.11A2.13,2.13,0,0,1,16.34,24Z"/>
		<path d="M26.19,24h0a2.13,2.13,0,0,1-2.13,2.13H24A2.13,2.13,0,0,1,21.81,24h0A2.13,2.13,0,0,1,24,21.87h.11A2.13,2.13,0,0,1,26.19,24Z"/>
		<path d="M36,24h0a2.13,2.13,0,0,1-2.13,2.13h-.11A2.13,2.13,0,0,1,31.66,24h0a2.13,2.13,0,0,1,2.13-2.13h.11A2.13,2.13,0,0,1,36,24Z"/>
	</symbol>
	<symbol id="entry"  viewBox="0 0 30 54">
		<polygon class="cls-1" points="2.18 54 30 27 2.18 0 0 2.12 25.64 27 0 51.88 2.18 54"/>
	</symbol>
	<symbol id="switch_chat_model"  viewBox="0 0 36 36">
		<defs><style>.cls-1{fill:#fff;}</style></defs>
		<path class="cls-1" d="M26.76,36H8.51A8.88,8.88,0,0,1-.37,27.13V8.87A8.88,8.88,0,0,1,8.51,0H26.76a8.88,8.88,0,0,1,8.87,8.87V27.13A8.88,8.88,0,0,1,26.76,36ZM8.51,1.52A7.36,7.36,0,0,0,1.15,8.87V27.13a7.36,7.36,0,0,0,7.36,7.35H26.76a7.36,7.36,0,0,0,7.35-7.35V8.87a7.36,7.36,0,0,0-7.35-7.35Z"/>
		<path class="cls-1" d="M26.22,18.76H9a.76.76,0,0,1,0-1.52H26.22a.76.76,0,0,1,0,1.52Z"/>
		<path class="cls-1" d="M26.22,13.35H9a.76.76,0,0,1,0-1.52H26.22a.76.76,0,1,1,0,1.52Z"/>
		<path class="cls-1" d="M20.17,24.17h-11a.76.76,0,0,1,0-1.52h11a.76.76,0,1,1,0,1.52Z"/>
	</symbol>
	<symbol id="switch_check_model" viewBox="0 0 36 36">
		<defs><style>.cls-1{fill:#fff;}</style></defs>
		<path class="cls-1" d="M5.08,36H4.44a.74.74,0,0,1-.68-.5.76.76,0,0,1,.2-.83,7.2,7.2,0,0,0,1.86-4.51A17,17,0,0,1,0,17.43C0,7.87,8,.05,18,0S36,7.75,36,17.27,28,34.66,18,34.7a18.46,18.46,0,0,1-5.9-.93A10.66,10.66,0,0,1,5.08,36Zm13-34.48C8.87,1.56,1.49,8.7,1.52,17.43A15.56,15.56,0,0,0,6.83,29a1.52,1.52,0,0,1,.5,1.33,10.83,10.83,0,0,1-1.27,4.1,8.74,8.74,0,0,0,5.11-1.85,1.51,1.51,0,0,1,1.44-.27,17.09,17.09,0,0,0,5.33.85c9.19,0,16.56-7.17,16.54-15.91S27.09,1.52,18.05,1.52Z"/><path class="cls-1" d="M26.59,18H9.41a.76.76,0,1,1,0-1.52H26.59a.76.76,0,0,1,0,1.52Z"/><path class="cls-1" d="M26.59,12.61H9.41a.76.76,0,0,1,0-1.52H26.59a.76.76,0,1,1,0,1.52Z"/><path class="cls-1" d="M20.54,23.42H15.46a.76.76,0,1,1,0-1.52h5.08a.76.76,0,1,1,0,1.52Z"/>
	</symbol>
	<symbol id="dial_dark" viewBox="0 0 42 17">
		<defs><style>.cls-1{fill:#3b3e3f;}</style></defs>
		<path class="cls-1" d="M21,0C3.57,0,.09,10.67.09,10.67a1.13,1.13,0,0,0,.21,1.2l5,4.91a.77.77,0,0,0,1.07,0l7.14-7a.88.88,0,0,0,.09-1.13l-.75-1a.71.71,0,0,1,.19-1s1.45-2,8-2,8,2,8,2a.71.71,0,0,1,.19,1l-.75,1a.89.89,0,0,0,.08,1.13l7.15,7.06a.77.77,0,0,0,1.07,0l5-4.91a1.13,1.13,0,0,0,.21-1.2S38.43,0,21,0Z"/>
	</symbol>
	<symbol id="dial_lighten" viewBox="0 0 50 50">
		<defs><style>.cls-1{fill:#fff;}</style></defs>
		<path class="cls-1" d="M21,0C3.57,0,.09,10.67.09,10.67a1.13,1.13,0,0,0,.21,1.2l5,4.91a.77.77,0,0,0,1.07,0l7.14-7a.88.88,0,0,0,.09-1.13l-.75-1a.71.71,0,0,1,.19-1s1.45-2,8-2,8,2,8,2a.71.71,0,0,1,.19,1l-.75,1a.89.89,0,0,0,.08,1.13l7.15,7.06a.77.77,0,0,0,1.07,0l5-4.91a1.13,1.13,0,0,0,.21-1.2S38.43,0,21,0Z"/>
	</symbol>
	<symbol id="volume_dark" viewBox="0 0 23 32">
		<defs><style>.cls-1{fill:#3b3e3f;}</style></defs>
		<path class="cls-1" d="M11,26.08a1,1,0,0,1-.7-1.71,11.9,11.9,0,0,0,0-16.74,1,1,0,0,1,0-1.42,1,1,0,0,1,1.4,0,13.93,13.93,0,0,1,0,19.58A1,1,0,0,1,11,26.08Z"/><path class="cls-1" d="M15.86,32a1,1,0,0,1-.7-1.71,20.36,20.36,0,0,0,0-28.58,1,1,0,0,1,0-1.42,1,1,0,0,1,1.4,0,22.38,22.38,0,0,1,0,31.42A1,1,0,0,1,15.86,32Z"/><path class="cls-1" d="M5.3,20.34a6.19,6.19,0,0,0,0-8.68L1,16Z"/><path class="cls-1" d="M5.3,21.34a1,1,0,0,1-.71-.29L.29,16.71a1,1,0,0,1,0-1.42L4.59,11A1,1,0,0,1,6,11a7.2,7.2,0,0,1,0,10.1A1,1,0,0,1,5.3,21.34Z"/>
	</symbol>
	<symbol id="volume_lighten" viewBox="0 0 23 32">
		<defs><style>.cls-1{}</style></defs>
		<path class="cls-1" d="M11,26.08a1,1,0,0,1-.7-1.71,11.9,11.9,0,0,0,0-16.74,1,1,0,0,1,0-1.42,1,1,0,0,1,1.4,0,13.93,13.93,0,0,1,0,19.58A1,1,0,0,1,11,26.08Z"/><path class="cls-1" d="M15.86,32a1,1,0,0,1-.7-1.71,20.36,20.36,0,0,0,0-28.58,1,1,0,0,1,0-1.42,1,1,0,0,1,1.4,0,22.38,22.38,0,0,1,0,31.42A1,1,0,0,1,15.86,32Z"/><path class="cls-1" d="M5.3,20.34a6.19,6.19,0,0,0,0-8.68L1,16Z"/><path class="cls-1" d="M5.3,21.34a1,1,0,0,1-.71-.29L.29,16.71a1,1,0,0,1,0-1.42L4.59,11A1,1,0,0,1,6,11a7.2,7.2,0,0,1,0,10.1A1,1,0,0,1,5.3,21.34Z"/>
	</symbol>
	<symbol id="play" viewBox="0 0 102 102">
		<circle cx="51" cy="51" r="50" style="fill: none;stroke: #fff;stroke-miterlimit: 10;stroke-width: 2px;"/>
		<path d="M74.6,53.22,44.25,70.75a2.57,2.57,0,0,1-3.85-2.23v-35a2.57,2.57,0,0,1,3.85-2.23L74.6,48.78A2.56,2.56,0,0,1,74.6,53.22Z" style='fill: #fff'/>
	</symbol>
	<symbol id="speak_up" viewBox="0 0 150 150">
		<circle cx="75" cy="75" r="75" style="fill:#fff;"/>
		<path d="M75,37.2a16,16,0,0,0-16,16V76.08a16,16,0,0,0,32,0V53.21A16,16,0,0,0,75,37.2Z" style="fill:#00c49c;"/>
		<path d="M47.4,73.25c.19,15.8,12.47,28.56,27.6,28.56s27.41-12.76,27.6-28.56" style="stroke:#00c49c;stroke-linecap:round;stroke-linejoin:round;stroke-width:3.6px;fill:none;"/>
		<line x1="75" y1="101.81" x2="75" y2="112.8" style="stroke:#00c49c;stroke-linecap:round;stroke-linejoin:round;stroke-width:3.6px;fill:none;"/>
		<line x1="60.61" y1="112.8" x2="89.39" y2="112.8" style="stroke:#00c49c;stroke-linecap:round;stroke-linejoin:round;stroke-width:3.6px;fill:none;"/>
	</symbol>
	<symbol id="speak_off"  viewBox="0 0 150 150">
		<circle class="cls-1" cx="75" cy="75" r="75" style="fill:#ff675c;"/>
		<path class="cls-2" d="M75,37.2a16,16,0,0,0-16,16V76.08a16,16,0,0,0,32,0V53.21A16,16,0,0,0,75,37.2Z" style="fill:#fff;"/>
		<path class="cls-3" d="M47.4,73.25c.19,15.8,12.47,28.56,27.6,28.56s27.41-12.76,27.6-28.56" style="stroke:#fff;stroke-linecap:round;stroke-linejoin:round;stroke-width:3.6px;fill:none;"/>
		<line class="cls-3" x1="75" y1="101.81" x2="75" y2="112.8" style="stroke:#fff;stroke-linecap:round;stroke-linejoin:round;stroke-width:3.6px;fill:none;"/>
		<line class="cls-3" x1="60.61" y1="112.8" x2="89.39" y2="112.8" style="stroke:#fff;stroke-linecap:round;stroke-linejoin:round;stroke-width:3.6px;fill:none;"/>
		<line class="cls-4" x1="66.24" y1="55.88" x2="83.77" y2="73.41" style="fill:none;stroke:#ff675c;stroke-miterlimit:10;stroke-width:3px;"/>
		<line class="cls-4" x1="83.77" y1="55.88" x2="66.24" y2="73.41" style="fill:none;stroke:#ff675c;stroke-miterlimit:10;stroke-width:3px;"/>
	</symbol>
	<symbol id="switch_to_speak" viewBox="0 0 56 56">
		<circle class="cls-1" cx="28" cy="28" r="28" style="fill:#02b187;"/>
		<circle class="cls-2" cx="28" cy="28" r="25" style="fill:#00c49c;"/>
		<path class="cls-3" d="M27.47,18.92a12.83,12.83,0,0,1,0,18.16" style="fill:none;stroke:#fff;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;"/>
		<path class="cls-3" d="M32.39,43a21.2,21.2,0,0,0,0-30" style="fill:none;stroke:#fff;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;"/>
		<path class="cls-4" d="M21.74,32.34a6.15,6.15,0,0,0,0-8.68L17.39,28Z" style="fill:#fff;stroke:#fff;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;"/>
	</symbol>
	<symbol id="switch_to_keyboard" viewBox="0 0 56 56">
		<circle class="cls-1" cx="28" cy="28" r="28" style="fill:#02b187;"/>
		<circle class="cls-2" cx="28" cy="28" r="25" style="fill:#00c49c;"/>
		<rect class="cls-3" x="12" y="15.31" width="5.52" height="5.52" style="fill:#fff;"/>
		<rect class="cls-3" x="20.83" y="15.31" width="5.52" height="5.52" style="fill:#fff;"/>
		<rect class="cls-3" x="29.66" y="15.31" width="5.52" height="5.52" style="fill:#fff;"/>
		<rect class="cls-3" x="38.48" y="15.31" width="5.52" height="5.52" style="fill:#fff;"/>
		<rect class="cls-3" x="12" y="23.59" width="5.52" height="5.52" style="fill:#fff;"/>
		<rect class="cls-3" x="20.83" y="23.59" width="5.52" height="5.52" style="fill:#fff;"/>
		<rect class="cls-3" x="29.66" y="23.59" width="5.52" height="5.52" style="fill:#fff;"/>
		<rect class="cls-3" x="38.48" y="23.59" width="5.52" height="5.52" style="fill:#fff;"/>
		<rect class="cls-3" x="19.17" y="35.17" width="17.66" height="5.52" style="fill:#fff;"/>
	</symbol>
	<symbol id="emoji"  viewBox="0 0 58 58">
		<circle cx="29" cy="29" r="28" style="fill:none;stroke:#656d70;stroke-miterlimit:10;stroke-width:2px;"/>
		<circle cx="19.75" cy="21.78" r="3.25" style="fill:#656d70;"/>
		<circle cx="38.25" cy="21.78" r="3.25" style="fill:#656d70;"/>
		<path d="M39.84,35A13.08,13.08,0,0,1,29,40.77" style="fill:none;stroke:#656d70;stroke-miterlimit:10;stroke-width:2px;stroke-linecap:round;"/>
		<path d="M18.16,35A13.08,13.08,0,0,0,29,40.77" style="fill:none;stroke:#656d70;stroke-miterlimit:10;stroke-width:2px;stroke-linecap:round;"/>
	</symbol>
	<symbol id="choose_more" viewBox="0 0 58 58">
		<circle class="cls-1" cx="29" cy="29" r="28" style="fill:none;stroke:#656d70;stroke-miterlimit:10;stroke-width:2px;"/>
		<line class="cls-1" x1="16.6" y1="29" x2="41.4" y2="29" style="fill:none;stroke:#656d70;stroke-miterlimit:10;stroke-width:2px;"/>
		<line class="cls-1" x1="29" y1="16.6" x2="29" y2="41.4" style="fill:none;stroke:#656d70;stroke-miterlimit:10;stroke-width:2px;"/>
	</symbol>
	<symbol id="android_send" viewBox="0 0 58 58"><defs><style>.cls-1,.cls-2{fill:none;stroke:#00c49c;stroke-width:2px;}.cls-1{stroke-miterlimit:10;}.cls-2{stroke-linecap:round;stroke-linejoin:round;}</style></defs>
		<circle class="cls-1" cx="29" cy="29" r="28" style="fill:none;stroke:#00c49c;stroke-width:2px;stroke-miterlimit:10;"/>
		<line class="cls-2" x1="22.59" y1="42.77" x2="28.63" y2="35.91" style="fill:none;stroke:#00c49c;stroke-width:2px;stroke-linecap:round;stroke-linejoin:round;"/>
		<polyline class="cls-2" points="18.35 31.71 46.49 17.23 23.95 33.35" style="fill:none;stroke:#00c49c;stroke-width:2px;stroke-linecap:round;stroke-linejoin:round;"/>
		<polygon class="cls-2" points="8.65 27.05 46.49 17.23 35.15 39.49 23.95 33.35 22.59 42.77 18.35 31.71 8.65 27.05" style="fill:none;stroke:#00c49c;stroke-width:2px;stroke-linecap:round;stroke-linejoin:round;"/>
	</symbol>
	<symbol id="local_patient_car" viewBox="0 0 114 114"><defs><style>.cls-1{fill:#c0c9ce;}.cls-2,.cls-3,.cls-5{fill:#fff;}.cls-2{opacity:0.7;}.cls-3{opacity:0.5;}.cls-4{fill:none;stroke:#00c49c;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;}</style></defs>
		<rect class="cls-1" width="114" height="114" rx="57" style="fill:#c0c9ce;"/>
		<path class="cls-2" d="M36.51,35.49H69a3,3,0,0,1,3,3V64.55a0,0,0,0,1,0,0H33.51a0,0,0,0,1,0,0V38.49A3,3,0,0,1,36.51,35.49Z" style="fill:#fff;opacity:0.7;"/>
		<path class="cls-3" d="M42,30v5.49H69a3,3,0,0,1,3,3v22h8.49V30a3,3,0,0,0-3-3H45A3,3,0,0,0,42,30Z" style="fill:#fff;opacity:0.5;"/>
		<line class="cls-4" x1="40.58" y1="42.75" x2="65.49" y2="42.75" style="fill:none;stroke:#00c49c;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;"/>
		<line class="cls-4" x1="58.13" y1="48.28" x2="65.49" y2="48.28" style="fill:none;stroke:#00c49c;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;"/>
		<path class="cls-5" d="M87,59.22V84a3,3,0,0,1-3,3H30a3,3,0,0,1-3-3V52.38a3,3,0,0,1,3-3H50.09a3,3,0,0,1,2.29,1.06l4,4.72a3,3,0,0,0,2.29,1.06H84A3,3,0,0,1,87,59.22Z" style="fill:#fff;"/>
	</symbol>
	<symbol id="take_photo" viewBox="0 0 114 114"><defs><style>.cls-1{fill:#c0c9ce;}.cls-2{fill:#fff;}.cls-3{fill:none;stroke:#00c49c;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;}</style></defs>
		<rect class="cls-1" width="114" height="114" rx="57" style="fill:#c0c9ce;"/>
		<path class="cls-2" d="M70.41,39.32h0A7.31,7.31,0,0,0,63.1,32H50.9a7.31,7.31,0,0,0-7.31,7.32H35a7,7,0,0,0-7,7V75a7,7,0,0,0,7,7H79a7,7,0,0,0,7-7V46.32a7,7,0,0,0-7-7Z" style="fill:#fff;"/>
		<circle class="cls-3" cx="57" cy="60.66" r="10.37" style="fill:none;stroke:#00c49c;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;"/>
	</symbol>
	<symbol id="real_time_voice" viewBox="0 0 114 114">
		<defs><style>.cls-1{fill:#c0c9ce;}.cls-2{fill:#fff;}.cls-3{fill:none;stroke:#00c49c;stroke-linecap:round;stroke-linejoin:round;stroke-width:2.27px;}</style></defs>
		<rect class="cls-1" width="114" height="114" rx="57" style="fill:#c0c9ce;"/>
		<path class="cls-2" d="M44,70c19.65,19.65,35.75,11.4,35.75,11.4a1.85,1.85,0,0,0,1.13-1.62V68.6a1.21,1.21,0,0,0-1.2-1.21H63.55a1.47,1.47,0,0,0-1.39,1.19l-.33,2a1.15,1.15,0,0,1-1.37,1s-3.93.66-11.3-6.71-6.71-11.3-6.71-11.3a1.14,1.14,0,0,1,.94-1.37l2-.33a1.47,1.47,0,0,0,1.2-1.39V34.34a1.21,1.21,0,0,0-1.21-1.2H34.19a1.84,1.84,0,0,0-1.61,1.13S24.33,50.37,44,70Z" style="fill:#fff;"/>
		<path class="cls-3" d="M56.42,43A14.6,14.6,0,0,1,71,57.58" style="fill:none;stroke:#00c49c;stroke-linecap:round;stroke-linejoin:round;stroke-width:2.27px;"/>
		<path class="cls-3" d="M79.73,58.38A24.12,24.12,0,0,0,55.62,34.27" style="fill:none;stroke:#00c49c;stroke-linecap:round;stroke-linejoin:round;stroke-width:2.27px;"/>
		<path class="cls-3" d="M62.6,58.38a7,7,0,0,0-7-7" style="fill:none;stroke:#00c49c;stroke-linecap:round;stroke-linejoin:round;stroke-width:2.27px;"/>
	</symbol>
	<symbol id="broadcast" viewBox="0 0 114 114"><defs><style>.cls-1{fill:#c0c9ce;}.cls-2,.cls-3{fill:#fff;}.cls-2{opacity:0.5;}.cls-4{fill:#00c49c;}</style></defs>
		<rect class="cls-1" width="114" height="114" rx="57" style="fill:#c0c9ce;"/>
		<path class="cls-2" d="M71.49,60.39V73.16a3.11,3.11,0,0,1-2.87,3.29H27.38a3.11,3.11,0,0,1-2.88-3.29V35.84a3.11,3.11,0,0,1,2.88-3.29H68.62a3.11,3.11,0,0,1,2.87,3.29V48.16l10.61-8c1.32-1,2.4-.34,2.4,1.46V67c0,1.81-1.08,2.47-2.4,1.47Z" style="fill:#fff;opacity:0.5;"/>
		<path class="cls-3" d="M76.49,65.39V78.16a3.11,3.11,0,0,1-2.87,3.29H32.38a3.11,3.11,0,0,1-2.88-3.29V40.84a3.11,3.11,0,0,1,2.88-3.29H73.62a3.11,3.11,0,0,1,2.87,3.29V53.16l10.61-8c1.32-1,2.4-.34,2.4,1.46V72c0,1.81-1.08,2.47-2.4,1.47Z" style="fill:#fff;"/>
		<circle class="cls-4" cx="37.86" cy="45.79" r="4.29" style="fill:#00c49c;"/>
	</symbol>
	<symbol id="contact_card_transfer" viewBox="0 0 114 114"><defs><style>.cls-1{fill:#c0c9ce;}.cls-2{fill:#fff;}.cls-3{fill:none;stroke:#00c49c;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;}</style></defs>
		<rect class="cls-1" width="114" height="114" rx="57" style="fill:#c0c9ce;"/>
		<rect class="cls-2" x="27" y="32" width="60" height="50" rx="2.95" transform="translate(0 114) rotate(-90)" style="fill:#fff;"/>
		<path class="cls-3" d="M56.31,75H57l3.16,0,3.48,0c3.58,0,11,0,11.25-2.38L75,70.5c0-2.18-1.79-4.32-5-6A29.3,29.3,0,0,0,57,61.79h-.12c-9.69,0-17.89,4-17.91,8.65l.07,2.11C39.26,75,47,75,51.58,75h4.73Z" style="fill:none;stroke:#00c49c;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;"/>
		<path class="cls-3" d="M57,39a9,9,0,0,0-9,9,9,9,0,0,0,9,9h0a9,9,0,1,0,0-18Z" style="fill:none;stroke:#00c49c;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;"/>
	</symbol>
	<symbol id="picture" viewBox="0 0 114 114">
		<rect class="cls-2" width="114" height="114" rx="57" style="fill:#c0c9ce;"/>
		<rect class="cls-3" x="29" y="35" width="50" height="50" rx="14.93" style="fill:#fff;opacity:0.5;"/>
		<rect class="cls-4" x="35" y="29" width="50" height="50" rx="14.93" style="fill:#fff;"/>
		<circle class="cls-5" cx="49.29" cy="43.29" r="4.29" style="fill:#00c49c;"/>
		<g class="cls-6">
			<path class="cls-7" d="M85.84,54.45l-8.75-4.74a4,4,0,0,0-4.79.51L58,63.59a3.76,3.76,0,0,1-4.89.22l-6.53-5.08a3.78,3.78,0,0,0-4.57,0l-8.4,6.22" style="stroke:#00c49c;stroke-linecap:round;stroke-linejoin:round;stroke-width:1.71px;fill:none;"/>
		</g>
	</symbol>
	<symbol id="folder" viewBox="0 0 114 114"><defs><style>.cls-1{fill:#c0c9ce;}.cls-2{fill:#fff;}.cls-3{fill:none;stroke:#00c49c;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;}</style></defs>
		<rect class="cls-1" width="114" height="114" rx="57" style="fill:#c0c9ce;"/>
		<path class="cls-2" d="M79.65,37.36h3.06C85.07,37.36,87,39,87,40.93v37.5c0,2-1.93,3.57-4.29,3.57H31.29C28.93,82,27,80.39,27,78.43V35.57c0-2,1.93-3.57,4.29-3.57H51.71l5.53,5.36Z" style="fill:#fff;"/>
		<line class="cls-3" x1="44.55" y1="60" x2="69.45" y2="60" style="fill:none;stroke:#00c49c;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;"/>
		<line class="cls-3" x1="44.55" y1="52.16" x2="69.45" y2="52.16" style="fill:none;stroke:#00c49c;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;"/>
		<line class="cls-3" x1="44.55" y1="67.84" x2="60.68" y2="67.84" style="fill:none;stroke:#00c49c;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;"/>
	</symbol>
	<symbol id="chat_history" viewBox="0 0 114 114"><defs><style>.cls-1{fill:#c0c9ce;}.cls-2{opacity:0.5;}.cls-3{fill:#fff;}.cls-4{fill:none;stroke:#00c49c;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;}</style></defs>
		<rect class="cls-1" width="114" height="114" rx="57" style="fill:#c0c9ce;"/>
		<g class="cls-2" style="opacity:0.5;">
			<path class="cls-3" d="M43.84,83.82a25.54,25.54,0,0,0,8.07,1.3h.14c13.8-.06,25-10.9,25-24.18S65.79,37,52.07,37H52c-13.8.06-25,10.91-25,24.18a23.65,23.65,0,0,0,8.07,17.63,1.07,1.07,0,0,1,.36,1c-.48,3.75-1.65,6-3,7.22l.88,0c4,0,7.18-1,9.54-3a1.07,1.07,0,0,1,.7-.25A1,1,0,0,1,43.84,83.82Z" style="fill:#fff;"/>
		</g>
		<path class="cls-3" d="M70.5,73.77a1.07,1.07,0,0,1,.7.25c2.36,2,5.57,3,9.54,3l.88,0c-1.4-1.26-2.57-3.47-3.05-7.22a1.07,1.07,0,0,1,.36-1A23.65,23.65,0,0,0,87,51.18c0-13.27-11.15-24.12-25-24.18h-.12C48.21,27,37,37.74,37,50.94S48.15,75.06,62,75.12h.14a25.54,25.54,0,0,0,8.07-1.3A1,1,0,0,1,70.5,73.77Z" style="fill:#fff;"/>
		<line class="cls-4" x1="49.55" y1="50.92" x2="74.45" y2="50.92" style="fill:none;stroke:#00c49c;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;"/>
		<line class="cls-4" x1="49.55" y1="43.08" x2="74.45" y2="43.08" style="fill:none;stroke:#00c49c;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;"/>
		<line class="cls-4" x1="58.32" y1="58.76" x2="65.68" y2="58.76" style="fill:none;stroke:#00c49c;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;"/>
	</symbol>
	<symbol id="reserve_meeting" viewBox="0 0 114 114"><defs><style>.cls-1{fill:#c0c9ce;}.cls-2{fill:#fff;}.cls-3{fill:none;stroke:#00c49c;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;}.cls-4{opacity:0.5;}</style></defs>
		<rect class="cls-1" width="114" height="114" rx="57" style="fill:#c0c9ce;"/>
		<rect class="cls-2" x="27" y="34.41" width="60" height="51.85" rx="3" style="fill:#fff;"/>
		<circle class="cls-3" cx="57" cy="60.33" r="16" style="fill:none;stroke:#00c49c;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;"/>
		<polyline class="cls-3" points="50.29 60.03 55.66 66.06 63.71 56" style="fill:none;stroke:#00c49c;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;"/>
		<g class="cls-4" style="opacity:0.5;">
			<rect class="cls-2" x="37.91" y="27.74" width="8.18" height="13.33" rx="3" style="fill:#fff;"/>
			<rect class="cls-2" x="67.91" y="27.74" width="8.18" height="13.33" rx="3" style="fill:#fff;"/>
		</g>
	</symbol>
	<symbol id="collection" viewBox="0 0 114 114"><defs><style>.cls-1{fill:#c0c9ce;}.cls-2{fill:#fff;}.cls-3{fill:none;stroke:#00c49c;stroke-linecap:round;stroke-linejoin:round;stroke-width:1.71px;}</style></defs>
		<rect class="cls-1" width="114" height="114" rx="57" style="fill:#c0c9ce;"/>
		<path class="cls-2" d="M79.65,37.36h3.06C85.07,37.36,87,39,87,40.93v37.5c0,2-1.93,3.57-4.29,3.57H31.29C28.93,82,27,80.39,27,78.43V35.57c0-2,1.93-3.57,4.29-3.57H51.71l5.53,5.36Z" style="fill:#fff;"/>
		<polygon class="cls-3" points="57 67.1 49.58 71 51 62.74 45 56.89 53.29 55.69 57 48.17 60.71 55.69 69 56.89 63 62.74 64.42 71 57 67.1" style="fill:none;stroke:#00c49c;stroke-linecap:round;stroke-linejoin:round;stroke-width:1.71px;"/>
	</symbol>
	<symbol id="iworks_white" viewBox="0 0 30 30"><defs><style>.cls-1{fill:#fff;}</style></defs>
		<path class="cls-1" d="M12,13.92H1.91A1.92,1.92,0,0,1,0,12V1.91A1.92,1.92,0,0,1,1.91,0H12a1.92,1.92,0,0,1,1.91,1.91V12A1.92,1.92,0,0,1,12,13.92ZM1.91,1.47a.44.44,0,0,0-.44.44V12a.45.45,0,0,0,.44.45H12a.45.45,0,0,0,.45-.45V1.91A.45.45,0,0,0,12,1.47Z" style="fill:#fff;"/>
		<path class="cls-1" d="M28.09,13.92H18A1.92,1.92,0,0,1,16.08,12V1.91A1.92,1.92,0,0,1,18,0h10.1A1.92,1.92,0,0,1,30,1.91V12A1.92,1.92,0,0,1,28.09,13.92ZM18,1.47a.45.45,0,0,0-.45.44V12a.45.45,0,0,0,.45.45h10.1a.45.45,0,0,0,.44-.45V1.91a.44.44,0,0,0-.44-.44Z" style="fill:#fff;"/>
		<path class="cls-1" d="M28.09,13.92H18A1.92,1.92,0,0,1,16.08,12V1.91A1.92,1.92,0,0,1,18,0h10.1A1.92,1.92,0,0,1,30,1.91V12A1.92,1.92,0,0,1,28.09,13.92ZM18,1.47a.45.45,0,0,0-.45.44V12a.45.45,0,0,0,.45.45h10.1a.45.45,0,0,0,.44-.45V1.91a.44.44,0,0,0-.44-.44Z" style="fill:#fff;"/>
		<path class="cls-1" d="M12,30H1.91A1.92,1.92,0,0,1,0,28.09V18a1.92,1.92,0,0,1,1.91-1.91H12A1.92,1.92,0,0,1,13.92,18v10.1A1.92,1.92,0,0,1,12,30ZM1.91,17.54a.45.45,0,0,0-.44.45v10.1a.44.44,0,0,0,.44.44H12a.45.45,0,0,0,.45-.44V18a.45.45,0,0,0-.45-.45Z" style="fill:#fff;"/>
		<path class="cls-1" d="M28.09,30H18a1.92,1.92,0,0,1-1.91-1.91V18A1.92,1.92,0,0,1,18,16.08h10.1A1.92,1.92,0,0,1,30,18v10.1A1.92,1.92,0,0,1,28.09,30ZM18,17.54a.45.45,0,0,0-.45.45v10.1a.45.45,0,0,0,.45.44h10.1a.44.44,0,0,0,.44-.44V18a.45.45,0,0,0-.44-.45Z" style="fill:#fff;"/>
		<path class="cls-1" d="M22.2,26a.7.7,0,0,1-.51-.22l-2.2-2.19a.74.74,0,1,1,1-1L22.2,24.2l3.34-3.34a.73.73,0,0,1,1,1l-3.85,3.85A.71.71,0,0,1,22.2,26Z" style="fill:#fff;"/>
	</symbol>
	<symbol id="iworks_blue" viewBox="0 0 34 34"><defs><style>.cls-1{fill:#56c7ff;}.cls-2{fill:none;stroke:#fff;stroke-linecap:round;stroke-linejoin:round;stroke-width:0.94px;}</style></defs>
		<circle class="cls-1" cx="17" cy="17" r="17" style="fill:#56c7ff;"/>
		<rect class="cls-2" x="7.81" y="7.81" width="8.02" height="8.02" rx="0.76" style="fill:none;stroke:#fff;stroke-linecap:round;stroke-linejoin:round;stroke-width:0.94px;"/>
		<rect class="cls-2" x="18.17" y="7.81" width="8.02" height="8.02" rx="0.76" style="fill:none;stroke:#fff;stroke-linecap:round;stroke-linejoin:round;stroke-width:0.94px;"/>
		<rect class="cls-2" x="18.17" y="7.81" width="8.02" height="8.02" rx="0.76" style="fill:none;stroke:#fff;stroke-linecap:round;stroke-linejoin:round;stroke-width:0.94px;"/>
		<rect class="cls-2" x="7.81" y="18.17" width="8.02" height="8.02" rx="0.76" style="fill:none;stroke:#fff;stroke-linecap:round;stroke-linejoin:round;stroke-width:0.94px;"/>
		<rect class="cls-2" x="18.17" y="18.17" width="8.02" height="8.02" rx="0.76" style="fill:none;stroke:#fff;stroke-linecap:round;stroke-linejoin:round;stroke-width:0.94px;"/>
		<polyline class="cls-2" points="24.12 21.11 21.64 23.59 20.23 22.18" style="fill:none;stroke:#fff;stroke-linecap:round;stroke-linejoin:round;stroke-width:0.94px;"/>
	</symbol>
	<symbol id="accept_video" viewBox="0 0 44 44"><defs><style>.cls-1{fill:#00c49c;}</style></defs>
		<path class="cls-1" d="M11.54,32.46C28.83,49.75,43,42.49,43,42.49a1.62,1.62,0,0,0,1-1.42V31.2a1.07,1.07,0,0,0-1.06-1.06H28.77a1.3,1.3,0,0,0-1.23,1.05L27.25,33a1,1,0,0,1-1.21.84s-3.45.58-9.94-5.91S10.19,18,10.19,18a1,1,0,0,1,.84-1.2l1.78-.29a1.29,1.29,0,0,0,1-1.23V1.06A1.06,1.06,0,0,0,12.79,0H2.93A1.62,1.62,0,0,0,1.51,1S-5.75,15.17,11.54,32.46Z" style="fill:#00c49c;"/>
		<path class="cls-1" d="M39.49,9.66v4.43a1.08,1.08,0,0,1-1,1.14H24.18a1.08,1.08,0,0,1-1-1.14V1.14a1.08,1.08,0,0,1,1-1.14H38.49a1.08,1.08,0,0,1,1,1.14V5.42l3.68-2.79c.46-.35.83-.12.83.51v8.8c0,.63-.37.86-.83.51Z" style="fill:#00c49c;"/>
	</symbol>
	<symbol id="accept_voice" viewBox="0 0 44 44"><defs><style>.cls-1{fill:#00c49c;}.cls-2{fill:none;stroke:#00c49c;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;}</style></defs>
		<path class="cls-1" d="M11.54,32.46C28.83,49.75,43,42.49,43,42.49a1.62,1.62,0,0,0,1-1.42V31.2a1.07,1.07,0,0,0-1.06-1.06H28.77a1.3,1.3,0,0,0-1.23,1.05L27.25,33a1,1,0,0,1-1.21.84s-3.45.58-9.94-5.91S10.19,18,10.19,18a1,1,0,0,1,.84-1.2l1.78-.29a1.29,1.29,0,0,0,1-1.23V1.06A1.06,1.06,0,0,0,12.79,0H2.93A1.62,1.62,0,0,0,1.51,1S-5.75,15.17,11.54,32.46Z" style="fill:#00c49c;"/>
		<path class="cls-2" d="M22.49,8.67A12.83,12.83,0,0,1,35.33,21.51" style="fill:none;stroke:#00c49c;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;"/>
		<path class="cls-2" d="M43,22.21A21.22,21.22,0,0,0,21.79,1" style="fill:none;stroke:#00c49c;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;"/>
		<path class="cls-2" d="M27.93,22.21a6.14,6.14,0,0,0-6.14-6.14" style="fill:none;stroke:#00c49c;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;"/>
	</symbol>
	<symbol id="off_dial" viewBox="0 0 44 44">
		<path class="cls-1" d="M11.54,32.46C28.83,49.75,43,42.49,43,42.49a1.62,1.62,0,0,0,1-1.42V31.2a1.07,1.07,0,0,0-1.06-1.06H28.77a1.3,1.3,0,0,0-1.23,1.05L27.25,33a1,1,0,0,1-1.21.84s-3.45.58-9.94-5.91S10.19,18,10.19,18a1,1,0,0,1,.84-1.2l1.78-.29a1.29,1.29,0,0,0,1-1.23V1.06A1.06,1.06,0,0,0,12.79,0H2.93A1.62,1.62,0,0,0,1.51,1S-5.75,15.17,11.54,32.46Z" style="fill:#ff675c;"/>
		<circle class="cls-1" cx="33.58" cy="10.42" r="10.42" style="fill:#ff675c;"/>
		<line class="cls-2" x1="28.95" y1="5.79" x2="38.21" y2="15.05" style="fill:none;stroke:#fff;stroke-linecap:round;stroke-linejoin:round;stroke-width:2.32px;"/>
		<line class="cls-2" x1="28.95" y1="15.05" x2="38.21" y2="5.79" style="fill:none;stroke:#fff;stroke-linecap:round;stroke-linejoin:round;stroke-width:2.32px;"/>
	</symbol>
	<symbol id="hand_on" viewBox="0 0 32 44">
		<path class="cls-1" d="M16,43.5C7.45,43.5.5,37.29.5,29.67V10A3.35,3.35,0,0,1,4,6.81a3.67,3.67,0,0,1,2.5,1V5.7A3.35,3.35,0,0,1,10,2.53a3.7,3.7,0,0,1,2.51,1A3.37,3.37,0,0,1,16,.5a3.35,3.35,0,0,1,3.5,3.17v.94a3.68,3.68,0,0,1,2.5-1,3.36,3.36,0,0,1,3.5,3.17v8.79a3.72,3.72,0,0,1,2.5-.95,3.35,3.35,0,0,1,3.5,3.17V29.67C31.5,37.29,24.55,43.5,16,43.5ZM4,7.81A2.35,2.35,0,0,0,1.5,10V29.67C1.5,36.74,8,42.5,16,42.5s14.5-5.76,14.5-12.83V17.83A2.35,2.35,0,0,0,28,15.66a2.35,2.35,0,0,0-2.5,2.17h-1v-11A2.36,2.36,0,0,0,22,4.65a2.36,2.36,0,0,0-2.5,2.17h-1V3.67A2.35,2.35,0,0,0,16,1.5a2.35,2.35,0,0,0-2.5,2.17v2h-1A2.36,2.36,0,0,0,10,3.53,2.36,2.36,0,0,0,7.5,5.7V10h-1A2.35,2.35,0,0,0,4,7.81Z" style="fill:#00c49c;"/>
		<path class="cls-1" d="M25,22.69a1,1,0,0,1-1-1V6.82a1.86,1.86,0,0,0-2-1.67,1.86,1.86,0,0,0-2,1.67V20.76a1,1,0,1,1-2,0V6.82a3.85,3.85,0,0,1,4-3.67,3.85,3.85,0,0,1,4,3.67V21.69A1,1,0,0,1,25,22.69Z" style="fill:#00c49c;"/>
		<path class="cls-1" d="M13,21.76a1,1,0,0,1-1-1V5.7A1.86,1.86,0,0,0,10,4,1.86,1.86,0,0,0,8,5.7V19.64a1,1,0,0,1-2,0V5.7A3.85,3.85,0,0,1,10,2,3.85,3.85,0,0,1,14,5.7V20.76A1,1,0,0,1,13,21.76Z" style="fill:#00c49c;"/>
		<path class="cls-1" d="M1,25.91a1,1,0,0,1-1-1V10A3.84,3.84,0,0,1,4,6.31,3.84,3.84,0,0,1,8,10V20.76a1,1,0,0,1-2,0V10A1.86,1.86,0,0,0,4,8.31,1.86,1.86,0,0,0,2,10V24.91A1,1,0,0,1,1,25.91Z" style="fill:#00c49c;"/>
		<path class="cls-1" d="M13,19.6a1,1,0,0,1-1-1V3.67A3.85,3.85,0,0,1,16,0a3.85,3.85,0,0,1,4,3.67V18.53a1,1,0,0,1-2,0V3.67A1.86,1.86,0,0,0,16,2a1.86,1.86,0,0,0-2,1.67V18.6A1,1,0,0,1,13,19.6Z" style="fill:#00c49c;"/>
		<path class="cls-1" d="M31,29.42a1,1,0,0,1-1-1V17.83a1.86,1.86,0,0,0-2-1.67,1.86,1.86,0,0,0-2,1.67v9.85a1,1,0,0,1-2,0V17.83a3.85,3.85,0,0,1,4-3.67,3.85,3.85,0,0,1,4,3.67V28.42A1,1,0,0,1,31,29.42Z" style="fill:#00c49c;"/>
		<path class="cls-1" d="M16,44C7.18,44,0,37.57,0,29.67V21.76a1,1,0,0,1,2,0v7.91C2,36.47,8.28,42,16,42s14-5.53,14-12.33V21.76a1,1,0,0,1,2,0v7.91C32,37.57,24.82,44,16,44Z" style="fill:#00c49c;"/>
	</symbol>
	<symbol id="hand_off"  viewBox="0 0 32 44">
		<path class="cls-1" d="M1,10V29.67C1,37,7.72,43,16,43h0c8.28,0,15-6,15-13.33V17.83a2.84,2.84,0,0,0-3-2.67,2.84,2.84,0,0,0-3,2.67v-11a2.85,2.85,0,0,0-3-2.67,2.85,2.85,0,0,0-3,2.67V3.67A2.84,2.84,0,0,0,16,1h0a2.84,2.84,0,0,0-3,2.67v2A2.85,2.85,0,0,0,10,3,2.85,2.85,0,0,0,7,5.7V10A2.84,2.84,0,0,0,4,7.31,2.84,2.84,0,0,0,1,10Z" style="fill:#a6ebdd;stroke:#00c49c;stroke-miterlimit:10;"/>
		<path class="cls-2" d="M25,21.69V6.82a2.85,2.85,0,0,0-3-2.67h0a2.85,2.85,0,0,0-3,2.67V20.76" style="fill:#a6ebdd;stroke:#00c49c;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;"/>
		<path class="cls-2" d="M13,20.76V5.7A2.85,2.85,0,0,0,10,3h0A2.85,2.85,0,0,0,7,5.7V19.64" style="fill:#a6ebdd;stroke:#00c49c;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;"/>
		<path class="cls-2" d="M7,20.76V10A2.84,2.84,0,0,0,4,7.31H4A2.84,2.84,0,0,0,1,10V24.91" style="fill:#a6ebdd;stroke:#00c49c;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;"/>
		<path class="cls-2" d="M19,18.53V3.67A2.84,2.84,0,0,0,16,1h0a2.84,2.84,0,0,0-3,2.67V18.6" style="fill:#a6ebdd;stroke:#00c49c;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;"/>
		<path class="cls-2" d="M31,28.42V17.83a2.84,2.84,0,0,0-3-2.67h0a2.84,2.84,0,0,0-3,2.67v9.85" style="fill:#a6ebdd;stroke:#00c49c;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;"/>
		<path class="cls-2" d="M1,21.76v7.91C1,37,7.72,43,16,43h0c8.28,0,15-6,15-13.33V21.76" style="fill:#a6ebdd;stroke:#00c49c;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;"/>
	</symbol>
	<symbol id="attendee_setting" viewBox="0 0 44 44">
		<path class="cls-1" d="M35.21,34H31.75a2.16,2.16,0,0,1-2.16-2.15l-.09-1.2a5.36,5.36,0,0,1-.57-.34l-1.12.57a2.12,2.12,0,0,1-1,.25,2.19,2.19,0,0,1-1.87-1.07l-1.73-3A2.11,2.11,0,0,1,23,25.46a2.15,2.15,0,0,1,1-1.32l1.19-.83v0l-1.09-.73a2.08,2.08,0,0,1-.94-1.27,2.11,2.11,0,0,1,.22-1.63l1.73-3a2.24,2.24,0,0,1,2.95-.78l1.1.55.34-.2L29.59,15a2.06,2.06,0,0,1,.62-1.42,2.17,2.17,0,0,1,1.54-.64h3.46a2.16,2.16,0,0,1,2.16,2.16l.07,1.2.54.32L39.1,16a2.08,2.08,0,0,1,1-.25A2.16,2.16,0,0,1,42,16.86l1.74,3a2.14,2.14,0,0,1,.22,1.64,2.22,2.22,0,0,1-1,1.32l-1.11.68,1.16.8a2.05,2.05,0,0,1,.92,1.25,2.14,2.14,0,0,1-.22,1.64l-1.74,3A2.22,2.22,0,0,1,39,30.9l-1.06-.54-.48.29-.07,1.27a2,2,0,0,1-.64,1.46A2.17,2.17,0,0,1,35.21,34Zm-6.09-6,.5.38a6.24,6.24,0,0,0,1.25.74l.54.24.17,2.44c0,.15.08.22.17.22h3.46c.07,0,.16-.06.16-.13l.13-2.52.56-.25a6.12,6.12,0,0,0,1.18-.7l.5-.36,2.31,1.14a.2.2,0,0,0,.15-.07l1.73-3A.15.15,0,0,0,42,26l-.12-.12-2.15-1.47,0-.57c0-.14,0-.28,0-.41s0-.29,0-.44l0-.6,2.21-1.36a.18.18,0,0,0,.1-.12.14.14,0,0,0,0-.11l-1.73-3a.18.18,0,0,0-.15-.08l-.11,0-2.21,1.1-.49-.38a6,6,0,0,0-1.23-.74l-.56-.25-.13-2.44c0-.14-.07-.21-.16-.21H31.75a.23.23,0,0,0-.13.05l0,.15-.17,2.46-.54.25a6,6,0,0,0-1.07.61l-.49.35L27,17.62a.17.17,0,0,0-.14.08l-1.73,3a.17.17,0,0,0,0,.12l.11.11,2.08,1.38-.06.6a5.68,5.68,0,0,0,0,.59c0,.11,0,.21,0,.32l0,.56-2.17,1.49c-.11.07-.14.1-.15.14a.14.14,0,0,0,0,.11l1.73,3a.17.17,0,0,0,.14.08l.11,0Z" style="fill:#00c49c;"/>
		<path class="cls-1" d="M24.1,22.56a2.08,2.08,0,0,1-.94-1.27,2.63,2.63,0,0,1-.06-.39A10.55,10.55,0,0,1,22,21a9.49,9.49,0,0,1-9.47-9.51A9.5,9.5,0,0,1,28.74,4.8a9.4,9.4,0,0,1,2.76,6.72A9.62,9.62,0,0,1,31.38,13a2.21,2.21,0,0,1,.37,0H33.4a10.45,10.45,0,0,0,.1-1.39A11.49,11.49,0,0,0,22,0h0a11.49,11.49,0,1,0,0,23,11.35,11.35,0,0,0,2.35-.24Z" style="fill:#00c49c;"/>
		<path class="cls-1" d="M39.65,31.13A2.05,2.05,0,0,1,39,30.9l-1.06-.54-.48.29-.07,1.27s0,.09,0,.13c3,1.67,4.65,3.72,4.64,5.65l-.12,2.44c-.06.69-1.72,1.86-12.13,1.86l-4.06,0h-.14L22,42H15.68c-8.93,0-13.5-.64-13.6-1.86L2,37.68c0-4.85,9.31-9.1,19.89-9.1H22c.7,0,1.39,0,2.08.06l-.91-1.55a2,2,0,0,1-.2-.48c-.32,0-.64,0-1,0h-.14c-12,0-21.87,5-21.89,11.13l.08,2.51C.36,43.68,7.31,44,15.68,44H22l3.53,0h.15l4.07,0c7,0,13.82-.28,14.12-3.73L44,37.76C44,35.34,42.47,33.05,39.65,31.13Z" style="fill:#00c49c;"/>
		<path class="cls-1" d="M33.45,27.77a4.31,4.31,0,1,1,4.35-4.31A4.34,4.34,0,0,1,33.45,27.77Zm0-6.62a2.31,2.31,0,1,0,2.35,2.31A2.33,2.33,0,0,0,33.45,21.15Z" style="fill:#00c49c;"/>
	</symbol>
	<symbol id="link_share" viewBox="0 0 44 44">
		<circle class="cls-1" cx="36.47" cy="7.53" r="6.53" style="fill:none;stroke:#00c49c;stroke-width:2px;stroke-miterlimit:10;"/>
		<circle class="cls-1" cx="36.47" cy="36.47" r="6.53" style="fill:none;stroke:#00c49c;stroke-width:2px;stroke-miterlimit:10;"/>
		<circle class="cls-1" cx="7.53" cy="22" r="6.53" style="fill:none;stroke:#00c49c;stroke-width:2px;stroke-miterlimit:10;"/>
		<line class="cls-2" x1="30.63" y1="10.45" x2="13.37" y2="19.08" style="fill:none;stroke:#00c49c;stroke-width:2px;stroke-linecap:round;stroke-linejoin:round;"/>
		<line class="cls-2" x1="30.67" y1="33.57" x2="13.37" y2="24.92" style="fill:none;stroke:#00c49c;stroke-width:2px;stroke-linecap:round;stroke-linejoin:round;"/>
	</symbol>
	<symbol id="mute" viewBox="0 0 32 44">
		<path class="cls-1" d="M16,32.49A9.81,9.81,0,0,1,6.31,22.6V9.89A9.81,9.81,0,0,1,16,0a9.81,9.81,0,0,1,9.69,9.89V22.6A9.81,9.81,0,0,1,16,32.49ZM16,2A7.82,7.82,0,0,0,8.27,9.89V22.6A7.82,7.82,0,0,0,16,30.49a7.82,7.82,0,0,0,7.73-7.89V9.89A7.82,7.82,0,0,0,16,2Z" style="fill:#ff675c;"/>
		<path class="cls-1" d="M16,37.89C7.29,37.89.11,30.33,0,21a1,1,0,0,1,1-1,1,1,0,0,1,1,1c.1,8.2,6.39,14.87,14,14.87S29.94,29.22,30,21a1,1,0,0,1,1-1,1,1,0,0,1,1,1C31.89,30.33,24.71,37.89,16,37.89Z" style="fill:#ff675c;"/>
		<path class="cls-1" d="M16,44a1,1,0,0,1-1-1V36.89a1,1,0,1,1,2,0V43A1,1,0,0,1,16,44Z" style="fill:#ff675c;"/>
		<path class="cls-1" d="M23.83,44H8.17a1,1,0,0,1,0-2H23.83a1,1,0,0,1,0,2Z" style="fill:#ff675c;"/>
		<path class="cls-1" d="M20.77,22.12a1,1,0,0,1-.69-.3l-9.54-9.74a1,1,0,0,1,0-1.41,1,1,0,0,1,1.38,0l9.54,9.74a1,1,0,0,1,0,1.41A1,1,0,0,1,20.77,22.12Z" style="fill:#ff675c;"/>
		<path class="cls-1" d="M11.23,22.12a1,1,0,0,1-.69-.3,1,1,0,0,1,0-1.41l9.54-9.74a1,1,0,0,1,1.38,0,1,1,0,0,1,0,1.41l-9.54,9.74A1,1,0,0,1,11.23,22.12Z" style="fill:#ff675c;"/>
	</symbol>
	<symbol id="no_mute" viewBox="0 0 32 44">
		<path class="cls-1" d="M16,32.49A9.81,9.81,0,0,1,6.31,22.6V9.89A9.81,9.81,0,0,1,16,0a9.81,9.81,0,0,1,9.69,9.89V22.6A9.81,9.81,0,0,1,16,32.49Z" style="fill:#00c49c;"/>
		<path class="cls-1" d="M16,37.89C7.29,37.89.11,30.33,0,21a1,1,0,0,1,1-1,1,1,0,0,1,1,1c.1,8.2,6.39,14.87,14,14.87S29.94,29.22,30,21a1,1,0,0,1,1-1,1,1,0,0,1,1,1C31.89,30.33,24.71,37.89,16,37.89Z" style="fill:#00c49c;"/>
		<path class="cls-1" d="M16,44a1,1,0,0,1-1-1V36.89a1,1,0,1,1,2,0V43A1,1,0,0,1,16,44Z" style="fill:#00c49c;"/>
		<path class="cls-1" d="M23.83,44H8.17a1,1,0,0,1,0-2H23.83a1,1,0,0,1,0,2Z" style="fill:#00c49c;"/>
	</symbol>
  <symbol id="hands" viewBox="0 0 22 30">
    <defs>
        <style>.cls-1{fill:none;}.cls-2,.cls-4{fill:#ffdbb8;}.cls-3{fill:#ffab44;}.cls-4{stroke:#ffab44;stroke-linecap:round;stroke-linejoin:round;}</style>
    </defs>
    <rect class="cls-1" width="22" height="30" style="fill:none;"/>
    <path class="cls-2" style="fill:#ffdbb8;" d="M11,29C5.49,29,1,25.09,1,20.29V12.12a1.49,1.49,0,0,1,1.6-1.34,1.49,1.49,0,0,1,1.6,1.34h1V4.52A1.49,1.49,0,0,1,6.8,3.18,1.49,1.49,0,0,1,8.4,4.52h1V2.34A1.49,1.49,0,0,1,11,1a1.49,1.49,0,0,1,1.6,1.34V3.75h1A1.5,1.5,0,0,1,15.2,2.4a1.5,1.5,0,0,1,1.6,1.35v3h1a1.49,1.49,0,0,1,1.6-1.34A1.49,1.49,0,0,1,21,6.7V20.29C21,25.09,16.51,29,11,29Z"/>
    <path class="cls-3" style="fill:#ffab44;" d="M11,1.5a1,1,0,0,1,1.1.84V3.75h2a1,1,0,0,1,1.1-.85,1,1,0,0,1,1.1.85v3h2a1,1,0,0,1,1.1-.84,1,1,0,0,1,1.1.84V20.29c0,4.53-4.26,8.21-9.5,8.21s-9.5-3.68-9.5-8.21V12.12a1,1,0,0,1,1.1-.84,1,1,0,0,1,1.1.84h2V4.52a1,1,0,0,1,1.1-.84,1,1,0,0,1,1.1.84h2V2.34A1,1,0,0,1,11,1.5m0-1h0A2,2,0,0,0,8.9,2.34V4.52A2,2,0,0,0,6.8,2.68,2,2,0,0,0,4.7,4.52v7.6a2,2,0,0,0-2.1-1.84A2,2,0,0,0,.5,12.12v8.17C.5,25.38,5.2,29.5,11,29.5h0c5.8,0,10.5-4.12,10.5-9.21V6.7a2,2,0,0,0-2.1-1.84A2,2,0,0,0,17.3,6.7V3.75A2,2,0,0,0,15.2,1.9a2,2,0,0,0-2.1,1.85V2.34A2,2,0,0,0,11,.5Z"/>
    <path class="cls-4" style="fill:#ffdbb8;stroke:#ffab44;stroke-linecap:round;stroke-linejoin:round;" d="M21.5,14.83v5.46c0,5.09-4.7,9.21-10.5,9.21h0C5.2,29.5.5,25.38.5,20.29V14.83"/>
    <path class="cls-4" style="fill:#ffdbb8;stroke:#ffab44;stroke-linecap:round;stroke-linejoin:round;" d="M4.7,14.79V4.52A2,2,0,0,1,6.8,2.68h0A2,2,0,0,1,8.9,4.52v9.62"/>
    <path class="cls-4" style="fill:#ffdbb8;stroke:#ffab44;stroke-linecap:round;stroke-linejoin:round;" d="M13.1,14.14V3.75A2,2,0,0,1,15.2,1.9h0a2,2,0,0,1,2.1,1.85v9.62"/>
    <path class="cls-4" style="fill:#ffdbb8;stroke:#ffab44;stroke-linecap:round;stroke-linejoin:round;" d="M17.3,14.14V6.7a2,2,0,0,1,2.1-1.84h0A2,2,0,0,1,21.5,6.7V17"/>
    <path class="cls-4" style="fill:#ffdbb8;stroke:#ffab44;stroke-linecap:round;stroke-linejoin:round;" d="M8.9,12.61V2.34A2,2,0,0,1,11,.5h0a2,2,0,0,1,2.1,1.84V12.66"/>
    <path class="cls-4" style="fill:#ffdbb8;stroke:#ffab44;stroke-linecap:round;stroke-linejoin:round;" d="M.5,19.43V12.12a2,2,0,0,1,2.1-1.84h0a2,2,0,0,1,2.1,1.84v6.8"/>
  </symbol>
  <symbol id="no_mute1" viewBox="0 0 44 44">
    <defs>
        <style>.cls-1,.cls-3{fill:#bbc4c9;}.cls-1,.cls-2{stroke:#bbc4c9;stroke-linecap:round;stroke-linejoin:round;}.cls-2{fill:none;}</style>
    </defs>
    <path class="cls-1" style="fill:#bbc4c9;stroke:#bbc4c9;stroke-linecap:round;stroke-linejoin:round;" d="M22,8.8a5.6,5.6,0,0,0-5.61,5.59v8a5.62,5.62,0,0,0,11.23,0v-8A5.61,5.61,0,0,0,22,8.8Z"/>
    <path class="cls-2" style="fill:none;stroke:#bbc4c9;stroke-linecap:round;stroke-linejoin:round;" d="M12.32,21.39a9.89,9.89,0,0,0,9.68,10,9.89,9.89,0,0,0,9.68-10"/>
    <line class="cls-2" style="fill:none;stroke:#bbc4c9;stroke-linecap:round;stroke-linejoin:round;" x1="22" y1="31.36" x2="22" y2="35.2"/>
    <line class="cls-2" style="fill:none;stroke:#bbc4c9;stroke-linecap:round;stroke-linejoin:round;" x1="16.95" y1="35.2" x2="27.05" y2="35.2"/>
    <path class="cls-3" style="fill:#bbc4c9;" d="M22,1A21,21,0,1,1,1,22,21,21,0,0,1,22,1m0-1A22,22,0,1,0,44,22,22,22,0,0,0,22,0Z"/>
  </symbol>
  <symbol id="mute1" viewBox="0 0 44 44">
    <defs>
        <style>.cls-1{fill:#bbc4c9;}.cls-2{fill:none;stroke:#ff675c;stroke-linecap:round;stroke-linejoin:round;}</style>
    </defs>
    <path class="cls-1" style="fill:#bbc4c9;" d="M28.09,13.79a6.12,6.12,0,0,0-12.2.6v8a6,6,0,0,0,.73,2.88Z"/>
    <path class="cls-1" style="fill:#bbc4c9;" d="M18.65,27.47a6.12,6.12,0,0,0,9.47-5.09V18Z"/>
    <path class="cls-1" style="fill:#bbc4c9;" d="M22,35.7a.5.5,0,0,1-.5-.5V31.36a.5.5,0,0,1,1,0V35.2A.5.5,0,0,1,22,35.7Z"/>
    <path class="cls-1" style="fill:#bbc4c9;" d="M27.05,35.7H17a.5.5,0,0,1,0-1h10.1a.5.5,0,0,1,0,1Z"/>
    <path class="cls-1" style="fill:#bbc4c9;" d="M14.76,27.12a9.79,9.79,0,0,1-1.94-5.74.5.5,0,0,0-.5-.49h0a.5.5,0,0,0-.49.51A10.8,10.8,0,0,0,14,27.84Z"/>
    <path class="cls-1" style="fill:#bbc4c9;" d="M31.69,20.89h0a.5.5,0,0,0-.5.49A9.42,9.42,0,0,1,22,30.86a8.82,8.82,0,0,1-5.1-1.63l-.71.71A9.85,9.85,0,0,0,22,31.86,10.43,10.43,0,0,0,32.18,21.4.5.5,0,0,0,31.69,20.89Z"/>
    <line class="cls-2" style="fill:none;stroke:#ff675c;stroke-linecap:round;stroke-linejoin:round;" x1="37.19" y1="6.81" x2="6.81" y2="37.19"/>
    <path class="cls-1" style="fill:#bbc4c9;" d="M22,1A21,21,0,1,1,1,22,21,21,0,0,1,22,1m0-1A22,22,0,1,0,44,22,22,22,0,0,0,22,0Z"/>
  </symbol>
  <symbol id="hands_reject" viewBox="0 0 24 34">
    <defs>
        <style>.cls-1{fill:#ffcfcf;stroke:#ff675c;stroke-linecap:round;stroke-linejoin:round;}.cls-2{fill:none;}</style>
    </defs>
    <path class="cls-1" style="fill:#ffcfcf;stroke:#ff675c;stroke-linecap:round;stroke-linejoin:round;" d="M.5,7.55V23C.5,28.81,5.65,33.5,12,33.5h0c6.35,0,11.5-4.69,11.5-10.48v-9.3a2.31,2.31,0,0,0-4.6,0V5.07A2.21,2.21,0,0,0,16.6,3a2.21,2.21,0,0,0-2.3,2.09V2.6A2.21,2.21,0,0,0,12,.5h0A2.21,2.21,0,0,0,9.7,2.6V4.19A2.21,2.21,0,0,0,7.4,2.1,2.21,2.21,0,0,0,5.1,4.19V7.55A2.21,2.21,0,0,0,2.8,5.46,2.21,2.21,0,0,0,.5,7.55Z"/>
    <path class="cls-1" style="fill:#ffcfcf;stroke:#ff675c;stroke-linecap:round;stroke-linejoin:round;" d="M.5,16.81V23C.5,28.81,5.65,33.5,12,33.5h0c6.35,0,11.5-4.69,11.5-10.48V16.81"/>
    <path class="cls-1" style="fill:#ffcfcf;stroke:#ff675c;stroke-linecap:round;stroke-linejoin:round;" d="M18.9,16.76V5.07A2.21,2.21,0,0,0,16.6,3h0a2.21,2.21,0,0,0-2.3,2.09V16"/>
    <path class="cls-1" style="fill:#ffcfcf;stroke:#ff675c;stroke-linecap:round;stroke-linejoin:round;" d="M9.7,16V4.19A2.21,2.21,0,0,0,7.4,2.1h0A2.21,2.21,0,0,0,5.1,4.19v11"/>
    <path class="cls-1" style="fill:#ffcfcf;stroke:#ff675c;stroke-linecap:round;stroke-linejoin:round;" d="M5.1,16V7.55A2.21,2.21,0,0,0,2.8,5.46h0A2.21,2.21,0,0,0,.5,7.55V19.29"/>
    <path class="cls-1" style="fill:#ffcfcf;stroke:#ff675c;stroke-linecap:round;stroke-linejoin:round;" d="M14.3,14.28V2.6A2.21,2.21,0,0,0,12,.5h0A2.21,2.21,0,0,0,9.7,2.6V14.33"/>
    <path class="cls-1" style="fill:#ffcfcf;stroke:#ff675c;stroke-linecap:round;stroke-linejoin:round;" d="M23.5,22V13.72a2.2,2.2,0,0,0-2.3-2.09h0a2.2,2.2,0,0,0-2.3,2.09v7.75"/>
    <rect class="cls-2" style="fill:none;" width="24" height="34"/>
  </symbol>
  <symbol id="hands_resolve" viewBox="0 0 24 34">
    <defs>
        <style>.cls-1{fill:#a6ebdd;stroke:#00bf97;stroke-linecap:round;stroke-linejoin:round;stroke-width:1.21px;}.cls-2{fill:none;}</style>
    </defs>
    <path class="cls-1" style="fill:#a6ebdd;stroke:#00bf97;stroke-linecap:round;stroke-linejoin:round;stroke-width:1.21px;" d="M.5,7.55V23C.5,28.81,5.65,33.5,12,33.5h0c6.35,0,11.5-4.69,11.5-10.48v-9.3a2.31,2.31,0,0,0-4.6,0V5.07A2.21,2.21,0,0,0,16.6,3a2.21,2.21,0,0,0-2.3,2.09V2.6A2.21,2.21,0,0,0,12,.5h0A2.21,2.21,0,0,0,9.7,2.6V4.19A2.21,2.21,0,0,0,7.4,2.1,2.21,2.21,0,0,0,5.1,4.19V7.55A2.21,2.21,0,0,0,2.8,5.46,2.21,2.21,0,0,0,.5,7.55Z"/>
    <path class="cls-1" style="fill:#a6ebdd;stroke:#00bf97;stroke-linecap:round;stroke-linejoin:round;stroke-width:1.21px;" d="M.5,16.81V23C.5,28.81,5.65,33.5,12,33.5h0c6.35,0,11.5-4.69,11.5-10.48V16.81"/>
    <path class="cls-1" style="fill:#a6ebdd;stroke:#00bf97;stroke-linecap:round;stroke-linejoin:round;stroke-width:1.21px;" d="M18.9,16.76V5.07A2.21,2.21,0,0,0,16.6,3h0a2.21,2.21,0,0,0-2.3,2.09V16"/>
    <path class="cls-1" style="fill:#a6ebdd;stroke:#00bf97;stroke-linecap:round;stroke-linejoin:round;stroke-width:1.21px;" d="M9.7,16V4.19A2.21,2.21,0,0,0,7.4,2.1h0A2.21,2.21,0,0,0,5.1,4.19v11"/>
    <path class="cls-1" style="fill:#a6ebdd;stroke:#00bf97;stroke-linecap:round;stroke-linejoin:round;stroke-width:1.21px;" d="M5.1,16V7.55A2.21,2.21,0,0,0,2.8,5.46h0A2.21,2.21,0,0,0,.5,7.55V19.29"/>
    <path class="cls-1" style="fill:#a6ebdd;stroke:#00bf97;stroke-linecap:round;stroke-linejoin:round;stroke-width:1.21px;" d="M14.3,14.28V2.6A2.21,2.21,0,0,0,12,.5h0A2.21,2.21,0,0,0,9.7,2.6V14.33"/>
    <path class="cls-1" style="fill:#a6ebdd;stroke:#00bf97;stroke-linecap:round;stroke-linejoin:round;stroke-width:1.21px;" d="M23.5,22V13.72a2.2,2.2,0,0,0-2.3-2.09h0a2.2,2.2,0,0,0-2.3,2.09v7.75"/>
    <rect class="cls-2" style="fill:none;" width="24" height="34"/>
  </symbol>
  <symbol id="mic_open" viewBox="0 0 104 104">
    <defs><style>
    .cls-1,.cls-2{fill:#fff;}
    .cls-2,.cls-3{stroke:#fff;stroke-linecap:round;stroke-linejoin:round;stroke-width:3.6px;}
    .cls-3{fill:none;}
    </style></defs>
    <path class="cls-1" style="fill:#fff;" d="M52,4A48,48,0,1,1,4,52,48.05,48.05,0,0,1,52,4m0-2a50,50,0,1,0,50,50A50,50,0,0,0,52,2Z"/>
    <path class="cls-2" style="fill:#fff;stroke:#fff;stroke-linecap:round;stroke-linejoin:round;stroke-width:3.6px;" d="M52,21.45A12.94,12.94,0,0,0,39.06,34.39V52.87a12.94,12.94,0,1,0,25.88,0V34.39A12.94,12.94,0,0,0,52,21.45Z"/>
    <path class="cls-3" style="fill:none;stroke:#fff;stroke-linecap:round;stroke-linejoin:round;stroke-width:3.6px;" d="M29.7,50.59C29.85,63.36,39.77,73.66,52,73.66s22.15-10.3,22.3-23.07"/>
	<line class="cls-3" style="fill:none;stroke:#fff;stroke-linecap:round;stroke-linejoin:round;stroke-width:3.6px;" x1="52" y1="73.66" x2="52" y2="82.55"/>
    <line class="cls-3" style="fill:none;stroke:#fff;stroke-linecap:round;stroke-linejoin:round;stroke-width:3.6px;" x1="40.37" y1="82.55" x2="63.63" y2="82.55"/>
  </symbol>
  <symbol id="mic_close" viewBox="0 0 104 104">
	<defs>
		<style>
			.cls-1,.cls-2{fill:#fff;}
			.cls-2,.cls-3{stroke:#fff;stroke-width:3.6px;}
			.cls-2,.cls-3,.cls-4{stroke-linecap:round;stroke-linejoin:round;}
			.cls-3,.cls-4{fill:none;}
			.cls-4{stroke:#e60012;stroke-width:2px;}
		</style>
	</defs>
	<path class="cls-1" style="fill:#fff;" d="M52,4A48,48,0,1,1,4,52,48.05,48.05,0,0,1,52,4m0-2a50,50,0,1,0,50,50A50,50,0,0,0,52,2Z"/>
	<path class="cls-2" style="fill:#fff;stroke:#fff;stroke-width:3.6px;stroke-linecap:round;stroke-linejoin:round;" d="M52,21.45A12.94,12.94,0,0,0,39.06,34.39V52.87a12.94,12.94,0,1,0,25.88,0V34.39A12.94,12.94,0,0,0,52,21.45Z"/>
	<path class="cls-3" style="stroke:#fff;stroke-width:3.6px;stroke-linecap:round;stroke-linejoin:round;fill:none;" d="M29.7,50.59C29.85,63.36,39.77,73.66,52,73.66s22.15-10.3,22.3-23.07"/>
	<line class="cls-3" style="stroke:#fff;stroke-width:3.6px;stroke-linecap:round;stroke-linejoin:round;fill:none;" x1="52" y1="73.66" x2="52" y2="82.55"/>
	<line class="cls-3" style="stroke:#fff;stroke-width:3.6px;stroke-linecap:round;stroke-linejoin:round;fill:none;" x1="40.37" y1="82.55" x2="63.63" y2="82.55"/>
	<line class="cls-4" style="stroke:#e60012;stroke-width:2px;fill:none;stroke-linecap:round;stroke-linejoin:round;" x1="16.14" y1="85.54" x2="89.74" y2="20.53"/>
  </symbol>
  <symbol id="speech_panel" viewBox="0 0 100 100">
    <defs>
        <style>.cls-1{fill:#fff;}.cls-2{fill:#a6ebdd;}</style>
    </defs>
    <path class="cls-1" style="fill:#fff;" d="M50,2A48,48,0,1,1,2,50,48.05,48.05,0,0,1,50,2m0-2a50,50,0,1,0,50,50A50,50,0,0,0,50,0Z"/>
    <path class="cls-2" style="fill:#a6ebdd;" d="M61.57,67.7h.69l3.11,0,3.43,0c3.54,0,10.89,0,11.09-2.34L80,63.31c0-2.15-1.76-4.26-5-5.9a28.9,28.9,0,0,0-12.73-2.69h-.12c-9.55,0-17.64,3.9-17.65,8.52l.06,2.08c.2,2.39,7.8,2.39,12.34,2.39h4.65Z"/>
    <path class="cls-2" style="fill:#a6ebdd;" d="M62.29,32.26a8.89,8.89,0,0,0-8.9,8.83A8.88,8.88,0,0,0,62.23,50h0a8.86,8.86,0,0,0,0-17.72Z"/>
    <path class="cls-2" style="fill:#a6ebdd;" d="M37.06,67.7h.68l3.12,0,3.43,0c3.53,0,10.88,0,11.09-2.34l.1-2.09c0-2.15-1.76-4.26-5-5.9a28.87,28.87,0,0,0-12.73-2.69h-.12C28.1,54.72,20,58.62,20,63.24l.06,2.08c.2,2.39,7.8,2.39,12.34,2.39h4.66Z"/>
    <path class="cls-2" style="fill:#a6ebdd;" d="M37.77,32.26a8.89,8.89,0,0,0-8.9,8.83A8.88,8.88,0,0,0,37.71,50h0a8.86,8.86,0,1,0,0-17.72Z"/>
    <path class="cls-1" style="fill:#fff;" d="M49,74.94h1c1.47,0,2.94,0,4.39,0l4.83,0c5,0,15.34,0,15.63-3.31L75,68.76c0-3-2.48-6-7-8.32C63.21,58,56.84,56.66,50,56.65h-.17c-13.45,0-24.85,5.5-24.87,12l.09,2.93C25.37,75,36.07,75,42.47,75H49Z"/>
    <path class="cls-1" style="fill:#fff;" d="M50,25A12.49,12.49,0,1,0,50,50h0a12.49,12.49,0,0,0,0-25Z"/>
  </symbol>
  <symbol id="speech_settings" viewBox="0 0 100 100">
    <defs>
        <style>.cls-1{fill:#fff;}</style>
    </defs>
    <path class="cls-1" style="fill:#fff;" d="M79.88,42.91a3.59,3.59,0,0,0-.38-2.75l-5.44-9.32a3.66,3.66,0,0,0-4.92-1.36L64,32.07a23.08,23.08,0,0,0-4.47-2.69l-.3-5.76A3.64,3.64,0,0,0,55.52,20H44.64A3.62,3.62,0,0,0,41,23.54l-.41,5.86a22.82,22.82,0,0,0-3.87,2.22L31.48,29a3.72,3.72,0,0,0-1.82-.48,3.68,3.68,0,0,0-3.16,1.8l-5.44,9.32a3.6,3.6,0,0,0-.37,2.76,3.52,3.52,0,0,0,1.64,2.16l4.94,3.29c-.07.71-.1,1.43-.1,2.16,0,.39,0,.79,0,1.18l-5.37,3.7a3.59,3.59,0,0,0-1.33,5l5.44,9.32A3.67,3.67,0,0,0,29.1,71a3.57,3.57,0,0,0,1.76-.45l5.19-2.62a22.43,22.43,0,0,0,4.52,2.7L41,76.38A3.63,3.63,0,0,0,44.64,80H55.52a3.61,3.61,0,0,0,3.65-3.56l.31-5.85a22.67,22.67,0,0,0,4.3-2.53l5.15,2.6a3.72,3.72,0,0,0,1.82.48,3.67,3.67,0,0,0,3.16-1.8L79.35,60a3.58,3.58,0,0,0,.37-2.75,3.51,3.51,0,0,0-1.62-2.16L72.83,51.5c0-.51,0-1,0-1.5s0-1.06,0-1.59l5.33-3.29A3.63,3.63,0,0,0,79.88,42.91ZM50,60.39A10.4,10.4,0,1,1,60.51,50,10.47,10.47,0,0,1,50,60.39Z"/>
    <path class="cls-1" style="fill:#fff;" d="M50,2A48,48,0,1,1,2,50,48.05,48.05,0,0,1,50,2m0-2a50,50,0,1,0,50,50A50,50,0,0,0,50,0Z"/>
  </symbol>
  <symbol id="camera_open" viewBox="0 0 104 104">
	<defs><style>.cls-1{fill:#fff;}</style></defs>
	<path class="cls-1" style="fill:#fff;" d="M52,4A48,48,0,1,1,4,52,48.05,48.05,0,0,1,52,4m0-2a50,50,0,1,0,50,50A50,50,0,0,0,52,2Z"/>
	<path class="cls-1" style="fill:#fff;" d="M69.1,58.08V68a3.06,3.06,0,0,1-2.82,3.24H25.71A3.06,3.06,0,0,1,22.88,68V36.61a3.06,3.06,0,0,1,2.83-3.24H66.28a3.06,3.06,0,0,1,2.82,3.24v9.44l10.44-7.91c1.3-1,2.35-.34,2.35,1.44v25c0,1.78-1,2.44-2.35,1.45Z"/>
  </symbol>
  <symbol id="camera_close" viewBox="0 0 104 104">
	<defs><style>.cls-1{fill:#fff;}.cls-2{fill:none;stroke:#e60012;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;}</style></defs>
	<path class="cls-1" style="fill:#fff;" d="M52,4A48,48,0,1,1,4,52,48.05,48.05,0,0,1,52,4m0-2a50,50,0,1,0,50,50A50,50,0,0,0,52,2Z"/>
	<path class="cls-1" style="fill:#fff;" d="M69.1,58.08V68a3.06,3.06,0,0,1-2.82,3.24H25.71A3.06,3.06,0,0,1,22.88,68V36.61a3.06,3.06,0,0,1,2.83-3.24H66.28a3.06,3.06,0,0,1,2.82,3.24v9.44l10.44-7.91c1.3-1,2.35-.34,2.35,1.44v25c0,1.78-1,2.44-2.35,1.45Z"/>
	<line class="cls-2" style="fill:#fff;fill:none;stroke:#e60012;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;" x1="16.14" y1="85.54" x2="89.74" y2="20.53"/>
  </symbol>
  <symbol id="doppler_open" viewBox="0 0 104 104">
	<defs><style>.cls-1{fill:#fff;}.cls-2{fill:#24b28e;}</style></defs>
	<path class="cls-1" style="fill:#fff;" d="M52,4A48,48,0,1,1,4,52,48.05,48.05,0,0,1,52,4m0-2a50,50,0,1,0,50,50A50,50,0,0,0,52,2Z"/>
	<path class="cls-1" style="fill:#fff;" d="M56.29,44.76a2.69,2.69,0,0,1,1.19,4.52,10.28,10.28,0,0,1-14.52,0,2.68,2.68,0,1,1,3.8-3.79,4.91,4.91,0,0,0,6.93,0A2.68,2.68,0,0,1,56.29,44.76Z"/>
	<path class="cls-1" style="fill:#fff;" d="M59.05,54a2.71,2.71,0,0,1,2.6-.73,2.69,2.69,0,0,1,1.19,4.52,17.85,17.85,0,0,1-25.23,0,2.68,2.68,0,0,1,1.18-4.52,2.69,2.69,0,0,1,2.6.73A12.52,12.52,0,0,0,59.05,54Z"/>
	<path class="cls-1" style="fill:#fff;" d="M76.64,58.94,60.56,32.14a2.67,2.67,0,0,0-2.3-1.3h-16a2.69,2.69,0,0,0-2.55,1.59C38.87,34,31,47.1,23.82,58.91a2.68,2.68,0,0,0,.38,3.31,36.77,36.77,0,0,0,52,0A2.68,2.68,0,0,0,76.64,58.94Z"/>
	<path class="cls-1" style="fill:#fff;" d="M43,49.28a2.68,2.68,0,1,1,3.8-3.79,4.91,4.91,0,0,0,6.93,0,2.68,2.68,0,1,1,3.79,3.79,10.28,10.28,0,0,1-14.52,0Z"/>
	<path class="cls-1" style="fill:#fff;" d="M62.84,57.78a17.85,17.85,0,0,1-25.23,0,2.68,2.68,0,0,1,1.18-4.52,2.69,2.69,0,0,1,2.6.73,12.52,12.52,0,0,0,17.66,0,2.71,2.71,0,0,1,2.6-.73,2.69,2.69,0,0,1,1.19,4.52Z"/>
	<path class="cls-2" style="fill:#24b28e;" d="M62.84,57.78a17.85,17.85,0,0,1-25.23,0,2.68,2.68,0,0,1,1.18-4.52,2.69,2.69,0,0,1,2.6.73,12.52,12.52,0,0,0,17.66,0,2.71,2.71,0,0,1,2.6-.73,2.69,2.69,0,0,1,1.19,4.52Z"/>
	<path class="cls-2" style="fill:#24b28e;" d="M57.48,49.28a10.28,10.28,0,0,1-14.52,0,2.68,2.68,0,1,1,3.8-3.79,4.91,4.91,0,0,0,6.93,0,2.68,2.68,0,1,1,3.79,3.79Z"/>
  </symbol>
  <symbol id="doppler_close" viewBox="0 0 104 104">
	<defs><style>.cls-1{fill:#fff;}.cls-2{fill:#24b28e;}.cls-3{fill:none;stroke:#e60012;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;}</style></defs>
	<path class="cls-1" style="fill:#fff;" d="M52,4A48,48,0,1,1,4,52,48.05,48.05,0,0,1,52,4m0-2a50,50,0,1,0,50,50A50,50,0,0,0,52,2Z"/>
	<path class="cls-1" style="fill:#fff;" d="M56.29,44.76a2.69,2.69,0,0,1,1.19,4.52,10.28,10.28,0,0,1-14.52,0,2.68,2.68,0,1,1,3.8-3.79,4.91,4.91,0,0,0,6.93,0A2.68,2.68,0,0,1,56.29,44.76Z"/>
	<path class="cls-1" style="fill:#fff;" d="M59.05,54a2.71,2.71,0,0,1,2.6-.73,2.69,2.69,0,0,1,1.19,4.52,17.85,17.85,0,0,1-25.23,0,2.68,2.68,0,0,1,1.18-4.52,2.69,2.69,0,0,1,2.6.73A12.52,12.52,0,0,0,59.05,54Z"/>
	<path class="cls-1" style="fill:#fff;" d="M76.64,58.94,60.56,32.14a2.67,2.67,0,0,0-2.3-1.3h-16a2.69,2.69,0,0,0-2.55,1.59C38.87,34,31,47.1,23.82,58.91a2.68,2.68,0,0,0,.38,3.31,36.77,36.77,0,0,0,52,0A2.68,2.68,0,0,0,76.64,58.94Z"/>
	<path class="cls-1" style="fill:#fff;" d="M43,49.28a2.68,2.68,0,1,1,3.8-3.79,4.91,4.91,0,0,0,6.93,0,2.68,2.68,0,1,1,3.79,3.79,10.28,10.28,0,0,1-14.52,0Z"/>
	<path class="cls-1" style="fill:#fff;" d="M62.84,57.78a17.85,17.85,0,0,1-25.23,0,2.68,2.68,0,0,1,1.18-4.52,2.69,2.69,0,0,1,2.6.73,12.52,12.52,0,0,0,17.66,0,2.71,2.71,0,0,1,2.6-.73,2.69,2.69,0,0,1,1.19,4.52Z"/>
	<path class="cls-2" style="fill:#24b28e;" d="M62.84,57.78a17.85,17.85,0,0,1-25.23,0,2.68,2.68,0,0,1,1.18-4.52,2.69,2.69,0,0,1,2.6.73,12.52,12.52,0,0,0,17.66,0,2.71,2.71,0,0,1,2.6-.73,2.69,2.69,0,0,1,1.19,4.52Z"/>
	<path class="cls-2" style="fill:#24b28e;" d="M57.48,49.28a10.28,10.28,0,0,1-14.52,0,2.68,2.68,0,1,1,3.8-3.79,4.91,4.91,0,0,0,6.93,0,2.68,2.68,0,1,1,3.79,3.79Z"/>
	<line class="cls-3" style="fill:none;stroke:#e60012;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;" x1="16.14" y1="85.54" x2="89.74" y2="20.53"/>
  </symbol>
  <symbol id="real_time_ultrasound" viewBox="0 0 114 114">
    <defs>
        <style>.cls-1{fill:#bbc4c9;}.cls-2,.cls-3{fill:#fff;}.cls-2{opacity:0.5;}.cls-4{fill:none;stroke:#00bf97;stroke-linecap:round;stroke-linejoin:round;stroke-width:1.53px;}</style>
    </defs>
    <rect class="cls-1" style="fill:#bbc4c9;" width="114" height="114" rx="57"/>
    <rect class="cls-2" style="fill:#fff;opacity:0.5;" x="30.95" y="46.32" width="48.5" height="37.89" rx="1.46"/>
    <path class="cls-3" style="fill:#fff;" d="M61.82,63A19.55,19.55,0,0,0,68,64h.11c10.55,0,19.12-8.34,19.09-18.5S78.62,27.19,68.12,27.19H68c-10.56,0-19.12,8.35-19.09,18.5a18.05,18.05,0,0,0,6.17,13.49.84.84,0,0,1,.28.73c-.37,2.87-1.27,4.56-2.33,5.53h.67A11.07,11.07,0,0,0,61,63.17a.8.8,0,0,1,.53-.19A.75.75,0,0,1,61.82,63Z"/>
    <line class="cls-4" style="fill:none;stroke:#00bf97;stroke-linecap:round;stroke-linejoin:round;stroke-width:1.53px;" x1="77.6" y1="45.49" x2="58.54" y2="45.49"/>
    <line class="cls-4" style="fill:none;stroke:#00bf97;stroke-linecap:round;stroke-linejoin:round;stroke-width:1.53px;" x1="77.6" y1="39.49" x2="58.54" y2="39.49"/>
    <line class="cls-4" style="fill:none;stroke:#00bf97;stroke-linecap:round;stroke-linejoin:round;stroke-width:1.53px;" x1="70.88" y1="51.5" x2="65.25" y2="51.5"/>
    <path class="cls-3" style="fill:#fff;" d="M78.8,86.81H31.32a4.52,4.52,0,0,1-4.52-4.52v-1a1.46,1.46,0,0,1,1.46-1.46H44.31l1.55,2.7h18.4l1.55-2.7h16a1.46,1.46,0,0,1,1.46,1.46v1A4.52,4.52,0,0,1,78.8,86.81Z"/>
  </symbol>
    <symbol id="share_panel" viewBox="0 0 36 36">
        <defs>
            <style>.cls-1,.cls-2{fill:none;}.cls-1{stroke:#fff;stroke-miterlimit:10;stroke-width:2px;}</style>
        </defs>
        <circle class="cls-1" style="fill:none;stroke:#fff;stroke-miterlimit:10;stroke-width:2px;" cx="28.98" cy="7.02" r="6.17"/>
        <circle class="cls-1" style="fill:none;stroke:#fff;stroke-miterlimit:10;stroke-width:2px;" cx="7.02" cy="17.88" r="6.17"/>
        <circle class="cls-1" style="fill:none;stroke:#fff;stroke-miterlimit:10;stroke-width:2px;" cx="26.74" cy="28.98" r="6.17"/>
        <line class="cls-1" style="fill:none;stroke:#fff;stroke-miterlimit:10;stroke-width:2px;" x1="11.28" y1="14.61" x2="24.05" y2="9.14"/>
        <line class="cls-1" style="fill:none;stroke:#fff;stroke-miterlimit:10;stroke-width:2px;" x1="11.49" y1="21.02" x2="21.33" y2="27.55"/>
        <rect class="cls-2" style="fill:none;" width="36" height="36"/>
    </symbol>
	<symbol id="camera_frame" viewBox="0 0 1024 1024">
       <path d="M791.551904 168.82608 580.084655 168.82608c-10.29753-38.975686-45.851281-67.793012-88.020706-67.793012L300.227806 101.033068c-42.61354 0-78.478376 29.433356-88.331791 69.037353-83.544765 10.17064-148.480706 81.538061-148.480706 167.787424l0 416.077324c0 93.200682 75.823919 169.031764 169.031764 169.031764L791.551904 922.966932c93.200682 0 169.031764-75.831083 169.031764-169.031764L960.583668 337.856821C960.583668 244.649999 884.752586 168.82608 791.551904 168.82608zM882.56885 753.935168c0 50.180889-40.836057 91.016947-91.016947 91.016947L232.448096 844.952114c-50.188053 0-91.016947-40.836057-91.016947-91.016947L141.43115 337.856821c0-50.188053 40.829917-91.016947 91.016947-91.016947l15.770172 0c21.541619 0 39.007409-17.46579 39.007409-39.007409l0-15.783475c0-7.16826 5.834892-13.002129 13.002129-13.002129l191.836143 0c7.1744 0 13.002129 5.834892 13.002129 13.002129l0 15.783475c0 21.541619 17.45965 39.007409 39.007409 39.007409L791.551904 246.839875c50.180889 0 91.016947 40.828894 91.016947 91.016947L882.56885 753.935168z" fill="#04A786" p-id="7569"></path><path d="M518.501064 324.854693c-121.884978 0-221.041302 99.156324-221.041302 221.041302s99.156324 221.041302 221.041302 221.041302 221.041302-99.156324 221.041302-221.041302S640.386042 324.854693 518.501064 324.854693zM518.501064 688.922479c-78.865185 0-143.026484-64.161299-143.026484-143.026484s64.161299-143.026484 143.026484-143.026484 143.026484 64.161299 143.026484 143.026484S597.36625 688.922479 518.501064 688.922479z" fill="#04A786" p-id="7570"></path><path d="M492.495784 506.891655m-52.009538 0a50.825 50.825 0 1 0 104.019075 0 50.825 50.825 0 1 0-104.019075 0Z" fill="#04A786" p-id="7571"></path>
    </symbol>
</svg>

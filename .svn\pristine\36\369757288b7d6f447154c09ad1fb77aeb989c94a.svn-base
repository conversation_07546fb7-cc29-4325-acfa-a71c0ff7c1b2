export default {
    name: 'Nom',
    tel: 'Tel',
    save: '<PERSON><PERSON><PERSON><PERSON>',
    confirm: 'Confirmer',
    cancel: '<PERSON><PERSON><PERSON>',
    delete: 'Supprimer',
    complete: 'Terminé',
    loading: 'Chargement...',
    telEmpty: '<PERSON><PERSON><PERSON><PERSON> remplir le tel',
    nameEmpty: 'Veu<PERSON>z remplir le nom',
    nameInvalid: 'Nom incorrect',
    confirmDelete: 'Êtes-vous sûr de vouloir supprimer?',
    telInvalid: 'Numéro de téléphone incorrect',
    vanCalendar: {
        end: 'Fin',
        start: 'Début',
        title: 'Calendrier',
        startEnd: 'Début/Fin',
        weekdays: ['Dim', 'Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam'],
        monthTitle: (year, month) => `${year}/${month}`,
        rangePrompt: (maxRange) => `Choisir pas plus de ${maxRange} jours`,
    },
    vanContactCard: {
        addText: 'Ajouter des informations de contact',
    },
    vanContactList: {
        addText: 'Ajouter un nouveau contact',
    },
    vanPagination: {
        prev: 'Précédent',
        next: 'Suivant',
    },
    vanPullRefresh: {
        pulling: 'Tirer pour rafraîchir ...',
        loosing: 'Relâchez pour rafraîchir...',
    },
    vanSubmitBar: {
        label: 'Total:',
    },
    vanCoupon: {
        unlimited: 'Illimité',
        discount: (discount) => `${discount * 10}% de réduction`,
        condition: (condition) => `Au moins ${condition}`,
    },
    vanCouponCell: {
        title: 'Coupon',
        tips: 'Pas de coupons',
        count: (count) => `Vous avez ${count} coupons`,
    },
    vanCouponList: {
        empty: 'Pas de coupons',
        exchange: 'Échange',
        close: 'Fermer',
        enable: 'Disponible',
        disabled: 'Indisponible',
        placeholder: 'Code promo',
    },
    vanAddressEdit: {
        area: 'Zone',
        postal: 'Postal',
        areaEmpty: 'Veuillez sélectionner une zone de réception',
        addressEmpty: "L'adresse ne peut pas être vide",
        postalEmpty: 'Code postal incorrect',
        defaultAddress: "Définir comme adresse par défaut",
        telPlaceholder: 'Téléphone',
        namePlaceholder: 'Nom',
        areaPlaceholder: 'Zone',
    },
    vanAddressEditDetail: {
        label: 'Adresse',
        placeholder: 'Adresse',
    },
    vanAddressList: {
        add: 'Ajouter une nouvelle adresse',
    },
};

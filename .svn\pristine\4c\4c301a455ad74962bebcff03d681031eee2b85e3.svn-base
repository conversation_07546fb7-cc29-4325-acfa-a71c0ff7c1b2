<template>
    <div class="education-live-training">
        <div class="coming-soon">
            <h1>直播培训</h1>
            <p>功能开发中，敬请期待...</p>
        </div>
    </div>
</template>

<script>
import base from "../../lib/base";

export default {
    mixins: [base],
    name: "EducationLiveTraining",
    components: {},
    data() {
        return {};
    },
    created() {
        console.log("直播培训页面已加载");
    },
    methods: {},
};
</script>

<style lang="scss" scoped>
@import '@/module/ultrasync_pc/style/education.scss';

.education-live-training {
    height: 100%;
    display: flex;
    flex-direction: column;
    background-color: #f7f9fc;
}

.coming-soon {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;

    h1 {
        font-size: 28px;
        color: #303133;
        font-weight: 600;
        margin-bottom: 20px;
    }

    p {
        font-size: 16px;
        color: #909399;
        font-weight: 400;
    }
}
</style>

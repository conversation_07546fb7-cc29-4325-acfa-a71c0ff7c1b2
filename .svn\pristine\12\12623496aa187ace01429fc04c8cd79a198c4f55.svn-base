import api from "./api";
import store from "../store";

const v2InterfaceList = {
    getResourceByShareId: "resource.list.by.shareid", //获取分享列表
    getWebLiveInfo: "live.broadcast.visit",
    getAssociationInfo: "association.get.user.info", // 查询关联信息
    bindPacsAccount:"association.bind.pacs", // 绑定PACS账号
    unbindPacsAccount:"association.unbind", // 解绑PACS账号
    getPacsUrl:"association.get.pacs.login.url", // 获取PACS跳转链接
    getEnvConfig:'config.env.data',//获取环境配置
};
const interfaceList = [
    "login",
    "logout",
    "query_login_config",
    "modify_personal_fancy",
    "AdminLogin",
    "query_history_version",
    "device_identify",
    "query_international_code_list",
];
const v2InterfaceFactory = (method, data) => {
    return api
        .post("/v2/api", {
            data: {
                method: method,
                bizContent: data,
            },
        })
        .then(
            (res) => {
                return res;
            },
            (error) => {
                console.log(error);
                return error;
            }
        );
};
const interfaceFactory = (method, data) => {
    return api
        .post(method, {
            data: data,
        })
        .then(
            (res) => {
                console.info(method, res);
                return res;
            },
            (error) => {
                console.error(method, error);
                throw error;
            }
        );
};

const service = {
    uploadLiveCoverImage: (formData) => {
        let config = {
            headers: {
                "Content-Type": "multipart/form-data",
            },
        };
        return api.post("/v2/upload/liveCoverImg", formData, config).then(
            (res) => {
                return res;
            },
            (error) => {
                console.log("error", error);
                return error;
            }
        );
    },
    getChannelInfoByVisitor: (url, data) => {
        return api
            .post(url + "/v2/api/visitorToken", {
                data,
            })
            .then(
                (res) => {
                    return res;
                },
                (error) => {
                    console.log("error", error);
                    return error;
                }
            );
    },
    getChannelStatus: (url, data) => {
        return api
            .get(url + "/v2/api/channelStatus", {
                params: data, // 使用 params 参数传递参数
            })
            .then(
                (res) => {
                    return res;
                },
                (error) => {
                    console.log("error", error);
                    return error;
                }
            );
    },
    getCurrentSubscribeUidList: (url, data) => {
        return api
            .post(url + "/v2/api/getCurrentSubscribeUidList", {
                data,
            })
            .then(
                (res) => {
                    return res;
                },
                (error) => {
                    console.log("error", error);
                    return error;
                }
            );
    },
    getInstallQrCodeInfo: (url) => {
        return api.get(url + "/install_qr_code_info").then(
            (res) => {
                return res;
            },
            (error) => {
                console.log("error", error);
                return error;
            }
        );
    },
    getQueryHistoryVersion: (url, data) => {
        return api
            .post(url + "/query_history_version", {
                data
            })
            .then(
                (res) => {
                    return res;
                },
                (error) => {
                    console.log("error", error);
                    return error;
                }
            )
    },
    getServerInfo: (url, data) => {
        return api
            .get(url + "/v2/server/info", {
                params: data, // 使用 params 参数传递参数
            })
            .then(
                (res) => {
                    return res;
                },
                (error) => {
                    console.log("error", error);
                    return error;
                }
            )
    },

};
for (let key in v2InterfaceList) {
    service[key] = (data) => {
        return v2InterfaceFactory(v2InterfaceList[key], data);
    };
}
interfaceList.forEach((api) => {
    service[api] = (data) => {
        return interfaceFactory(`/${api}`, data);
    };
});
export default service;

import Tool from "@/common/tool.js";
import { cloneDeep } from "lodash";
import moment from "moment";
import CReverseControl from "@/common/CLiveConferenceNative/CReverseControl";
import IdleJs from "idle-js";
import CEvent from "@/common/CEvent";
const service = require(`../../module/${Tool.getCurrentModuleName()}/service/service.js`).default
import requestManager from '@/common/CommunicationMng/requestManager';
import {Logger} from '@/common/console.js'
//设设备类型
const MEDIA_DEVICE_TYPE = {
    UNKNOWN_AUDIO_DEVICE: -1,
    AUDIO_PLAYOUT_DEVICE: 0,
    AUDIO_RECORDING_DEVICE: 1,
    AUDIO_RECORDING_DEVICE: 2,
    VIDEO_CAPTURE_DEVICE: 3,
    AUDIO_APPLICATION_PLAYOUT_DEVICE: 4,
};
const MEDIA_DEVICE_STATE_TYPE = {
    MEDIA_DEVICE_STATE_IDLE: 0,
    MEDIA_DEVICE_STATE_ACTIVE: 1,
    MEDIA_DEVICE_STATE_DISABLED: 2,
    MEDIA_DEVICE_STATE_NOT_PRESENT: 4,
    MEDIA_DEVICE_STATE_UNPLUGGED: 8,
    MEDIA_DEVICE_STATE_UNRECOMMENDED: 16,
};
const LEAVE_CHANNEL_REASON = {
    NORMAL: "NORMAL",
    NET_WORK_ERROR: "NET_WORK_ERROR",
    OTHERS: "OTHERS",
};
let storeState = null;
let lang = null;
let Toast;
setTimeout(() => {
    storeState = window.vm.$store.state;
    lang = storeState.language;
    Toast = window.vm.$root.platformToast;
});
class liveLogger {
    constructor() {
        this.log = function ({ message, data }) {
            Logger.save({
                message,
                eventType: `live_log`,
                data
            });
        };
        this.error = function ({ message, data }) {
            Logger.saveError({
                message,
                eventType: `live_error`,
                data
            });
        };
    }
}
const logger = new liveLogger();
class CLiveRoom {
    constructor(option) {
        // if (CLiveRoom.instances[option.cid]) {
        //     // 如果已存在对应 cid 的实例，则直接返回该实例
        //     return CLiveRoom.instances[option.cid];
        // }
        // CLiveRoom.instances[option.cid] = this;
        this.cid = option.cid; //当前会话id
        this.reconnectAgoraTimeout = 60; //等待声网重连的时间 秒
        this.ultrasync_uid = storeState.user.uid; //云++id
        this.is_single_chat = window.main_screen.conversation_list[option.cid].is_single_chat;
        this.lostConnectAndOpenConfirmDialogTimer = null; //失去连接并弹窗延时器
        this.lostConnectAndLeaveChannelTimer = null; //失去连接并退出直播延时器
        this.lostConnectAndLeaveChannelTimer2 = null; //弹窗后无操作等待延时器
        this.disconnectDialogId = 0;//断网提示框id
        this.idle = null; // 活跃监控
        this.maxVideoNum = 3;//最大小视频路数量
        this.autoPushStream = false;//是否自动推流
        this.event = new CEvent()
        this.pendingPromises = new Map(); // 使用Map存储待处理的Promise，key为函数名
        // 记录正在进行中的辅流订阅，防止并发导致重复订阅
        this.pendingSubscribeAux = new Set();
        // 添加Promise状态检查方法
        this.isPromisePending = (promise) => {
            return promise && typeof promise.then === 'function' && !promise._isResolved && !promise._isRejected;
        };
        this.data = {
            FreeSpeakLimitUserCount: storeState.systemConfig.serverInfo.voice_config.FreeSpeakLimitUserCount || 10, //可最多支持开麦人数
            isSender: 0, //是否发起身份
            joiningMain: false, //主流正在加入房间
            joiningAux: false, //辅流正在加入房间
            joinedMain: false, //本地主流是否在房间中
            joinedAux: false, //本地辅流是否在房间中
            channelId: 0, //当前房间号
            channelUUId: 0, //当前房间唯一编码
            localAuxUid: 0, //本地申请的辅流Uid
            localMainUid: 0, //本地申请的主流Uid
            currentMainUid: 0, //当前主流UID
            currentMainUltrasyncId: 0, //当前播放主流的云++用户id
            currentSubscribeMain: 0, //当前正在订阅的远端主流
            currentSubscribeAux: [], //当前正在订阅的远端辅流
            roomUserMap: {}, //所有成员uid map信息表
            videoSource: "", //当前需要推送主流的类型/doppler/desktop/
            from: "", //启动直播的业务场景/chat_window会话 live_management预约直播 tv_wall电视墙
            isRecording: false, //是否正在录制
            losing_connect_server: false, //是否正在失去和自己服务器的连接
            lost_connect_server: false, //已失去连接
            hasWhiteBoard: false, //该房间内是否存在白板
            is_open_white_board: false, //是否打开了白板
            whiteBoardInfo: {}, //白板房间信息
            localVideoStream: -1,
            localAudioStream: -1,
            isHost: 0, //是否主讲人
            isLastLocalMain: false, // 断网前本地是否推主流
            memberLength: 0, //成员人数
            cloud_record_auth: 0, //是否可以操作云录制功能
            currentTime: new Date().getTime(),
            remoteControlStatus: 0,
            isConnectedRemoteDopplerSocket: false,
            // beStopSubscribeByCloseVideoAux: 0, //由于远端关闭视频导致的退出订阅
            currentShowWindowAction:'',//当前需要被打开的窗口内容,
            currentVideoList:[],//当前群内的摄像头开启顺序列表
            serviceKnowSelfJoinRoom:false,//服务器是否已经知道该用户加入了群聊
            unReadMsgCount:0,//当前直播时，未打开聊天页面而产生的未读消息数量
            isAIAnalyzeLive:false,//是否是AI分析的直播
            isAIAnalyzing:false,//是否在AI分析中
            AIAnalyzeTaskId:'',//AI分析任务ID
            isLeavingAux: false, // 标记是否正在等待离开辅流完成
            canJoinAuxAgain: true, // 标记是否允许再次加入辅流
            leaveAuxTimer: null, // 离开辅流后的超时计时器
            trainingInfo:null//题目信息
        };
        this.CReverseControl = null;
        if (!window.CReverseControl) {
            window.CReverseControl = {};
        }
        window.CReverseControl[this.cid] = null;
        this.nativeEventListeners = []; // 存储原生通信的事件和对应的监听器

        this.NotifyApplySwitchAIAnalyze = Tool.debounce(this.NotifyApplySwitchAIAnalyzeNoDebounce, 1500);
    }
    bindNativeEvents() {
        const nativeEvents = [
            'NotifyOtherUserJoined',
            'NotifyOtherUserOffline',
            'NotifyLeaveChannelMain',
            'NotifyLeaveChannelAux',
            'NotifyMuteLocalVideoStream',
            'NotifyMuteLocalAudioStream',
            'NotifyRejoinChannelMain',
            'NotifyRejoinChannelAux',
            'NotifyRemoteVideoStateChanged',
            'NotifyRemoteAudioStateChanged',
            'NotifyRemoteVideoStats',
            'NotifyRemoteAudioStats',
            'NotifyLocalAudioStateChanged',
            'NotifyLocalVideoStateChanged',
            'NotifyApplyPermissionOfMainStream',
            'NotifyApplyPermissionOfMuteAudio',
            'NotifyApplyPermissionOfMuteVideo',
            'NotifyAudioDeviceChanged',
            'NotifyStopSubscribeRemoteStreamAux',
            'NotifyRtcStats',
            'NotifyClickNativePlayerButton',
            'NotifyClickNativeClosePlayerButton',
            'NotifyConnectionLost',
            'NotifyApplyOpenWhiteBoard',
            'NotifyTokenPrivilegeWillExpireAux',
            'NotifyTokenPrivilegeWillExpireMain',
            'NotifyTokenExpiredMain',
            'NotifyTokenExpiredAux',
            'NotifyOpenWhiteBoard',
            'NotifyCloseWhiteBoard',
            'NotifyConnectionStateChanged',
            'NotifyCallApplyTurnOnCloudRecording',
            'NotifyCallApplyTurnOffCloudRecording',
            'NotifyApplyOpenConferenceWebWindow',
            'NotifyApplySwitchAIAnalyze'
        ];
        nativeEvents.forEach((event) => {
            const boundListener = this.handleNativeNotify.bind(this, event);
            requestManager.on(event, boundListener);
            // 存储事件和监听器
            this.nativeEventListeners.push({ event, listener: boundListener });
        });
    }
    // 解绑所有事件
    unbindNativeEvents() {
        this.nativeEventListeners.forEach(({ event, listener }) => {
            requestManager.off(event, listener);
        });

        // 清空存储的事件和监听器
        this.nativeEventListeners = [];
    }
    handleNativeNotify(eventName, params) {
        if(this[eventName]&&typeof this[eventName] === 'function'){
            this[eventName](params)
        }
    }
    socketNotify(name, data) {
        const socketNotifyAction = {
            'notify_forbid_audio':(data)=>{
                //收到禁言通知(新版声网)
                if (Number(window.vm.$root.currentLiveCid) !== Number(this.cid)) {
                    return;
                }
                if (this.data.joinedAux && data.requestUserId !== this.ultrasync_uid && !this.data.isHost) {
                //自己发起的不处理 或者自己为主持人不处理
                    if (!data.userId) {
                    //没有指定userId 则为全体静音
                        this.MuteLocalAudioStream({ uid: this.data.localAuxUid, isMute: true });
                    } else {
                        if (data.userId === this.ultrasync_uid) {
                            this.MuteLocalAudioStream({ uid: this.data.localAuxUid, isMute: true });
                        }
                    }
                }
            },
            'conference.host.request.close.channel':(data)=>{
                if (Number(window.vm.$route.params.cid) !== Number(this.cid)) {
                    return;
                }
                if (!this.data.joinedAux) {
                    return;
                }
                logger.log({message:"conference.host.request.close.channel"});
                Toast(lang.live_ended, 2);
                window.vm.$store.commit("liveConference/updateConferenceState", {
                    cid: this.cid,
                    obj: {
                        conferenceState: 0,
                    },
                });
                if (this.data.localAuxUid) {
                    this.LeaveChannelAux("nothing", 2);
                }
            },
            'notify_conference_remote_stream_change':(data)=>{
                if (Number(window.vm.$root.currentLiveCid) !== Number(this.cid)) {
                    return;
                }
                if (data.user_id !== this.ultrasync_uid) {
                    //不处理远端告知自己的音视频状态变更
                    if (!this.data.roomUserMap.hasOwnProperty(data.uid)) {
                        //用户不存在则不处理更新
                        return;
                    }
                    let params = {};
                    if (data.type === "audio") {
                        params.audioStream = data.status;
                    } else {
                        params.videoStream = data.status;
                    }
                    this.updateUidInfo(data.uid, params);
                    // if (!data.status && data.type === "video" && this.data.currentSubscribeAux.includes(data.uid)) {
                    //     //如果订阅的流主动关闭摄像头

                    //     this.StopSubscribeRemoteStreamAux(data.uid);
                    //     // this.data.beStopSubscribeByCloseVideoAux = data.uid;
                    // }
                    // if (data.status && data.type === "video") {
                    //     if (
                    //         this.data.beStopSubscribeByCloseVideoAux === data.uid &&
                    //         this.data.currentSubscribeAux.length === 0
                    //     ) {
                    //         this.SubscribeRemoteStreamAux(data.uid);
                    //     }
                    //     // this.checkSubscribe();
                    // }
                }
            },
            'disconnect':(data)=>{
                logger.log({message:'live socket disconnect',data:this.cid})
                window.vm.$store.commit("liveConference/updateConferenceState", {
                    cid: this.cid,
                    obj: {
                        conferenceState: 0,
                    },
                });
                if (Number(window.vm.$root.currentLiveCid) !== Number(this.cid)) {
                    return;
                }
                if (this.data.joinedAux) {
                    // this.LeaveChannelAux("nothing");

                    window.CWorkstationCommunicationMng.CallSocketConnectStatus({ status: 0 });
                    Toast(lang.lost_connect_with_server);

                    //pc
                    window.CWorkstationCommunicationMng.TopNativeWindow();
                    window.CWorkstationCommunicationMng.ShowPopupMsg({
                        info: lang.lost_connect_with_server,
                    });
                    this.data.losing_connect_server = true;
                    this.event.emit("HandleDisconnectAux")
                }
            },
            'connect':async(data)=>{
                logger.log({message:'live socket connect',data:this.cid})
                if(this.disconnectDialogId === Tool.getCommonDialogId()){ //关闭断网的提示框
                    Tool.closeCommonDialog();
                }
                if (Number(window.vm.$root.currentLiveCid) !== Number(this.cid)) {
                    return;
                }
                this.clearLostConnectTimer()
                await Tool.handleAfterConversationCreated(this.cid)
                window.CWorkstationCommunicationMng.CallSocketConnectStatus({ status: 1 });
                this.ServiceReportJoinChannel({uid:this.data.localAuxUid,status:1})
                this.data.losing_connect_server = false;
                const channelDetail = await this.ServiceGetChannelCurrentStatus();
                logger.log({message:channelDetail,data:"channelDetail"})
                this.data.isHost = channelDetail.host_user_id === this.ultrasync_uid ? 1 : 0;
                this.data.cloud_record_auth = this.checkCloudRecordAuth();
                this.data.isRecording = channelDetail.recording_status;
                this.data.remoteControlStatus = channelDetail.remoteControlStatus;
                this.data.isLocalControl = channelDetail.remote_control_pc_uid === this.ultrasync_uid;
                this.data.currentVideoList = channelDetail.video_agora_uid_list;
                window.CWorkstationCommunicationMng.CallConferenceWebStatus({
                    recording_status: channelDetail.recording_status,
                    living_time: channelDetail.living_time,
                    role: this.data.isHost ? "host" : "audience",
                    isShowCloseConferenceBtn: this.checkShowLeaveConferenceBtn().isShowCloseConferenceBtn,
                    isShowLeaveConferenceBtn: this.checkShowLeaveConferenceBtn().isShowLeaveConferenceBtn,
                    cloud_record_auth: this.data.cloud_record_auth,
                    remoteControlStatus: channelDetail.remoteControlStatus,
                    isLocalControl: channelDetail.remote_control_pc_uid === this.ultrasync_uid,
                });
                this.checkLockAuxAuth();
                if (channelDetail.uuid !== this.data.channelUUId) {

                    // if (this.data.is_open_white_board) {
                    //     this.CloseWhiteBoardFromMainWeb();
                    // }
                    // setTimeout(() => {
                    //     if (channelDetail.whiteboard_status) {
                    //         this.OpenWhiteBoardFromMainWeb(true);
                    //     }
                    // }, 1000);
                    // 非同一场直播，直接结束
                    Toast(lang.live_expired_tips)
                    this.LeaveChannelAux()
                    return
                }
                if (this.data.joinedAux) {

                    setTimeout(async () => {
                        const { mainInfo } = await this.getCurrentUserList();
                        logger.log({message:'mainInfo',data:mainInfo})
                        const rejoinMain = () => {
                            logger.log({message:'rejoinMain'})
                            // 如果断网前是推主流的，联网后如果没有其他人推主流，则重新推起
                            window.vm.$root.eventBus.$emit(
                                "startJoinRoom",
                                {
                                    main: 1,
                                    aux: 0,
                                    isSender: 0,
                                    videoSource: this.data.videoSource,
                                    cid:this.cid
                                },
                                (is_suc, data) => {
                                    let message = "";
                                    if (!is_suc && typeof data === "string") {
                                        message = data;
                                    }
                                    window.CWorkstationCommunicationMng.CallApplyPermissionResult({
                                        action: "mainStream",
                                        result: is_suc ? 1 : 0,
                                        message,
                                    });
                                }
                            );
                            this.data.isLastLocalMain = false;

                        }
                        logger.log({message:'this.data.isLastLocalMain',data:this.data.isLastLocalMain})
                        if (!mainInfo && this.data.isLastLocalMain) {
                            rejoinMain()
                            return
                        }
                        if(mainInfo && (mainInfo.user_id === this.ultrasync_uid)){ //断网后重连时，服务器认为的主流依然是自己
                            if(this.data.localMainUid){
                                this.ServiceReportJoinChannel({uid:this.data.localMainUid,status:1})
                            }else{
                                rejoinMain()
                            }
                        }
                        await this.judgeIfNeedMuteLocalStreamAfterJoinChannelAux({ uid: this.data.localAuxUid });
                        this.checkSubscribeAux();
                    }, 500);
                }
            },
            'notify_join_whiteboard':(data)=>{
                if (Number(window.vm.$root.currentLiveCid) !== Number(this.cid)) {
                    return;
                }
                if (data.senderUserId !== this.ultrasync_uid && this.data.joinedAux) {
                    //非本人发起
                    this.OpenWhiteBoardFromMainWeb();
                }
            },
            'notify_conference_user_status_change':(data)=>{
                if (Number(window.vm.$root.currentLiveCid) !== Number(this.cid)) {
                    return;
                }
                if(data.user_id === this.ultrasync_uid){
                    if(data.status){
                        this.data.serviceKnowSelfJoinRoom = true
                        this.event.emit('selfJoinRoom',data)
                    }
                }
                if(!this.data.joinedAux){
                    return
                }
                if(data.status === 0){ //用户离线了
                    this.data.memberLength--
                    if(data.user_id === this.ultrasync_uid){
                        return
                    }
                    if(this.data.from === 'tv_wall_push' || this.data.from === 'tv_wall_play'){ // 电视墙会话，另一端离开后，直接强制关闭会话
                        if (this.data.localAuxUid&&data.stream_type === 0) {
                            this.LeaveChannelAux("nothing", 2);
                            return
                        }
                    }
                    if (this.data.currentMainUid === data.agora_uid) {
                        //当离开的是主流
                        this.StopSubscribeRemoteStreamMain();
                    } else if (this.data.currentSubscribeAux.includes(data.agora_uid)) {
                        //当离开的是被订阅的辅流，则主动停止该订阅
                        this.StopSubscribeRemoteStreamAux(data.agora_uid);
                    }
                    this.updateUidInfo(data.agora_uid, { isOnline: 0 });
                }else{
                    this.updateUidInfo(
                        data.agora_uid,
                        {
                            videoStream: data.user_id === this.ultrasync_uid?this.data.localVideoStream:data.video,
                            audioStream: data.user_id === this.ultrasync_uid?this.data.localAudioStream:data.audio,
                            streamType: data.stream_type === 0 ? "aux" : "main",
                            isHost: data.isHost ? 1 : 0,
                            isOnline:data.status,
                            uid: data.agora_uid,
                            user_id: data.user_id, //对应云++用户id
                            nickname:data.nickname
                        },
                        true
                    );
                    if(data.user_id !== this.ultrasync_uid){
                        this.checkSubscribeMain();
                    }
                    window.CWorkstationCommunicationMng.CallConferenceWebStatus({
                        roomUserMap: this.data.roomUserMap,
                    });

                }
                // if (this.data.joinedAux) {
                //     this.getCurrentUserList();
                // }

            },
            'notify_cloud_recording_state_changed':(data)=>{
                logger.log({message:"notify_cloud_recording_state_changed",data:data})
                if (Number(window.vm.$root.currentLiveCid) !== Number(this.cid)) {
                    return;
                }
                if (this.data.joinedAux) {
                    if (data.status) {
                        this.data.isRecording = true;
                        window.CWorkstationCommunicationMng.CallConferenceWebStatus({
                            recording_status: 1,
                        });
                    } else {
                        this.data.isRecording = false;
                        window.CWorkstationCommunicationMng.CallConferenceWebStatus({
                            recording_status: 0,
                        });
                    }
                }
            },
            'remoteControlAvailableStatusChange':(data)=>{
                if (Number(window.vm.$root.currentLiveCid) !== Number(this.cid)) {
                    return;
                }
                if (this.data.joinedAux) {
                    logger.log({message:"remoteControlAvailableStatusChange",data:data})

                    this.data.remoteControlStatus = data.status;
                    this.data.isLocalControl = data.remote_control_pc_uid === this.ultrasync_uid;
                    const controlledUserInfo = this.getAttendUserInfo(data.remote_control_doppler_uid);
                    let params = {
                        remoteControlStatus: data.status,
                        isLocalControl: data.remote_control_pc_uid === this.ultrasync_uid,
                    };
                    if (controlledUserInfo) {
                        params.controlledUserInfo = {
                            nickname: controlledUserInfo.nickname,
                        };
                    }
                    window.CWorkstationCommunicationMng.CallConferenceWebStatus(params);
                }
            },
            'conference.specify.agora.uid':(data)=>{
                logger.log({message:"conference.specify.agora.uid",data:data})
                if (Number(window.vm.$root.currentLiveCid) !== Number(this.cid)) {
                    return;
                }
                if (this.data.joinedAux) {
                    console.log('this.data.joinedAux',this.data.joinedAux)
                    if(data.video_agora_uid_list.length>this.data.currentVideoList.length){ //可订阅摄像头有新增
                        console.log('data.video_agora_uid_list >',JSON.parse(JSON.stringify(data.video_agora_uid_list)),JSON.parse(JSON.stringify(this.data.currentVideoList)))
                        this.data.currentVideoList = data.video_agora_uid_list
                        this.checkSubscribeAux(data.video_agora_uid_list[data.video_agora_uid_list.length-1])
                    }else if(data.video_agora_uid_list.length<this.data.currentVideoList.length){ //可订阅摄像头有减少
                        console.log('data.video_agora_uid_list <',JSON.parse(JSON.stringify(data.video_agora_uid_list)),JSON.parse(JSON.stringify(this.data.currentVideoList)))
                        this.data.currentVideoList = data.video_agora_uid_list
                        this.checkStopSubscribeAux()
                    }else{
                        console.log('data.video_agora_uid_list =',JSON.parse(JSON.stringify(data.video_agora_uid_list)),JSON.parse(JSON.stringify(this.data.currentVideoList)))
                        this.data.currentVideoList = data.video_agora_uid_list
                    }
                }
            },
            'conference_living_ping':(data)=>{
                if (Number(window.vm.$root.currentLiveCid) !== Number(this.cid)) {
                    return;
                }
                if(data.hasOwnProperty('uuid')){
                    if(this.data.channelUUId){ //本地如果没有chanelUUID 不做判断
                        if(data.uuid!==this.data.channelUUId){
                            logger.log({message:'channelUUId not match',data:data.uuid})
                            this.LeaveChannelAux()
                            return
                        }
                    }
                }
                if(this.data.localMainUid){
                    this.ServicePingChannel(this.data.localMainUid)
                }
                if(this.data.localAuxUid){
                    this.ServicePingChannel(this.data.localAuxUid)
                }
            }

        }
        if (socketNotifyAction[name]&&typeof socketNotifyAction[name] === 'function') {
            socketNotifyAction[name](data);
        }
    }
    /**
     * @description  静默加入房间 u-liner静默推流
     */
    clearStatus(type) {
        if (type === "Main") {
            // 主流退出
            this.data.joiningMain = false;
            this.data.joinedMain = false;
            this.data.localMainUid = 0;
            this.data.currentMainUid = 0;
            this.data.currentMainUltrasyncId = 0;
            delete this.data.roomUserMap[this.data.localMainUid];
            this.data.isAIAnalyzing = false;
            this.data.AIAnalyzeTaskId = false;
        }
        if (type === "Aux") {
            // 在清空状态前reject所有pending的Promise
            this.pendingPromises.forEach((promise) => {
                console.error('promise',promise._isResolved,promise._isRejected)
                if (!promise._isResolved && !promise._isRejected) {
                    promise.reject(new Error('Room status cleared'));
                }
            });
            this.pendingPromises.clear(); // 清空Map

            this.data.isSender = 0;
            this.data.isHost = 0;
            this.data.joiningAux = false;
            this.data.joinedAux = false;
            this.data.localAuxUid = 0;
            this.data.roomUserMap = {};
            this.data.currentMainUid = 0;
            this.data.currentMainUltrasyncId = 0;
            this.data.currentSubscribeMain = 0;
            this.data.currentSubscribeAux = [];

            this.data.joiningMain = false;
            this.data.joinedMain = false;
            this.data.localMainUid = 0;
            this.data.currentMainUid = 0;
            this.data.currentMainUltrasyncId = 0;
            this.data.losing_connect_server = false;
            this.data.lost_connect_server = false;
            this.data.is_open_white_board = false;
            this.data.hasWhiteBoard = false;
            this.data.localAudioStream = -1;
            this.data.localVideoStream = -1;
            this.data.isLastLocalMain = false;
            this.data.isRecording = false;
            this.data.memberLength = 0;
            this.data.cloud_record_auth = 0;
            window.vm.$root.currentLiveCid = 0;
            window.livingStatus = 0
            this.data.remoteControlStatus = 0;
            // this.data.beStopSubscribeByCloseVideoAux = 0;
            this.data.currentShowWindowAction = ''
            this.data.currentVideoList = []
            this.data.serviceKnowSelfJoinRoom = false
            this.data.isAIAnalyzeLive = false;
            this.data.channelUUId = 0
            this.data.trainingInfo = null
            this.clearLostConnectTimer();
            if (this.CReverseControl && Tool.checkAppClient('Cef')) {
                this.CReverseControl.destroy();
                this.CReverseControl = null;
                window.CReverseControl[this.cid] = null;
            }
            this.unbindNativeEvents()
            // 清空正在订阅的辅流集合，防止状态残留
            if (this.pendingSubscribeAux) {
                this.pendingSubscribeAux.clear();
            }
        }
    }
    clearLostConnectTimer() {
        if(this.lostConnectAndOpenConfirmDialogTimer||this.lostConnectAndLeaveChannelTimer||this.lostConnectAndLeaveChannelTimer2){
            Tool.closeCommonDialog();
        }
        clearTimeout(this.lostConnectAndOpenConfirmDialogTimer);
        clearTimeout(this.lostConnectAndLeaveChannelTimer);
        clearTimeout(this.lostConnectAndLeaveChannelTimer2);
        this.lostConnectAndOpenConfirmDialogTimer = null;
        this.lostConnectAndLeaveChannelTimer = null;
        this.lostConnectAndLeaveChannelTimer2 = null;


    }
    /**
     * @description  辅流申请加入房间 (音频路)
     * @param {Object} params 参数说明
     */
    JoinChannelAux(odata) {
        return new Promise((resolve, reject) => {
            if (!this.data.canJoinAuxAgain) {
                const message = 'Attempted to joinChannelAux too soon after leaving. Ignoring.'
                logger.error({message});
                return reject(message); // 如果不允许重加，则直接返回
            }
            if (this.data.joiningAux||this.data.joinedAux) {
                Toast(lang.processing_wait, 4);
                return reject(false);
            }
            this.clearStatus()
            this.bindNativeEvents()
            this.data.joiningAux = true;
            this.data.channelId = odata.channelId;
            this.autoPushStream = odata.autoPushStream
            this.data.isSender = odata.isSender;
            this.data.isHost = odata.isSender; //默认认为发起者也是主讲人
            this.data.isAIAnalyzeLive = odata.isAIAnalyzeLive
            console.error('trainingInfo',odata.trainingInfo)
            this.data.trainingInfo = odata.trainingInfo
            if (storeState.liveConference[this.cid] && storeState.liveConference[this.cid].senderUserId) {
                this.data.isHost = storeState.liveConference[this.cid].senderUserId === this.ultrasync_uid ? 1 : 0;
            }
            this.data.from = odata.from;
            logger.log({message:'this.data.from',data:this.data.from})
            let params = {
                uid: odata.uid,
                channelId: odata.channelId,
                token: odata.token,
                group_title: odata.group_title,
                isAIAnalyzeLive:odata.isAIAnalyzeLive,
                groupId: this.cid,
            };
            logger.log({message:`${moment(new Date()).format("YYYY-MM-DD HH.mm.ss z")} ${storeState.user.nickname}${lang.whose_recording_file} ${odata.channelId}`,data:'params'})
            window.vm.$root.currentLiveCid = this.cid;
            window.vm.$root.currentLiveCid = this.cid;
            window.localStorage.setItem(`lastPushStreamCid_${this.ultrasync_uid}`, this.cid);
            try {
                Tool.createCWorkstationCommunicationMng({
                    name: "JoinChannelAux",
                    emitName: "NotifyJoinChannelAux",
                    params,
                    timeout: 10000,
                })
                    .then(async (notifyRes) => {
                        if (notifyRes.error_code === 0) {
                            this.handleAfterJoinChannelAux(odata).then((res)=>{
                                resolve(res)
                            }).catch(error=>{
                                reject(error)
                                this.data.joiningAux = false;
                                Toast(`error,event:handleAfterJoinChannelAux`);
                                this.LeaveChannelAux();
                                this.clearStatus("Aux");
                                return
                            })
                        } else {
                            reject(`error,code:${notifyRes.error_code},event:NotifyJoinChannelAux`);
                            this.data.joiningAux = false;
                            Toast(`error,code:${notifyRes.error_code},event:NotifyJoinChannelAux`);
                            this.LeaveChannelAux();
                            this.clearStatus("Aux");
                            return
                        }
                    })
                    .catch((error) => {
                        reject(error);
                        this.data.joiningAux = false;
                        this.LeaveChannelAux()
                        this.clearStatus("Aux");
                        logger.error({message:error.message})
                        this.ServiceReportJoinChannel({
                            uid:odata.localAuxUid,
                            status:0
                        })
                        return
                    });
            } catch (error) {
                reject(false);
                Toast(error);
                logger.error({message:error.message})
                this.LeaveChannelAux()
                this.clearStatus("Aux");
                this.data.joiningAux = false;
                return
            }
        });
    }
    /**
     * @description  确认服务器层面，本端是否已经加入了直播间
     * @param {Object} params 参数说明
     */
    checkSelfJoinRoomByService(){
        return new Promise((resolve,reject)=>{
            if(this.data.serviceKnowSelfJoinRoom){
                resolve(true)
            }
            this.event.on('selfJoinRoom',()=>{
                resolve(true)
            })
            setTimeout(()=>{
                if(this.data.serviceKnowSelfJoinRoom){
                    resolve(true)
                }else{
                    resolve(true)
                    console.error('checkSelfJoinRoomByService timeout')
                }
            },3000)
            // 保存Promise以便后续可以reject，使用函数名作为key
            const promise = {promise: this, resolve, reject};
            this.pendingPromises.set('checkSelfJoinRoomByService', promise);
        })
    }
    handleAfterJoinChannelAux(odata){
        return new Promise(async (resolve,reject)=>{
            try {
                let data = odata;
                this.ServiceReportJoinChannel({
                    uid:data.uid,
                    status:1
                })
                this.data.joiningAux = false;
                this.data.joinedAux = true;
                this.data.localAuxUid = data.uid;
                if (Tool.checkAppClient('Cef')) {
                    this.createCReverseControl();
                }
                this.clearLostConnectTimer();
                this.data.lost_connect_server = false;
                data.isOnline = 1;
                data.user_id = this.ultrasync_uid;
                data.isHost = this.data.isHost;
                let role = "audience";
                if (this.data.isHost) {
                    role = "host";
                } else {
                    role = "audience";
                }

                this.event.emit('HandleNotifyJoinChannelAux')
                resolve(true)
                await this.checkSelfJoinRoomByService()
                const channelDetail = await this.ServiceGetChannelCurrentStatus();
                logger.log({message:'channelDetail',data:channelDetail})
                this.data.channelUUId = channelDetail.uuid;
                this.data.isHost = channelDetail.host_user_id === this.ultrasync_uid ? 1 : 0;
                this.data.cloud_record_auth = this.checkCloudRecordAuth();
                this.data.isRecording = channelDetail.recording_status;
                this.data.remoteControlStatus = channelDetail.remoteControlStatus;
                this.data.isLocalControl =
                    channelDetail.remote_control_pc_uid === this.ultrasync_uid;
                this.data.currentVideoList = channelDetail.video_agora_uid_list;
                this.ServiceReportTopicInfo()
                if (channelDetail.whiteboard_status) {
                    //是否开启白板
                    this.OpenWhiteBoardFromMainWeb();
                }
                window.CWorkstationCommunicationMng.CallConferenceWebStatus({
                    recording_status: channelDetail.recording_status,
                    living_time: channelDetail.living_time,
                    role,
                    cloud_record_auth: this.data.cloud_record_auth,
                    group_title: data.group_title,
                    isShowCloseConferenceBtn:
                        this.checkShowLeaveConferenceBtn().isShowCloseConferenceBtn,
                    isShowLeaveConferenceBtn:
                        this.checkShowLeaveConferenceBtn().isShowLeaveConferenceBtn,
                    recordFileName: `${moment(new Date()).format("YYYY-MM-DD HH.mm.ss z")} ${
                        storeState.user.nickname
                    }${lang.whose_recording_file} ${odata.channelId}`,
                    remoteControlStatus: channelDetail.remoteControlStatus,
                    isLocalControl: channelDetail.remote_control_pc_uid === this.ultrasync_uid,
                });
                this.checkLockAuxAuth();
                await this.getCurrentUserList();
                await this.judgeIfNeedMuteLocalStreamAfterJoinChannelAux(data);
                this.checkSubscribeAux();

                // this.idle.start();

            } catch (error) {
                reject(error)
                console.error(error)

            }
        })

    }
    /**
     * @description  主流申请加入房间 (超声机器路/桌面推送)
     */
    JoinChannelMain(odata) {
        return new Promise((resolve, reject) => {
            if (this.data.joiningMain) {
                Toast(lang.processing_wait, 4);
                return reject(false);
            }
            this.data.joiningMain = true;
            this.data.channelId = odata.channelId;
            this.autoPushStream = odata.autoPushStream
            let params = {
                uid: odata.uid,
                channelId: odata.channelId,
                token: odata.token,
            };
            try {
                Tool.createCWorkstationCommunicationMng({
                    name: "JoinChannelMain",
                    emitName: "NotifyJoinChannelMain",
                    params,
                    timeout: 10000,
                })
                    .then((notifyRes) => {
                        logger.log({message:"NotifyJoinChannelMain",data:notifyRes})
                        this.StopSubscribeRemoteStreamMain();
                        this.data.joiningMain = false;
                        if (notifyRes.error_code === 0) {
                            this.ServiceReportJoinChannel({
                                uid:odata.uid,
                                status:1
                            })
                            this.data.joinedMain = true;
                            let data = notifyRes.data;
                            data.isOnline = 1;
                            data.user_id = this.ultrasync_uid;
                            this.data.localMainUid = params.uid;
                            this.data.currentMainUid = params.uid;
                            this.data.currentMainUltrasyncId = this.ultrasync_uid;
                            this.judgeIfNeedMuteLocalStreamAfterJoinChannelMain(data);
                            this.updateUidInfo(notifyRes.data.uid, data);
                            this.checkLockAuxAuth();
                            return resolve(true);
                        } else {
                            reject(false);
                            Toast(`error,code:${notifyRes.error_code},event:NotifyJoinChannelMain`);
                            this.clearStatus("Main");
                            return
                        }
                    })
                    .catch((error) => {
                        reject(error);
                        this.LeaveChannelMain();
                        this.clearStatus("Main");
                        this.data.joiningMain = false;
                        this.ServiceReportJoinChannel({
                            uid:odata.uid,
                            status:0
                        })
                        return
                    });
            } catch (error) {
                reject(false);
                Toast(error);
                this.clearStatus("Main");
                this.data.joiningMain = false;
                return
            }
        });
    }
    /**
     * @description  主流重新加入频道回调
     * @param {Object} params 参数说明
     */
    NotifyRejoinChannelMain(json) {
        logger.log({message:"NotifyRejoinChannelMain",data:json})
        this.data.joiningMain = false;
        if (json.error_code === 0) {
            this.data.joinedMain = true;
            let data = json.data;
            data.isOnline = 1;
            data.user_id = this.ultrasync_uid;
            // this.data.localMainUid = data.uid;
            // this.data.currentMainUid = data.uid;
            // this.data.currentMainUltrasyncId = this.ultrasync_uid;
            // this.judgeIfNeedMuteLocalStreamAfterJoinChannelMain(data);
            this.updateUidInfo(data.uid, data);
        } else {
            Toast(`error,code:${json.error_code},event:NotifyRejoinChannelMain`);
            this.clearStatus("Main");
        }
    }
    /**
     * @description  辅流重新加入频道回调
     * @param {Object} params 参数说明
     */
    NotifyRejoinChannelAux(json) {
        logger.log({message:"NotifyRejoinChannelAux",data:json})
        this.data.joiningAux = false;
        if (json.error_code === 0) {
            this.data.joinedAux = true;
            this.data.lost_connect_server = false;
            this.clearLostConnectTimer();

            let data = json.data;
            data.isOnline = 1;
            data.user_id = this.ultrasync_uid;
            this.data.localAuxUid = data.uid;
            this.updateUidInfo(data.uid, data);
            //此处，由于可能声网sdk断开，导致服务器清空状态，重新上报当前本地视频流音频流状态给服务器。
            this.ServiceReportLocalStreamStatus("audio", this.data.localAudioStream);
            this.ServiceReportLocalStreamStatus("video", this.data.localVideoStream);
            this.event.emit('HandleNotifyJoinChannelAux')

        } else {
            Toast(`error,code:${json.error_code},event:NotifyRejoinChannelAux`);
            this.clearStatus("Aux");
        }
    }
    /**
     * @description 主流申请离开房间
     * @param {Object} params 参数说明
     */
    LeaveChannelMain() {
        window.CWorkstationCommunicationMng.LeaveChannelMain({
            uid: this.data.localMainUid,
        });
    }
    /**
     * @description app通知主流离开结果
     * @param {Object} params 参数说明
     */
    NotifyLeaveChannelMain() {
        this.ServiceReportLeaveChannel({uid:this.data.localMainUid,status:1})
        this.checkLockAuxAuth();
        if(this.data.isAIAnalyzing&&this.data.AIAnalyzeTaskId){
            window.vm.$root.eventBus.$emit("sendImageWithAiReport",this.data.AIAnalyzeTaskId);
            this.stopAIAnalyze(false)
        }
        this.clearStatus("Main");
    }
    formatStackTrace(stackTrace) {
        var stackArray = stackTrace.split('\n');
        stackArray.shift(); // 去掉第一行 "Error"

        var formattedStack = stackArray.map(function (stack) {
            return stack.replace(/^\s+at\s+/g, ''); // 去掉 "at" 前缀
        });

        return formattedStack;
    }
    getStackTrace() {
        var stackTrace;
        try {
            throw new Error();
        } catch (error) {
            stackTrace = error.stack;
        }
        return this.formatStackTrace(stackTrace);
    }
    /**
     * @description 辅流申请离开房间
     * @param {Object} params 参数说明
     */
    LeaveChannelAux(action = "normal", state = 0, reason = LEAVE_CHANNEL_REASON["NORMAL"]) {
        logger.log({message:'leaveChannelAux',data:this.getStackTrace()})
        if(this.data.joiningAux){
            return
        }
        let leaveState = state; //0离开 1结束 2.远端结束 3异常结束
        if (action === "sender" || action === "groupOwner" || (this.is_single_chat && action !== "nothing")) {
            leaveState = 1;
            window.vm.$store.commit("liveConference/updateConferenceState", {
                cid: this.cid,
                obj: {
                    conferenceState: 0,
                },
            });
            this.ServiceDestroyChannel(); //强制结束在场直播并通知所有人
        }
        logger.log({message:"LeaveChannelAux",data:leaveState,reason:reason})
        // 设置离开状态并启动超时计时器
        this.data.isLeavingAux = true;
        this.data.canJoinAuxAgain = false;
        if (this.data.leaveAuxTimer) {
            clearTimeout(this.data.leaveAuxTimer); // 清除可能存在的旧计时器
        }
        this.data.leaveAuxTimer = setTimeout(() => {
            logger.log({message:'LeaveChannelAux timeout reached (1000ms). Allowing rejoin.'})
            this.data.isLeavingAux = false;
            this.data.canJoinAuxAgain = true;
            this.data.leaveAuxTimer = null;
        }, 1000);

        window.CWorkstationCommunicationMng.LeaveChannelAux({
            uid: this.data.localAuxUid,
            state: leaveState,
            reason,
        });
        this.handleAfterLeaveChannel()
    }
    /**
     * @description 离开直播后的处理
     */
    handleAfterLeaveChannel(){
        if(this.data.joinedMain){
            this.ServiceReportLeaveChannel({uid:this.data.localMainUid,status:1})
        }
        this.ServiceReportLeaveChannel({uid:this.data.localAuxUid,status:1})
        if(this.data.isAIAnalyzing&&this.data.AIAnalyzeTaskId){
            window.vm.$root.eventBus.$emit("sendImageWithAiReport",this.data.AIAnalyzeTaskId);
        }
        const isLastMember = this.ifLastChildLeaveRoom();
        let finalAction = isLastMember ? "nothing" : "normal"; //最后一个离开的用户，就没必要在通知组件处理弹窗操作了
        this.event.emit('HandleNotifyLeaveChannelAux',{
            isHost: this.data.isHost,
            action: finalAction,
        })

        this.data.from = "";
        if (this.data.is_open_white_board) {
            this.CloseWhiteBoardFromMainWeb();
        }

        this.clearStatus("Aux");
        setTimeout(() => {
            window.CWorkstationCommunicationMng.CallServiceDestroyChannel();
        }, 0);
    }
    /**
     * @description app通知辅流离开结果
     * @param {leaveType}  参数说明leaveType：0 离开会议  1结束会议
     */
    NotifyLeaveChannelAux(json) {
        // 如果收到了离开通知，且我们正处于等待离开状态，则清除定时器并允许重新加入
        if (this.data.isLeavingAux) {
            logger.log({message:'NotifyLeaveChannelAux received while leaving. Allowing rejoin.'})
            if (this.data.leaveAuxTimer) {
                clearTimeout(this.data.leaveAuxTimer);
                this.data.leaveAuxTimer = null;
            }
            this.data.isLeavingAux = false;
            this.data.canJoinAuxAgain = true;
        }
        if (this.data.joinedAux) {
            this.handleAfterLeaveChannel()
            return;
        }
    }
    /**
     * @description  通知远端用户加入
     * @param {Object} params 参数说明
     */
    async NotifyOtherUserJoined(json) {
        logger.log({message:'NotifyOtherUserJoined',data:json})
    }

    /**
     * @description  通知远端用户离开
     * @param {Object} params 参数说明
     */
    async NotifyOtherUserOffline(json) {
        logger.log({message:"NotifyOtherUserOffline",data:json})
        if (json.error_code === 0) {
        }
    }
    /**
     * @description  是否在限制开麦范围内
     * @param {Object} params 参数说明
     */
    IsLimitAudioCount() {
        let audioStreamCount = this.getCurrentAudioStreamCount();
        if (audioStreamCount >= this.data.FreeSpeakLimitUserCount) {
            // //超过最大开麦的人数范围
            // Toast(lang.open_mic_to_many_tips);
            return true;
        } else {
            return false;
        }
    }
    /**
     * @description  开/关音频流
     * @param {Object} params 参数说明
     */
    async MuteLocalAudioStream({ uid, isMute }) {
        logger.log({message:"MuteLocalAudioStream",data:uid,isMute:isMute})
        try {
            if(!isMute){
                await Tool.queryAppPermissions(['RECORD_AUDIO'])
            }
            if (this.IsLimitAudioCount() && isMute === false) {
                //不在允许开麦的人数范围内
                Toast(lang.open_mic_to_many_tips);
                window.CWorkstationCommunicationMng.CallApplyPermissionResult({
                    message: lang.open_mic_to_many_tips,
                });
                return;
            }
            let params = {
                uid,
                isMute,
            };
            window.CWorkstationCommunicationMng.MuteLocalAudioStream(params);
        } catch (error) {
            logger.error({message:error})
        }

    }
    /**
     * @description  通知开/关音频流结果
     * @param {Object} params 参数说明
     */
    NotifyMuteLocalAudioStream(json) {
        logger.log({message:"NotifyMuteLocalAudioStream",data:json})
        if (json.error_code === 0 && json.data.uid === this.data.localAuxUid) {
            let audioStream = 0;
            if (json.data.isMute) {
                audioStream = 0;
            } else {
                audioStream = 1;
            }
            this.data.localAudioStream = audioStream;

            this.updateUidInfo(json.data.uid, { audioStream });
            this.ServiceReportLocalStreamStatus("audio", audioStream);
        }else if(json.error_code !== 0){
            this.data.localAudioStream = 0;
        }
    }
    /**
     * @description  开/关视频流
     * @param {Object} params 参数说明
     */
    async MuteLocalVideoStream({ uid, isMute }) {
        try {
            if(!isMute && uid!==this.data.localMainUid){
                await Tool.queryAppPermissions(['CAMERA'])
            }
            let params = {
                uid,
                isMute,
                automatic:this.autoPushStream
            };
            logger.log({message:'MuteLocalVideoStream',data:params})
            if (uid === this.data.localMainUid) {
                //推主流时，如果有默认videoSource，需要带上
                if (this.data.videoSource) {
                    params.videoSource = this.data.videoSource;
                }
                if (Tool.checkAppClient('UltraSoundMobile')) {
                    this.data.videoSource = "doppler";
                    this.ServiceReportCurrentMainStreamType("ultrasync", isMute);
                }
                window.CWorkstationCommunicationMng.MuteLocalVideoStream(params);
            }else{
                try {
                    if(!isMute){
                        await this.handleCheckMuteVideo()
                    }
                    window.CWorkstationCommunicationMng.MuteLocalVideoStream(params);
                    let videoStream = 0;
                    if (isMute) {
                        videoStream = 0;
                    } else {
                        videoStream = 1;
                    }
                    this.data.localVideoStream = videoStream;
                } catch (error) {
                    logger.error({message:error})
                }

            }
        } catch (error) {
            logger.error({message:error})
        }



    }
    /**
     * @description  通知开/关视频流结果
     * @param {Object} params 参数说明
     */
    NotifyMuteLocalVideoStream(json) {
        logger.log({message:"NotifyMuteLocalVideoStream",data:json})
        if (json.error_code === 0 && json.data.uid === this.data.localAuxUid) {
            let videoStream = 0;
            if (json.data.isMute) {
                videoStream = 0;
            } else {
                videoStream = 1;
            }
            this.data.localVideoStream = videoStream;
            this.updateUidInfo(json.data.uid, { videoStream });
            this.ServiceReportLocalStreamStatus("video", videoStream);
        } else if (json.error_code === 0 && json.data.uid === this.data.localMainUid) {
            const type = ["", "doppler", "desktop", ""]; //用于给原生传的参数
            const service_type = ["", "ultrasync", "desktop", ""]; //用于给服务器传的参数
            if (!json.data.isMute) {
                this.data.videoSource = type[json.data.videoSourceType];
            }
            this.ServiceReportCurrentMainStreamType(service_type[json.data.videoSourceType], json.data.isMute);
        }else if(json.error_code !== 0){
            this.data.localVideoStream = 0;
        }
    }
    /**
     * @description  判断当前已经有多少路小视频流
     * @param {Object} params 参数说明
     */
    getCurrentShowVideoNum(){
        let videoNum = 0
        if(this.data.localVideoStream === 1){
            videoNum++
        }
        videoNum+=this.data.currentSubscribeAux.length
        return videoNum
    }
    /**
     * @description  检测是否可以打开摄像头
     * @param {Object} params 参数说明
     */
    handleCheckMuteVideo(){
        return new Promise((resolve,reject)=>{
            if(this.getCurrentShowVideoNum()<this.maxVideoNum){
                resolve(true)
            }else{
                if(this.data.localVideoStream === 1){ //video has open
                    resolve(true)
                    return
                }else{
                    Tool.openCommonDialog({
                        buttons: [lang.confirm_txt, lang.cancel_btn],
                        message: lang.auto_cancel_video_tips,
                        confirm: () => {
                            if(this.getCurrentShowVideoNum()<this.maxVideoNum){
                                this.MuteLocalVideoStream({ uid: this.data.localAuxUid, isMute: false })
                            }else{
                                this.data.currentSubscribeAux[0]&&this.StopSubscribeRemoteStreamAux(this.data.currentSubscribeAux[0])
                                this.MuteLocalVideoStream({ uid: this.data.localAuxUid, isMute: false })
                            }
                        },
                        reject: () => {
                            console.log('cancel')
                        },
                    });
                }

            }
        })
    }
    NotifyConnectionLost(json) {
        if(!this.data.joinedAux){
            return
        }
        console.error("NotifyConnectionLost", json, moment(new Date()).format("YYYY-MM-DD HH:mm:ss z"));
        this.data.lost_connect_server = true;
        // if(this.data.memberLength === 1){
        //     this.LeaveChannelAux()
        // }else{

        // }
        this.HandleAgoraDisconnect();
    }
    HandleAgoraDisconnect() {
        this.clearLostConnectTimer();
        this.ServiceReportLeaveChannel({uid:this.data.localAuxUid,status:1})
        this.lostConnectAndOpenConfirmDialogTimer = setTimeout(() => {
            //断开60s,弹出是否继续等待连接提示
            this.lostConnectAndLeaveChannelTimer2 = setTimeout(() => {
                //断开2min无操作,不再继续尝试重连
                this.LeaveChannelAuxAfterTimeout();
            }, this.reconnectAgoraTimeout * 1000);
            Tool.openCommonDialog({
                buttons: [lang.keep_waiting, lang.close_consultation],
                message: lang.live_connection_continue_waiting_tips,
                confirm: () => {
                    if (this.lostConnectAndLeaveChannelTimer) {
                        clearTimeout(this.lostConnectAndLeaveChannelTimer);
                    }
                    if (this.lostConnectAndLeaveChannelTimer2) {
                        clearTimeout(this.lostConnectAndLeaveChannelTimer2);
                    }
                    window.CWorkstationCommunicationMng.ShowPopupMsg({
                        info: lang.waiting_for_reconnection,
                    });
                    this.lostConnectAndLeaveChannelTimer = setTimeout(() => {
                        //点击继续重连后，1min未重连,不再继续尝试重连
                        this.LeaveChannelAuxAfterTimeout();
                    }, this.reconnectAgoraTimeout * 1000);
                },
                reject: () => {
                    this.LeaveChannelAux("nothing", 3, LEAVE_CHANNEL_REASON["NET_WORK_ERROR"]);
                },
                cancel: () => {},
            });
        }, this.reconnectAgoraTimeout * 1000);

        if (this.data.localMainUid) {
            this.MuteLocalVideoStream({ uid: this.data.localMainUid, isMute: true }); //关掉本地预览
            this.LeaveChannelMain();
            this.data.isLastLocalMain = true;
        }
    }
    LeaveChannelAuxAfterTimeout() {
        this.LeaveChannelAux("nothing", 3, LEAVE_CHANNEL_REASON["NET_WORK_ERROR"]);
        setTimeout(() => {
            this.disconnectDialogId = Tool.openCommonDialog({
                buttons: [lang.got_it],
                message: lang.live_connection_continue_waiting_tips,
            });
        }, 800);
    }
    /**
     * @description  获取当前用户列表
     * @param {Object} params 参数说明
     */
    async getCurrentUserList() {
        if (!this.data.joinedAux) {
            return;
        }
        let list = await this.ServiceGetAgoraUidList();
        logger.log({message:"getCurrentUserList",data:list})

        let mainInfo = null;
        let uidList = [];
        let memberLength = 0;
        let leaveUserArr = [];

        for (let item of list) {
            uidList.push(item.agora_uid);

            if (item.stream_type === 1) {
                mainInfo = item;
            } else {
                memberLength++;
            }

            if (item.user_id === this.ultrasync_uid) {
                item.video = this.data.localVideoStream;
                item.audio = this.data.localAudioStream;
            }
        }

        this.data.memberLength = memberLength;
        this.updateAgoraUidList(list);

        for (let item of Object.values(this.data.roomUserMap)) {
            if (!uidList.includes(item.uid)) {
                leaveUserArr.push(item.uid);
            }
        }

        for (let uid of leaveUserArr) {
            if (this.data.currentMainUid === uid) {
                this.StopSubscribeRemoteStreamMain();
            } else if (this.data.currentSubscribeAux.includes(uid)) {
                this.StopSubscribeRemoteStreamAux(uid);
            }

            this.updateUidInfo(uid, { isOnline: 0 });
        }

        window.CWorkstationCommunicationMng.CallConferenceWebStatus({
            roomUserMap: this.data.roomUserMap,
        });

        this.checkSubscribeMain();

        return {
            mainInfo,
        };
    }
    /**
     * @description  远端用户开/关视频流
     * @param {Object} params 参数说明
     */
    NotifyRemoteVideoStateChanged(json) {
        // console.info('NotifyRemoteVideoStateChanged',json)
        // if(json.error_code === 0){
        //     if(!this.data.roomUserMap.hasOwnProperty(json.data.uid)){ //用户不存在则不处理更新
        //         return
        //     }
        //     let videoStream = 0
        //     let openState = [1,2]
        //     if(openState.includes(json.data.state)){
        //         videoStream = 1
        //     }else{
        //         videoStream = 0
        //     }
        //     this.updateUidInfo(json.data.uid,{videoStream})
        // }
    }
    /**
     * @description  远端用户开/关音频流
     * @param {Object} params 参数说明
     */
    NotifyRemoteAudioStateChanged(json) {
        // console.info('NotifyRemoteAudioStateChanged',json)
        // if(json.error_code === 0){
        //     if(!this.data.roomUserMap.hasOwnProperty(json.data.uid)){ //用户不存在则不处理更新
        //         return
        //     }
        //     let audioStream = 0
        //     let openState = [1,2]
        //     if(openState.includes(json.data.state)){
        //         audioStream = 1
        //     }else{
        //         audioStream = 0
        //     }
        //     this.updateUidInfo(json.data.uid,{audioStream})
        // }
    }
    /**
     * @description  远端用户视频流信息统计
     * @param {Object} params 参数说明
     */
    NotifyRemoteVideoStats(json) {
        // if (json.error_code === 0) {
        //     if (!this.data.roomUserMap.hasOwnProperty(json.data.uid)) {
        //         //用户不存在则不处理更新
        //         return;
        //     }
        //     let closeState = [5, 6];
        //     let videoStream = 0;
        //     if (closeState.includes(json.data.quality)) {
        //         videoStream = 0;
        //     } else {
        //         videoStream = 1;
        //     }
        //     this.updateUidInfo(json.data.uid, {
        //         videoStream,
        //         videoStreamDetail: json.data,
        //     });
        // }
    }
    /**
     * @description  远端用户音频流信息统计
     * @param {Object} params 参数说明
     */
    NotifyRemoteAudioStats(json) {
        // if (json.error_code === 0) {
        //     if (!this.data.roomUserMap.hasOwnProperty(json.data.uid)) {
        //         //用户不存在则不处理更新
        //         return;
        //     }
        //     let closeState = [5, 6];
        //     let audioStream = 0;
        //     if (closeState.includes(json.data.quality)) {
        //         audioStream = 0;
        //     } else {
        //         audioStream = 1;
        //     }
        //     this.updateUidInfo(json.data.uid, {
        //         audioStream,
        //         audioStreamDetail: json.data,
        //     });
        // }
    }
    /**
     * @description  本地音频状态发生改变回调  // 用来检测本地音频出现异常情况，不作为开关音频状态处理
     * @param {Object} params 参数说明
     */
    NotifyLocalAudioStateChanged(json) {
        logger.log({message:"NotifyLocalAudioStateChanged",data:json})
        let errorState = [3];
        if (errorState.includes(json.data.state)) {
            // Toast(json.error_code)
            this.updateUidInfo(json.data.uid, { audioStream: 0 });
        }
    }
    /**
     * @description  本地视频状态发生改变回调 // 用来检测本地视频频出现异常情况，不作为开关视频状态处理
     * @param {Object} params 参数说明
     */
    NotifyLocalVideoStateChanged(json) {
        logger.log({message:"NotifyLocalVideoStateChanged",data:json})
        let errorState = [3];
        if (errorState.includes(json.data.state)) {
            // Toast(json.error_code)
            this.updateUidInfo(json.data.uid, { audioStream: 0 });
        }
    }
    /**
     * @description  辅流判断加入房间后是否需要打开音视频流
     */
    async judgeIfNeedMuteLocalStreamAfterJoinChannelAux(data) {
        let isMuteVideo = true;
        let isMuteAudio = true;
        if (this.data.isSender === 1) {
            //发起者
            isMuteVideo = true;
            isMuteAudio = false;
        } else {
            isMuteVideo = true;
            isMuteAudio = true;
        }
        if(this.data.isAIAnalyzeLive){
            isMuteAudio = true
            isMuteVideo = true
        }
        logger.log({message:"judgeIfNeedMuteLocalStreamAfterJoinChannelAux",data:this.data.localAudioStream})
        if (this.data.localAudioStream > -1) {
            // 判断是否有上一次的麦克风摄像头开启记录
            isMuteAudio = !!!this.data.localAudioStream;
        }
        if (this.data.localVideoStream > -1) {
            // 判断是否有上一次的麦克风摄像头开启记录
            isMuteVideo = !!!this.data.localVideoStream;
        } else {
            // 检查用户的摄像头默认设置
            const cameraDefaultSetting = Tool.getCameraDefaultSetting();
            // 如果用户设置了默认开启摄像头，并且不是AI分析模式，则默认打开摄像头
            if (cameraDefaultSetting && !this.data.isAIAnalyzeLive) {
                isMuteVideo = false;
            }
        }
        if (data.uid !== this.data.localMainUid) {
            //非主流，才默认打开声音
            await this.MuteLocalAudioStream({ isMute: isMuteAudio, uid: data.uid });
        }
        await this.MuteLocalVideoStream({ isMute: isMuteVideo, uid: data.uid });//为经春雨安卓端而加的，由于queryPermission相关的会导致问题，这里临时处理
    }
    /**
     * @description  主流判断加入房间后是否需要打开音视频流
     */
    judgeIfNeedMuteLocalStreamAfterJoinChannelMain(data) {
        let isMute = !!!this.data.localMainUid;
        this.MuteLocalVideoStream({ isMute: isMute, uid: data.uid });
    }
    /**
     * @description  确认当前房间需要订阅的主流
     * @param {Object} params 参数说明
     */
    checkSubscribeMain() {
        Object.keys(this.data.roomUserMap).forEach((key) => {
            if (this.data.roomUserMap[key].user_id !== this.ultrasync_uid) {
                //非本人
                if (this.data.roomUserMap[key].streamType === "main") {
                    if (!this.data.currentSubscribeMain) {
                        this.SubscribeRemoteStreamMain(this.data.roomUserMap[key].uid);
                    }
                }
            }
        });
    }
    /**
     * @description  取消订阅当前房间不存在的辅流
     * @param {Object} params 参数说明
     */
    checkStopSubscribeAux(){
        this.data.currentSubscribeAux.forEach(uid=>{ //退出已经关闭得摄像头订阅
            if(!this.data.currentVideoList.includes(uid)){
                this.StopSubscribeRemoteStreamAux(uid)
            }
        })
    }
    /**
     * @description  确认当前房间需要订阅的辅流
     * @param {Object} params 参数说明
     */
    async checkSubscribeAux(targetUid){

        this.checkStopSubscribeAux()
        const {canSubscribeAuxNum} = await this.checkAfterSubscribeAux({showTips:false})
        if(canSubscribeAuxNum === 0){
            return
        }
        let uidList = []
        if(targetUid&&this.data.roomUserMap[targetUid]){
            uidList.push(targetUid)
        }else{
            this.data.currentVideoList.forEach(uid=>{
                if(uid!==this.data.localAuxUid&&!this.data.currentSubscribeAux.includes(uid)&&uidList.length<canSubscribeAuxNum&&this.data.roomUserMap[uid]){ //非本人辅流
                    uidList.push(uid)
                }
            })
        }
        this.SubscribeRemoteStreamAuxList(uidList)
    }
    resetObjectValue(oObj) {
        let obj = cloneDeep(oObj);
        if (typeof obj === "object") {
            if (obj === null || obj === undefined) {
                obj = {};
            }
            for (let key in obj) {
                if (typeof obj[key] === "object") {
                    obj[key] = this.resetObjectValue(obj[key]);
                }
            }
        }
        return obj;
    }
    /**
     * @description  更新uid详细信息
     * @param {Object} params 参数说明
     */
    updateUidInfo(uid, oInfo = {}, force = false) {
        let info = oInfo;
        logger.log({message:'updateUidInfo',data:info})
        if (!this.data.joinedAux && !force) {
            // 退出房间后不再更新参数信息
            return;
        }
        if (info.isOnline === 0) {
            //离线直接踢出成员数组
            delete this.data.roomUserMap[uid];
        } else {
            if (!this.data.roomUserMap[uid]) {
                let streamType = "aux";
                if (this.data.currentMainUid === uid) {
                    streamType = "main";
                }

                this.data.roomUserMap[uid] = {
                    videoStream: 0,
                    videoStreamDetail: {},
                    audioStream: 0,
                    audioStreamDetail: {},
                    streamType,
                    token: "",
                    isHost: 0,
                    isOnline: 1,
                    uid: uid,
                    user_id: "", //对应云++用户id
                    auxInfo: {},
                    nickname: "",
                    introduction:""
                };
            }
            Object.keys(info).map((key) => {
                if (this.data.roomUserMap[uid].hasOwnProperty(key)) {
                    this.data.roomUserMap[uid][key] = info[key];
                }
            });

            if (info.streamType === "main") {
                if (this.data.localMainUid && info.user_id !== this.ultrasync_uid) {
                    //本地推主流时，出现主流易主
                    logger.log({message:'本地推主流时，出现主流易主'})
                    this.MuteLocalVideoStream({ uid: this.data.localMainUid, isMute: true }); //关掉本地预览
                    this.LeaveChannelMain();
                }
                this.data.currentMainUid = info.uid;
                this.data.currentMainUltrasyncId = info.user_id;
            }
        }
    }
    /**
     * @description  更新本地流状态
     * @param {Object} params 参数说明
     */
    updateAgoraUidList(list) {
        if (Array.isArray(list)) {
            list.forEach((item) => {
                if (item.status) {
                    //只写入在线用户
                    this.updateUidInfo(
                        item.agora_uid,
                        {
                            videoStream: item.video,
                            audioStream: item.audio,
                            streamType: item.stream_type === 0 ? "aux" : "main",
                            isHost: item.isHost ? 1 : 0,
                            // isOnline:item.status,
                            uid: item.agora_uid,
                            user_id: item.user_id, //对应云++用户id
                            auxInfo: item.auxInfo ? item.auxInfo : {},
                            nickname: item.nickname,
                            introduction:item.introduction
                        },
                        true
                    );
                }
            });
        }
    }
    /**
     * @description  订阅远方主流
     * @param {Object} params 参数说明
     */
    async SubscribeRemoteStreamMain(uid) {
        if (!uid || !this.data.joinedAux) {
            return;
        }
        if (uid === this.data.currentSubscribeMain) {
            //如何已经订阅了一样的，则不处理
            return;
        }
        if (uid === this.data.localMainUid) {
            //不允许订阅自己的流
            return;
        }
        const notifyRes = await Tool.createCWorkstationCommunicationMng({
            name: "SubscribeRemoteStreamMain",
            emitName: "NotifySubscribeRemoteStreamMain",
            params: {
                uid,
                nickname: this.data.roomUserMap[uid].nickname,
            },
        });
        logger.log({message:"SubscribeRemoteStreamMain",data:notifyRes})
        this.data.currentSubscribeMain = uid;
    }
    /**
     * @description  检测是否可以订阅辅流
     * @param {Object} params 参数说明
     */
    checkAfterSubscribeAux({showTips=true}={}){
        return new Promise((resolve,reject)=>{
            console.error('getCurrentShowVideoNum',this.getCurrentShowVideoNum(),this.maxVideoNum)
            if(this.getCurrentShowVideoNum()<this.maxVideoNum){
                resolve({canSubscribeAuxNum:this.maxVideoNum-this.getCurrentShowVideoNum()})
            }else{
                showTips&&Tool.openCommonDialog({
                    buttons: [lang.confirm_txt],
                    message: lang.video_path_full_tips
                });
                resolve({canSubscribeAuxNum:0})
            }
        })
    }
    /**
     * @description  订阅远方辅流
     * @param {Object} params 参数说明
     */
    async SubscribeRemoteStreamAux(uid) {
        // 并发保护：若已在订阅流程中，直接返回
        if (this.pendingSubscribeAux.has(uid)) {
            return;
        }

        // 标记开始
        this.pendingSubscribeAux.add(uid);

        const {canSubscribeAuxNum} = await this.checkAfterSubscribeAux()
        if(canSubscribeAuxNum===0){ //当前没有多余空位支持订阅
            this.pendingSubscribeAux.delete(uid);
            return
        }
        if (!uid || !this.data.joinedAux) {
            this.pendingSubscribeAux.delete(uid);
            return;
        }

        if (this.data.currentSubscribeAux.includes(uid)) {
            //如果已经订阅，则移除标记后返回
            this.pendingSubscribeAux.delete(uid);
            return;
        }
        if (uid === this.data.localAuxUid) {
            this.pendingSubscribeAux.delete(uid);
            //不允许订阅自己的辅流
            return;
        }
        let uids = [];
        uids.push(this.data.roomUserMap[uid]);
        this.data.currentSubscribeAux.forEach((uid) => {
            uids.push({ ...this.data.roomUserMap[uid] });
        });
        if(!this.data.currentSubscribeAux.includes(uid)){
            this.data.currentSubscribeAux.push(uid);
        }
        try {
            const notifyRes = await Tool.createCWorkstationCommunicationMng({
                name: "SubscribeRemoteStreamAux",
                emitName: "NotifySubscribeRemoteStreamAux",
                params: {
                    uid,
                    uids,
                },
            });
            if(notifyRes.error_code!==0){
                logger.log({message:'SubscribeRemoteStreamAux error',data:notifyRes})
                this.data.currentSubscribeAux = this.data.currentSubscribeAux.filter((item) => item !== uid);
            }
        } catch (error) {
            logger.error({message:error})
            this.data.currentSubscribeAux = this.data.currentSubscribeAux.filter((item) => item !== uid);
        } finally {
            // 移除并发标记
            this.pendingSubscribeAux.delete(uid);
        }
    }
    /**
     * @description  同时订阅多路远方辅流
     * @param {Object} params 参数说明
     */
    async SubscribeRemoteStreamAuxList(uids=[]){
        const {canSubscribeAuxNum} = await this.checkAfterSubscribeAux()
        if(canSubscribeAuxNum===0){ //当前没有多余空位支持订阅
            return
        }
        if (!this.data.joinedAux||uids.length===0) {
            return;
        }
        let uidList = []

        uidList = uids.filter((uid)=>{
            return !this.data.currentSubscribeAux.includes(uid) && uid !== this.data.localAuxUid && !this.pendingSubscribeAux.has(uid)
        })
        uidList = uidList.slice(0, canSubscribeAuxNum) //限制订阅数量，避免超过上限
        if(uidList.length === 0){
            return
        }
        // 预先将待订阅 uid 放入 pending 集合，防止并发重复
        uidList.forEach(id => this.pendingSubscribeAux.add(id));

        this.data.currentSubscribeAux = this.data.currentSubscribeAux.concat(uidList);
        let uidListInfo = []
        this.data.currentSubscribeAux.forEach(uid=>{
            if(this.data.roomUserMap[uid]){
                uidListInfo.push(this.data.roomUserMap[uid])
            }
        })
        if(uidListInfo.length === 0){
            return
        }
        const notifyRes = await Tool.createCWorkstationCommunicationMng({
            name: "SubscribeRemoteStreamAux",
            emitName: "NotifySubscribeRemoteStreamAux",
            params: {
                uid:uidList[uidList.length-1],
                uids:uidListInfo,
            },
        });
        if(notifyRes.error_code!==0){
            this.data.currentSubscribeAux = this.data.currentSubscribeAux.filter((uid) => !uidList.includes(uid));
        }

        // 无论成功失败，移除 pending 标记
        uidList.forEach(id => this.pendingSubscribeAux.delete(id));
    }
    /**
     * @description  pc端用户点击APP 发起开/关主流
     * @param {Object} params 参数说明
     */
    NotifyApplyPermissionOfMainStream(json) {
        logger.log({message:"NotifyApplyPermissionOfMainStream",data:json})
        if (json.error_code === 0) {
            window.vm.$root.eventBus.$emit(
                "startJoinRoom",
                {
                    main: 1,
                    aux: 0,
                    isSender: 0,
                    videoSource: json.data.videoSource,
                    cid:this.cid
                },
                (is_suc, data) => {
                    let message = "";
                    if (!is_suc && typeof data === "string") {
                        message = data;
                    }
                    window.CWorkstationCommunicationMng.CallApplyPermissionResult({
                        action: "mainStream",
                        result: is_suc ? 1 : 0,
                        message,
                    });
                }
            );
        }
    }
    /**
     * @description  pc端用户点击APP 发起开/关摄像头
     * @param {Object} params 参数说明
     */
    NotifyApplyPermissionOfMuteVideo(json) {
        logger.log({message:"NotifyApplyPermissionOfMuteVideo",data:json})
        if (json.error_code === 0) {
            this.MuteLocalVideoStream({
                uid: this.data.localAuxUid,
                isMute: json.data.mute===1?true:false,
            });
        }
    }
    /**
     * @description  pc端用户点击APP 发起开/关麦克风
     * @param {Object} params 参数说明
     */
    NotifyApplyPermissionOfMuteAudio(json) {
        logger.log({message:"NotifyApplyPermissionOfMuteAudio",data:json})
        if (json.error_code === 0) {
            this.MuteLocalAudioStream({
                uid: this.data.localAuxUid,
                isMute: false,
            });
        }
    }
    /**
     * @description  本地音频设备状态变化
     * @param {Object} params 参数说明
     */
    NotifyAudioDeviceChanged(json) {
        logger.log({message:"NotifyAudioDeviceChanged",data:json})
        if (json.error_code === 0) {
            let normalState = [
                MEDIA_DEVICE_STATE_TYPE.MEDIA_DEVICE_STATE_IDLE,
                MEDIA_DEVICE_STATE_TYPE.MEDIA_DEVICE_STATE_ACTIVE,
            ];
            if (!normalState.includes(json.data.deviceState)) {
                Toast(
                    `${lang.AGORA_MEDIA_DEVICE_TYPE[json.data.deviceType]}${
                        lang.AGORA_MEDIA_DEVICE_STATE_TYPE[json.data.deviceState]
                    }`
                );
            }
        }
    }
    /**
     * @description  停止订阅远端流
     * @param {Object} params 参数说明
     */
    StopSubscribeRemoteStreamAux(uid) {
        if (!uid) {
            this.data.currentSubscribeAux.forEach((uid) => {
                window.CWorkstationCommunicationMng.StopSubscribeRemoteStreamAux({
                    uid,
                });
            });
            this.data.currentSubscribeAux = [];
        } else {
            if (!this.data.currentSubscribeAux.includes(uid)) {
                return;
            }
            window.CWorkstationCommunicationMng.StopSubscribeRemoteStreamAux({
                uid,
            });
            this.data.currentSubscribeAux = this.data.currentSubscribeAux.filter((item) => item !== uid);
        }
    }
    /**
     * @description  通知停止订阅远端流
     * @param {Object} params 参数说明
     */
    NotifyStopSubscribeRemoteStreamAux(json) {
        if (json.error_code === 0) {
            if (json.data.uid) {
                this.data.currentSubscribeAux = this.data.currentSubscribeAux.filter(
                    (item) => item !== json.data.uid
                );
            }
        }
    }
    /**
     * @description  停止订阅远端主流
     * @param {Object} params 参数说明
     */
    StopSubscribeRemoteStreamMain() {
        if (!this.data.currentSubscribeMain) {
            return;
        }
        window.CWorkstationCommunicationMng.StopSubscribeRemoteStreamMain({
            uid: this.data.currentSubscribeMain,
        });
        this.data.currentSubscribeMain = 0;
        this.data.currentMainUid = 0;
        this.data.currentMainUltrasyncId = 0;
    }
    /**
     * @description  通知停止订阅远端主流
     * @param {Object} params 参数说明
     */
    NotifyStopSubscribeRemoteStreamMain(json) {
        if (json.error_code === 0) {
            this.data.currentSubscribeMain = 0;
            this.data.currentMainUid = 0;
            this.data.currentMainUltrasyncId = 0;
        }
    }

    /**
     * @description  RTC状态变化
     * @param {Object} params 参数说明
     */
    NotifyRtcStats(json) {}
    /**
     * @description  获取当前所有开麦的人数
     * @param {Object} params 参数说明
     */
    getCurrentAudioStreamCount() {
        let count = 0;
        Object.values(this.data.roomUserMap).forEach((value) => {
            if (value.audioStream) {
                count++;
            }
        });
        return count;
    }
    /**
     * @description  获取当前所有开摄像头的人数
     * @param {Object} params 参数说明
     */
    getCurrentVideoStreamCount() {
        let count = 0;
        Object.values(this.data.roomUserMap).forEach((value) => {
            if (value.videoStream) {
                count++;
            }
        });
        return count;
    }
    /**
     * @description  用户点击PC端播放器上的按钮响应
     * @param {Object} params 参数说明
     */
    NotifyClickNativePlayerButton(params) {
        let action = params.action;
        switch (action) {
        case "leaveConference":
            break;
        case "closeConference":
            window.vm.$store.commit("liveConference/updateConferenceState", {
                cid: this.cid,
                obj: {
                    conferenceState: 0,
                },
            });
            this.ServiceDestroyChannel(); //强制结束在场直播并通知所有人
            break;
        default:
            break;
        }
    }
    /**
     * @description  用户点击移动端播放器上的按钮响应
     * @param {Object} params 参数说明
     */
    NotifyClickNativeClosePlayerButton(params) {
        this.event.emit('clickLeaveChannel')
    }
    /**
     * @description  判断自己是否为最后一个用户 是的话通知服务器强制结束房间
     * @param {Object} params 参数说明
     */
    ifLastChildLeaveRoom() {
        let members = [];
        Object.values(this.data.roomUserMap).forEach((value) => {
            if (!members.includes(value.user_id)) {
                members.push(value.user_id);
            }
        });
        logger.log({message:"ifLastChildLeaveRoom",data:members})
        if (members.length === 1) {
            if (members[0] === this.ultrasync_uid) {
                //只剩下自己本人
                return true;
            } else {
                return false;
            }
        } else if (members.length === 0) {
            //一个人都没了
            return true;
        } else {
            return false;
        }
    }
    /**
     * @description  打开白板
     * @param {Boolean} force 是否强制走开启白板流程
     *
     *
     */
    async OpenWhiteBoardFromMainWeb(force = false) {
        if (!force && this.data.is_open_white_board) {
            return;
        }
        const data = await this.ServiceGetWhiteBoardToken();
        let params = {
            appIdentifier: data.appId,
            region: data.region,
            uuid: data.uuid,
            uid: data.uid,
            roomToken: data.token,
            groupId: this.cid,
            loginToken: window.localStorage.getItem("loginToken"),
            isShowToolBar: force,
        };
        this.data.whiteBoardInfo = params;
        window.CWorkstationCommunicationMng.OpenWhiteBoard(params);
    }
    /**
     * @description  关闭白板
     * @param {Object} params 参数说明
     */
    async CloseWhiteBoardFromMainWeb() {
        window.CWorkstationCommunicationMng.CloseWhiteBoardFromMainWeb(this.data.whiteBoardInfo);
    }
    /**
     * @description  用户在原生入口点击开启/关闭白板
     * @param {Object} params 参数说明
     */
    async NotifyApplyOpenWhiteBoard(json) {
        logger.log({message:"NotifyApplyOpenWhiteBoard",data:json})
        if (json.error_code === 0) {
            this.OpenWhiteBoardFromMainWeb(true);
        }
    }
    /**
     * @description  通知开启白板结果
     * @param {Object} params 参数说明
     */
    NotifyOpenWhiteBoard(params) {
        logger.log({message:"NotifyOpenWhiteBoard",data:params})
        if (!params.error_code) {
            this.data.is_open_white_board = true;
        }
    }
    /**
     * @description  通知关闭白板结果
     * @param {Object} params 参数说明
     */
    NotifyCloseWhiteBoard(params) {
        logger.log({message:"NotifyCloseWhiteBoard",data:params})
        this.data.is_open_white_board = false;
    }
    /**
     * @description  通知Token过期
     * @param {Object} params 参数说明
     */
    NotifyTokenExpiredMain(params) {
        this.LeaveChannelMain();
    }
    /**
     * @description  通知Token过期
     * @param {Object} params 参数说明
     */
    NotifyTokenExpiredAux(params) {
        Toast(lang.live_token_has_expired);
        this.LeaveChannelAux("nothing", 3, LEAVE_CHANNEL_REASON["OTHERS"]);
    }
    /**
     * @description  通知Token即将过期
     * @param {Object} params 参数说明
     */
    async NotifyTokenPrivilegeWillExpireMain(params) {
        const res = await this.ServiceGetConferenceRefreshToken({
            channelId: this.data.channelId,
            uid: this.data.localMainUid,
        });
        logger.log({message:"NotifyTokenPrivilegeWillExpireMain",data:res})
        this.RenewTokenMain(res.data.token);
    }
    /**
     * @description  通知Token即将过期
     * @param {Object} params 参数说明
     */
    async NotifyTokenPrivilegeWillExpireAux(params) {
        const res = await this.ServiceGetConferenceRefreshToken({
            channelId: this.data.channelId,
            uid: this.data.localAuxUid,
        });
        logger.log({message:"NotifyTokenPrivilegeWillExpireAux",data:res})
        this.RenewTokenAux(res.data.token);
    }

    /**
     * @description  更新主流token
     * @param {Object} params 参数说明
     */
    async RenewTokenMain(newToken) {
        let params = {
            newToken,
            channelId: this.data.channelId,
            uid: this.data.localMainUid,
        };
        window.CWorkstationCommunicationMng.RenewTokenMain(params);
    }
    /**
     * @description  更新辅流token
     * @param {Object} params 参数说明
     */
    async RenewTokenAux(newToken) {
        let params = {
            newToken,
            channelId: this.data.channelId,
            uid: this.data.localMainUid,
        };
        window.CWorkstationCommunicationMng.RenewTokenAux(params);
    }
    /**
     * @description  通知开启云录制
     * @param {Object} params 参数说明
     */
    NotifyCallApplyTurnOnCloudRecording(params) {
        let uids = []
        if(params?.data?.uids?.length > 0){
            uids = params.data.uids
        }
        this.ServiceStartConferenceRecording(uids);
    }
    /**
     * @description  通知关闭云录制
     * @param {Object} params 参数说明
     */
    NotifyCallApplyTurnOffCloudRecording() {
        this.ServiceStopConferenceRecording();
    }
    /**
     * @description  原生通知打开/关闭实时分析
     * @param {Object} params 参数说明
     */
    async NotifyApplySwitchAIAnalyzeNoDebounce(data){
        logger.log({message:'NotifyApplySwitchAIAnalyze'})
        if(data.hasOwnProperty('switch')&&data.switch){
            this.startAIAnalyze(data.aiInfo)
        }else{
            logger.log({message:'stop ai analysis'})
            window.vm.$root.eventBus.$emit("sendImageWithAiReport", this.data.AIAnalyzeTaskId);
            this.stopAIAnalyze()
        }
    }
    /**
     * @description  打开实时分析
     * @param {Object} params 参数说明
     */
    async startAIAnalyze(aiInfo){
        let params = {
            ...aiInfo,
            isUpload: true,
            groupId:this.cid
        }
        try {
            const {data} = await this.ServiceStartAIAnalyzeByLive(params)
            logger.log({message:'startAIAnalyze',data:data})
            let task_id = data.data.task_id
            if(task_id){
                window.CWorkstationCommunicationMng.CallApplySwitchAIAnalyzeResult({
                    error_code:0,
                    data:{
                        switch:true
                    },
                    error_message:data.error_message||'',
                    key: data.key||''
                });
                this.data.isAIAnalyzing = !data.error_code
                this.data.AIAnalyzeTaskId = task_id
            }else{
                window.CWorkstationCommunicationMng.CallApplySwitchAIAnalyzeResult({
                    error_code:-1,
                    data:{
                        switch:true
                    },
                    error_message:'no task id',
                    key:''
                });
            }

        } catch (error) {
            window.CWorkstationCommunicationMng.CallApplySwitchAIAnalyzeResult({
                error_code:-1,
                data:{
                    switch:true
                },
                error_message:error
            });
        }
    }
    /**
     * @description  关闭实时分析
     * @param {Object} params 参数说明
     */
    async stopAIAnalyze(isSendService = true){
        let params = {
            groupId:this.cid,
            task_id:this.data.AIAnalyzeTaskId
        }
        if(!this.data.AIAnalyzeTaskId||!this.data.isAIAnalyzing||!isSendService){
            window.CWorkstationCommunicationMng.CallApplySwitchAIAnalyzeResult({
                error_code:0,
                data:{
                    switch:false
                },
                error_message:''
            });
            this.data.isAIAnalyzing = false
            this.data.AIAnalyzeTaskId = false;
            return
        }
        try {
            const {data} = await this.ServiceStopAIAnalyzeByLive(params)
            logger.log({message:'stopAIAnalyze',data:data})
            window.CWorkstationCommunicationMng.CallApplySwitchAIAnalyzeResult({
                error_code:0,
                data:{
                    switch:false
                },
                error_message:''
            });
            this.data.isAIAnalyzing = false
            this.data.AIAnalyzeTaskId = false;
        } catch (error) {
            window.CWorkstationCommunicationMng.CallApplySwitchAIAnalyzeResult({
                error_code:-1,
                data:{
                    switch:false
                },
                error_message:error
            });
        }
    }
    /**
     * @description  主讲人禁止发言
     * @param {Object} params 参数说明
     */
    async forbiddenSpeak(data) {
        return new Promise((resolve, reject) => {
            window.main_screen.conversation_list[this.cid].forbiddenSpeak(data, (res) => {
                if (res.error_code === 0 && res.data) {
                    resolve(res);
                } else {
                    console.error(res);
                    reject(res);
                }
            });
        });
    }
    /**
     * @description  通知声网房间状态变化
     * @param {Object} params 参数说明
     */
    NotifyConnectionStateChanged(params) {
        logger.log({message:"NotifyConnectionStateChanged",data:params.data})
        //当前网络连接状态
        const state = {
            CONNECTION_STATE_DISCONNECTED: 1,
            CONNECTION_STATE_CONNECTING: 2,
            CONNECTION_STATE_CONNECTED: 3,
            CONNECTION_STATE_RECONNECTING: 4,
            CONNECTION_STATE_FAILED: 5,
        };
        const reason = {
            CONNECTION_CHANGED_CONNECTING: 0,
            CONNECTION_CHANGED_JOIN_SUCCESS: 1,
            CONNECTION_CHANGED_INTERRUPTED: 2,
            CONNECTION_CHANGED_BANNED_BY_SERVER: 3,
            CONNECTION_CHANGED_JOIN_FAILED: 4,
            CONNECTION_CHANGED_LEAVE_CHANNEL: 5,
            CONNECTION_CHANGED_INVALID_APP_ID: 6,
            CONNECTION_CHANGED_INVALID_CHANNEL_NAME: 7,
            CONNECTION_CHANGED_INVALID_TOKEN: 8,
            CONNECTION_CHANGED_TOKEN_EXPIRED: 9,
            CONNECTION_CHANGED_REJECTED_BY_SERVER: 10,
            CONNECTION_CHANGED_SETTING_PROXY_SERVER: 11,
            CONNECTION_CHANGED_RENEW_TOKEN: 12,
            CONNECTION_CHANGED_CLIENT_IP_ADDRESS_CHANGED: 13,
            CONNECTION_CHANGED_KEEP_ALIVE_TIMEOUT: 14,
            CONNECTION_CHANGED_REJOIN_SUCCESS: 15,
            CONNECTION_CHANGED_LOST: 16,
            CONNECTION_CHANGED_ECHO_TEST: 17,
            CONNECTION_ERROR: 21,
        };

        if (params.data.reason === reason["CONNECTION_CHANGED_BANNED_BY_SERVER"]) {
            if(this.data.joinedAux){
                this.LeaveChannelAux("nothing", 2);
                Toast(lang.server_ended_live);
            }
        } else if (params.data.reason === reason["CONNECTION_CHANGED_REJECTED_BY_SERVER"]) {
            logger.log({message:"CONNECTION_CHANGED_REJECTED_BY_SERVER"})
            // this.LeaveChannelAux()
            // Toast('该会议已被服务器强制结束')
        }
    }
    /**
     * @description  通知声网房间状态变化
     * @param {Object} params 参数说明
     */
    checkShowLeaveConferenceBtn() {
        let isShowLeaveConferenceBtn = this.is_single_chat ? 0 : 1; //除了单聊都有离开直播按钮
        let isShowCloseConferenceBtn = this.data.isHost || this.is_single_chat || this.getIsManager() ? 1 : 0;
        logger.log({message:"checkShowLeaveConferenceBtn",data:{isShowLeaveConferenceBtn,isShowCloseConferenceBtn}})
        return {
            isShowLeaveConferenceBtn,
            isShowCloseConferenceBtn,
        };
    }
    /**
     * @description  检测云录制权限
     * @param {Object} params 参数说明
     */
    checkCloudRecordAuth() {
        let condition1 = this.data.isHost;
        let condition2 = this.getIsManager();
        let condition3 = this.is_single_chat;
        return condition1 || condition2 || condition3 ? 1 : 0; //单聊，群主，主讲人时 都可以操作云录制
    }
    checkManagerAuth(){//全体静音权限
        let condition1 = this.data.isHost;
        let condition2 = this.getIsManager();
        const auth =  (condition1 || condition2)?1:0
        return auth
    }
    /**
     * @description  检测是否有对齐摄像头按钮权限
     * @param {Object} params 参数说明
     */
    checkLockAuxAuth() {
        let condition1 = this.data.isHost;
        let condition2 = this.getIsManager();
        let condition3 = this.data.localMainUid;
        const auth = condition1 || condition2 || condition3 ? 1 : 0; //群主，主讲人时 主流都可以操作对齐摄像头
        window.CWorkstationCommunicationMng.CallConferenceWebStatus({
            lock_aux_auth: auth,
        });
        return auth;
    }
    createCReverseControl() {
        // 创建
        if (!this.CReverseControl) {
            this.CReverseControl = new CReverseControl({
                uid: this.ultrasync_uid,
                cid: this.cid,
                channelId: this.data.channelId,
            });
        } else {
            this.CReverseControl.destroy();
            this.CReverseControl = new CReverseControl({
                uid: this.ultrasync_uid,
                cid: this.cid,
                channelId: this.data.channelId,
            });
        }
        window.CReverseControl[this.cid] = this.CReverseControl;
        this.CReverseControl.event.on("dataChange", (data) => {
            logger.log({message:"dataChange",data:data})
            if (data.hasOwnProperty("isConnectedRemoteDopplerSocket")) {
                this.data.isConnectedRemoteDopplerSocket = data["isConnectedRemoteDopplerSocket"];
            }
        });
        this.CReverseControl.event.on("checkIsLocalMainStream", (callback) => {
            if (this.data.localMainUid) {
                callback(true);
            } else {
                callback(false);
            }
        });
    }
    checkChannelInfoLoaded(){
        return new Promise((resolve,reject)=>{
            if(this.data.channelUUId){
                resolve(true)
            }else{
                let interval = setInterval(()=>{
                    if(this.data.channelUUId){
                        resolve(true)
                        clearInterval(interval)
                        interval = null
                    }
                },200)
            }

        })
    }
    requestRemoteControl() {
        this.CReverseControl.requestRemoteControlEvent();
    }
    getAttendUserInfo(uid) {
        const attendeeList = window.main_screen.conversation_list[this.cid].attendeeList;
        if (attendeeList[`attendee_${uid}`]) {
            return attendeeList[`attendee_${uid}`];
        } else {
            return null;
        }
    }
    addIdleListener() {
        // Those are the default values
        this.idle = new IdleJs({
            idle: 1000 * 60 * 30, // idle time in ms
            events: ["mousemove", "keydown", "mousedown", "touchstart"], // events that will trigger the idle resetter
            onIdle: () => {
                logger.log({message:"onIdle"})
            }, // callback function to be executed after idle time
            onActive: () => {
                logger.log({message:"onActive"})
            }, // callback function to be executed after back form idleness
            onHide: () => {
                logger.log({message:"onHide"})
            }, // callback function to be executed when window become hidden
            onShow: () => {
                logger.log({message:"onShow"})
            }, // callback function to be executed when window become visible
            keepTracking: true, // set it to false if you want to be notified only on the first idleness change
            startAtIdle: false, // set it to true if you want to start in the idle state
        });
    }
    getIsManager() {
        let attendeeList = window.main_screen.conversation_list[this.cid]?window.main_screen.conversation_list[this.cid].attendeeList:[];
        let isManager = false;
        let isAdmin = false;
        for (let key in attendeeList) {
            let item = attendeeList[key];
            if (item.role == storeState.systemConfig.groupRole.manager && item.userid == storeState.user.uid) {
                isManager = true;
                continue;
            } else if (item.role == storeState.systemConfig.groupRole.creator && item.userid == storeState.user.uid) {
                isAdmin = true;
                continue;
            }
        }

        return isManager || isAdmin;
    }
    /**
     * @description  js-->PC 关闭直播时前端的窗口
     * @param {Object} params 参数说明
     */
    closeConferenceWebWindow (){
        this.data.currentShowWindowAction = ''
        window.CWorkstationCommunicationMng.CloseConferenceWebWindow()
    }
    /**
     * @description  PC-->js 用户点击原生界面的聊天/成员列表
     * @param {Object} params 参数说明
     */
    NotifyApplyOpenConferenceWebWindow(params){
        logger.log({message:'NotifyApplyOpenConferenceWebWindow',data:params})
        if(this.data.currentShowWindowAction === params.data.action){
            this.closeConferenceWebWindow()
        }else{
            this.data.currentShowWindowAction = params.data.action
            if(params.data.action === 'chat'){
                this.clearUnReadMsgCount()
            }
            setTimeout(()=>{
                var dpr = window.devicePixelRatio;
                window.CWorkstationCommunicationMng.OpenConferenceWebWindow({width:500* dpr})
            })
        }

    }
    /**
     * @description 更新未读消息数量
     * @param {Object} params 参数说明
     */
    updateUnReadMsgCount(){
        if(this.data.currentShowWindowAction!=='chat'){
            this.data.unReadMsgCount = this.data.unReadMsgCount + 1
            window.CWorkstationCommunicationMng.CallConferenceWebStatus({
                unReadMsgCount:this.data.unReadMsgCount
            });
        }

    }
    /**
     * @description 清空未读消息数量
     * @param {Object} params 参数说明
     */
    clearUnReadMsgCount(){
        this.data.unReadMsgCount = 0
        window.CWorkstationCommunicationMng.CallConferenceWebStatus({
            unReadMsgCount:this.data.unReadMsgCount
        });
    }
    /**
     * @description  js-->service 发起者退出房间，通知服务器并通知所有人
     * @param {Object} params 参数说明
     */
    ServiceDestroyChannel({ channelId, channelUUId } = {}) {
        return new Promise((resolve, reject) => {
            logger.log({message:"ServiceDestroyChannel",data:{channelId,channelUUId}})
            window.main_screen.conversation_list[this.cid].destroyChannel(
                {
                    channelId: channelId || this.data.channelId,
                    uuid: channelUUId || this.data.channelUUId,
                },
                (res) => {
                    if (res.error_code === 0) {
                        resolve(res.data);
                    } else {
                        // Toast(lang.cannot_operate_last_live)
                        logger.error({message:res})
                        reject(res);
                    }
                }
            );
        });
    }

    /**
     * @description  js-->service 声网成员列表状态数据，确定当前主流uid
     * @param {Object} params 参数说明
     */
    ServiceGetAgoraUidList() {
        return new Promise((resolve, reject) => {
            window.main_screen.conversation_list[this.cid].getAgoraUidList({}, (res) => {
                if (res.error_code === 0) {
                    // console.error("ServiceGetAgoraUidList", res.data);
                    // this.updateAgoraUidList(res.data)
                    resolve(res.data);
                } else {
                    reject(res);
                }
            });
        });
    }
    /**
     * @description  上报本地音视频状态
     * @param {Object} params 参数说明
     */
    async ServiceReportLocalStreamStatus(type, status) {
        logger.log({message:"ServiceReportLocalStreamStatus",data:{type,status}})
        return new Promise((resolve, reject) => {
            window.main_screen.conversation_list[this.cid].reportLocalStreamStatus(
                {
                    type,
                    status,
                    channelId: this.data.channelId,
                    uid: this.data.localAuxUid,
                },
                (res) => {
                    if (res.error_code === 0) {
                        logger.log({message:"ServiceReportLocalStreamStatus",data:{type,status,res}})
                        resolve(res.data);
                    } else {
                        reject(res);
                    }
                }
            );
        });
    }
    /**
     * @description  获取用户在声网内信息
     * @param {Object} params 参数说明
     */
    async ServiceGetUserinfo(uid, { isOnline = 0 }) {
        return new Promise((resolve, reject) => {
            window.main_screen.conversation_list[this.cid].getUserinfo(
                {
                    uid,
                    channelId: this.data.channelId,
                },
                (res) => {
                    if (res.error_code === 0 && res.data) {
                        let userInfo = res.data;
                        if (isOnline) {
                            userInfo.status = 1;
                        }
                        logger.log({message:"userInfo",data:userInfo})
                        this.updateAgoraUidList([userInfo]);
                        resolve(userInfo);
                    } else {
                        reject(res);
                    }
                }
            );
        });
    }
    /**
     * @description  向服务器获取当前群是否有直播
     * @param {Object} params 参数说明
     */
    async ServiceGetConferenceIfLiving() {
        return new Promise((resolve, reject) => {
            if (!window.main_screen.conversation_list[this.cid]) {
                return;
            }
            window.main_screen.conversation_list[this.cid].getIfLiving({}, (res) => {
                if (res.error_code === 0 && res.data) {
                    window.vm.$store.commit("liveConference/updateConferenceState", {
                        cid: this.cid,
                        obj: {
                            conferenceState: res.data.living ? 1 : 0,
                            senderUserId: res.data.sender_id,
                        },
                    });
                    if (res.data.living) {
                        resolve(true);
                    } else {
                        resolve(false);
                    }
                } else {
                    logger.log({message:"ServiceGetConferenceIfLiving",data:res})
                    reject(false);
                }
            });
        });
    }
    /**
     * @description  获取白板房间Token
     * @param {Object} params 参数说明
     */
    async ServiceGetWhiteBoardToken() {
        return new Promise((resolve, reject) => {
            if (!window.main_screen.conversation_list[this.cid]) {
                return;
            }
            window.main_screen.conversation_list[this.cid].getWhiteBoardToken(
                {
                    groupId: this.cid,
                },
                (res) => {
                    if (res.error_code === 0 && res.data) {
                        resolve(res.data);
                    } else {
                        logger.log({message:"ServiceGetWhiteBoardToken",data:res})
                        reject(false);
                    }
                }
            );
        });
    }
    /**
     * @description  获取新的token
     * @param {Object} params 参数说明
     */
    async ServiceGetConferenceRefreshToken(data) {
        return new Promise((resolve, reject) => {
            window.main_screen.conversation_list[this.cid].getConferenceRefreshToken(data, (res) => {
                if (res.error_code === 0 && res.data) {
                    resolve(res);
                } else {
                    reject(res);
                }
            });
        });
    }
    /**
     * @description  获取当前房间的状态
     * @param {Object} params 参数说明
     */
    async ServiceGetChannelCurrentStatus(data) {
        return new Promise((resolve, reject) => {
            window.main_screen.conversation_list[this.cid].getChannelCurrentStatus(data, (res) => {
                if (res.error_code === 0 && res.data) {
                    resolve(res.data);
                } else {
                    reject(res);
                }
            });
        });
    }
    /**
     * @description  上报当前主流状态
     * @param {Object} params 参数说明
     */
    async ServiceReportCurrentMainStreamType(type = "desktop", isMute = true) {
        return new Promise(async (resolve, reject) => {
            await this.checkChannelInfoLoaded()
            logger.log({message:'ServiceReportCurrentMainStreamType',data:this.data.channelUUId})
            window.main_screen.conversation_list[this.cid].reportCurrentMainStreamType(
                {
                    uuid: this.data.channelUUId,
                    streamType: type,
                    status: isMute ? 0 : 1,
                },
                (res) => {
                    if (res.error_code === 0 && res.data) {
                        resolve(res.data);
                    } else {
                        reject(res);
                    }
                }
            );
        });
    }
    /**
     * @description  开启云录制
     * @param {Object} params 参数说明
     */
    async ServiceStartConferenceRecording(uids=[]) {
        return new Promise((resolve, reject) => {
            let params = {
                channelId: this.data.channelId,
            }
            if(uids.length > 0){
                params.agora_uids = uids
            }
            window.main_screen.conversation_list[this.cid].startConferenceRecording(
                params,
                (res) => {
                    logger.log({message:"startRecord",data:res})
                    if (res.error_code === 0) {
                        // this.data.isRecording = true
                        // window.CWorkstationCommunicationMng.CallRecordingStatus({status:1,auth:this.data.isHost})
                        resolve(res);
                    } else {
                        reject(res);
                    }
                }
            );
        });
    }
    /**
     * @description  停止云录制
     * @param {Object} params 参数说明
     */
    async ServiceStopConferenceRecording() {
        return new Promise((resolve, reject) => {
            window.main_screen.conversation_list[this.cid].stopConferenceRecording(
                { channelId: this.data.channelId },
                (res) => {
                    logger.log({message:"stopRecord",data:res})
                    if (res.error_code === 0) {
                        // this.data.isRecording = false
                        // window.CWorkstationCommunicationMng.CallRecordingStatus({status:0})
                        resolve(res);
                    } else {
                        reject(res);
                    }
                }
            );
        });
    }
    /**
     * @description  开启实时分析
     * @param {Object} params 参数说明
     */
    async ServiceStartAIAnalyzeByLive(params) {
        return new Promise(async (resolve, reject) => {
            await service.startAIAnalyzeByLive(
                params
            ).then((res)=>{
                if (res.data.error_code === 0) {
                    resolve(res);
                } else {
                    reject(res.data.error_msg);
                }
            }).catch((error)=>{
                reject('request startAIAnalyzeByLive error')
            })
        });
    }
    /**
     * @description  关闭实时分析
     * @param {Object} params 参数说明
     */
    async ServiceStopAIAnalyzeByLive(params) {
        return new Promise(async (resolve, reject) => {
            await service.stopAIAnalyzeByLive(
                params
            ).then((res)=>{
                if (res.data.error_code === 0) {
                    resolve(res);
                } else {
                    reject(res.data.error_msg);
                }
            }).catch((error)=>{
                reject('request stopAIAnalyzeByLive error')
            })
        });
    }
    /**
     * @description  上报服务器，用户进入房间
     * @param {Object} params 参数说明
     */
    async ServiceReportJoinChannel({uid,status}) {
        return new Promise((resolve, reject) => {
            if(!uid){
                resolve('uid=0');
                return
            }
            window.main_screen.conversation_list[this.cid].reportUserJoinChannel(
                {
                    channelId: this.data.channelId,
                    clientSeq:new Date().getTime(),
                    uid,
                    status
                },
                (res) => {
                    logger.log({message:"reportUserJoinChannel",data:res})
                    if (res.error_code === 0) {
                        resolve(res);
                    } else {
                        reject(res);
                    }
                }
            );
        });
    }
    /**
     * @description  上报服务器，用户离开房间
     * @param {Object} params 参数说明
     */
    async ServiceReportLeaveChannel({uid,status}) {
        logger.log({message:"ServiceReportLeaveChannel",data:{uid,status}})
        return new Promise((resolve, reject) => {
            if(!uid){
                resolve('uid=0');
                return
            }
            window.main_screen.conversation_list[this.cid].reportUserLeaveChannel(
                {
                    channelId: this.data.channelId,
                    clientSeq:new Date().getTime(),
                    uid,
                    status
                },
                (res) => {
                    logger.log({message:"reportUserLeaveChannel",data:res})
                    if (res.error_code === 0) {
                        resolve(res);
                    } else {
                        reject(res);
                    }
                }
            );
        });
    }
    /**
     * @description  用于上报状态，让服务器依然知道用户在房间
     * @param {Object} params 参数说明
     */
    async ServicePingChannel(uid) {
        return new Promise((resolve, reject) => {
            if(!uid){
                resolve('uid=0');
                return
            }
            window.main_screen.conversation_list[this.cid].pingChannel(
                {
                    channelId: this.data.channelId,
                    clientSeq:new Date().getTime(),
                    uid,
                },
                (res) => {
                    logger.log({message:"pingChannel",data:res})
                    if (res.error_code === 0) {
                        resolve(res);
                    } else {
                        reject(res);
                    }
                }
            );
        });
    }
    /**
     * @description  用于考试信息
     * @param {Object} params 参数说明
     */
    async ServiceReportTopicInfo() {
        if(!this.data.trainingInfo){
            return
        }
        console.error('ServiceReportTopicInfo',this.data.trainingInfo,this.data.channelId)
        return new Promise((resolve, reject) => {
            window.main_screen.conversation_list[this.cid].reportConferenceExamLive(
                {
                    topicInfo: this.data.trainingInfo,
                    channelId: this.data.channelId,
                    uuid: this.data.channelUUId,
                },
                (res) => {
                    logger.log({message:"reportConferenceExamLive",data:res})
                    if (res.error_code === 0) {
                        resolve(res);
                    } else {
                        reject(res);
                    }
                }
            );
        });
    }

}
//

CLiveRoom.instances = {};
export default CLiveRoom;

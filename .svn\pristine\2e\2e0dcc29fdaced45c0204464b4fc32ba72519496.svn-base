<template>
  <div class="hrf_data_view_page">
    <mr-query-form>
      <mr-query-form-top-panel>
        <div class="search_bar">
          <el-form ref="form" :inline="true" label-width="70px" :model="queryForm"
            @submit.native.prevent>
            <el-form-item :label="lang.submission_time">
              <el-date-picker
                v-model="queryForm.uploadDateRange"
                type="daterange"
                unlink-panels
                :range-separator="lang.date_to"
                :start-placeholder="lang.start_date"
                :end-placeholder="lang.end_date"
                :picker-options="pickerOptions">
              </el-date-picker>
            </el-form-item>
            <el-form-item :label="lang.case_status" prop="case_status">
                <el-select v-model="queryForm.caseStatus">
                    <el-option
                    v-for="caseItem of caseSelectList"
                    :label="caseItem.value"
                    :value="caseItem.id"
                    :key="caseItem.id"
                    />
                </el-select>
            </el-form-item>
            <el-form-item>
              <el-button
                icon="el-icon-search"
                native-type="submit"
                type="primary"
                @click="handleQuery"
              >
                {{lang.query_btn}}
              </el-button>
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                @click="openExport"
              >
                {{lang.export_case}}
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </mr-query-form-top-panel>
    </mr-query-form>
    <export-dialog
      :examList="examList"
      :queryForm="queryForm"
      :examListTotal="examListTotal"
      :handleCurrentChange="handleCurrentChange"
      :handleSizeChange="handleSizeChange"
      :loadingData="loading"
      ref="exportDialog"></export-dialog>
    <div class="pagelist">
        <table v-loading="loading">
            <thead>
                <tr>
                    <th>{{lang.index_num}}</th>
                    <th>{{lang.case_num}}</th>
                    <th>{{lang.submission_time}}</th>
                    <th>{{lang.group_by}}</th>
                    <th>{{lang.patient_name}}</th>
                    <th>{{lang.patient_age}}</th>
                    <th>{{lang.patient_sex}}</th>
                    <th>{{lang.numberOfImages}}</th>
                    <th>{{lang.hfr.assignment_A}}</th>
                    <th>{{lang.hfr.assignment_B}}</th>
                    <th>{{lang.case_status}}</th>
                    <th>{{lang.operation}}</th>
                </tr>
            </thead>
            <tbody>
                <tr v-for="(item,index) of examList" :key="index">
                    <td>{{getRowIndex(index)}}</td>
                    <td>{{item.statusInfo.id}}</td>
                    <td>{{formatTime(item.statusInfo.create_ts)}}</td>
                    <td>{{item.group_subject[0].group_subject}}</td>
                    <td>{{item.patientInfo.patient_name}}</td>
                    <td>{{item.patientInfo.patient_age}}</td>
                    <td>{{item.patientInfo.patient_sex}}</td>
                    <td>{{item.count}}</td>
                    <td>{{formatAssignment(item,1)}}</td>
                    <td>{{formatAssignment(item,2)}}</td>
                    <td>{{formatCaseStatus(item)}}</td>
                    <td>
                         <el-button  v-loading="item.openState" type="text" @click="handleShow(item)">{{lang.view_btn}}</el-button>
                    </td>
                </tr>
            </tbody>
        </table>
        <el-image v-if="examList.length==0" class="md-data-empty" src="static/resource_pc/images/nodata.png"/>
      <!-- <el-table v-loading="loading" border :data="examList">

        <el-table-column align="center" :label="lang.index_num" show-overflow-tooltip width="85">
          <template slot-scope='scope'>
            {{getRowIndex(scope)}}
          </template>
        </el-table-column>
        <el-table-column align="center" :label="lang.case_num" prop="status_id" show-overflow-tooltip/>
        <el-table-column align="center" :label="lang.upload_datetime" prop="upload_ts" show-overflow-tooltip/>
        <el-table-column align="center" :label="lang.group_by" prop="group_subject[0].group_subject" show-overflow-tooltip/>
        <el-table-column align="center" :label="lang.patient_name" prop="patientInfo.patient_name" show-overflow-tooltip/>
        <el-table-column align="center" :label="lang.patient_age" prop="patientInfo.patient_age" show-overflow-tooltip/>
        <el-table-column align="center" :label="lang.patient_sex" prop="patientInfo.patient_sex" show-overflow-tooltip/>
        <el-table-column align="center" :label="lang.numberOfImages" prop="count" show-overflow-tooltip/>
        <el-table-column align="center" :label="lang.hfr.assignment_A"
        :formatter="formatAssignment" show-overflow-tooltip/>
        <el-table-column align="center" :label="lang.hfr.assignment_B"
        :formatter="formatAssignment" show-overflow-tooltip/>
        <el-table-column align="center" :label="lang.case_status" prop="status"
        :formatter="formatCaseStatus" show-overflow-tooltip/>
        <el-table-column align="center" :label="lang.operation" show-overflow-tooltip width="85">
          <template #default="{ row }">
            <div v-loading="row.openState">
            <el-button type="text" @click="handleShow(row)">{{lang.view_btn}}</el-button>
            </div>
          </template>
        </el-table-column>
        <template #empty>
          <el-image class="md-data-empty" src="static/resource_pc/images/nodata.png"/>
        </template>
      </el-table> -->
    </div>
    <mr-gallery ref="MrGallery" class="mr_gallery" :loading='isSubmiting'>
      <div class="tabs_wrap">
        <el-tabs v-model="activeName" :stretch="true" type="card">
          <el-tab-pane v-for="tab of tabList" :key="tab.id" :label="tab.title" :name="tab.name"></el-tab-pane>
        </el-tabs>
      </div>
      <div v-show="activeName=='case_view'" class="case_view_wrap">
        <case-info ref="caseInfo"></case-info>
      </div>
      <div v-show="activeName=='annotation_view'" class="annotation_view_wrap">
        <annotate-case :exam.sync="examObj" ref="annotate" @startLoading="isSubmiting = true" @endLoading="isSubmiting = false"></annotate-case>
      </div>
      <div v-show="activeName=='create_view'" class="annotation_view_wrap">
          <annotate-case :exam.sync="examObj" ref="annotate2" @startLoading="isSubmiting = true" @endLoading="isSubmiting = false" :assignmentType="4"></annotate-case>
      </div>
      <div v-show="activeName=='judge_view'" class="annotation_view_wrap">
          <judge-annotation ref="judgeAnnotation" :exam.sync="examObj" :isJudge='false'></judge-annotation>
      </div>
    </mr-gallery>
    <el-pagination
      background
      :current-page="queryForm.pageNo"
      :layout="layout"
      :page-size="queryForm.pageSize"
      :total="examListTotal"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
      style="margin-top:20px"
    />
  </div>
</template>
<script>
import base from '../../../lib/base'
import service from '../../../service/multiCenterService.js'
import { transferPatientInfo,deDuplicatingImg } from '../../../lib/common_base'
import MrGallery from '../common/MrGallery'
import AnnotateCase from '../annotation/annotateCase'
import CaseInfo from '../assignment/caseInfo'
import exportDialog from '../common/exportDialog'
import JudgeAnnotation from '../judge/judgeAnnotation'
import mrQueryForm from '../common/MrQueryForm'
import mrQueryFormTopPanel from '../common/MrQueryForm/components/mrQueryFormTopPanel'
import Tool from '@/common/tool'
export default {
    mixins:[base],
    components:{MrGallery, AnnotateCase, CaseInfo, JudgeAnnotation,exportDialog,mrQueryForm,mrQueryFormTopPanel,},
    data(){
        return {
            examList:[],
            loading:false,
            isSubmiting: false,
            queryForm: {
                pageNo: 1,
                pageSize: 10,
                caseStatus: -1,
                uploadDateRange:[]
            },
            layout: 'total, sizes, prev, pager, next, jumper',
            caseSelectList:[
                {id:-1,value:''},
                // {id:0,value:''},
                // {id:1,value:''},
                {id:2,value:''},
                {id:3,value:''},
                {id:4,value:''},
                {id:5,value:''},
                {id:6,value:''}
            ],
            pickerOptions:{
                shortcuts: [{
                    text: window.vm.$store.state.language.recent_two_week,
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 13);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: window.vm.$store.state.language.recent_one_month,
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setMonth(start.getMonth()-1)
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: window.vm.$store.state.language.recent_two_month,
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setMonth(start.getMonth()-2)
                        picker.$emit('pick', [start, end]);
                    }
                }],
                disabledDate:(time)=>{
                    return time.getTime()>new Date().getTime()
                }
            },
            activeName:'case_view',
            examObj: {},
            tabList:[
                {
                    id:1,
                    title:window.vm.$store.state.language.case_view_label,
                    name:'case_view'
                },{
                    id:2,
                    title:window.vm.$store.state.language.hfr.annotation_view_label,
                    name:'annotation_view'
                }
            ],
            examListTotal:0,
        }
    },
    computed:{
        currentMulticenter(){
            return this.$store.state.multicenter.currentMulticenter||{}
        },
        currentConfig(){
            return this.$store.state.multicenter.currentConfig;
        },
        assignment_type(){
            return this.currentConfig.assignment_type
        }
    },
    created(){
        this.init()
    },
    mounted(){
    },
    updated(){
    },
    methods:{
        init(){
            this.fetchData()
            this.caseSelectList.forEach(item=>{
                item.value=this.lang.exam_status[item.id]
            })
        },
        handleQuery(){
            this.queryForm.pageNo = 1
            this.fetchData()
        },
        handleOnlyView() {
            // 已经仲裁过了，展示只查看模式UI
            const judgeObj = this.examObj.process.find(v => v.type == this.assignment_type.Arbitrate && v.authority_id !== 0)
            const assignA = this.examObj.process.find(v => v.type == this.assignment_type.A)
            const assignB = this.examObj.process.find(v => v.type == this.assignment_type.B)
            if(judgeObj) { // 已会诊
                let tab = {
                    id:this.tabList.length+1,
                    title:this.lang.hfr.end_comment_text,
                    name:'create_view',
                }
                let newDetails = judgeObj.more_details
                this.tabList.splice(1,1)

                this.tabList.push(tab)
                this.activeName = 'create_view'
                this.$nextTick(() => {
                    this.$refs.annotate2.updateByAnotate(newDetails)
                })
            }else if(assignA && assignB){
                let tab = {
                    id:this.tabList.length+1,
                    title:this.lang.hfr.annotation_view_label,
                    name:'judge_view',
                }
                this.tabList.splice(1,1)
                this.tabList.push(tab)
                this.activeName = 'judge_view'
                this.$nextTick(() => {
                    this.examObj.is_annotation_same = false
                    this.$refs.judgeAnnotation.initAnotationData()
                })
            }else{
                this.tabList.splice(1,1)
                this.activeName = 'case_view'
            }
        },
        resetTabList(){
            this.tabList = [
                {
                    id:1,
                    title:this.lang.case_view_label,
                    name:'case_view'
                },{
                    id:2,
                    title:this.lang.hfr.annotation_view_label,
                    name:'annotation_view'
                }
            ]
            this.activeName = 'annotation_view'
        },
        async handleShow(row){
            row.openState = true;
            this.resetTabList()
            await this.getExamDetail(row)
            this.examObj = row
            console.log(row)
            if(row.caseData==null){
                row.openState = false
                this.handleOnlyView()
                this.$refs.MrGallery.openGallery(row.image_list[0],0,row)
                this.$nextTick(() => {
                    this.$refs.caseInfo.clearAllData()
                })
                return
            }
            try{
                row.openState = false;
                console.log('row.image_list[0]',row.image_list[0])
                this.handleOnlyView()
                this.$refs.MrGallery.openGallery(row.image_list[0],0,row)
                this.$nextTick(() => {
                    this.$refs.caseInfo.clearAllData()
                    this.$refs.caseInfo.initImageURL(row.caseData)
                    this.$refs.caseInfo.updateToView(row.caseData, row.topic)
                    this.$refs.annotate2.clearAllData()
                    this.$refs.annotate2.initReviewData()
                })
            }catch(err){
                console.error(err)
            }
        },
        formatCaseStatus(row){
            return this.lang.exam_status[row.statusInfo.status]
        },
        formatAssignment(row,type){
            if(row.statusInfo.assignmentInfo){
                if(type==1){
                    return row.statusInfo.assignmentInfo.assignmentA.nickname;
                }else if(type==2){
                    return row.statusInfo.assignmentInfo.assignmentB.nickname;
                }
            }else{
                return this.lang.hfr.no_assignment
            }
        },
        fetchData(){
            this.loading=true;
            let start='',end='';
            if(this.queryForm.uploadDateRange&&this.queryForm.uploadDateRange.length>1){
                start = this.queryForm.uploadDateRange[0]
                end = this.queryForm.uploadDateRange[1]
                start = this.formatDate(start)
                end = this.formatDate(end)
            }
            service.getExamList({
                mcID:this.currentMulticenter.id,
                page:this.queryForm.pageNo,
                pageSize:this.queryForm.pageSize,
                condition:{
                    start_time:start,
                    end_time:end,
                    status:this.queryForm.caseStatus,
                }
            }).then(async (res)=>{
                this.loading=false;
                if (res.data.error_code==0) {
                    let examList = res.data.data.data
                    for(let item of examList){
                        item.patientInfo = transferPatientInfo(item)
                        this.$set(item,'openState',false)
                        this.$set(item,'initImageList',false)
                    }
                    this.examList = examList;
                    this.examListTotal=res.data.data.total;
                }
            })
        },
        async getExamDetail(exam){
            if(!exam.initImageList){
                await service.getExamDetail({
                    mcID:this.currentMulticenter.id,
                    protocolGUID:exam.protocol_guid,
                    statusID:exam.statusInfo.id,
                }).then(res => {
                    if(res.data.error_code==0){
                        exam.caseData=res.data.data.caseData;
                        exam.image_list=deDuplicatingImg(res.data.data.image_list);
                        exam.process=res.data.data.process;
                        exam.topic=res.data.data.topic;
                    }
                    exam.initImageList=true;
                    console.log('annotation -->> ',exam,this.caseData)
                })
            }
        },
        handleSizeChange(val) {
            this.queryForm.pageSize = val
            this.fetchData()
        },
        handleCurrentChange(val) {
            this.queryForm.pageNo = val
            this.fetchData()
        },
        formatDate(date){
            let year = date.getFullYear()
            let month = '00' + (date.getMonth()+1)
            let day = '00' + date.getDate()
            month = month.substr(-2);
            day = day.substr(-2);
            return `${year}-${month}-${day}`
        },
        getRowIndex(index) {
            index = index + 1
            return this.queryForm.pageNo * this.queryForm.pageSize - (this.queryForm.pageSize - index)
        },
        openExport(){
            if(!Tool.checkAppClient('Cef')){
                this.$message.error(this.lang.use_app_tip)
                return
            }
            this.$refs.exportDialog.init();
        }
    }
}
</script>
<style lang="scss">
  .hrf_data_view_page{
    padding:15px 20px;
    .search_bar{
      .el-range-separator{
        width:9%;
      }
      .el-form-item__label{
        width:auto !important;
      }
    }

  }
</style>

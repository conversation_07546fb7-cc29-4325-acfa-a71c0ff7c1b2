<template>
	<div class="clip_page"
        v-loading.fullscreen.lock="loading"
        :element-loading-text="lang.media_transfer.creating_task">
    </div>
</template>
<script>
import base from '../lib/base'
export default {
    mixins: [base],
    name: 'ClipPage',
    components: {},
    data(){
        return {
            loading:false
        }
    },
    computed:{

    },
    mounted(){
        this.$nextTick(()=>{
            this.addMediaTransaferTasks()
        });        
    },
    methods:{
        addMediaTransaferTasks(){
            console.log("addMediaTransaferTasks");
            var that = this;
            var list = window.media_transfer_task_list;
            let controller = window.main_screen.controller;
            if (controller) {
                that.loading = true;
                let timer = setTimeout(function () {
                    that.loading = false;
                    that.$message.error(that.lang.media_transfer.error.add_task_error);
                    that.back();
                },20000);
                console.log("addMediaTransaferTasks2");
                controller.emit("add_media_transfer_tasks", {list:list}, function (err, result) {
                    console.log("addMediaTransaferTasks3");
                    clearTimeout(timer);

                    that.loading = false;
                    if (err) {
                        if (result && "waiting_queue_full" == result.error_code) {
                            that.$message.error(that.lang.media_transfer.error.add_task_error_waiting_queue_full);
                        } else {
                            that.$message.error(that.lang.media_transfer.error.add_task_error);
                        }
                    } else {
                        that.$message.success(that.lang.media_transfer.clip_tip_startup);
                       
                    }
                    that.back();
                });
            } else {
                that.$message.error(that.lang.media_transfer.error.add_task_error);
            }
        }
    }
}
</script>
<style lang="scss">
.clip_page{
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
}
</style>
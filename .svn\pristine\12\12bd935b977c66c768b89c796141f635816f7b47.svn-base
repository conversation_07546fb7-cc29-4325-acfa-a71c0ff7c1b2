<template>
    <div>
        <CommonDialog
            :title="lang.live_detail"
            :show.sync="show"
            width="500px"
            height="300px"
            @open="handleAfterOpen"
            @closed="handleAfterClose"
            @cancel="cancel"
            @submit="submit"
            append-to-body
            :submitText="lang.confirm_txt"
            class="wrapper"
        >
            <div class="wrapper-content" v-loading="isLoading">
                <template v-if="(!creator_name)&&(!isLoading)">
                    <no-Data :text="lang.no_data_txt"></no-Data>
                </template>
                <template v-if="creator_name&&!isLoading">
                    <p class="wrapper-content-info">
                        <span class="content-info-label">{{ lang.live_broadcast_initiator }}:</span>
                        <span class="longwrap content-info-text" v-if="creator_name">{{ creator_name }}</span>
                    </p>
                    <p class="wrapper-content-info">
                        <span class="content-info-label">{{ lang.live_broadcast_initiation_time }}:</span>
                        <span class="longwrap content-info-text">{{ formatTime(start_ts) }}</span>
                    </p>
                    <div class="wrapper-content-line"></div>
                    <div class="wrapper-bottom">
                        <p class="wrapper-bottom-title">{{ lang.live_broadcast_information }}</p>
                        <div class="wrapper-bottom-info">
                            <div class="info-item">
                                <span class="content-info-label">{{ lang.live_broadcast_duration }}:</span>
                                <span class="longwrap content-info-text" v-if="start_ts">{{
                                    getDateDiff(start_ts, stop_ts)
                                }}</span>
                            </div>
                            <div class="info-item">
                                <span class="content-info-label">{{ lang.live_broadcast_viewers }}:</span>
                                <span class="longwrap content-info-text">{{ count }}</span>
                            </div>
                        </div>
                        <div class="wrapper-bottom-more" v-if="hasMoreDetail">
                            <div v-show="isShowMore" class="more-list">
                                <el-tabs v-model="activeMoreDetail">
                                     <el-tab-pane :label="`${lang.live_detail_title}(${moreLiveList.length})`" name="live">
                                        <div class="more-item" v-for="member of moreLiveList" :key="member.userId">
                                            <mr-avatar :url="getLocalAvatar(member)" :key="member.avatar" :radius="30"></mr-avatar>
                                            <p class="more-item-name">{{member.nickname}}</p>
                                            <p>{{member.durationStr}}</p>
                                        </div>
                                     </el-tab-pane>
                                     <el-tab-pane :label="`${lang.review_detail_title}(${moreReviewList.length})`" name="review">
                                        <div class="more-item" v-for="member of moreReviewList" :key="member.userId">
                                            <mr-avatar :url="getLocalAvatar(member)" :key="member.avatar" :radius="30"></mr-avatar>
                                            <p class="more-item-name">{{member.nickname}}</p>
                                            <p>{{member.durationStr}}</p>
                                        </div>
                                        <div v-if="moreReviewList.length==0">{{lang.no_data_txt}}</div>
                                     </el-tab-pane>
                                </el-tabs>
                            </div>
                            <div class="wrapper-bottom-tools">
                                <span class="more-toggle-btn" @click="exportLiveDetail">{{lang.export_live_data}}</span>
                                <span class="more-toggle-btn" v-show="!isShowMore" @click="toggleShowMore">{{lang.expand_more_details}}</span>
                                <span class="more-toggle-btn" v-show="isShowMore" @click="toggleShowMore">{{lang.retract_more_details}}</span>
                            </div>

                        </div>
                    </div>
                </template>
            </div>
        </CommonDialog>
    </div>
</template>
<script>
import base from "../../lib/base";
import CommonDialog from "../../MRComponents/commonDialog.vue";
import { getDateDiff,getLocalAvatar } from "../../lib/common_base";
import moment from "moment";
import noData from "../../MRComponents/noData.vue";
export default {
    mixins: [base],
    name: "reviewDetail",
    model: {
        prop: "value",
        event: "change",
    },
    props: {
        message: {
            type: Object,
            default: () => {
                return {};
            },
        },
        value: {
            type: Boolean,
            default: false,
        },
        hasMoreDetail: {
            type: Boolean,
            default: false,
        },
    },
    components: {
        CommonDialog,
        noData,
    },
    computed:{
    },
    watch: {
        value: {
            handler(val) {
                this.show = val;
                if(val){
                    this.$root.eventBus.$emit("hideRealTimeVideo");
                }else{
                    this.$root.eventBus.$emit("showRealTimeVideo");
                }
            },
            immediate: true,
        },
        show: {
            handler(val) {
                this.$emit("change", val);
            },
        },
    },
    data() {
        return {
            getDateDiff,
            getLocalAvatar,
            that: this,
            show: false,
            count: 0,
            start_ts: 0,
            stop_ts: 0,
            creator_name: "",
            isLoading: false,
            isShowMore:false,
            moreLiveList:[],
            moreReviewList:[],
            activeMoreDetail:'live'
        };
    },
    methods: {
        handleAfterOpen(){
            this.getLiveRecordInfo();
        },
        handleAfterClose(){
            this.isLoading=false
        },
        submit() {
            this.show = false;
        },
        cancel() {
            this.show = false;
        },
        getLiveRecordInfo() {
            this.isLoading = true;
            this.count = 0;
            this.start_ts = 0;
            this.stop_ts = 0;
            this.creator_name = "";
            this.isShowMore=false;
            return new Promise((resolve, reject) => {
                window.main_screen.getReviewDetail({ gmsg_id: this.message.gmsg_id }, (res) => {
                    console.log(res);
                    this.isLoading = false;
                    if (!res.error_code) {
                        this.count = res.data.count;
                        this.start_ts = res.data.start_ts;
                        this.stop_ts = res.data.stop_ts;
                        this.creator_name = res.data.creator_name;
                        this.setMoreList(res.data.list,res.data.audience_list);
                    }
                });
            });
        },
        toggleShowMore(){
            this.isShowMore=!this.isShowMore;
        },
        setMoreList(list,audience_list){
            this.moreLiveList=[];
            this.moreReviewList=[];
            for(let member of list){
                member.duration=0;
                for(let item of member.dataList){
                    member.duration+=item.duration;
                }
                member.durationStr=getDateDiff(0,member.duration)
                this.moreLiveList.push(member);
            }
            for(let member of audience_list){
                member.durationStr=getDateDiff(0,member.duration)
                this.moreReviewList.push(member);
            }
        },
        async exportLiveDetail(){
            try {
                const XLSX = await import(/* webpackPrefetch: true */ 'xlsx');

                const workbook = XLSX.utils.book_new();
                let liveData = [
                    [this.lang.nickname, this.lang.total_duration,],
                ]
                this.moreLiveList.forEach(item=>{
                    liveData.push([item.nickname,item.durationStr])
                })
                const liveSheet = XLSX.utils.aoa_to_sheet(liveData);
                let reviewData = [
                    [this.lang.nickname, this.lang.total_duration,],
                ]
                this.moreReviewList.forEach(item=>{
                    reviewData.push([item.nickname,item.durationStr])
                })
                const reviewSheet = XLSX.utils.aoa_to_sheet(reviewData);
                XLSX.utils.book_append_sheet(workbook, liveSheet, this.lang.live_detail_title);
                XLSX.utils.book_append_sheet(workbook, reviewSheet, this.lang.review_detail_title);
                const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
                const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

                const fileName = `${this.formatTime(this.start_ts)}.xlsx`;
                if (navigator.msSaveBlob) {
                    navigator.msSaveBlob(blob, fileName);
                } else {
                    const link = document.createElement('a');
                    if (link.download !== undefined) {
                        const url = URL.createObjectURL(blob);
                        link.setAttribute('href', url);
                        link.setAttribute('download', fileName);
                        link.style.visibility = 'hidden';
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                    }
                }
            } catch (error) {
                console.error('加载 XLSX 模块失败:', error);
            }
        }
    },
};
</script>
<style lang="scss">
.wrapper {
    width: 100%;
    height: 100%;
    color: #fff;
    .wrapper-header {
        height: 3rem;

        position: relative;
        .van-icon {
            position: absolute;
            bottom: 0;
            right: 1rem;
        }
    }
    .wrapper-title {
        height: 10rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        font-size: 20px;
    }
    .wrapper-content {
        background: #fff;
        font-size: 16px;
        margin: 0 auto;
        box-sizing: border-box;
        .wrapper-content-info {
            display: flex;
            margin-bottom: 12px;
            .content-info-label {
                margin-right: 12px;
                flex-shrink: 0;
            }
            .content-info-text {
                flex: 1;
            }
        }
        .wrapper-content-line {
            width: 100%;
            height: 1px;
            background: #000;
        }
    }
    .wrapper-bottom {
        margin-top: 12px;
        .wrapper-bottom-info {
            margin-top: 12px;
            display: flex;
            font-size: 14px;
            color: #000;
            justify-content: space-between;
            .info-item {
                // flex: 1;
            }
        }
        .wrapper-bottom-more{
            margin-top: 12px;
            font-size: 14px;
            color: #000;
            .wrapper-bottom-tools{
                display: flex;
                justify-content: flex-end;
                .more-toggle-btn{
                    color: #779a98;
                    cursor: pointer;
                    user-select:none;
                    margin-left: 24px;
                }
            }

            .more-list{
                max-height: 200px;
                overflow: auto;
                .more-item{
                    display: flex;
                    align-items: center;
                    margin-bottom: 10px;
                    .more-item-name{
                        padding: 0 10px;
                        flex:1;
                    }
                }
            }
        }
    }
}
</style>

<template>
	<div></div>
</template>
<script>
import Tool from '@/common/tool'
export default {
    name: 'transmitControler',
    components: {},
    data(){
        return {
            inited:false
        }
    },
    mounted(){
        this.$nextTick(()=>{
            this.inited=true
            window.vm.$root.eventBus.$emit('initTransmit')
        })
    },
    activated(){
        this.$nextTick(()=>{
            if (this.inited) {
                return ;
            }
            window.vm.$root.eventBus.$emit('initTransmit')
        })
    },
    beforeRouteLeave(to,from,next){
        next();
        this.inited=false;
        window.vm.$root.eventBus.$emit('destroyTransmit')

    },
    beforeDestroy(){

    }
}
</script>

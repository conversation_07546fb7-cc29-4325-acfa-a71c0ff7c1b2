<template>
    <div class="groupset_exam_mode">
        <vue-scroll>
            <exam-search-bar ref="exam_search_bar" :searchCallback="searchCallback" :changeCondition="changeCondition"></exam-search-bar>
            <div class="exam_list">
                <div v-for="(group,groupIndex) of examGroup" :key="groupIndex" class="group_container">
                    <p class="group_title">{{group.title}}</p>
                    <div v-for="(item,e_index) of group.list"  class="exam_item" :key="e_index">
                        <div @click="toggleImages(item)" class="exam_title clearfix" >
                            <div class="exam_uploader">
                                <div class="avatar">
                                    <img :src="item.sender_nickname[0].avatar_local">
                                </div>
                                <div class="uploader_info">
                                    <p class="sender">
                                        <span v-for="(sender,senderIndex) of item.sender_nickname" :key="senderIndex">
                                    {{sender.hospital_name?sender.hospital_name+'-'+sender.nickname:sender.nickname}}
                                </span>
                                    </p>
                                    <p class="date">{{lang.upload_date_text}}{{item.upload_ts}}</p>
                                </div>
                            </div><!--
                            <p class="left_item">{{lang.sender_nickname}}
                                <span v-for="(sender,senderIndex) of item.sender_nickname" :key="senderIndex">
                                    {{sender.hospital_name?sender.hospital_name+'-'+sender.nickname:sender.nickname}}
                                </span>
                            </p> --><!--
                            <p class="right_item">{{lang.upload_date_text}}{{item.upload_ts}}</p> -->
                            <i v-show="!item.openState" class="icon iconfont icondown"></i>
                            <i v-show="item.openState" class="icon iconfont iconup"></i>
                        </div>
                        <div class="exam_patient">
                            <div class="patient_info clearfix">
                                <p>{{lang.patient_name}}: {{item.patientInfo.patient_name}}</p>
                                <p>{{lang.exam_type}}: {{lang.exam_types[item.exam_type]}}</p>
                                <p>{{lang.patient_age}}: {{item.patientInfo.patient_age}}</p>
                            </div>
                            <div class="patient_info clearfix">
                                <p>{{lang.patient_sex}}: {{item.patientInfo.patient_sex}}</p>
                                <p>{{lang.calibrater_text}}{{lang.uncalibrated_text}}</p>
                                <p>{{lang.exam_time}}: {{formatTime(item.patient_series_datetime)}}</p>
                            </div>
                            <div class="patient_info clearfix">
                                <p>{{lang.group_by}}: {{getItemGroup(item)}}</p>
                                <p>{{lang.image_count}}: {{item.count}}</p>
                                <p></p>
                            </div>
                        </div>
                        <div class="exam_image_list clearfix" v-show="item.openState">
                            <div  class="exam_title">
                                <!-- <div class="clearfix">
                                    <p class="left_item">{{lang.patient_name}}: {{item.patientInfo.patient_name}}</p>
                                    <p class="right_item">{{lang.exam_type}}: {{lang.exam_types[item.exam_type]}}</p>
                                </div>
                                <div class="clearfix">
                                    <p  class="left_item">{{lang.patient_age}}: {{item.patientInfo.patient_age}}</p>
                                    <p  class="right_item">{{lang.patient_sex}}: {{item.patientInfo.patient_sex}}</p>
                                </div>
                                <div class="clearfix">
                                    <p class="left_item">{{lang.calibrater_text}}{{lang.uncalibrated_text}}</p>
                                </div> -->
                                <div v-if="item.iworks_protocol" class="iworks_info clearfix">
                                    <div class="icon_title">
                                        <i class="iconfont iconquanbuwenjian-weixuanzhong" id="iworks"></i>
                                    </div>
                                    <p>iWorks：{{item.iworks_protocol_execution.protocol_name}}
                                        （{{item.uploaded_view_number}}/{{item.all_view_number}}）
                                        <!-- <el-popover
                                            placement="right"
                                            popper-class="iworks_tree_popper"
                                            trigger="click">
                                            <el-tree
                                            :data="iworksProtocolTree"
                                            :default-expand-all="true"
                                            show-checkbox
                                            node-key="id"
                                            :ref="'protocol_tree_'+item.exam_id"
                                            ></el-tree>
                                            <i @click="showProtocol(item)" slot="reference" class="iconfont iconwarning-o"></i>
                                        </el-popover> -->
                                    </p>
                                </div>
                            </div>
                            <div class="list clearfix" v-loading="item.imageState==1">
                                <div v-for="(img,imgIndex) of item.showImageList" class="file_item" @click.stop="clickGallery($event,img,2,item)" @contextmenu="callImageMenu($event,img)" :class="{is_create:img.isCreate,is_delete:img.isDelete}" :key="imgIndex">
                                    <v-touch @press="callImageMenu($event,img)">
                                        <p v-if="img.protocol_view_name" class="view_name">{{img.protocol_view_name}}</p>
                                        <img v-if="img.url_local" :src="img.error_image||img.url_local"  class="file_image" @error="setErrorImage(img)">
                                        <img v-else src="static/resource_pc/images/default.png" class="file_image">
                                        <!-- <div v-else class="empty_view"></div> -->
                                        <i v-if="img.msg_type==systemConfig.msg_type.Cine" class="icon iconfont iconvideo_fill_light"></i>
                                    </v-touch>
                                </div>
                            </div>
                            <!-- <el-button @click="openSupplyCaseDialog(item)" type="primary">{{lang.supply_case_title}}</el-button> -->
                        </div>
                    </div>
                </div>
                <template v-if="loadFail">
                    <p class="tip" @click="search">{{lang.loading_fail}}</p>
                </template>
                <template v-else>
                    <p class="tip" v-show="index!=-1" @click="loadmore" v-loading="loadingMore">{{lang.click_more_text}}</p>
                    <p class="tip" v-show="index==-1">{{lang.no_more_data}}</p>
                </template>
        </div>
        </vue-scroll>
    </div>
</template>
<script>
import base from '../lib/base';
import examListCommon from '../lib/examListCommon';
import iworksTool from '../lib/iworksTool';
import examSearchBar from './examSearchBar'
import {parseImageListToLocal,transferPatientInfo} from '../lib/common_base'
export default {
    mixins:[base,iworksTool,examListCommon],
    components: {examSearchBar},
    data(){
        return {
            cid:0,
            condition:{},
            examGroup:[],
            loadingMore:false,
            loadFail:false,
            isShowSupplyDialog: false
        }
    },
    mounted(){
        this.$root.eventBus.$off('initGroupsetExamView').$on('initGroupsetExamView',this.initGroupsetExamView);
    },
    computed:{
        examObj(){
            return this.$store.state.examList[this.cid]||{}
        },
        examList(){
            return this.examObj.list||[]
        },
        index(){
            return this.examObj.index||0
        },
    },
    methods:{
        initGroupsetExamView(){
            this.cid='groupset_'+this.$store.state.groupset.currentGroupset.id;
            this.$refs.exam_search_bar.init()
            this.search()
            // if (!this.examObj.init) {
            //     this.search()
            // }else{
            //     //检查列表初始化过再次进入只渲染上次搜索条件
            //     this.$refs.exam_search_bar.renderSearchCondition(this.examObj)
            //     this.parseExamListToGroup(this.examList);
            // }
        },
        search(){
            this.examGroup=[]
            this.$refs.exam_search_bar.search();
        },
        searchCallback(condition){
        	this.examGroup=[]
            this.condition=condition
            this.$store.commit('examList/initExamObj',this.cid)
            this.getExamList();
        },
        getExamList(callback){
            if (this.loadingMore) {
                return ;
            }
            this.loadingMore=true;
            this.loadFail=false;
            var that=this;
            let index=this.examObj.index;
            this.controller=window.main_screen.controller;
            let sort_by=this.condition.sortSelected==1?'exam_ts':'upload_ts'
            let keys=this.condition
            this.$store.commit('examList/updateExamObj',{
                cid:this.cid,
                keys:keys
            })
            this.controller.emit('get_groupset_exam_list',{
                key:keys,
                start:index,
                count:20,
                sort_by:sort_by,
                groupset_id:this.$store.state.groupset.currentGroupset.id
            },function(err,data){
                that.loadingMore=false;
                if (!err) {
                    for(let item of data.exam_list){
                        item.patientInfo=transferPatientInfo(item);
                        parseImageListToLocal(item.sender_nickname,'avatar')
                        that.setDefaultImg(item.sender_nickname)
                        item.imageState=0;
                        item.openState=false;
                        item.isShowCheck=false
                        item.imageList=[]
                        item.selected=[];
                        item.iworksImages=[]
                        item.noneIworksImages=[]
                        item.opinionList=[]
                    }
                    that.$store.commit('examList/setExamList',{
                        cid:that.cid,
                        exam_list:data.exam_list,
                        index:data.index,
                        start:index
                    })
                    that.parseExamListToGroup();
                    callback&&callback()
                }else{
                    that.loadFail=true;
                    // that.$message.error(that.lang[data])
                }
            })
        },
        loadmore(){
            this.getExamList();
        },
        changeCondition(condition){
        	this.condition=condition
            this.$store.commit('examList/updateExamObj',{
                cid:this.cid,
                keys:condition
            })
            this.parseExamListToGroup();
        },
        toggleImages(exam){
            var that=this;
            var item = exam
            this.$store.commit('examList/updateExamById',{
                cid:this.cid,
                exam_id:exam.exam_id,
                key:{
                    openState:!item.openState
                }
            })
            //切换展示状态
            if (item.imageState==0) {
                //未下载过则下载图像列表
                this.$store.commit('examList/updateExamById',{
                    cid:this.cid,
                    exam_id:exam.exam_id,
                    key:{
                        imageState:1
                    }
                })
                this.controller=window.main_screen.controller;
                var tag=this.condition.tag||[];
                this.controller.emit('get_groupset_exam_image_list',{
                    exam_id:item.exam_id,
                    key:{
                        tag:tag
                    },
                    groupset_id:this.$store.state.groupset.currentGroupset.id
                },function(err,data){
                    let key={}
                    if (!err) {
                        key.imageState=2;
                        parseImageListToLocal(data.image_list,'url')
                        if (exam.iworks_protocol) {
                            key.iworks_protocol=exam.iworks_protocol
                            that.setIworksProtocol(exam.iworks_protocol)
                        }
                        if (exam.iworks_protocol_execution) {
                            key.iworks_protocol_execution=exam.iworks_protocol_execution
                            that.setIworksProtocol(exam.iworks_protocol_execution)
                        }
                        //对检查图像进行去重
                        key.imageList=that.deDuplicatingImg(data.image_list);
                        that.parseIworksImage(key)
                        key.showImageList=key.iworksImages.concat(key.noneIworksImages)

                    }else{
                        that.$message.error(that.lang.get_groupset_exam_fail)
                        key.imageState=0;
                        key.openState=!item.openState;
                    }
                    that.$store.commit('examList/updateExamById',{
                        cid:that.cid,
                        exam_id:exam.exam_id,
                        key:key
                    })
                })
            }
        },
        clickGallery(event,file,type,exam){
            // this.$message.error('待实现')
            let list=exam.iworksImages.concat(exam.noneIworksImages)
            var cid=file.group_id;
            let gid=this.$store.state.groupset.currentGroupset.id
            var param={
                openFile:file,
                list:list
            }
            this.$store.commit('gallery/setGallery',param);
            this.$router.push(`/index/chat_window/0/groupset_wall/${gid}/exam_mode/gallery`)
            // this.openConversation(cid,9);
        },
        getItemGroup(exam) {
            let groupList = exam.group_subject
            let groupName = ''
            if(groupList && groupList.length > 0) {
                groupList.forEach((v,i) => {
                    groupName += v.group_subject
                    if(groupList[i+1]) {
                        groupName += ','
                    }
                })
            }else{
                groupName = this.lang.unknow_text
            }
            return groupName
        }
    }
}
</script>
<style lang="scss">
.groupset_exam_mode{
	flex: 1;
    min-height: 1px;
    overflow: auto;
    .exam_search_bar{
    	background:#f5f8fa;
    }
	.exam_list{
        flex: 1;
        overflow: auto;
        .group_container{
            padding: 0 0.8rem;
            .group_title{
                padding-left:10px;
                line-height: 1.8;
                margin-top: 10px;
                font-size:16px;
            }
            .exam_item>.exam_title{
                cursor:pointer;
            }
            .exam_title{
                font-size: .8rem;
                color: #333;
                line-height: 1.6;
                position: relative;
                .exam_uploader{
                    display: flex;
                    padding-bottom: 0.6rem;
                    border-bottom: 1px solid #DBDBDB;
                    .avatar{
                        width: 2rem;
                        height: 2rem;
                        border-radius: 50%;
                        overflow: hidden;
                        img{
                            width: 100%;
                            height: 100%;
                        }
                    }
                    .uploader_info{
                        flex: 1;
                        padding-left: 0.6rem;
                        .date{
                            font-size: 0.4rem;
                            color: #999;
                        }
                    }
                }
                .icondown,.iconup{
                    position: absolute;
                    right: 0;
                    top: 22px;
                    line-height:1;
                    font-size: 26px;
                }
                .iconcomment{
                    position: absolute;
                    right: 2px;
                    top: -6px;
                    font-size:22px;
                    span{
                        position: absolute;
                        right: -6px;
                        top: 0px;
                        min-width: 16px;
                        height: 16px;
                        line-height: 16px;
                        border-radius: 50%;
                        background-color: #0dc6a0;
                        text-align: center;
                        color: #FFFFFF;
                        font-size: 12px;
                    }
                }
                .iconwarning-o{
                    cursor:pointer;
                }
            }
            .exam_item{
                padding: .8rem .8rem 1rem;
                margin-top: 10px;
                background-color: #fff;
                border-radius: 0.3rem;
                box-shadow: 0.1rem 0.1rem 0.2rem rgba(140,152,155,0.7);
                .exam_patient{
                    font-size: 0.7rem;
                    padding-top: 0.6rem;
                    color: rgb(101,109,112);
                    .patient_info{
                        display: flex;
                        margin-bottom: 0.5rem;
                        p{
                            flex: 1;
                            word-break: break-all;
                            padding-right: 0.3rem;
                        }
                    }
                }
                .exam_image_list{
                    background-color: #fff;
                    .iworks_info{
                        display: flex;
                        align-items: center;
                        .icon_title{
                            background-color: #666;
                            border-radius: 50%;
                            width: 1rem;
                            height: 1rem;
                            i{
                                position: relative;
                                font-size: 0.9em;
                                top: -0.1em;
                                left: 0.225em;
                                fill: none;
                                color: #fff;
                                float: left;
                            }
                        }
                        &>p{
                            display: inline-block;
                            line-height: 1rem;
                            padding-left: 0.4rem;
                            font-size: 0.55rem;
                        }
                    }
                    .list{
                        margin-top:10px;
                    }
                    .file_item{
                        float: left;
                        margin-right: 6px;
                        margin-bottom: 6px;
                        width: 140px;
                        height: 110px;
                        background-color: #000;
                        position: relative;
                        cursor: pointer;
                        border-radius: 0.2rem;
                        img{
                            max-width: 100%;
                            max-height: 100%;
                            position: absolute;
                            top: 50%;
                            left: 50%;
                            transform: translate(-50%, -50%);
                        }
                        i{
                            position: absolute;
                            bottom: 6px;
                            color: #fff;
                            font-size: 24px;
                            line-height: 1;
                            left: 4px;
                        }
                        .review_time{
                            font-size: 12px;
                            text-align: center;
                            color: #fff;
                            position: absolute;
                            white-space: nowrap;
                            top: 50%;
                            left: 50%;
                            transform: translate(-50%, -50%);
                        }
                        .review_text{
                            font-size: 12px;
                            text-align: center;
                            color: yellow;
                            position: absolute;
                            width: 100%;
                            top: 5%;
                        }
                        &.is_create{
                            .view_name{
                                color: #f9f122;
                            }
                        }
                        &.is_delete{
                            .view_name{
                                text-decoration: line-through;
                            }
                        }
                        .view_name{
                            color: #fff;
                            position: absolute;
                            top: 6px;
                            z-index: 9;
                            left: 2px;
                            transform: none;
                            font-size: 14px;
                            white-space: normal;
                            text-align: left;
                        }
                        .empty_view{
                            width: 100%;
                            height: 100px;
                            position: absolute;
                            top: 50%;
                            left: 50%;
                            background-color:#2c2d2f;
                            transform: translate(-50%, -50%);
                        }
                    }
                    .iworks_panel{
                        border: 1px solid #aaa;
                        margin-bottom: 10px;
                        padding: 10px;
                        border-radius: 4px;
                        &>p{
                            margin-bottom: 4px;
                            padding-right: 26px;
                            position: relative;
                            display:inline-block;
                            i{
                                font-size: 20px;
                                position: absolute;
                                right: 0;
                                top: -2px;
                                cursor: pointer;
                            }
                        }
                        .iworks_list{
                            .iworks_view{
                                float: left;
                                margin-right: 6px;
                                margin-bottom: 6px;
                                width: 140px;
                                height: 120px;
                                background-color: #000;
                                position: relative;
                                cursor: pointer;
                                img{
                                    max-width: 100%;
                                    max-height: 100%;
                                    position: absolute;
                                    top: 50%;
                                    left: 50%;
                                    transform: translate(-50%, -50%);
                                }
                                &>p{
                                    color: #f9f90f;
                                    position: absolute;
                                    top: 6px;
                                    z-index: 9;
                                    left: 2px;
                                }
                            }
                        }

                    }
                }
                .exam_comments{
                    border-top: 1px solid #aaa;
                    margin-top: 10px;
                    padding-top: 10px;
                    .comment_editor{
                        display:flex;
                        &>p{
                            font-size:20px;
                            line-height:50px;
                        }
                        textarea{
                            resize: none;
                            font-size: 16px;
                            font-weight: normal;
                            flex: 1;
                            line-height: 18px;
                            color: #555555;
                            border: 1px solid #ccc;
                            padding: 4px;
                            border-radius: 3px;
                        }
                        .add_exam_comment{
                            background: #a1b7b6;
                            margin: 8px;
                            padding: 2px 10px;
                            color: #fff;
                            border-radius: 3px;
                            line-height: 32px;
                            cursor: pointer;
                        }
                    }
                    .exam_comment_list{
                        padding: 12px 0px;
                        .comment_item{
                            margin: 4px;
                            border-top: 1px solid #e4e4e4;
                            color: #333;
                            padding: 4px;
                            &>p{
                                font-size:18px;
                                user-select:text;
                                span{
                                    font-size: 12px;
                                    background-color: #01c59d;
                                    color: #fff;
                                    line-height: 1;
                                    padding: 4px 6px;
                                    border-radius: 4px;
                                    margin-top: 0;
                                    margin-left: 4px;
                                    display: inline-block;
                                }
                            }
                            &>div{
                                span{
                                    margin:0 4px;
                                }
                            }
                        }
                    }
                }
            }
        }
        .tip{
            text-align: center;
            line-height: 2;
            margin-top:8px;
        }
    }
}
</style>

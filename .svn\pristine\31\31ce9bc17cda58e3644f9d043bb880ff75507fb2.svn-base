<template>
<div>
    <el-dialog
      class="reset_login_name"
      :title="lang.reset_login_name"
      :visible="true"
      :close-on-click-modal="false"
      :append-to-body="true"
      width="400px"
      :modal="false"
      :before-close="back">
        <div class="reset_login_name_container">
            <!-- <div v-show="reset_login_name_step==1">
                <div class="step_tip">{{lang.verify_password}}</div>
                <el-input type="password" v-model="password" :placeholder="lang.login_password" maxlength="16"></el-input>
                <div class="normal_btn" @click="verifyAccount">{{lang.confirm_txt}}</div>
            </div> -->
            <div>
                <div class="step_tip">{{lang.reset_login_name_tip_reset_login_name}}</div>
                <el-input v-model="login_name" :placeholder="lang.login_account" maxlength="16"></el-input>
                <div class="normal_btn" v-loading="loading"  @click="resetLoginName">{{lang.confirm_txt}}</div>
            </div>
        </div>
    </el-dialog>
</div>

</template>
<script>
import base from '../lib/base'
import service from '../service/service'
import {
    accountRegExp1,
} from '@/common/regExpMapping.js'
export default {
    mixins: [base],
    name: 'ResetLoginName',
    components: {},
    data(){
        return {
            password:'',
            login_name:'',
            loading:false,
            reset_login_name_step:1
        }
    },
    computed:{
    },
    mounted(){
        this.$nextTick(()=>{
        })
    },
    methods:{
        resetLoginName(){
            if (!accountRegExp1.test(this.login_name)){
                this.$message.error(this.lang.account_format_tip);
                return ;
            }
            if (this.loading) {
                return ;
            }
            this.loading=true;
            let params={
                newLoginName:this.login_name,
                safeKey:window.vm.$store.state.dynamicGlobalParams.safeKey,
            }
            service.updateLoginName(params).then((res)=>{
                this.loading=false;
                if(res.data.error_code==0){
                    this.$root.eventBus.$emit('reset_login_name', {
                        login_name:this.login_name
                    });
                    this.back()
                }else{
                    this.$store.commit('dynamicGlobalParams/updateDynamicGlobalParams',{
                        isSafeAuth:false
                    })
                    this.back();
                }
            })
        }
    }
}
</script>
<style lang="scss">
.reset_login_name{
    .el-dialog{
        height:240px !important;
        .el-dialog__header{
            margin-top:300px;
            .el-tabs__content{
                position:static;
            }
        }
        .el-dialog__body{
            padding:20px;

            input{
                margin-top:20px;
            }
            .normal_btn{
                margin-top:20px;
            }
            .step_tip {
                font-size: 18px;
                margin-top:20px;
            }
        }
    }
    input{
        padding: 8px 12px;
        font-size: 18px;
        height: 50px;
        background: #CFE5E3;
        color: #5a817e;
        box-sizing: border-box;
        letter-spacing: 2px;
        font-weight: 500;
        line-height: 24px;
        box-shadow: inset 1px 1px 2px rgba(0, 0, 0, 0.1);
        -webkit-appearance: none;
        border-radius: 0;
        outline: none;
        resize: none;
        border: none;
        -webkit-box-shadow: 0 0 0px 1000px #CFE5E3 inset;
    }
    input:-webkit-autofill {
        transition:background-color 999999s ease-in-out 0s;
        -webkit-text-fill-color: #5a817e;
    }
    input:focus{
        border:1px solid #6ab3ad;
    }
}
</style>

<template>
    <div class="practice-select">
        <div class="practice-select-header">
            <div class="practice-select-title">{{ lang.welcome_clinical_thinking_exercise_tips }}</div>

            <!-- <el-button
                type="primary"
                @click="sequencePractice"
                class="random-practice-btn"
                :loading="getAiPracticeCaseLoading"
                >顺序练习(测试用)</el-button
            > -->

        </div>

        <div class="body-map-container">
            <!-- 基础人体图 -->
            <div class="body-map">
                <div class="image-wrapper" ref="imageWrapper">
                    <img
                        ref="bodyImage"
                        :src="currentImage"
                        :alt="lang.human_body_diagram"
                        @load="handleImageLoad"
                        :class="{ 'image-loaded': isImageLoaded }"
                    />
                    <!-- 可点击区域 - 第一个人体 -->
                    <template v-if="isImageLoaded">
                        <!-- 第一个人体的可点击区域 -->
                        <div
                            v-for="area in bodyAreas1"
                            :key="area.id"
                            :class="['clickable-area', area.clickImageClass]"
                            :style="getAreaPosition(area)"
                            @mouseenter="() => handleAreaHover(area.id)"
                            @mouseleave="resetImage"
                            @click="() => handleAreaClick(area.id)"
                        ></div>

                        <!-- 第一个人体的文本区域 -->
                        <div
                            v-for="area in bodyAreas1"
                            :key="area.id + 'text'"
                            :class="[
                                'clickable-area',
                                area.clickTextClass,
                                { highlight: currentHighlightText === area.id },
                            ]"
                            :style="getTextPosition(area)"
                            @mouseenter="() => handleAreaHover(area.id)"
                            @mouseleave="resetImage"
                            @click="() => handleAreaClick(area.id)"
                        >
                            <span>{{ area.text }}</span>
                        </div>

                        <!-- 第二个人体的可点击区域 -->
                        <div
                            v-for="area in bodyAreas2"
                            :key="area.id"
                            :class="['clickable-area', area.clickImageClass]"
                            :style="getAreaPosition(area)"
                            @mouseenter="() => handleAreaHover(area.id)"
                            @mouseleave="resetImage"
                            @click="() => handleAreaClick(area.id)"
                        ></div>

                        <!-- 第二个人体的文本区域 (只渲染有textPosition的区域) -->
                        <div
                            v-for="area in bodyAreas2.filter(a => a.textPosition)"
                            :key="area.id + 'text'"
                            :class="[
                                'clickable-area',
                                area.clickTextClass,
                                { highlight: currentHighlightText === area.id },
                            ]"
                            :style="getTextPosition(area)"
                            @mouseenter="() => handleAreaHover(area.id)"
                            @mouseleave="resetImage"
                            @click="() => handleAreaClick(area.id)"
                        >
                            <span>{{ area.text }}</span>
                        </div>
                    </template>
                </div>
            </div>
        </div>
        <el-button
            type="primary"
            @click="randomPractice"
            :class="['random-practice-btn']"
            :loading="getAiPracticeCaseLoading"
            >{{ lang.random_exercise }}</el-button
        >
        <!-- 添加新的悬浮按钮 -->
        <!-- <div
            class="floating-practice-btn"
            :class="{ 'loading': getAiPracticeCaseLoading }"
            @click="sequencePractice"
        ></div> -->
    </div>
</template>

<script>
import base from "../../lib/base";
import body_base from "/static/resource_pc/images/body_base_2.png";
import body_fubu from "/static/resource_pc/images/body_fubu_2.png";
import body_fuchan from "/static/resource_pc/images/body_fuchan_2.png";
import body_qianbiao from "/static/resource_pc/images/body_qianbiao_2.png";
import body_xinxueguan from "/static/resource_pc/images/body_xinxueguan_2.png";
import AIBookServiceInstance from "@/common/aiBookService";
export default {
    mixins: [base],
    name: "PracticeSelect",
    data() {
        return {
            currentImage: "", // 基础图片
            currentText: "", // 当前选中的文本
            bodyAreas1: [
                {
                    id: "fubu_1",
                    clickImageClass: "fubu-area",
                    highlightImage: body_fubu,
                    text: "腹部",
                    clickTextClass: "fubu-text-area",
                    position: { top: 30, left: 10, width: 20, height: 20 },
                    textPosition: { top: 28, left: 50 },
                },
                {
                    id: "qianbiao_1",
                    clickImageClass: "qianbiao-area",
                    highlightImage: body_qianbiao,
                    text: "浅表介入",
                    clickTextClass: "qianbiao-text-area",
                    // 第一个人体的位置信息
                    position: { top: 6, left: 16, width: 23, height: 11 },
                    textPosition: { top: 7, left: 50 },
                },
                {
                    id: "xinxueguan_1",
                    clickImageClass: "xinxueguan-area",
                    highlightImage: body_xinxueguan,
                    text: "心血管",
                    clickTextClass: "xinxueguan-text-area",
                    // 第一个人体的位置信息
                    position: { top: 19, left: 10, width: 20, height: 10 },
                    textPosition: { top: 17, left: 50 },
                },
            ],
            bodyAreas2: [
                {
                    id: "fubu_2",
                    clickImageClass: "fubu-area",
                    highlightImage: body_fubu,
                    text: "腹部",
                    clickTextClass: "fubu-text-area",
                    position: { top: 30, left: 71, width: 20, height: 13 },
                },
                {
                    id: "qianbiao_2",
                    clickImageClass: "qianbiao-area",
                    highlightImage: body_qianbiao,
                    text: "浅表介入",
                    clickTextClass: "qianbiao-text-area",
                    // 第一个人体的位置信息
                    position: { top: 6, left: 60, width: 23, height: 11  },
                },
                {
                    id: "xinxueguan_2",
                    clickImageClass: "xinxueguan-area",
                    highlightImage: body_xinxueguan,
                    text: "心血管",
                    clickTextClass: "xinxueguan-text-area",
                    // 第一个人体的位置信息
                    position: { top: 19, left: 71, width: 20, height: 10  },
                },
                {
                    id: "fuchan_2",
                    clickImageClass: "fuchan-area",
                    highlightImage: body_fuchan,
                    text: "妇产",
                    clickTextClass: "fuchan-text-area",
                    // 第一个人体的位置信息
                    position: { top: 42, left: 71, width: 20, height: 10  },
                    textPosition: { top: 37, left: 50 },
                },
            ],
            randomBtnAreas:{
                id:"randomPracticeBtn",
                textPosition:{ top: 87, left: 50 },
                clickTextClass: "random-practice-text-area",
            },
            currentHighlight: null,
            currentHighlightText: null,
            mousePosition: {
                x: 0,
                y: 0,
            },
            imageRect: null,
            resizeObserver: null,
            aiBookService: AIBookServiceInstance,
            isImageLoaded: false,
            getAiPracticeCaseLoading: false,
            preloadedImages: [],
        };
    },
    created() {
        this.currentImage = body_base;
        this.preloadImages();
    },
    mounted() {
        this.setupResizeObserver();
        window.addEventListener("resize", this.updateTextPositions);
    },
    beforeDestroy() {
        if (this.resizeObserver) {
            this.resizeObserver.disconnect();
        }
        window.removeEventListener("resize", this.updateTextPositions);
    },
    methods: {
        preloadImages() {
            const images = [
                "/static/resource_pc/images/body_fubu_2.png",
                "/static/resource_pc/images/body_fuchan_2.png",
                "/static/resource_pc/images/body_qianbiao_2.png",
                "/static/resource_pc/images/body_xinxueguan_2.png",
            ];

            images.forEach(src => {
                const img = new Image();
                img.src = src;
                this.preloadedImages.push(img); // 保存预加载的图片引用以防止被垃圾回收
            });
        },
        setupResizeObserver() {
            this.resizeObserver = new ResizeObserver(() => {
                this.updatePositions();
            });
            this.resizeObserver.observe(this.$refs.imageWrapper);
        },
        updatePositions() {
            // 更新文本和区域位置
            this.updateTextPositions();
        },
        updateTextPositions() {
            const img = this.$refs.bodyImage;
            const wrapper = this.$refs.imageWrapper;
            if (!img || !wrapper) {
                return;
            }

            // 等待图片加载完成
            if (!img.complete) {
                img.onload = () => this.calculateImageRect();
                return;
            }

            this.calculateImageRect();
        },
        calculateImageRect() {
            const img = this.$refs.bodyImage;
            const wrapper = this.$refs.imageWrapper;

            const wrapperRect = wrapper.getBoundingClientRect();
            const imgNaturalRatio = img.naturalWidth / img.naturalHeight;
            const containerRatio = wrapperRect.width / wrapperRect.height;

            let imgWidth, imgHeight, offsetX, offsetY;

            if (imgNaturalRatio > containerRatio) {
                // 图片会适应宽度
                imgWidth = wrapperRect.width;
                imgHeight = wrapperRect.width / imgNaturalRatio;
                offsetX = 0;
                offsetY = (wrapperRect.height - imgHeight) / 2;
            } else {
                // 图片会适应高度
                imgHeight = wrapperRect.height;
                imgWidth = wrapperRect.height * imgNaturalRatio;
                offsetX = (wrapperRect.width - imgWidth) / 2;
                offsetY = 0;
            }

            this.imageRect = {
                width: imgWidth,
                height: imgHeight,
                left: offsetX,
                top: offsetY,
            };
        },
        getTextPosition(area) {
            if (!this.imageRect || !area || !area.textPosition) {
                return {};
            }

            const pos = area.textPosition;
            const x = this.imageRect.left + (this.imageRect.width * pos.left) / 100;
            const y = this.imageRect.top + (this.imageRect.height * pos.top) / 100;

            // 计算缩放比例
            const scale = this.calculateScale();

            return {
                position: "absolute",
                left: `${x}px`,
                top: `${y}px`,
                transform: `translate(-50%, -50%) scale(${scale})`,
                transformOrigin: "center center",
            };
        },
        calculateScale() {
            if (!this.imageRect) {
                return 1;
            }

            // 基准尺寸为250x546px
            const baseWidth = 250;
            const baseHeight = 546;

            // 分别计算宽度和高度的缩放比例
            const widthScale = this.imageRect.width / baseWidth;
            const heightScale = this.imageRect.height / baseHeight;

            // 取较小的缩放比例，确保完全适应容器
            // 这样可以避免在任一维度上过度缩放
            const scale = Math.min(widthScale, heightScale);

            // 设置最小和最大限制
            const finalScale = Math.max(
                0.8, // 最小缩放比例
                Math.min(
                    1.5, // 最大缩放比例
                    scale
                )
            );

            return finalScale.toFixed(2); // 保留两位小数，避免过度精确导致的渲染问题
        },
        highlightImageArea(areaId) {
            // 在两个数组中查找区域
            const area = this.findAreaById(areaId);
            if (area) {
                this.currentImage = area.highlightImage;
                this.currentHighlight = areaId;
            }
        },
        highlightTextArea(areaId) {
            // 在两个数组中查找区域
            const area = this.findAreaById(areaId);
            if (area) {
                this.currentHighlightText = areaId;
            }
        },
        findAreaById(areaId) {
            // 在两个数组中查找区域
            return this.bodyAreas1.find(a => a.id === areaId) ||
                   this.bodyAreas2.find(a => a.id === areaId);
        },
        resetImage() {
            this.currentImage = body_base;
            this.currentHighlight = null;
            this.currentHighlightText = null; // 重置文字高亮状态
        },
        async handleAreaClick(areaId) {
            // 提取基本部位名称（不含编号）
            const baseName = areaId.split('_')[0];

            const areaMap = {
                qianbiao: "superficial", // 浅表介入
                fubu: "abdomen", // 腹部
                fuchan: "gyn", // 妇科 (gynecology)
                xinxueguan: "cardio", // 心脏血管 (cardiovascular)
            };

            const res = await this.getAiPracticeCase({
                bodyPart: areaMap[baseName],
            });

            this.$router.push({
                name: "practice_answer_action",
                params: {
                    bodyPart: res.bodyPart,
                    caseID: res._id,
                    content: res.content,
                    questionTips: res.question,
                    title: res.title,
                    practiceType: "bodyPart",
                },
            });
        },
        handleMouseMove(e) {
            const image = this.$refs.bodyImage;
            if (!image) {
                return;
            }

            const rect = image.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            // 计算百分比位置
            this.mousePosition = {
                x: Math.round((x / rect.width) * 100),
                y: Math.round((y / rect.height) * 100),
            };
        },
        handleAreaHover(areaId) {
            // 高亮图片区域
            this.highlightImageArea(areaId);

            // 获取当前区域对象
            const currentArea = this.findAreaById(areaId);

            if (currentArea && areaId.endsWith('_2')) {
                // 如果是右侧区域
                if (currentArea.textPosition) {
                    // 如果有自己的textPosition配置，使用自己的ID
                    this.highlightTextArea(areaId);
                } else {
                    // 如果没有textPosition配置，使用对应左侧区域的ID
                    const baseId = areaId.split('_')[0];
                    const leftAreaId = baseId + '_1';
                    this.highlightTextArea(leftAreaId);
                }
            } else {
                // 左侧区域直接使用自己的ID
                this.highlightTextArea(areaId);
            }
        },
        async randomPractice() {
            console.log("随机练习");
            const res = await this.getAiPracticeCase();
            this.$router.push({
                name: "practice_answer_action",
                params: {
                    bodyPart: res.bodyPart,
                    caseID: res._id,
                    content: res.content,
                    questionTips: res.question,
                    title: res.title,
                    practiceType: "random",
                },
            });
        },
        async getAiPracticeCase(params) {
            this.getAiPracticeCaseLoading = true;
            try {
                const res = await this.aiBookService.getAiPracticeCase(params);
                this.getAiPracticeCaseLoading = false;
                return res;
            } catch (error) {
                this.getAiPracticeCaseLoading = false;
                this.$message.error("获取题目失败");
                throw error;
            }
        },
        handleImageLoad() {
            this.updatePositions();
            requestAnimationFrame(() => {
                this.isImageLoaded = true;
            });
        },
        getAreaPosition(area) {
            if (!this.imageRect || !area || !area.position) {
                return {};
            }

            const pos = area.position;
            const left = this.imageRect.left + (this.imageRect.width * pos.left) / 100;
            const top = this.imageRect.top + (this.imageRect.height * pos.top) / 100;
            const width = (this.imageRect.width * pos.width) / 100;
            const height = (this.imageRect.height * pos.height) / 100;

            return {
                position: "absolute",
                left: `${left}px`,
                top: `${top}px`,
                width: `${width}px`,
                height: `${height}px`,
            };
        },
        async sequencePractice() {
            console.log("顺序练习");
            const params = {
                index: 1,
            };
            const res = await this.getAiPracticeCase(params);
            this.$router.push({
                name: "practice_answer_action",
                params: {
                    bodyPart: res.bodyPart,
                    caseID: res._id,
                    content: res.content,
                    questionTips: res.question,
                    title: res.title,
                    practiceType: "sequence",
                },
            });
        },
    },
};
</script>

<style scoped lang="scss">
@import '@/module/ultrasync_pc/style/aiChat.scss';
.practice-select {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    padding: 16px;
    position: relative; // 保持相对定位
    background: #F0F2F5;
    border-radius: 8px;
    overflow: hidden;
    &-header {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 16px;
    }
    &-title {
        font-size: 16px;
        color: #000;
        position: relative;
        padding-left: 16px;

        &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 16px;
            background: $ai-theme-gradient;
            border-radius: 2px;
        }
    }

    .random-practice-btn {
        @extend .ai-theme-background;
        padding: 10px 24px;
        border-radius: 6px;
        color: white;
        font-weight: 500;
        transition: all 0.3s ease;
        width: 180px;
        position: absolute;
        font-size: 16px;
        bottom: 30px;
        left: 50%;
        transform: translateX(-50%);
    }

    // 修改浮动按钮样式
    .floating-practice-btn {
        @extend .ai-theme-background;
        position: absolute; // 改为绝对定位
        right: 40px;
        bottom: 40px;
        width: 100px; // 稍微调小一点
        height: 100px;
        border-radius: 50%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 6px 16px rgba(33, 150, 243, 0.3);
        z-index: 10; // 降低z-index，但保持在容器内元素之上

        &::before {
            content: '测试';
            font-size: 20px; // 稍微调小字体
            color: white;
            margin-bottom: 4px;
            font-weight: bold;
        }

        &::after {
            content: '专用';
            font-size: 20px;
            color: white;
            font-weight: bold;
        }

        // 悬停效果
        &:hover {
            transform: scale(1.05) rotate(5deg);
            box-shadow: 0 8px 24px rgba(33, 150, 243, 0.4);
        }

        // 点击效果
        &:active {
            transform: scale(0.95);
        }

        // 加载状态
        &.loading {
            pointer-events: none;
            opacity: 0.8;
            &::before {
                content: '加载中';
                animation: loading 1s infinite;
            }
            &::after {
                content: '...';
            }
        }
    }
}
.body-map-container{
    background: #fff;
    border-radius: 8px;
    flex:1;
    min-width: 250px;
    overflow: hidden;
    .body-map {
        width: 100%;
        height: 100%;
        min-width: 250px;
        &-container {
            position: relative; // 确保定位上下文正确
            width: 100%;
            margin: 0 auto;
            flex: 1;
            overflow: visible; // 改为visible以显示浮动按钮
            background-color: #fff;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 24px;
            overflow: hidden;
        }

        position: relative;

        .image-wrapper {
            position: relative;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            display: block;
            visibility: hidden; // 初始隐藏图片
            &.image-loaded {
                visibility: visible; // 加载完成后显示
            }
        }
    }
}


.clickable-area {
    position: absolute;
    cursor: pointer;
    font-size: 16px;
    width: 80px;
    text-align: center;
    display: flex;
    justify-content: center;
}

/* 各个可点击区域的位置 */
%area-base {
    position: absolute;
    cursor: pointer;
    transition: opacity 0.3s;
}

.fubu-area,
.fuchan-area,
.qianbiao-area,
.xinxueguan-area {
    @extend %area-base;
}

/* 添加悬停效果 */
.clickable-area:hover {
    //   background-color: rgba(255, 255, 255, 0.2);
    //   border-radius: 50%;
}

.position-info {
    position: fixed;
    bottom: 20px;
    left: 20px;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 14px;
}

/* 文字区域的基础样式 */
%text-area-base {
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    color: #333;
    font-size: 12px; // 恢复固定字体大小
    white-space: nowrap;
    pointer-events: auto;
    display: flex;
    align-items: center;
    gap: 4px;
    border: 1px solid #dcdfe6;
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

    i {
        font-size: 0.85em;
    }

    &:hover {
        border-color: #409eff;
        color: #409eff;
    }
}

// 左侧文本区域（浅表、腹部）
.fubu-text-area,
.qianbiao-text-area {
    @extend %text-area-base;

    &.highlight {
        border-color: #409eff;
        color: #409eff;
    }
}

// 右侧文本区域（心血管、妇产）
.xinxueguan-text-area,
.fuchan-text-area {
    @extend %text-area-base;

    &.highlight {
        border-color: #409eff;
        color: #409eff;
    }
}

// 添加动画关键帧
@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes loading {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}
</style>

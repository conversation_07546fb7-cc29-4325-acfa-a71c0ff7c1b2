<template>
    <div>
    	<table class="table_view">
            <thead>
                <th v-for="(item,index) of data.head" :key="index">{{item}}</th>
            </thead>
            <tbody>
                <tr v-for="(row,index) of data.body" :key="index">
                    <td v-for="(item,index) of row" :key="index">{{item}}</td>
                </tr>
            </tbody>
        </table>
    </div>
</template>

<script>
import base from '../../lib/base'
export default {
    mixins: [base],
    name: "bi_table_view",
    data: function () {
        return {
        };
    },
    props: {
        data: {
            required: true,
            type: Object,
            default:()=>{
                return {
                    head:[],
                    body:[]
                }
            }
        },
    },
    watch: {
    },
    computed: {},
    mounted() {
    },
    beforeDestroy() {
    },
    methods: {
        
    },
};
</script>

<style scoped lang="scss">
.table_view {
    width: 100%;
    text-align: center;
    // border-collapse: collapse;
    table, th, td {
        // border: 1px solid #666;
    }
    th,td{
        padding: 4px 8px;
    }
}
</style>

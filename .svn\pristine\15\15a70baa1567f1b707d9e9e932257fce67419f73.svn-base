<template>
<div>
	<el-dialog
      class="export_case_dialog"
      :title="lang.export_case"
      :visible="isVisible"
      :close-on-click-modal="false"
      width="90%"
      append-to-body
      v-loading="loading"
      :before-close="back">
      <div class="export_container">
        <table v-loading="loadingData">
            <thead>
                <tr>
                    <th>
                        <el-checkbox v-model="isCheckAll" @change="toggleCheckAll"></el-checkbox>
                    </th>
                    <th>{{lang.case_num}}</th>
                    <th>{{lang.patient_id}}</th>
                    <th>{{lang.submission_time}}</th>
                    <th>{{lang.group_by}}</th>
                    <th>{{lang.patient_name}}</th>
                    <th>{{lang.patient_age}}</th>
                    <th>{{lang.patient_sex}}</th>
                    <th>{{lang.numberOfImages}}</th>
                </tr>
            </thead>
            <tbody>
                <tr v-for="(item,index) of examList" :key="index">
                    <td>
                        <el-checkbox v-model="checkList" :label="item" @change="toggleCheck">{{''}}</el-checkbox>
                    </td>
                    <td>{{item.statusInfo.id}}</td>
                    <td>{{item.patient_id}}</td>
                    <td>{{formatTime(item.statusInfo.create_ts)}}</td>
                    <td>{{item.group_subject[0].group_subject}}</td>
                    <td>{{item.patientInfo.patient_name}}</td>
                    <td>{{item.patientInfo.patient_age}}</td>
                    <td>{{item.patientInfo.patient_sex}}</td>
                    <td>{{item.count}}</td>
                </tr>
            </tbody>
        </table>
        <!-- <el-table :data="examList"
        border
        @selection-change="handleSelectionChange">
        		<el-table-column
  		      type="selection"
  		      width="55">
  		    </el-table-column>
        		<el-table-column align="center" :label="lang.case_num" prop="status_id" show-overflow-tooltip/>
        		<el-table-column align="center" :label="lang.patient_id" prop="patient_id" show-overflow-tooltip/>
        		<el-table-column align="center" :label="lang.upload_datetime" prop="upload_ts" show-overflow-tooltip/>
        		<el-table-column align="center" :label="lang.group_by" prop="group_subject[0].group_subject" show-overflow-tooltip/>
        		<el-table-column align="center" :label="lang.patient_name" prop="patientInfo.patient_name" show-overflow-tooltip/>
  	        <el-table-column align="center" :label="lang.patient_age" prop="patientInfo.patient_age" show-overflow-tooltip/>
  	        <el-table-column align="center" :label="lang.patient_sex" prop="patientInfo.patient_sex" show-overflow-tooltip/>
  	        <el-table-column align="center" :label="lang.numberOfImages" prop="count" show-overflow-tooltip/>
        </el-table> -->
        <div class="footer clearfix">
          <el-pagination
            background
            :current-page="queryForm.pageNo"
            layout="total, sizes, prev, pager, next, jumper"
            :page-size="queryForm.pageSize"
            :total="examListTotal"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            style="margin-top:20px"
          />
        	<el-button
  	        type="primary"
  	        class="fr"
  	        @click="exportHandler"
  	      >
  	        {{lang.confirm_txt}}
  	     </el-button>
        </div>
      </div>
  	</el-dialog>
</div>

</template>
<script>
import base from '../../../lib/base'
import service from '../../../service/multiCenterService.js'
export default {
    name: 'ExportDialogPage',
    mixins:[base],
    props:{
        examList:{
            type:Array,
            default:()=>{
                return []
            },
        },
        queryForm:{
            type:Object,
            default:()=>{
                return{
                    pageNo:1,
                    pageSize: 10,
                }
            }
        },
        examListTotal:{
            type:Number,
            default:0,
        },
        handleCurrentChange:{
            type:Function,
            default:()=>{}
        },
        handleSizeChange:{
            type:Function,
            default:()=>{}
        },
        loadingData:{
            type:Boolean,
            default:false,
        }
    },
    computed:{
        currentMulticenter(){
            return this.$store.state.multicenter.currentMulticenter||{}
        },
    },
    data() {
        return {
            isVisible:false,
            checkList:[],
            loading:false,
            isCheckAll:false,
        }
    },
    methods:{
        init(){
            loading:false;
            this.checkList=[];
            this.isVisible=true;
        },
        back(){
            this.isVisible=false;
        },
        handleSelectionChange(val){
            this.checkList=val
        },
        async exportHandler(){
            let exportExam=[];
            let failExam=[];
            this.loading=true;
            for(let exam of this.checkList){
                if (exam.initImageList) {
                    exportExam=exportExam.concat(exam.image_list)
                }else{
                    try{
                        const res = await service.getExamDetail({
                            mcID:this.currentMulticenter.id,
                            protocolGUID:exam.protocol_guid,
                            statusID:exam.statusInfo.id,
                        })
                        if(res.data.error_code==0){
                            exportExam=exportExam.concat(res.data.data.image_list)
                        }else{
                            failExam.push(exam.patient_id)
                        }
                    }catch(err){
                    	failExam.push(exam.patient_id)
                    }
                }
            }
            this.loading=false;
            if (failExam.length>0) {
                try{
                    const msg=this.lang.export_fail_tip.replace('{1}',failExam.join());
                    const confirmResult=await this.$confirm(msg,this.lang.tip_title,{
                        confirmButtonText:this.lang.confirm_txt,
                        cancelButtonText:this.lang.cancel_btn,
                        type:'warning',
                        closeOnClickModal:false,
                    })
                }catch(err){
                    return ;
                }
            }
            if (exportExam.length===0) {
                this.$message.error(this.lang.export_empty_tip)
                return ;
            }
            const arr=[]
            exportExam.forEach((item)=>{
                let downloadUrl=this.getDownloadUrl(item);
                let surl=window.location.origin;
                if (downloadUrl.indexOf('http')!=0) {
                    downloadUrl=surl+'/'+downloadUrl;
                }
                let json={
                    src:downloadUrl,
                    dest_name:item.patient_id
                }
                arr.push(json);
            })
            if (!this.$store.state.globalParams.isCef) {
                this.$message.error(this.lang.client_only_tip)
                return ;
            }
            this.$root.eventBus.$emit("createDownLoadTask",{
                downLoadUrlList:arr,
                needsInputFileName:false
            })
            this.back();
        },
        toggleCheck(){
            if (this.checkList.length==this.examList.length) {
                this.isCheckAll=true;
            }else{
                this.isCheckAll=false;
            }
        },
        toggleCheckAll(val){
            if (val) {
                this.checkList=[]
                this.examList.forEach(item=>{
                    this.checkList.push(item);
                })
            }else{
                this.checkList=[]
            }
        }
  	}
}
</script>
<style lang="scss">
.export_case_dialog{
    th,td{
        border:1px solid #bbb;
        font-size: 14px;
        padding: 6px;
        text-align: left;
    }
    table{
        color:#333;
        border:1px solid #bbb;
        width:100%;
        border-collapse: collapse;
        margin-bottom:10px;
    }
    .el-dialog{
        height: 90% !important;
        margin-top:5vh !important;
    }
    .el-dialog__body{
    }
    .export_container{
        height:100%;
        overflow:auto;
    }
	.footer{
		margin-top:10px;
	}
}
</style>

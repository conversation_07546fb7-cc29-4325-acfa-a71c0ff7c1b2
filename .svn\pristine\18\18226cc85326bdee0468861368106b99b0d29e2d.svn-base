<template>
    <div class="notify_wrapper">
        <div
            v-if="message.attendee_changed_info && message.attendee_changed_info.attendee_detach_type == 1"
            class="system_notify longwrap"
        >
            <span @click="openVisitingCard(message, 5)">{{ message.attendee_changed_info.nickname }}</span>
            {{ lang.active_exit_group_tip }}
        </div>
        <div
            v-if="message.attendee_changed_info && message.attendee_changed_info.attendee_detach_type == 2"
            class="system_notify longwrap"
        >
            <span
                @click="openVisitingCard(message, 6)"
                v-if="
                    attendeeList &&
                    attendeeList['attendee_' + message.attendee_changed_info.inviter_id]
                "
                >{{
                    attendeeList
                        ? attendeeList["attendee_" + message.attendee_changed_info.inviter_id].nickname
                        : "."
                }}</span
            >
            {{ lang.remove_group_tip1 }}
            <span @click="openVisitingCard(message, 5)">{{ message.attendee_changed_info.nickname }}</span>
            {{ lang.remove_group_tip2 }}
        </div>
    </div>
</template>

<script>
import base from "../../lib/base";
import { openVisitingCard } from "../../lib/common_base";
export default {
    name: "SysJoinAttendeeMsg",
    mixins: [base],
    props: {
        message: {
            type: Object,
            default: () => {
                return {};
            },
        },
        attendeeList: {
            type: Object,
            default: () => {
                return null;
            },
        },
    },
    data() {
        return {
            openVisitingCard,
        };
    },
};
</script>

<style></style>

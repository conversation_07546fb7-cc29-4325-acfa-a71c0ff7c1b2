// dialog.js
let DialogId = null;
import { Dialog } from 'vant';
import Tool from '@/common/tool'
export function openCommonDialog({
    buttons=[],
    title='',
    message='',
    des='common',
    confirm=function(){},
    reject=function(){},
    cancel=function(){},
    id=''
}) {
    const lang=window.vm.$store.state.language
    const isCef  = window.vm.$store.state.globalParams.isCef
    if(!Array.isArray(buttons)){
        return
    }
    DialogId = id||Tool.genID(3)
    let params={
        title:title||lang.tip_title,
        message:message,
        des:des
    }
    if(buttons[0]){
        params.yes_button = buttons[0]
    }
    if(buttons[1]){
        params.no_button = buttons[1]
    }
    if(buttons[2]){
        params.append_button = buttons[2]
    }
    if(isCef){
        Tool.createCWorkstationCommunicationMng({
            name:'ShowConfirmDialog',
            emitName:'NotifyShowConfirmDialogByUser',
            params,
            timeout:null,
            des:'common'
        }).then((res)=>{
            if(res.no === 0){
                confirm()
            }else if(res.no === 1){
                reject()
            }else{
                cancel()
            }
        })
    }else if(Tool.checkAppClient('PCBrowser')){
        window.vm.$MessageBox.confirm(message, title||lang.tip_title, {
            confirmButtonText: buttons[0],
            cancelButtonText:buttons[1],
            showCancelButton:buttons[1]?true:false,
            showConfirmButton:buttons[0]?true:false,
            closeOnClickModal:false,
            customClass:DialogId,
            callback: async (action) => {
                console.error(action)
                if(action==='confirm'){
                    confirm()
                }else if(action === 'cancel'){
                    reject()
                }
            }
        });
    }else{
        if(window.vm&&window.vm.$root&&window.vm.$root.functionalDialog){
            window.vm.$root.functionalDialog.showDialog(
                {
                    title: title||lang.tip_title,
                    message: message,
                    closeOnPopstate: true,
                    showConfirmButton:buttons[0]?true:false,
                    showRejectButton:buttons[1]?true:false,
                    confirmButtonText:buttons[0],
                    rejectButtonText:buttons[1],
                    confirm:()=>{
                        confirm()
                    },
                    reject:()=>{
                        reject()
                    },
                    cancel:()=>{
                        cancel()
                    }
                }
            )
        }else{
            Dialog.confirm({
                title: title||lang.tip_title,
                message: message,
                closeOnPopstate: true,
                showConfirmButton:buttons[0]?true:false,
                showCancelButton:buttons[1]?true:false,
                confirmButtonText:buttons[0],
                cancelButtonText:buttons[1]
            }).then(() => {
                confirm()
            }).catch(() => {
                reject()
            });
        }
    }
    return DialogId
}

export function getCommonDialogId(){
    return DialogId
}

export function openMobileDialog({
    showRejectButton = false,
    showConfirmButton = true,
    showCancelButton = true,
    confirmButtonText = '',
    rejectButtonText = '',
    title = '',
    beforeClose = null,
    confirm = null,
    reject=null,
    cancel=null,
    message='',
    close=null,
    messageAlign='center',
    closeOnClickOverlay=false,
    closeOnPopstate=true,
} = {}){
    if(Tool.checkAppClient('App')||Tool.checkAppClient('MobileBrowser')){

        DialogId = Tool.genID(3)
        if(window.vm&&window.vm.$root&&window.vm.$root.functionalDialog){
            window.vm.$root.functionalDialog.showDialog(
                {
                    showRejectButton,
                    showConfirmButton,
                    showCancelButton,
                    confirmButtonText,
                    rejectButtonText,
                    title,
                    beforeClose,
                    message,
                    close,
                    messageAlign,
                    closeOnClickOverlay,
                    confirm,
                    reject,
                    cancel,
                    closeOnPopstate
                }
            )
        }else{
            const lang=window.vm.$store.state.language
            Dialog.confirm({
                title: title||lang.tip_title,
                message: message,
                closeOnPopstate: false,
                showConfirmButton,
                showCancelButton:showRejectButton,
                confirmButtonText,
                cancelButtonText:rejectButtonText,
            }).then(() => {
                confirm&&confirm()
            }).catch(() => {
                reject&&reject()
                cancel&&cancel()
            });
        }
        return DialogId
    }

}
export function closeAllDialog(){
    if (Tool.checkAppClient('App') || Tool.checkAppClient('MobileBrowser')) {
        if(window.vm&&window.vm.$root&&window.vm.$root.currentDialogList.length>0){
            window.vm.$root.currentDialogList.forEach(item=>{
                item.el.closeDialog()
            })
            window.vm.$root.currentDialogList = []
        }
    }

}
export function closeMobileDialog() {
    // 检查是否为App客户端或MobileBrowser
    if (Tool.checkAppClient('App') || Tool.checkAppClient('MobileBrowser')) {
        try {
            // 确保Vue实例和root对象存在，并且currentDialogList数组有内容
            if (window.vm && window.vm.$root && window.vm.$root.currentDialogList.length > 0) {
                const initialLength = window.vm.$root.currentDialogList.length;

                // 关闭最后一个对话框
                window.vm.$root.currentDialogList[window.vm.$root.currentDialogList.length - 1].el.closeDialog();
                setTimeout(()=>{
                // 如果数组长度未变化，手动移除最后一个数据
                    if (window.vm.$root.currentDialogList.length === initialLength) {
                        window.vm.$root.currentDialogList.pop();
                    }
                },0)

            } else {
                // 如果没有对话框，使用全局Dialog关闭
                Dialog.close();
            }
        } catch (error) {
            console.error('Error closing the mobile dialog:', error);
        }
    }
}

export function closeCommonDialog(){
    const isCef  = window.vm.$store.state.globalParams.isCef
    if(isCef){
        window.CWorkstationCommunicationMng.RemoveConfirmDialog()
    }else{
        closeMobileDialog()
    }
}

export function checkMobileDialogShow(){
    try {
        let isShow = false
        if(Tool.checkAppClient('App')||Tool.checkAppClient('MobileBrowser')){
            // let dialogList = document.querySelectorAll(".common_dialog");
            // let index = -1;
            // for (let i = 0; i < dialogList.length; i++) {
            //     if (getComputedStyle(dialogList[i]).display !== "none" && !dialogList[i].classList.contains('van-dialog-bounce-leave-active')) {
            //         index = i;
            //         break;
            //     }
            // }
            // if (index > -1) {
            //     isShow = true
            // }

            if(window.vm&&window.vm.$root&&window.vm.$root.currentDialogList.length>0){
                isShow = true
            }
        }

        return isShow
    } catch (error) {
        console.error(error)
        return false
    }

}

export function checkMobileCanCloseDialog(){
    try {
        if(Tool.checkAppClient('App')||Tool.checkAppClient('MobileBrowser')){
            if(window.vm&&window.vm.$root&&window.vm.$root.currentDialogList.length>0){
                if(window.vm.$root.currentDialogList[window.vm.$root.currentDialogList.length-1].el.checkCanClose() === false){
                    return false
                }
                return true
            }else{
                return true
            }
        }
        return true
    } catch (error) {
        console.error(error)
        return true
    }

}

export function checkMobileCanCloseOnPopstate(){
    try {
        if(Tool.checkAppClient('App')||Tool.checkAppClient('MobileBrowser')){
            if(window.vm&&window.vm.$root&&window.vm.$root.currentDialogList.length>0){
                if(window.vm.$root.currentDialogList[window.vm.$root.currentDialogList.length-1].el.checkCanCloseOnPopstate() === false){
                    return false
                }
                return true
            }else{
                return true
            }
        }
        return true
    } catch (error) {
        console.error(error)
        return true
    }
}


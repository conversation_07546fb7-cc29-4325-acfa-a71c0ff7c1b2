<template>
    <transition name="slide">
        <div class="reset_mobile_page fourth_level_page">
            <mrHeader>
                <template #title>
                    {{resetTitle}}
                </template>
            </mrHeader>
            <div class="reset_mobile_container">
                <div v-if="type==1">
                    <div class="step_tip">{{lang.reset_mobile_tip_reset_mobile}}</div>
                    <div>
                        <mobile-international :mobile.sync="cellphone" :internationalCode.sync="nationalCode" ref="register_mobile_international"></mobile-international>
                        <template v-if="showQuerySmsVerificationCodeButton">
                            <div class="sms_verification_father">
                                <input  type="tel" v-model="verifyCode" class="commont_input get_sms_code_input" maxlength="6" :placeholder="lang.sms_verification_code">
                                <van-button v-show="count==0" color="#00c59d" class="get_sms_verification_code_btn"  @click.stop="loginByTracelessValid('mobile')">{{lang.forget_password_getcode}}</van-button>
                                <van-button v-show="count!=0" color="#00c59d" class="get_sms_verification_code_btn" disabled >{{count}}</van-button>
                            </div>
                        </template>
                    </div>
                </div>
                <div v-else-if="type==2">
                    <div class="step_tip">{{lang.reset_email_tip}}</div>
                    <input type="text" v-model="email" :placeholder="lang.register_email" class="commont_input" >
                    <template v-if="showQuerySmsVerificationCodeButton">
                        <div class="sms_verification_father">
                            <input  type="tel" v-model="verifyCode" class="commont_input get_sms_code_input" maxlength="6" :placeholder="lang.email_verification_code">
                            <van-button v-show="count==0" color="#00c59d" class="get_sms_verification_code_btn"  @click.stop="loginByTracelessValid('email')">{{lang.forget_password_get_email_code}}</van-button>
                            <van-button v-show="count!=0" color="#00c59d" class="get_sms_verification_code_btn" disabled >{{count}}</van-button>
                        </div>
                    </template>
                </div>
                <button class="primary_bg reset_btn" v-loading="loading" @click="resetAction">{{lang.confirm_txt}}</button>
                <ValidateCaptcha name="ResetMobile" ref='validateCaptcha'></ValidateCaptcha>
            </div>
        </div>
    </transition>
</template>
<script>
import base from '../lib/base'
import service from '../service/service'
import Tool from '@/common/tool.js'
import { Toast, Button } from 'vant';
import mobileInternational from '../components/mobileInternational.vue'
import ValidateCaptcha from '../MRComponents/ValidateCaptcha.vue'
export default {
    mixins: [base],
    name: 'ResetMobile',
    components: {
        mobileInternational,
        ValidateCaptcha,
        VanButton: Button,
    },
    data(){
        return {
            cellphone:'',
            email:'',
            nationalCode:'',
            verifyCode:'',
            count:0,
            countTimer:'',
            type:1,
            accountType:'',
            loading:false,
            resetTitle:'',
        }
    },
    computed:{
        showQuerySmsVerificationCodeButton() {
            let is = false;
            if (this.user && this.user.enable_sms_identification) {
                is = true;
            }
            return is;
        }
    },
    mounted(){
        this.type=this.$route.params.type;
        if (this.type==1) {
            this.resetTitle=this.lang.reset_mobile;
            this.accountType='mobile'
        }else if (this.type==2) {
            this.resetTitle=this.lang.reset_email_title;
            this.accountType='email'
        }
    },
    methods:{
        getVerifyCode(accountType,afsCode){
            this.accountType=accountType;
            let verifyType='getVerityCodeToMobile';
            let params={}
            if (accountType==='mobile') {
                verifyType='getVerityCodeToMobile';
                params={
                    mobile:this.cellphone,
                    countryCode:this.nationalCode,
                    type:'bindAccount',
                    afsCode:afsCode
                }
            }else{
                verifyType='getVerityCodeToEmail';
                params={
                    email:this.email,
                    afsCode:afsCode,
                    type:'bindAccount',
                }
            }
            service[verifyType](params).then((res)=>{
                if (res.data.error_code===0) {
                    this.count=90;
                    this.countTimer=setInterval(()=>{
                        if (this.count>0) {
                            this.count-=1
                        }else{
                            this.count=0;
                            clearInterval(this.countTimer)
                        }
                    },1000)
                }
            })
        },
        validateMobileOrEmail(){
            let result=true;
            if (this.accountType==='mobile') {
                if (this.cellphone.trim().length === 0){
                    Toast(this.lang.login_mobile_phone_empty);
                    result=false;
                    return result;
                }
                let mobileValidate=this.$refs.register_mobile_international.validate()
                if (!mobileValidate){
                    // Toast(mobileValidate.tip);
                    result=false;
                    return result;
                }
            }else{
                if (!Tool.isEmail(this.email)) {
                    Toast(this.lang.email_is_invalid_input_again);
                    result=false;
                    return result;
                }
            }
            return result;
        },
        resetAction(){
            if (!this.validateMobileOrEmail()) {
                return ;
            }
            if(this.verifyCode.length === 0){
                Toast(this.lang.please_enter_code);
                return ;
            }
            if (this.loading) {
                return ;
            }
            this.loading=true;
            let safeKey = window.vm.$store.state.dynamicGlobalParams.safeKey;
            if (safeKey === '') {
                Toast(this.lang.verify_expired)
                this.back();
                return;
            }
            let params={
                accountType:this.accountType,
                code:this.verifyCode,
                safeKey:safeKey,
            }
            if (this.accountType=='mobile') {
                params.account=this.cellphone;
                params.countryCode=this.nationalCode;
            }else if (this.accountType=='email') {
                params.account=this.email;
            }
            service.bindSocialAccount(params).then((res)=>{
                this.loading=false;
                if (res.data.error_code==0) {
                    // this.$root.eventBus.$emit('reset_mobile', {
                    //     accountType:this.accountType,
                    //     mobile_phone:this.cellphone,
                    //     international_code:this.nationalCode,
                    //     email:this.email
                    // });
                    if (this.accountType=='mobile') {
                        this.$store.commit('user/updateUser',{
                            international_code:this.nationalCode,
                            mobile_phone:this.cellphone
                        });
                        Toast(this.lang.reset_mobile_success)
                    } else if (this.accountType=='email') {
                        this.email = this.email;
                        this.$store.commit('user/updateUser',{
                            email:this.email
                        });
                        Toast(this.lang.reset_email_success)
                    }
                    this.back()
                }else{
                    this.$store.commit('dynamicGlobalParams/updateDynamicGlobalParams',{
                        isSafeAuth:false
                    })
                    this.back();
                }
            })
        },
        loginByTracelessValid:Tool.debounce(async function (login_type) {
            if(login_type == 'mobile'){
                if (!this.validateMobileOrEmail()) {
                    return ;
                }
            }
            if(login_type == 'email'){
                if (!this.validateMobileOrEmail()) {
                    return ;
                }
            }
            const afsCode = await this.$refs['validateCaptcha'].validateCaptcha()
            this.successVerifyByTraceless(afsCode)
        },500,true),
        successVerifyByTraceless(afsCode){
            if(this.type == 1){
                this.getVerifyCode('mobile',afsCode)
            }
            if(this.type == 2){
                this.getVerifyCode('email',afsCode)
            }
        }
    }
}
</script>
<style lang="scss">
.reset_mobile_page{
    background-color: #fff;
    .reset_mobile_container{
        margin:1rem;
        .sms_verification_father{
            position:relative;
            .svg_wrap{
                position:absolute;
                right:0;
                top:0;
                & > i {
                    width:4rem;
                    height:2rem;
                }
            }
        }
        .get_sms_verification_code_btn{
            position:absolute;
            right:0rem;
            bottom:0.3rem;
            display: block;
            min-width: 50%;
            border: none;
            font-size: 0.8rem;
            line-height: 1.5rem;
            height:2rem;
            margin: .5rem 0 .6rem;
            border-radius: .2rem;
            margin:auto;
        }
        .reset_btn{
            display: block;
            width: 100%;
            border: none;
            font-size: 1rem;
            line-height: 2rem;
            margin: 1rem 0 .6rem;
            border-radius: .2rem;
        }
    }
}
</style>

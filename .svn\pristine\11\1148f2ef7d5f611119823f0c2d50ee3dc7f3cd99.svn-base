<template>
    <transition name="slide">
        <div class="reserved_conference_page fourth_level_page">
            <mrHeader>
                <template #title>
                    {{lang.reserved_conference}}
                </template>
                <template #right>
                    <span class="add_reserved_btn" @click="addReserved">{{lang.add_reserved_conference}}</span>
                </template>
            </mrHeader>
            <div class="container">
                <div class="reserved_conference_list">
                    <div v-for="reserved of conferencePlanList" class="reserved_conference_item" :key="reserved.id">
                        <div class="item_info">
                            <p class="subject">{{lang.reserved_conference_subject}}：{{reserved.subject}}</p>
                            <p class="time">{{lang.reserved_conference_date}}：{{reserved.reservedDate}}</p>
                            <p class="time">{{lang.reserved_conference_time}}：{{reserved.reservedTime}}</p>
                        </div>
                        <div class="item_actions">
                            <i class="iconfont icon-share-1-copy" @click="shareReserved(reserved)"></i>
                            <i v-if="reserved.creator_id==user.uid" @click="deleteReserved(reserved)" class="iconfont icon-icon-1"></i>
                        </div>
                    </div>
                    <!-- <p class="empty_tip" >{{lang.no_data_txt}}</p> -->
                    <van-empty :description="lang.no_data_txt" v-if="conferencePlanList.length==0"/>
                </div>
            </div>
            <router-view></router-view>
                        <CommonDialog v-model="showQrCodeDialog" :title="qrCodeDialogTitle" :showConfirmButton="false" :closeOnClickOverlay="true" class="qr_code_dialog">
                <div id='reserved_conference_qr_code' class="qr_code"></div>
            </CommonDialog>
        </div>
    </transition>
</template>
<script>
import base from '../lib/base'
import Tool from '@/common/tool.js'
import {Dialog,Empty} from 'vant';
import CommonDialog from '../MRComponents/commonDialog.vue'
import share_to_wechat from '../lib/share_to_wechat.js'
export default {
    mixins: [base,share_to_wechat],
    name: 'reserved_conference',
    components: {
        CommonDialog,
        VanEmpty:Empty
    },
    data(){
        return {
            cid:this.$route.params.cid,
            channelId:0,
            showQrCodeDialog:false,
            qrCodeDialogTitle:''
        }
    },
    beforeDestroy(){
    },
    activated(){
        this.cid=this.$route.params.cid
    },
    created(){
    },
    mounted(){
        this.$nextTick(()=>{
        })
    },
    computed:{
        conversation(){
            return this.conversationList[this.cid]||{}
        },
        controller(){
            return this.conversation.socket;
        },
        conferencePlanList(){
            let list=this.parseObjToArr(this.conversation.conferencePlanList||[]);
            this.parseReservedList(list)
            return list;
        }
    },
    methods:{
        parseReservedList(list){
            for(let reserved of list){
                reserved.reservedDate=reserved.begin_ts.split(" ")[0]
                reserved.reservedTime=reserved.begin_ts.split(" ")[1]+' - '+reserved.end_ts.split(" ")[1]
            }
            //按开始时间排序
            list.sort((a,b)=>{
                if (new Date(a.begin_ts).valueOf()>new Date(b.begin_ts).valueOf()) {
                    return -1
                }else{
                    return 0
                }
            })
        },
        deleteReserved(reserved){
            Tool.openMobileDialog(
                {
                    message:this.lang.delete_reserved_tip,
                    showRejectButton:true,
                    confirm:()=>{
                        this.controller.emit('del_conference_plan',{
                            conference_id:reserved.conference_id
                        },(is_succ,data)=>{
                            this.loading=false
                            if (is_succ) {
                                this.$store.commit('conversationList/delConferencePlan', data);
                            }
                        })
                    }
                }
            )
        },
        getChannelIdByGroupId(){
            return new Promise((resolve,reject)=>{
                window.main_screen.getChannelIdByGroupId({groupId:this.cid},(res)=>{
                    this.channelId  = res.data
                    resolve(res.data)
                })
            })

        },
        getQrcodeAddress(){
            let serverInfo=this.systemConfig.serverInfo;
            let channelInfo = `channel_id=${this.channelId}`
            let groupInfo = `group_id=${this.cid}`
            let str = window.btoa(`${channelInfo}#####${groupInfo}`)
            let url = this.systemConfig.server_type.protocol + this.systemConfig.server_type.host + this.systemConfig.server_type.port
            return Tool.transferLocationToCe(`${url}/activity/activity.html#/webLive/${str}`)
        },
        setQrCode(){
            this.$nextTick(()=>{
                document.getElementById("reserved_conference_qr_code").innerHTML=''
                var qrcode = new window.QRCode(document.getElementById("reserved_conference_qr_code"), {
                    text: this.getQrcodeAddress(),
                    width : 200,
                    height : 200
                });
            })
        },
        async shareReserved(reserved){
            if(!this.channelId){
                await this.getChannelIdByGroupId()
            }
            // console.error('****************reservedConference-shareReserved',this.osName)
            if(Tool.checkAppClient('Android')&&!Tool.checkAppClient('Browser')){
                this.shareLinkToWeChat({
                    href:this.getQrcodeAddress(),
                    title:this.lang.reserved_conference,
                    content:reserved.subject
                })
            }else{
                this.qrCodeDialogTitle = reserved.subject;
                this.showQrCodeDialog = true
                this.setQrCode()
            }
        },
        addReserved(){
            this.$router.push(`/index/chat_window/${this.cid}/group_setting/reserved_conference/add`);
        }
    }
}

</script>
<style lang="scss">
.reserved_conference_page{
    .add_reserved_btn{
        width: 4rem;
        color: #fff;
        font-size: 0.8rem;
        text-align: right;
    }
    .container{
        background:#f7f8fa;
        flex: 1;
        overflow: auto;
        padding: 0.8rem;
        .reserved_conference_list{
            color:#333;
            .reserved_conference_item{
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 1rem;
                background-color: #fff;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
                margin-bottom: 0.8rem;

                .item_info {
                    .subject{
                        font-size: 0.9rem;
                        font-weight: 500;
                        color: #323233;
                        margin-bottom: 0.5rem;
                    }
                    .time {
                        font-size: 0.75rem;
                        color: #969799;
                        line-height: 1.5;
                    }
                }

                .item_actions {
                    display: flex;
                    align-items: center;
                    color: #666;
                    i {
                        cursor: pointer;
                        font-size: 1.1rem;
                        &:not(:last-child) {
                            margin-right: 1rem;
                        }
                    }
                }
            }
        }
    }
    .qr_code_dialog{
        display: flex;
        justify-content: center;
        padding: 40px 0;
    }
}
</style>

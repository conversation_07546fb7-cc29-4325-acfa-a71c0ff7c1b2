const path = require("path");
const glob = require("glob");
const fs = require("fs");
const mode = process.env.MODE || 'prod';
const isProduction = mode === 'prod';
let argv = process.argv
let project_nov = 'CN'
if(argv.includes('ce')||argv.includes('productionCE')){
    project_nov = 'CE'
}

// 自定义不同模块的页面 title
const titleMap = {
    CN:{
        ultrasync: "瑞影云++",
        ultrasync_pc: "瑞影云++",
        audit: "audit",
        activity: "瑞影云++",
        whiteboard: "瑞影云++白板",
        // statistic: "云端统计",
    },
    CE:{
        ultrasync: "MiCo+",
        ultrasync_pc: "MiCo+",
        audit: "audit",
        activity: "MiCo+",
        // statistic: "Cloud Statistics",
    }

};

function getPages(globPath) {

    const pages = {};
    glob.sync(globPath).forEach((item) => {

        const stats = fs.statSync(item);
        if (stats.isDirectory()) {
            const basename = path.basename(item, path.extname(item));

            let filename = basename
            if(isProduction&&basename==='statistic'){
                // filename = 'ultrasync_offline'
                return;
            }
            pages[basename] = {
                entry: `${item}/${basename}.js`,
                title: titleMap[project_nov][basename] || "default",
                template: `${item}/${basename}.html`,
                // 这行代码很重要
                // 兼容开发和生产时 html 页面层级一致
                filename: `${filename}.html`,
            };
        }

    });
    return pages;
}

const pages = getPages(path.join(__dirname, "../src/module/*"));
module.exports = pages;

<template>
    <transition name="slide">
        <div class="iworks_statistics_page second_level_page">
            <mrHeader @click-left="closeIWorksStatistics">
                <template #title>
                    {{lang.cloud_statistics_iworks}}
                </template>
            </mrHeader>
            <div class="container">
                <iframe :src="iworksStatisticsUrl" id="iworks_statistics_iframe" class="iframe_content"></iframe>
            </div>
        </div>
    </transition>
</template>
<script>
import base from '../lib/base'
import { Toast } from 'vant';
export default {
    mixins: [base],
    name: 'iworks_statistics',
    components: {},
    data(){
        return {
            historylen:history.length,
            iworks_statistics_url:"",
            currentGroupsetId:this.$route.params.id
        }
    },
    beforeDestroy(){
    },
    mounted(){
        this.$nextTick(()=>{
            this.getStatisticsCommand();
            this.$root.eventBus.$off('close_iworks_statistics').$on('close_iworks_statistics',this.closeIWorksStatistics);
            window.addEventListener('message', this.onWindowMessage, false);
        })
    },
    computed:{
        iworksStatisticsUrl() {
            if (this.iworks_statistics_url) {
                return this.iworks_statistics_url
            }

            return "about:blank";
        },
        // currentGroupsetId(){
        //     let groupset_id = 0;
        //     if (this.$store.state.chatList.currentGroupset) {
        //         groupset_id = this.$store.state.chatList.currentGroupset.id || 0;
        //     }
        //     return groupset_id;
        // },
    },
    methods:{
        closeIWorksStatistics(){
            window.removeEventListener('message', this.onWindowMessage, false);
            this.onIworksStatisticsLoaded();

            let len = this.historylen - history.length - 1;
            console.log("*********************closeIWorksStatistics:go:",len);
            this.$router.go(len);
        },
        getStatisticsCommand(){
            let controller = window.main_screen.controller;
            if (controller) {
                controller.emit("get_statistics_command",{groupset_id:this.currentGroupsetId},(err,result)=>{
                    console.log("[event] callback in get_statistics_command",err,result);

                    if (err) {
                        //失败
                        Toast(this.lang.cloud_statistics_iworks_get_token_err);
                    } else {
                        if (result.iworks_statistics_url) {
                            const lang = window.localStorage.getItem('lang') || 'CN'
                            this.iworks_statistics_url = result.iworks_statistics_url + `&lang=${lang}`;

                            this.iworks_statistics_timer = setTimeout(()=> {
                                this.getStatisticsCommand();
                            }, 10000);
                        } else {
                            //失败
                            Toast(this.lang.cloud_statistics_iworks_get_token_err);
                        }
                    }
                });
            } else {
                Toast(this.lang.cloud_statistics_iworks_get_token_err);
            }
        },
        onWindowMessage(event) {
            console.log("window.addEventListener-message:iwork statistics");

            var option = {};
            try{
                option = JSON.parse(event.data);
            } catch (e) {
                option = {};
            }

            if ("iworks_statistics_loaded" == option.message) {
                this.onIworksStatisticsLoaded();
            } else  if ("iworks_statistics_closed" == option.message) {
                this.closeIWorksStatistics();
            }
        },
        onIworksStatisticsLoaded(){
            if (this.iworks_statistics_timer) {
                clearTimeout(this.iworks_statistics_timer);
            }
        },
        openMenu() {
            document.getElementById("iworks_statistics_iframe").contentWindow.postMessage(JSON.stringify({message:'open_menu'}),'*');
        }
    }
}

</script>
<style lang="scss">
.iworks_statistics_page{
    .container{
        padding:0rem 0rem;
        background:#fff;

        width:100%;
        height:100%;

        .iframe_content{
            width:100%;
            height:100%;
        }
    }
}
</style>

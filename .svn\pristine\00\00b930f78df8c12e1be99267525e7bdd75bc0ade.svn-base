import Vue from 'vue'
import ServiceConfig from '@/common/ServiceConfig.js'
import regionConfig from '@/common/regionConfig.js'
import proxyConfig from '../../../../../config/proxy'
import cloneDeep from 'lodash/cloneDeep'
import Tool from '@/common/tool'
const host = proxyConfig.env.includes('https://') ? proxyConfig.env.split('https://')[1] : proxyConfig.env.split('http://')[1]
const initState = {
    clientType:1,//1,PC浏览器,2,工作站,3,会诊端,4,app,5,手机浏览页面
    //服务器类型
    server_type: {
        protocol: "https://",
        host: host,
        port:":443",
        enable_sms_identification: false,
        websocket_protocol: "wss://"
    },
    ServiceConfig:ServiceConfig,
    regionConfig:regionConfig,
    //客户端类型
    client_type: {
        Client: 1,
        AppWorkstation: 2,
        AppClient: 3,
        AppMobile:4,
        MobileBrowser: 5,
        Doppler:6,
        AppUltraSyncBox:7,
        AppPad:8,
        UltraSoundMobile:9,
        DR:10,
    },
    //会话启动类型
    start_type: {
        Default: 0,
        Gallery: 1,
        UltrasoundDesktop:2,
        VoiceSession:3,
        HistoryChatMessage:4,
        SendTo:5,
        TransmitConsultationFile:6,
        RequestMonitorWall:7,
        MonitorWall:8,
        Comment:9,
        RequestUltrasoundDesktop:10,
        RequestUltrasoundDesktopByMonitorWall:11,
        KickoutAttendee:12,
        NewChatMessage:13,
        RequestStartDeviceUltrasoundDesktop:14,
        RequestStopDeviceUltrasoundDesktop:15,
        RequestDeleteChatMessages:16,
        NotifyDeleteChatMessages:17
    },
    role: {
        Applicant: 0,       //还没有通过审核
        Member: 1,       //普通成员
        Admin: 2,        //管理员
        SuperAdmin: 3,   //超级管理员
        Director: 4    //主任
    },
    groupRole: {
        normal:0,
        manager:1,
        creator:2,
    },
    start_ultrasound_desktop_timeout: 28800000,
    ConversationConfig:{
        type: {
            Single:1,
            Group:2,
            GroupSet:3
        },
        //会话模式：群聊、单聊
        mode: {
            Group: 0,
            Single: 1
        },
        share_img_size: 800 * 1024,
    },
    groupPublicState: {
        Private:0,      //私有群
        Public:1,       //公开群
        SemiPublic:2    //半公开群
    },
    joinGroupState:{
        unapply:0,//申请入群状态
        applyed:1,
        joined:2,
        rejected:3
    },
    ScanRoomConfig:{
        data_source_type:{
            Unknown:0,
            UltrasoundDesktop:1,
            LocalDesktop:2,
            CameraDesktop:3
        }
    },
    //会话参与者状态
    attendee_state: {
        Exit:0,    // 退出
        Enter:1,   // 加入
        Temp:2,    // 临时
        Applying:3 // 申请中
    },
    consultationImageShowNum:36,//一次获取图像列表的数量
    historyMessageNum:20, //一次获取消息记录条数
    //消息类型
    msg_type: {
        Text: 0,
        Image: 1,
        File: 2,
        Frame: 3,
        Cine: 4,
        Sound: 5,
        RealTimeVideo: 6,
        Video:7,
        RealTimeVideoReview:8,
        MedicRealTimeVideoReview:9,//预留类型，实时回放入库后类型

        SYS_START_RT_VOICE:11,
        SYS_STOP_RT_VOICE:12,
        SYS_START_REALTIME_CONSULTATION:13,
        SYS_STOP_REALTIME_CONSULTATION:14,
        SYS_START_REALTIME_CONFERENCE:15,
        SYS_STOP_REALTIME_CONFERENCE:16,
        SYS_JOIN_ATTENDEE:18,
        SYS_KICKOUT_ATTENDEE:19,
        WITHDRAW:20,
        SYS_CONFERENCE_PLAN:25,
        VIDEO_CLIP:26,
        COMMENT:30,
        TAG:31,
        LIVE_INVITE:32,
        IWORKS_PROTOCOL:41,
        EXAM_IMAGES:42,
        HISTORY_TIP:71,//前端历史消息提示类型
        AI_ANALYZE:100,
        IWORKS_SCORE:101,
        EXPIRATION_RES:201 // 前端定义：引用失效资源
    },
    file_type:{
        DCM:'DCM'
    },
    //下载器配置
    downLoad: {
        //任务池-还没有下载的任务
        Queue : [],
        //任务池-正再下载的，或者下载完的任务
        DownQueue : [],
        //当前正在下载的任务数量
        CurrentTaskCount : 0,
        //最大并发下载数量
        MaxTaskCount : 1,
        //正在下载,有重复的图片
        DownLoadImgDuplication : [],
    },
    //画廊图像模式
    gallery_image_mode: {
        None: 0,
        Image: 1,
        Video: 2,
        Frame: 3,
        Cine: 4,
        RealTimeVideo:6
    },
    start_catch_type: {
        UltrasoundDesktop: 1,
        MonitorWall: 2,
        CameraVideoEx: 3,
        StorageConsultationFile: 4,
        LocalDesktop: 5,
        CameraDesktop: 6,
    },
    realtimeModeInfo:{
        SecondaryFlowPushEnable:1
    },
    VideoType: ["mp4", "avi", "mov", "quicktime",'rmvb','mkv','mtv','wmv'],
    voice_identity:{
        start: 0,
        accept: 1,
    },
    voice_state_start:{
        none: 0,
        starting: 1,
        start: 2,
        closing: 3
    },

    voice_state_accept:{
        none: 0,
        unaccept: 1,
        accepting: 2,
        accept: 3,
        closing:4
    },

    voice_connect_type:{
        none: 0,
        webrtc: 1,
        rtmp: 2,
        aliyun:3
    },

    voice_ctrl_mode: {
        freedom:0, //自由发言
        control:1  //发言控制
    },
    //设定群多大人数为会议模式
    group_mode_num: 10,

    //默认会议模式下，能有几个人发言
    //暂时不控制，设置成最大
    group_can_speek_num: 10000,

    rt_video_state: {
        None: 0,
        Start: 1,
        Enter: 2,
        EnterSelf: 3
    },

    rt_voice_state: {
        None: 0,
        Start: 1,
        Enter: 2,
        EnterSelf: 3
    },

    voice_operate_type: {
        None: 0,
        can_speak: 1,
        mute: 2,
        hand: 3,
        hand_down: 4
    },

    AudioDeviceCheck: {
        enum_exception: 1,
        no_input_output_device: 2,
        no_output_device: 3,
        no_input_device: 4,
        has_mult_input_output_device: 5,
    },

    //控制设备的类型
    ctrl_device_type:{
        Mobile: 1,  //移动端
        HandShank:2, //手柄
    },

    //第三方账户来源
    account_type:{
        WebIM:0,
        WECHAT:1,
        QQ:2,
        WEIBO:3,
        PACS:4,
        MDT:5
    },

    DeviceInfoUpdateType:{
        cur_session: 1,
        ftp_info: 2,
    },

    serverInfo:{
        g_attachment_storage_type:1,
        network_environment: 1,
        EnablePlayback:false,
        EnableVideoTimeShift:false,
        video_server_type: "Nginx",
        record_video_server_type: "Nginx",
        client_send_chat_message_retry_timeout:60000,
        sound_record_timeout:60000,
        sound_server_listen_port:8845,
        webrtc_addr: Tool.getHostConfig().dev,
        webrtc_port: 8881,
        VideoServerAddr: Tool.getHostConfig().dev,
        RtmpVideoServerPort: 8554,
        rtmp_video_server_port:8935,
        RtmpVideoServerChannel: "mrlive",
        RtmpVideoServerChannelNoRecord: "tvwall",
        RtmpVideoServerCDN: "video-center.alivecdn.com",
        oss_attachment_server:{
            access_key_id:"",
            access_key_secret:"",
            bucket:"rmtus-attachment",
            end_point:"oss-cn-shanghai.aliyuncs.com",
            endpoint:"https://oss-cn-shanghai.aliyuncs.com",
            location:"oss-cn-shanghai",
            playback_http_addr:"http://rmtus-attachment.mindray.com",
            sub_dir:"upload"
        },
        rtc_voice_type:"Webrtc",
        enable_mute_close_link:false,
        not_support_aliRtc_hospital_id_set:"",
        enable_record_mode_for_default:false,

        enable_edge_push:false,
        rtmp_video_server_edge_pull:"",
        rtmp_video_server_edge_push:"",
        enable_ai_analyze:false,

        audio_video_separate:false,
        SupportFileType: [],
        enable_verification_code:false,
        enable_statistics:false,
        msg_withdrawal_max_time:180000, // 毫秒
        voice_config:{
            FreeSpeakLimitUserCount:10,
            DefaultForbiddenLimitUserCount:6
        },
        agora_appid:0
    },
    conference_plan_tip_type:{
        New:1,
        Prepare:2,
        Cancel:3
    },
    cloudInfo:{},
    appVersion:'Test',
    cloudConfig:{
        Prod:{
            '云++':{
                protocol: "https://",
                host: "consult.mindray.com",
                port:":443",
                enable_sms_identification: true,
                websocket_protocol: "wss://",
                websocket_prot:':443'
            },
        },
        Beta:{
            '云++':{
                protocol: "https://",
                host: Tool.getHostConfig().beta,
                port:":443",
                enable_sms_identification: true,
                websocket_protocol: "wss://",
                websocket_prot:':443'
            },
        },
        Dev:{
            '云++':{
                protocol: "https://",
                host: Tool.getHostConfig().dev,
                port:":443",
                enable_sms_identification: true,
                websocket_protocol: "wss://",
                websocket_prot:':443'
            },
        },
        Web:{
            '云++':{
                protocol: "",
                host: "",
                port:"",
                enable_sms_identification: true,
                websocket_protocol: "wss://",
                websocket_prot:':443'
            },
        }
    },
    multiCenter:{
        port:8910,
        role:{},
        exam_status:{
            unsubmit:0,
            saved:1,
            submited:2,
            reject:3,
            assigned:4,
            review:5,
            judge:6
        }
    },
    userStatus:{
        Apply: 0,//试用期
        Pass: 1,//正式用户
        Deny: 2,//禁用?
        Undetermined: 3, // 待审核
        Destroy: 4 // 用户注销
    },
    liveManagement:{
        waiting:1,
        starting:0,
        end:2,
        cancel:3
    },
    maxShareNumber:99,
}
export default {
    state:cloneDeep(initState),
    mutations: {
        resetStore(state){
            state = cloneDeep(initState)
            Vue.set(window.vm.$store.state,'systemConfig',cloneDeep(initState))
        },
        updateSystemConfig(state, valObj) {
            for (let key in valObj) {
                Vue.set(state,key,valObj[key])
            }
        }
    },
    actions: {},
    getters: {}
}

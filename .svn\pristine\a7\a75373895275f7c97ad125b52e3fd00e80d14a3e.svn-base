<template>
    <CommonDialog
        class="privacy_settings_page"
        :title="lang.privacy_policy"
        :show.sync="dialogVisible"
        width="600px"
        :close-on-click-modal="false"
        @closed="handleClose"
        :footShow="false"
    >
        <div class="privacy_settings_container">
            <!-- 隐私协议版本历史 -->
            <div class="version_history_section">
                <div class="section_title">{{ lang.privacy_policy_historical_versions }}</div>
                <div class="version_list">
                    <div
                        v-for="(version, index) in privacyVersions"
                        :key="version.version"
                        class="version_item"
                        @click="viewPrivacyVersion(version)"
                    >
                        <div class="version_info">
                            <span class="version_number">
                                {{version.version}}
                                <span v-if="index === 0" class="latest_tag">{{ lang.latest_version }}</span>
                            </span>
                            <span class="version_date">{{version.date}}</span>
                        </div>
                        <i class="el-icon-arrow-right"></i>
                    </div>
                </div>
            </div>

            <!-- 撤销隐私协议同意按钮 - 固定在底部 -->
            <div class="revoke_section" v-if="shouldShowRevokeButton">
                <el-button
                    type="default"
                    class="revoke_button"
                    @click="showRevokeConfirm"
                >
                    {{ lang.revocation_privacy_agreement_consent }}
                </el-button>
            </div>
        </div>
    </CommonDialog>
</template>

<script>
import base from '../lib/base'
import Tool from '@/common/tool'
import CommonDialog from '../MRComponents/commonDialog.vue'

export default {
    mixins: [base],
    name: 'privacySettings',
    components: {
        CommonDialog,
    },
    props: {
        show: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            privacyVersions: [],
        }
    },
    computed: {
        dialogVisible: {
            get() {
                return this.show;
            },
            set(val) {
                if (!val) {
                    this.$emit('closePrivacySettings');
                }
            }
        },
        // 判断是否显示撤销按钮，login路由下不显示
        shouldShowRevokeButton() {
            return !this.$route.path.includes('/login/');
        },
    },
    mounted() {
        // 初始化隐私协议版本历史
        this.initPrivacyVersions();
    },
    methods: {
        // 初始化隐私协议版本历史
        initPrivacyVersions() {
            const isCNEnvironment = process.env.VUE_APP_PROJECT_NOV === "CN";
            if (isCNEnvironment) {
                this.privacyVersions = [
                    {
                        version: '2.0',
                        date: '2025-01-07',
                        env: 'CN'
                    },
                    {
                        version: '1.0',
                        date: '2021-11-01',
                        env: 'CN'
                    }
                ];
            } else {
                this.privacyVersions = [
                    {
                        version: '2.0',
                        date: '2025-07-30',
                        env: 'CE'
                    },
                    {
                        version: '1.0',
                        date: '2024-06-27',
                        env: 'CE'
                    },
                    {
                        version: '2.0',
                        date: '2025-07-30',
                        env: 'CE'
                    }
                ];
            }
        },

        // 查看特定版本的隐私协议
        viewPrivacyVersion(version) {
            const server_type = this.$store.state.systemConfig.server_type;
            let host = server_type.protocol + server_type.host + server_type.port;
            if (host.indexOf("localhost") > -1 || host.indexOf("192.168") > -1) {
                host = `https://${Tool.getHostConfig().dev}`;
            }
            const url = host + `/privacyPolicyPage/${version.env}/pravicyPolicy${version.env}_${version.version}.html`;

            // 在新窗口中打开特定版本的隐私协议
            if (Tool.checkAppClient('Browser')) {
                window.open(url, "blank");
            } else {
                window.CWorkstationCommunicationMng.OpenNewWindow({ url });
            }
        },

        // 显示撤销确认对话框
        showRevokeConfirm() {
            this.$MessageBox.confirm(this.lang.sure_revoke_privacy_tips, this.lang.tip_title, {
                confirmButtonText: this.lang.confirm_txt,
                cancelButtonText: this.lang.cancel_btn,
                type: 'warning'
            }).then(() => {
                this.revokePrivacyAgreement();
            }).catch(() => {
                // 用户取消操作
            });
        },

        // 撤销隐私协议同意
        revokePrivacyAgreement() {
            try {
                // 调用隐私协议组件中的方法来撤销同意
                this.setPrivacyStatus(0);

                // 延迟执行退出登录，直接调用退出登录逻辑
                setTimeout(() => {
                    this.$root.eventBus.$emit('logout');
                }, 500);
            } catch (error) {
                console.error('撤销隐私协议失败:', error);
            }
        },

        // 设置隐私协议状态
        setPrivacyStatus(status) {
            let serverType = localStorage.getItem('serverType') || '云++';
            let privacyStatus = JSON.parse(localStorage.getItem('isAgreePrivacyPolicy') || "{}");

            // 从envConfig中获取版本号
            let privacy_version = this.$store.state.systemConfig.envConfig && this.$store.state.systemConfig.envConfig.privacy_agreement_version;

            // 如果同意隐私协议，记录版本号；如果不同意，记录0
            if (status) {
                privacyStatus[serverType] = privacy_version;
            } else {
                privacyStatus[serverType] = '';
            }

            localStorage.setItem('isAgreePrivacyPolicy', JSON.stringify(privacyStatus));
            // 向客户端通知隐私协议状态，传递实际的版本号
            if (window.CWorkstationCommunicationMng && window.CWorkstationCommunicationMng.setPrivacyPolicyStatus) {
                window.CWorkstationCommunicationMng.setPrivacyPolicyStatus({
                    status: status ? 1 : 0,
                    version: privacy_version
                });
            }
        },

        handleClose() {
            this.$emit('closePrivacySettings');
        }
    },
}
</script>

<style lang="scss" scoped>
.privacy_settings_page {
    .privacy_settings_container {
        display: flex;
        flex-direction: column;
        height: 500px;

        .version_history_section {
            flex: 1;
            overflow-y: auto;

            .section_title {
                padding: 16px 0 12px;
                font-size: 14px;
                font-weight: 600;
                color: #2c3e50;
                border-bottom: 1px solid #e8eaed;
                margin-bottom: 12px;
            }

            .version_list {
                .version_item {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding: 12px 16px;
                    border: 1px solid #e8eaed;
                    border-radius: 6px;
                    margin-bottom: 8px;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    background: #fff;

                    &:hover {
                        background: #f8f9fc;
                        border-color: #00c59d;
                    }

                    .version_info {
                        display: flex;
                        flex-direction: column;
                        flex: 1;

                        .version_number {
                            font-size: 14px;
                            font-weight: 600;
                            color: #2c3e50;
                            margin-bottom: 4px;
                            display: flex;
                            align-items: center;
                            gap: 8px;

                            .latest_tag {
                                background: #00c59d;
                                color: #fff;
                                font-weight: 500;
                                font-size: 12px;
                                padding: 2px 8px;
                                border-radius: 12px;
                            }
                        }

                        .version_date {
                            font-size: 12px;
                            color: #8492a6;
                            font-weight: 400;
                        }
                    }

                    .el-icon-arrow-right {
                        color: #c0c4cc;
                        font-size: 14px;
                        transition: all 0.3s ease;
                    }

                    &:hover .el-icon-arrow-right {
                        color: #00c59d;
                    }
                }
            }
        }

        .revoke_section {
            flex-shrink: 0;
            padding-top: 16px;
            border-top: 1px solid #e8eaed;
            margin-top: 16px;

            .revoke_button {
                width: 100%;
                background: #fff;
                border: 1px solid #e1e6ef;
                color: #666;
                font-size: 14px;
                font-weight: 500;
                height: 40px;
                border-radius: 6px;
                transition: all 0.3s ease;

                &:hover {
                    background: #f8f9fa;
                    border-color: #d1d9e6;
                    color: #555;
                }

                &:active {
                    background: #e9ecef;
                    border-color: #ced4da;
                    color: #444;
                }
            }
        }
    }
}
</style>

import cloneDeep from 'lodash/cloneDeep'
import Vue from 'vue'

const initState ={
    data:null,
}

export default {
    state: cloneDeep(initState),
    mutations: {
        resetStore(state){
            if(window.vm.$store.state){
                Vue.set(window.vm.$store.state,'clipBoard',cloneDeep(initState))
            }
        },
        initClipBoardInfo(state) {
            state.data = null
        },
        updateClipBoardInfo(state, data){
            state.data =data
        },
    },
    actions: {},
    getters: {}
}

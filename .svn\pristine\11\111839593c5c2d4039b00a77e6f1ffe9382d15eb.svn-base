<template>
    <transition name="slide">
        <div class="bi_data_page second_level_page">
            <mrHeader @click-left="closeBI">
                <template #title>
                    {{lang.bi_data_display}}
                </template>
            </mrHeader>
            <div class="container">
                <iframe :src="BIUrl" id="bi_iframe" class="iframe_content" :key="tempKey"></iframe>
            </div>
        </div>
    </transition>
</template>
<script>
import base from '../lib/base'
import Tool from '@/common/tool.js'
export default {
    mixins: [base],
    name: 'iworks_statistics',
    components: {},
    data(){
        return {
            tempKey:'',
            BIUrl:'',
            rlen:history.length,
            currentGroupsetId:parseInt(this.$route.params.id)
        }
    },
    beforeDestroy(){
    },
    mounted(){
        this.$nextTick(()=>{
            this.getBIUrl();
        })
    },
    activated(){
        this.$nextTick(()=>{
            this.getBIUrl();
        })
    },
    computed:{
        // currentGroupsetId(){
        //     let groupset_id = 0;
        //     if (this.$store.state.chatList.currentGroupset) {
        //         groupset_id = this.$store.state.chatList.currentGroupset.id || 0;
        //     }
        //     return groupset_id;
        // },
    },
    methods:{
        closeBI(){
            let len =  this.rlen - history.length -1
            this.$router.go(len)
        },
        getBIUrl(){
            this.tempKey = Date.now();
            let type = this.$route.query.type
            let params = {}
            let id = -1;
            if(type === 'group'){
                params.group_id = this.$route.query.cid
                id = this.$route.query.cid
            }else if(type === 'groupset'){
                params.groupset_id = this.currentGroupsetId
                id = this.currentGroupsetId
            }
            const requestConfig = this.systemConfig.server_type
            let ajaxServer= requestConfig.protocol+requestConfig.host+requestConfig.port;
            if(process.env.NODE_ENV === 'production'){
                ajaxServer += '/statistic'
            }else{
                ajaxServer = window.location.origin
            }
            const url = Tool.transferLocationToCe(`${ajaxServer}/statistic.html#/mobile_index/data_display?dataFrom=${type}&id=${id}&token=${window.vm.$store.state.user.new_token}&language=${this.lang.currentLanguage}`)
            this.BIUrl = url;
        },
    }
}

</script>
<style lang="scss">
.bi_data_page{
    .container{
        padding:0rem 0rem;
        background:#fff;

        width:100%;
        height:100%;

        .iframe_content{
            width:100%;
            height:100%;
        }
    }
}
</style>

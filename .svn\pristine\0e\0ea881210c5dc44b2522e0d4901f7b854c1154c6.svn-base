<template>
    <div class="practice-select">
        <van-notice-bar
            :text="lang.welcome_clinical_thinking_exercise_tips"
            color="#333"
            background="#f5f5f5"
            :scrollable="false"
            :wrapable="true"
            class="tips-bar"
        />

        <div class="body-map">
            <div class="image-wrapper" ref="imageWrapper">
                <img
                    :src="currentImage"
                    alt="body"
                    class="body-image"
                    ref="bodyImage"
                    @load="handleImageLoad"
                    :class="{ 'image-loaded': isImageLoaded }"
                />
                <template v-if="isImageLoaded">
                    <div
                        v-for="area in bodyAreas"
                        :key="area.id"
                        :class="['area', area.id]"
                        @click="handleAreaClick(area.id)"
                    ></div>
                    <div
                        v-for="area in bodyAreas"
                        :key="area.id + '_text'"
                        :class="['area-button', { active: currentHighlightText === area.id }]"
                        :style="getTextPosition(area.id)"
                        @click="handleAreaClick(area.id)"
                    >
                        {{ area.text }}
                    </div>
                </template>
                <div v-if="!isImageLoaded" class="image-loading">
                    <van-loading type="spinner" color="#00c3b0" />
                </div>
            </div>
        </div>
        <div class="action-button-wrapper">
            <van-button
                type="primary"
                class="action-button"
                @click="randomPractice"
                :loading="getAiPracticeCaseLoading"
            >
                {{ lang.random_exercise }}
            </van-button>
        </div>
    </div>
</template>

<script>
import base from "../../lib/base";
import { Button, NoticeBar, Icon, Loading } from "vant";
import AIBookServiceInstance from "@/common/aiBookService";
export default {
    mixins: [base],
    components: {
        [Button.name]: Button,
        [NoticeBar.name]: NoticeBar,
        [Icon.name]: Icon,
        [Loading.name]: Loading,
    },
    name: "PracticeSelect",
    data() {
        return {
            currentImage: "static/resource/images/body_base_1.png",
            currentHighlightText: null,
            isImageLoaded: false,
            bodyAreas: [
                { id: "fubu", text: "腹部", highlightImage: "static/resource/images/body_fubu_1.png" },
                { id: "fuchan", text: "妇产", highlightImage: "static/resource/images/body_fuchan_1.png" },
                { id: "qianbiao", text: "浅表介入", highlightImage: "static/resource/images/body_qianbiao_1.png" },
                { id: "xinxueguan", text: "心血管", highlightImage: "static/resource/images/body_xinxueguan_1.png" },
            ],
            imageRect: null,
            textPositions: {
                fubu: { top: 27, left: 28 },
                fuchan: { top: 30, right: 25 },
                qianbiao: { top: 8, left: 28 },
                xinxueguan: { top: 12, right: 25 },
            },
            aiBookService: AIBookServiceInstance,
            getAiPracticeCaseLoading: false,
            preloadedImages: [],
            resizeObserver: null,
            imageNaturalSize: { width: 0, height: 0 },
        };
    },
    mounted() {
        this.preloadBaseImage();
        this.preloadImages();
        this.setupResizeObserver();
        window.addEventListener('resize', this.handleResize);
    },
    beforeDestroy() {
        this.cleanupResizeObserver();
        window.removeEventListener('resize', this.handleResize);
    },
    watch: {
        currentImage() {
            this.$nextTick(() => {
                this.updateTextPositions();
            });
        }
    },
    methods: {
        setupResizeObserver() {
            if (window.ResizeObserver) {
                this.resizeObserver = new ResizeObserver(entries => {
                    for (const entry of entries) {
                        if (entry.target === this.$refs.imageWrapper) {
                            this.updateTextPositions();
                        }
                    }
                });

                if (this.$refs.imageWrapper) {
                    this.resizeObserver.observe(this.$refs.imageWrapper);
                }
            }
        },

        cleanupResizeObserver() {
            if (this.resizeObserver) {
                this.resizeObserver.disconnect();
                this.resizeObserver = null;
            }
        },

        handleResize() {
            this.updateTextPositions();
        },

        preloadBaseImage() {
            const img = new Image();
            img.src = this.currentImage;
            img.onload = () => {
                const aspectRatio = (img.height / img.width) * 100;
                const wrapper = this.$refs.imageWrapper;
                if (wrapper) {
                    wrapper.style.setProperty("--aspect-ratio", `${aspectRatio}%`);
                }
                this.$nextTick(() => {
                    this.updateTextPositions();
                });
            };
        },
        preloadImages() {
            const images = [
                "static/resource/images/body_fubu_1.png",
                "static/resource/images/body_fuchan_1.png",
                "static/resource/images/body_qianbiao_1.png",
                "static/resource/images/body_xinxueguan_1.png",
            ];

            images.forEach((src) => {
                const img = new Image();
                img.src = src;
                this.preloadedImages.push(img);
            });
        },
        handleImageLoad() {
            const img = this.$refs.bodyImage;
            if (img) {
                const aspectRatio = (img.naturalHeight / img.naturalWidth) * 100;
                const wrapper = this.$refs.imageWrapper;
                if (wrapper) {
                    this.$set(this, 'imageNaturalSize', {
                        width: img.naturalWidth,
                        height: img.naturalHeight
                    });
                }
            }

            this.$nextTick(() => {
                this.calculateImageRect();
                this.updateTextPositions();
                requestAnimationFrame(() => {
                    this.isImageLoaded = true;
                });
            });
        },

        updateTextPositions() {
            if (!this.$refs.bodyImage || !this.$refs.imageWrapper) {
                return;
            }

            this.calculateImageRect();
        },

        calculateImageRect() {
            const img = this.$refs.bodyImage;
            const wrapper = this.$refs.imageWrapper;
            if (!img || !wrapper) {
                return;
            }

            const wrapperRect = wrapper.getBoundingClientRect();
            const imgRect = img.getBoundingClientRect();

            this.imageRect = {
                width: imgRect.width,
                height: imgRect.height,
                left: imgRect.left - wrapperRect.left,
                top: imgRect.top - wrapperRect.top,
                scale: imgRect.width / img.naturalWidth
            };
        },

        getTextPosition(areaId) {
            if (!this.imageRect || !this.textPositions[areaId]) {
                return {};
            }

            const pos = this.textPositions[areaId];
            const isRightSide = "right" in pos;

            let x, y;
            if (isRightSide) {
                x = this.imageRect.left + this.imageRect.width - (this.imageRect.width * pos.right) / 100;
            } else {
                x = this.imageRect.left + (this.imageRect.width * pos.left) / 100;
            }
            y = this.imageRect.top + (this.imageRect.height * pos.top) / 100;

            // const baseFontSize = parseFloat(getComputedStyle(document.documentElement).fontSize) * 0.7;
            // const fontSize = Math.max(baseFontSize * 0.8, baseFontSize * this.imageRect.scale);

            return {
                position: "absolute",
                left: `${x}px`,
                top: `${y}px`,
                transform: isRightSide ? "translate(0, -50%)" : "translate(-100%, -50%)",
                zIndex: 2,
            };
        },

        async handleAreaClick(areaId) {
            const area = this.bodyAreas.find((a) => a.id === areaId);
            if (!area) {
                return;
            }
            this.currentImage = area.highlightImage;
            this.currentHighlightText = areaId;
            const areaMap = {
                qianbiao: "superficial",
                fubu: "abdomen",
                fuchan: "gyn",
                xinxueguan: "cardio",
            };
            const res = await this.getAiPracticeCase({
                bodyPart: areaMap[areaId],
            });


            if (this.$route.name === "index_practice_overview") {
                this.$router.push({
                    name: "index_practice_answer_action",
                    params: {
                        bodyPart: res.bodyPart,
                        caseID: res._id,
                        content: res.content,
                        questionTips: res.question,
                        title: res.title,
                        practiceType: "bodyPart",
                    },
                });
            } else if (this.$route.name === "ai_chat_practice_overview") {
                this.$router.push({
                    name: "ai_chat_practice_answer_action",
                    params: {
                        bodyPart: res.bodyPart,
                        caseID: res._id,
                        content: res.content,
                        questionTips: res.question,
                        title: res.title,
                        practiceType: "bodyPart",
                    },
                });
            }
            // 在路由跳转前重置状态
            this.$nextTick(() => {
                this.currentImage = "static/resource/images/body_base_1.png";
                this.currentHighlightText = null;
            });
        },

        handleAreaHover(areaId) {
            const area = this.bodyAreas.find((a) => a.id === areaId);
            if (area) {
                this.currentImage = area.highlightImage;
                this.currentHighlightText = areaId;
            }
        },

        async getAiPracticeCase(params) {
            this.getAiPracticeCaseLoading = true;
            try {
                const res = await this.aiBookService.getAiPracticeCase(params);
                this.getAiPracticeCaseLoading = false;
                return res;
            } catch (error) {
                this.getAiPracticeCaseLoading = false;
                this.$toast.fail("get topic error");
                throw error;
            }
        },

        async randomPractice() {
            const res = await this.getAiPracticeCase();
            if (this.$route.name === "index_practice_overview") {
                this.$router.push({
                    name: "index_practice_answer_action",
                    params: {
                        bodyPart: res.bodyPart,
                        caseID: res._id,
                        content: res.content,
                        questionTips: res.question,
                        title: res.title,
                        practiceType: "random",
                    },
                });
            } else if (this.$route.name === "ai_chat_practice_overview") {
                this.$router.push({
                    name: "ai_chat_practice_answer_action",
                    params: {
                        bodyPart: res.bodyPart,
                        caseID: res._id,
                        content: res.content,
                        questionTips: res.question,
                        title: res.title,
                        practiceType: "random",
                    },
                });
            }
        },
    },
};
</script>

<style lang="scss" scoped>
@import '@/module/ultrasync/style/aiChat.scss';
.practice-select {
    background: #fff;
    border-radius: 0.533rem;
    padding: 0.8rem;
    margin-bottom: 0.533rem;
    height: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    .tips-bar {
        margin-bottom: 0.8rem;

        :deep(.van-notice-bar__wrap) {
            height: auto;
            padding: 0.4rem 0;
        }

        :deep(.van-notice-bar__content) {
            position: relative;
            white-space: normal;
            padding: 0 0.267rem;
            font-size: 0.6rem;
        }
    }

    .body-map {
        position: relative;
        width: 100%;
        flex: 1;
        overflow: hidden;

        .image-wrapper {
            position: relative;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .body-image {
            position: relative;
            max-width: 100%;
            max-height: 100%;
            width: auto;
            height: auto;
            object-fit: contain;
            visibility: hidden;

            &.image-loaded {
                visibility: visible;
            }
        }

        .image-loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .area {
            position: absolute;
            cursor: pointer;
            -webkit-tap-highlight-color: transparent;
            z-index: 1;
            border-radius: 50%;

            &.fubu {
                width: 25%;
                height: 0;
                padding-bottom: 25%;
                left: 38%;
                top: 35%;
                transform: translate(-50%, -50%);
            }
            &.fuchan {
                width: 25%;
                height: 0;
                padding-bottom: 25%;
                left: 38%;
                top: 48%;
                transform: translate(-50%, -50%);
            }
            &.qianbiao {
                width: 25%;
                height: 0;
                padding-bottom: 25%;
                left: 38%;
                top: 15%;
                transform: translate(-50%, -50%);
            }
            &.xinxueguan {
                width: 22%;
                height: 0;
                padding-bottom: 22%;
                left: 50%;
                top: 25%;
                transform: translate(-50%, -50%);
            }
        }

        .area-button {
            position: absolute;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0.2rem 0.5rem;
            background: #ffffff;
            border-radius: 0.213rem;
            cursor: pointer;
            min-width: 2rem;
            text-align: center;
            font-size: 0.6rem;
            font-weight: 500;
            background: #fff;
            border: 1px solid #D8D8D8;
            color: #000000;
            .van-icon {
                margin-left: 0.2rem;
                color: #999;
                font-size: 0.6rem;
                transition: transform 0.2s ease;
            }

            // 选中状态
            &.active {
                @extend .ai-theme-background;
                color: #fff;
            }
        }
    }
    .action-button-wrapper {
        margin-top: 0.8rem;
        flex-shrink: 1;
        .action-button {
            @extend .ai-theme-background;
            height: 2rem;
            font-size: 0.7rem;
            border-radius: 0.2rem;
            width: 100%;
        }
    }
    :deep(.van-tag) {
        &.area-text {
            padding: 0.267rem 0.4rem;
            font-size: 0.7rem;
            border-radius: 0.267rem;
        }
    }

    .action-button {
        height: 2rem;
        font-size: 0.7rem;
        border-radius: 0.2rem;
    }
}

// 移除之前的呼吸动画，改用更简单的hover效果
@media (hover: hover) {
    .area-button {
        &:hover {
            border-color: #00c3b0;
            color: #00c3b0;
            background: #f5fffd;

            .van-icon {
                color: #00c3b0;
            }
        }
    }
}
</style>

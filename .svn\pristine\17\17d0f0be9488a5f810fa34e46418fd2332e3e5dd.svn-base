<template>
    <div class="file_list_page second_level_page">
        <mrHeader @click-left="back">
            <template #title>
                {{lang.files}}
            </template>
        </mrHeader>
        <div class="container file-list">
            <gallery-file-list :galleryList="consultationImageList" :span="spanCount" @onScrollToBottom="loadMore" :loadingStatus="allloaded?'loaded':'loading'" ref="galleryFileList" :showMonth="true"></gallery-file-list>
        </div>
        <router-view></router-view>
    </div>
</template>
<script>
import base from '../lib/base';
import { Toast } from 'vant';
import Tool from '@/common/tool.js'
import GalleryFileList from '../components/galleryFileList.vue';
import {
    parseImageListToLocal,
    patientDesensitization,
} from '../lib/common_base'
export default {
    mixins:[base],
    props:{
        editImage:{
            type:<PERSON><PERSON>an,
            default:false
        },
        checkList:{
            type:Array,
            default:function(){
                return []
            }
        }
    },
    components:{
        GalleryFileList,
    },
    data(){
        return {
            loadingConfig:this.$store.state.loadingConfig,
            systemConfig:this.$store.state.systemConfig,
            allloaded:false,
            tempList:[],
            debounceType:1,
            loadingMore:false,
            windowWidth: window.innerWidth,
        }
    },
    computed:{
        consultationImageObj(){
            return this.$store.state.consultationImageList
        },
        consultationImageList(){
            return this.$store.state.consultationImageList.list
        },
        spanCount(){
            if(this.windowWidth < 540){
                return 3;
            }else if(this.windowWidth >= 540 && this.windowWidth < 768){
                return 4;
            }else if(this.windowWidth >= 768 && this.windowWidth < 1024){
                return 5;
            }else{
                return 6;
            }
        }
    },
    watch:{
    },
    mounted(){
        this.$nextTick(()=>{
            this.$root.eventBus.$off('folderCollectHandler').$on('folderCollectHandler',this.folderCollectHandler)
        })
        window.addEventListener('resize', this.handleResize);
    },
    beforeDestroy(){
        window.removeEventListener('resize', this.handleResize);
    },
    methods:{
        handleResize(){
            this.windowWidth = window.innerWidth
        },
        loadMore(callback){
            if (this.loadingMore) {
                callback&&callback('loading')
                return
            }

            let controller=window.main_screen&&window.main_screen.controller;
            if(!controller){
                callback&&callback('error')
                return
            }
            let start=this.consultationImageObj.index
            if (start==-1||this.consultationImageList.length<this.systemConfig.consultationImageShowNum) {
                //全部加载完了
                Toast(this.lang.image_all_loaded)
                this.allloaded=true
                callback&&callback('finished')
                // this.$refs.loadMoreImage.onBottomLoaded()
            }else{
                this.loadingMore=true;
                controller.emit("get_consultation_image_list",{
                    start:start,
                    count:this.systemConfig.consultationImageShowNum
                },(is_succ,data)=>{
                    this.setMoreConsultationImageList(is_succ,data,callback)
                })

            }

        },
        setMoreConsultationImageList(is_succ,data,callback){
            this.loadingMore=false;
            if (is_succ) {
                patientDesensitization(data.consultation_image_list);
                parseImageListToLocal(data.consultation_image_list,'url')
                this.$store.commit('consultationImageList/pushMoreConsultationImages',data)
                // this.$refs.loadMoreImage.onBottomLoaded()
                if(this.consultationImageList.length==this.consultationImageObj.total_count){
                    this.allloaded=true
                    Toast(this.lang.image_all_loaded)
                }
                callback&&callback('loaded')
            }
        },
        folderCollectHandler(checkList,cancelCb){
            if (checkList.length==0) {
                Toast(this.lang.choose_favorites_tip)
            }else{
                window.vm.$root.eventBus.$emit('initFavoriteConfirm',checkList);
                cancelCb&&cancelCb()
            }
        },
    }
}
</script>
<style lang="scss">
.file_list_page{
    .file-list{
        flex:1;
        overflow: auto;
        background:#fff;
        padding: 0 0.7rem 0.7rem 0.7rem;
    }
    .van_loading_spinner{
        display: flex;
        justify-content: center;
        align-items: center;

        .van-loading__spinner{
            width: 2.8rem;
            height: 2.8rem;
        }
    }
}
</style>

<template>
    <video-player
        :video-src="videoSrc"
        :poster="poster"
        :auto-play="autoPlay"
        :id="id"
        @play="onPlay"
        @pause="onPause"
        @ended="onEnded"
        @timeupdate="onTimeUpdate"
        @error="onError"
        ref="player"
    />
</template>

<script>
import VideoPlayer from './videoPlayer.vue';

export default {
    name: "VideoReportingPlayer",
    components: {
        VideoPlayer
    },
    props: {
        videoSrc: {
            type: String,
            required: true,
        },
        poster: {
            type: String,
            default: "",
        },
        autoPlay: {
            type: Boolean,
            default: true,
        },
        id: {
            type: [String, Number],
            default: () => `video-reporting-player-${Date.now()}-${Math.floor(Math.random() * 1000)}`
        },
        // 上报相关配置
        enableReporting: {
            type: Boolean,
            default: false,
        },
        fileInfo: {
            type: Object,
            default: () => ({})
        },
        reportInterval: {
            type: Number,
            default: 5000 // 默认5秒上报一次
        }
    },
    data() {
        return {
            // 上报相关状态
            isReporting: false,
            reportStartTime: 0,
            reportTimer: null,
            isDestroyed: false, // 标记组件是否已销毁
        }
    },
    beforeDestroy() {
        this.isDestroyed = true;
        this.stopReporting();
    },
    methods: {
        // 播放事件处理
        onPlay() {
            this.$emit('play', this.fileInfo);
            
            if (this.enableReporting) {
                this.startReporting();
            }
        },
        
        // 暂停事件处理
        onPause() {
            this.$emit('pause', this.fileInfo);
            
            if (this.enableReporting) {
                this.pauseReporting();
            }
        },
        
        // 结束事件处理
        onEnded() {
            this.$emit('ended', this.fileInfo);
            
            if (this.enableReporting) {
                this.stopReporting();
            }
        },
        
        // 时间更新事件处理
        onTimeUpdate(data) {
            this.$emit('timeupdate', data);
        },
        
        // 错误事件处理
        onError(error) {
            this.$emit('error', { error, fileInfo: this.fileInfo });
            
            if (this.enableReporting) {
                this.stopReporting();
            }
        },
        
        // 开始上报
        startReporting() {
            if (!this.enableReporting || this.isReporting || !this.fileInfo.resource_id) {
                return;
            }

            this.isReporting = true;
            this.reportStartTime = Date.now();
            
            // 立即上报一次
            this.reportVideoTime();
            
            // 定时上报
            this.reportTimer = setInterval(() => {
                this.reportVideoTime();
            }, this.reportInterval);

            console.log('Started video reporting for:', this.fileInfo.resource_id);
        },
        
        // 暂停上报（但不清除状态）
        pauseReporting() {
            if (this.reportTimer) {
                clearInterval(this.reportTimer);
                this.reportTimer = null;
            }
            
            // 暂停时上报一次当前的观看时长（但如果组件已销毁则不上报）
            if (this.isReporting && !this.isDestroyed) {
                this.reportVideoTime();
            }
            
            console.log('Paused video reporting');
        },
        
        // 停止上报并清除状态
        stopReporting() {
            if (this.reportTimer) {
                clearInterval(this.reportTimer);
                this.reportTimer = null;
            }
            
            // 停止时最后上报一次（但如果组件已销毁则不上报，避免错误）
            if (this.isReporting && !this.isDestroyed) {
                this.reportVideoTime();
            }
            
            this.isReporting = false;
            this.reportStartTime = 0;
            
            console.log('Stopped video reporting');
        },
        
        // 上报视频观看时间
        reportVideoTime() {
            if (!this.enableReporting || !this.reportStartTime || !this.fileInfo.resource_id || this.isDestroyed) {
                return;
            }

            const duration = Date.now() - this.reportStartTime;
            const reportData = {
                type: 5,
                cid: this.fileInfo.group_id,
                business_data: {
                    resource_id: this.fileInfo.resource_id,
                    uuid: this.reportStartTime,
                    duration: duration,
                },
                showErrorToast: false,
            };

            console.log('Reporting video time:', Math.round(duration / 1000), 'seconds');
            
            // 发射事件给父组件处理上报
            this.$emit('reportTime', reportData);
        },
        
        // 暴露播放器控制方法
        play() {
            this.$refs.player.play();
        },
        
        pause() {
            this.$refs.player.pause();
        },
        
        getPlayerState() {
            return this.$refs.player.getPlayerState();
        }
    }
};
</script>

<style scoped>
/* 样式继承自VideoPlayer组件 */
</style> 
<!-- 预置数据表格 -->
<template>
    <div>
        <el-dialog
            class="obstetric_qc_preset_data_table_dialog"
            custom-class="obstetric_qc_preset_data_table_dialog"
            :title="currentSection.name"
            :visible="isShow"
            :close-on-click-modal="false"
            height="100%"
            width="100%"
            :modal="false"
            :append-to-body="true"
            :before-close="closeTable"
        >
            <div class="preset_data_table">
                <div class="container">
                    <div class="pagelist">
                        <table v-loading="loading">
                            <thead>
                                <tr>
                                    <th>{{ lang.index_num }}</th>
                                    <th>{{ lang.view_type }}</th>
                                    <th>{{ lang.admin_activation }}</th>
                                    <th>{{ lang.view_name }}</th>
                                    <th>{{ lang.view_group }}</th>
                                    <th>{{ lang.proportion_weight }}</th>
                                    <th>{{ lang.proportion_weight_value }}(%)</th>
                                    <th>{{ lang.ai_score_standard }}</th>
                                    <th>{{ lang.ai_score_basic_standard }}</th>
                                    <th>{{ lang.ai_score_no_standard }}</th>
                                    <th>{{ lang.last_uploader }}</th>
                                    <th>{{ lang.last_update_time }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="(item, index) of currentList" :key="index">
                                    <td>{{ (tab_conf.pageNo - 1) * tab_conf.pageSize + index + 1 }}</td>
                                    <td>{{ viewClass[item.view_class] }}</td>
                                    <td>
                                        <el-checkbox
                                            v-model="item.enabled"
                                            @change="handleWeightChange(item.id)"
                                        ></el-checkbox>
                                    </td>
                                    <td class="name" @click="handShowViewDetail(item)">{{ getItemName(item) }}</td>
                                    <td class="name">
                                        <span
                                            @click="handShowGroupViewDetail(item.group_view_id)"
                                            v-if="item.group_view_id"
                                        >
                                            {{
                                                getItemName(
                                                    currentSection.more_details.groupObj[item.group_view_id + ""]
                                                )
                                            }}
                                        </span>
                                        <span v-else> -- </span>
                                    </td>
                                    <td
                                        v-if="
                                            !item.group_view_id ||
                                            (item.group_view_id && index == 0) ||
                                            (index != 0 &&
                                                currentList[index - 1].group_view_id &&
                                                currentList[index].group_view_id &&
                                                currentList[index].group_view_id !=
                                                    currentList[index - 1].group_view_id)
                                        "
                                        :rowspan="
                                            item.group_view_id
                                                ? currentSection.more_details.groupObj[item.group_view_id + ''].types
                                                      .length
                                                : 1
                                        "
                                        class="select_item"
                                    >
                                        <template v-if="obstetricEarlyPregnancy.viewClass.base_view == item.view_class">
                                            <template v-if="item.group_view_id">
                                                <el-select
                                                    v-model="goupListObj[item.group_view_id].weight.weight_desc"
                                                    class="weight_select"
                                                    @change="handleWeightChange(item.id)"
                                                    :size="'mini'"
                                                >
                                                    <el-option
                                                        v-for="(item, index) in 10"
                                                        :key="index"
                                                        :value="index + 1"
                                                    >
                                                    </el-option>
                                                </el-select>
                                            </template>
                                            <template v-else>
                                                <el-select
                                                    v-model="item.weight.weight_desc"
                                                    class="weight_select"
                                                    @change="handleWeightChange(item.id)"
                                                    :size="'mini'"
                                                >
                                                    <el-option
                                                        v-for="(item, index) in 10"
                                                        :key="index"
                                                        :label="getItemName(item)"
                                                        :value="index + 1"
                                                    >
                                                    </el-option>
                                                </el-select>
                                            </template>
                                        </template>
                                    </td>
                                    <td
                                        v-if="
                                            !item.group_view_id ||
                                            (item.group_view_id && index == 0) ||
                                            (index != 0 &&
                                                currentList[index - 1].group_view_id &&
                                                currentList[index].group_view_id &&
                                                currentList[index].group_view_id !=
                                                    currentList[index - 1].group_view_id)
                                        "
                                        :rowspan="
                                            item.group_view_id
                                                ? currentSection.more_details.groupObj[item.group_view_id + ''].types
                                                      .length
                                                : 1
                                        "
                                        class="select_item"
                                    >
                                        <template v-if="item.group_view_id">
                                            {{
                                                toFixedNumber(goupListObj[item.group_view_id].weight.weight_numb * 100)
                                            }}
                                        </template>
                                        <template v-else>
                                            {{ toFixedNumber(item.weight.weight_numb * 100) }}
                                        </template>
                                    </td>
                                    <!-- <td class='enabled'>{{ item.weight.weight_numb }}</td> -->
                                    <td>{{ item.ai_height.lowest + "-" + item.ai_height.highest }}</td>
                                    <td>{{ item.ai_middle.lowest + "-" + item.ai_middle.highest }}</td>
                                    <td>{{ item.ai_lower.lowest + "-" + item.ai_lower.highest }}</td>
                                    <td>{{ section.updator.login_name }}</td>
                                    <td>{{ formatTime(section.updated_at) }}</td>
                                </tr>
                            </tbody>
                        </table>
                        <el-image
                            v-if="list.length == 0"
                            class="mr-data-empty"
                            src="static/resource_pc/images/nodata.png"
                        />
                    </div>
                    <el-pagination
                        background
                        :current-page="tab_conf.pageNo"
                        :layout="tab_conf.layout"
                        :page-size="tab_conf.pageSize"
                        :total="totalSections"
                        @current-change="handleCurrentChange"
                        @size-change="handleSizeChange"
                        style="margin-top: 20px"
                    />
                </div>
                <div class="button_arear" v-if="list.length != 0">
                    <div class="button_ele">
                        <el-button
                            native-type="submit"
                            type="primary"
                            class="general_cancel_button"
                            @click="handleReset"
                        >
                            {{ lang.register_reset_btn }}
                        </el-button>
                    </div>
                    <div class="button_ele">
                        <el-button
                            native-type="submit"
                            type="primary"
                            class="general_confirm_button"
                            @click="handleSave"
                        >
                            {{ lang.save_txt }}
                        </el-button>
                    </div>
                </div>
            </div>
        </el-dialog>
        <el-dialog
            :title="getItemName(currentSubSetion)"
            :visible.sync="isShowDeatil"
            :close-on-click-modal="false"
            width="40%"
            height="0%"
            :append-to-body="true"
            custom-class="view_detail_preset_data_table"
            :modal="false"
        >
            <div class="preset_data_table">
                <template v-if="type == 'view'">
                    <div class="title">{{ lang.structure_evaluation }}</div>
                    <table>
                        <thead>
                            <tr>
                                <th>{{ lang.index_num }}</th>
                                <th>{{ lang.structure_name }}</th>
                                <!-- <th>{{lang.full_mark}}</th> -->
                                <!-- <th>{{lang.proportion_weight}}</th> -->
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="(item, index) of currentSubSetion.detail" :key="index">
                                <td>{{ index + 1 }}</td>
                                <td>{{ getItemName(item) }}</td>
                                <!-- <td>{{ item.full }}</td> -->
                                <!-- <td>{{ item.rate }}</td> -->
                            </tr>
                        </tbody>
                    </table>
                </template>
                <div class="title">{{ lang.score_items }}</div>
                <table>
                    <thead>
                        <tr>
                            <th>{{ lang.index_num }}</th>
                            <td>{{ lang.score_items }}</td>
                            <td>{{ lang.full_mark }}</td>
                            <th>{{ lang.min_point }}</th>
                            <td>{{ lang.proportion_weight }}</td>
                            <td>{{ lang.structure_name }}</td>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="(item, index) of currentSubSetion.item" :key="index">
                            <td>{{ index + 1 }}</td>
                            <td>{{ getItemName(item) }}</td>
                            <td>{{ item.full }}</td>
                            <td>{{ item.threshold || "" }}</td>
                            <td>{{ item.rate }}</td>
                            <td>{{ getStructName(item) }}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import base from "../../lib/base";
import moment from "moment";
import service from "../../service/multiCenterService.js";
import { cloneDeep, sortBy } from "lodash";
export default {
    mixins: [base],
    name: "MulticenterPage",
    components: {},
    props: {
        currentSection: {
            type: Object,
            default: () => {
                return {};
            },
        },
    },
    data() {
        return {
            isShowDeatil: false,
            obstetricEarlyPregnancy: this.$store.state.multicenter.obstetricEarlyPregnancy,
            type: "view",
            tab_conf: {
                pageNo: 1,
                pageSize: 10,
                layout: "total, sizes, prev, pager, next, jumper",
                tatal: 0,
            },
            loading: false,
            isShow: true,
            currentSubSetion: {},
            list: [],
            goupListObj: {},
            currentList: [],
            totalSections: 0,
        };
    },
    computed: {
        currentMulticenter() {
            return this.$store.state.multicenter.currentMulticenter || {};
        },
        optionList() {
            return this.$store.state.multicenter.optionList || {};
        },
        viewClass() {
            let viewClass = this.$store.state.multicenter.obstetricEarlyPregnancy.viewClass;
            let list = [];
            for (let k in viewClass) {
                list[viewClass[k]] = this.lang.obstetric_qc[k];
            }
            return list;
        },
    },
    created() {},
    mounted() {
        this.handleWeightChange();
    },
    watch: {
        currentSection: {
            handler(value, oldValue) {
                this.section = value;
                this.list = Object.values(value.more_details.listObj).filter((v) => {
                    return v;
                });
                this.goupListObj = cloneDeep(value.more_details.groupObj);
                this.list = sortBy(this.list, (v) => {
                    return -1 * Number(v.group_view_id);
                });
                this.list = sortBy(this.list, (v) => {
                    return Number(v.view_class);
                });
                this.totalSections = this.list.length;
                if (this.totalSections > this.tab_conf.pageSize) {
                    this.currentList = this.list.slice(
                        (this.tab_conf.pageNo - 1) * this.tab_conf.pageSize,
                        this.tab_conf.pageNo * this.tab_conf.pageSize
                    );
                } else {
                    this.currentList = this.list;
                }
            },
            deep: true,
            immediate: true,
        },
    },
    methods: {
        getItemName(item) {
            if (item.key && item.key in this.lang) {
                return this.lang[item.key];
            }
            return item.name;
        },
        getStructName(item) {
            let names = [];
            if (this.currentSubSetion && item && item.detail_ids && item.detail_ids.length > 0) {
                for (let i in item.detail_ids) {
                    let detail_id = item.detail_ids[i];
                    for (let j in this.currentSubSetion.detail) {
                        let detail = this.currentSubSetion.detail[j];
                        if (detail_id == detail.id) {
                            names.push(this.getItemName(detail));
                        }
                    }
                }
            }
            return names.join(",");
        },
        updteData() {
            this.loading = true;
            delete this.section.more_details.list;
            service
                .updteCenteOptions({
                    mcID: this.currentMulticenter.id,
                    uid: this.user.id,
                    id: this.section.id,
                    more_details: this.section.more_details,
                })
                .then(async (res) => {
                    this.loading = false;
                    if (res.data.error_code == 0) {
                        this.$message.success(this.lang.update_success_text);
                        // console.error('updteCenteOptions',this.section.more_details)
                        this.setStoreOptions();
                    } else {
                        this.$message.error(this.lang.update_failed_text);
                    }
                });
        },
        handleCurrentChange(val) {
            this.tab_conf.pageNo = val;
            this.currentList = this.list.slice(
                (this.tab_conf.pageNo - 1) * this.tab_conf.pageSize,
                this.tab_conf.pageNo * this.tab_conf.pageSize
            );
        },
        handleSizeChange(val) {
            this.tab_conf.pageSize = val;
            this.tab_conf.pageNo = 1;
            this.currentList = this.list.slice(
                (this.tab_conf.pageNo - 1) * this.tab_conf.pageSize,
                this.tab_conf.pageNo * this.tab_conf.pageSize
            );
        },
        closeTable() {
            this.$emit("change");
        },
        handleReset() {
            let msg = this.lang.reset_data_tips;
            this.$confirm(msg, this.lang.tip_title, {
                confirmButtonText: this.lang.confirm_button_text,
                cancelButtonText: this.lang.cancel_button_text,
                type: "warning",
            }).then(() => {
                this.section.more_details.activedNumber = { total: 0 };
                for (let k in this.obstetricEarlyPregnancy.viewClass) {
                    this.section.more_details.activedNumber[this.obstetricEarlyPregnancy.viewClass[k]] = 0;
                }
                this.section.more_details.listObj = this.list.reduce((h, v) => {
                    v.weight = cloneDeep(v.default_weight);
                    v.enabled = v.default_enabled;
                    v.weight.weight_numb = v.enabled ? v.weight.weight_numb : 0;
                    h[v.id] = v;
                    if (v.enabled && !v.group_view_id) {
                        this.section.more_details.activedNumber.total =
                            this.section.more_details.activedNumber.total + 1;
                        this.section.more_details.activedNumber[v.view_class] =
                            this.section.more_details.activedNumber[v.view_class] + 1;
                    }
                    // console.log(v.default_weight.weight_desc)
                    return h;
                }, {});
                for (let k in this.goupListObj) {
                    let v = this.goupListObj[k];
                    v.weight = cloneDeep(v.default_weight);
                    v.enabled = v.default_enabled;
                    this.section.more_details.activedNumber.total = this.section.more_details.activedNumber.total + 1;
                    this.section.more_details.activedNumber[v.view_class] =
                        this.section.more_details.activedNumber[v.view_class] + 1;
                    this.section.more_details.groupObj[v.id] = cloneDeep(v);
                }

                this.updteData();
            });
        },

        handleSave() {
            this.updteData();
        },

        handleWeightChange(view_id = null) {
            let total = 0;
            let view_item = null;
            for (let k in this.goupListObj) {
                this.goupListObj[k].enabled = false;
                this.goupListObj[k].enabled_types = [];
            }
            if (view_id != null) {
                if (view_id + "" in this.goupListObj) {
                    for (let k in this.goupListObj) {
                        let v = this.goupListObj[k];
                        if (v.id == view_id) {
                            view_item = v;
                        }
                    }
                } else {
                    this.list.map((v) => {
                        if (v.id == view_id && v.group_view_id) {
                            view_item = v;
                        }
                    });
                }
            }

            this.list.map((v) => {
                if (this.obstetricEarlyPregnancy.viewClass.base_view == v.view_class) {
                    if (v.group_view_id) {
                        this.goupListObj[v.group_view_id].enabled =
                            this.goupListObj[v.group_view_id].enabled || v.enabled;
                        this.goupListObj[v.group_view_id].enabled_types.push(v.id);
                    } else {
                        total = (v.enabled ? v.weight.weight_desc : 0) + total;
                    }
                }
            });
            for (let k in this.goupListObj) {
                total = (this.goupListObj[k].enabled ? this.goupListObj[k].weight.weight_desc : 0) + total;
            }
            // console.error(total)
            // if(enabled_child_view.length>0){
            //     total =  view_item.weight.weight_desc + total
            // }
            for (let k in this.goupListObj) {
                if (this.goupListObj[k].enabled) {
                    this.goupListObj[k].weight.weight_numb = this.toFixedNumber(
                        this.goupListObj[k].weight.weight_desc / total
                    );
                } else {
                    this.goupListObj[k].weight.weight_numb = 0;
                }
            }
            let list_temp = this.list.reduce((h, v) => {
                if (this.obstetricEarlyPregnancy.viewClass.base_view == v.view_class) {
                    if (v.group_view_id) {
                        if (v.enabled) {
                            v.weight.weight_numb = this.toFixedNumber(
                                this.goupListObj[v.group_view_id].weight.weight_desc /
                                    (total * this.goupListObj[v.group_view_id].enabled_types.length)
                            );
                            v.weight.weight_desc = this.toFixedNumber(
                                this.goupListObj[v.group_view_id].weight.weight_desc /
                                    this.goupListObj[v.group_view_id].enabled_types.length
                            );
                        } else {
                            v.weight.weight_numb = 0;
                            v.weight.weight_desc = 0;
                        }
                    } else {
                        v.weight.weight_numb = v.enabled ? this.toFixedNumber(v.weight.weight_desc / total) : 0;
                    }
                } else {
                    v.weight.weight_numb = 0;
                }
                h.push(v);
                return h;
            }, []);
            this.section.more_details.activedNumber = { total: 0 };
            for (let k in this.obstetricEarlyPregnancy.viewClass) {
                this.section.more_details.activedNumber[this.obstetricEarlyPregnancy.viewClass[k]] = 0;
            }
            this.section.more_details.listObj = {};
            for (let i = list_temp.length - 1; i >= 0; i--) {
                let view = list_temp[i];
                if (view.enabled && !view.group_view_id) {
                    this.section.more_details.activedNumber.total = this.section.more_details.activedNumber.total + 1;
                    this.section.more_details.activedNumber[view.view_class] =
                        this.section.more_details.activedNumber[view.view_class] + 1;
                }
                this.section.more_details.listObj[view.id + ""] = view;
            }
            for (let k in this.goupListObj) {
                let v = this.goupListObj[k];
                if (v.enabled) {
                    this.section.more_details.activedNumber.total = this.section.more_details.activedNumber.total + 1;
                    this.section.more_details.activedNumber[v.view_class] =
                        this.section.more_details.activedNumber[v.view_class] + 1;
                }
            }
            this.section.more_details.groupObj = this.goupListObj;
            this.list = list_temp;
        },
        toFixedNumber(q) {
            if (!q || q == undefined || q == null) {
                return 0;
            }
            q = parseFloat(q);
            if (q < 100 && q > 0) {
                return parseFloat(q.toFixed(5));
            } else {
                return parseFloat(q.toFixed(0));
            }
        },
        handShowViewDetail(subSetion) {
            this.currentSubSetion = subSetion;
            this.type = "view";
            this.isShowDeatil = true;
        },
        handShowGroupViewDetail(group_view_id) {
            this.currentSubSetion = this.currentSection.more_details.groupObj[group_view_id + ""];
            this.isShowDeatil = true;
            this.type = "group_view";
        },
        async setStoreOptions() {
            let oldOptions = {};
            if (this.optionList && Object.keys(this.optionList).length > 0) {
                oldOptions = this.optionList[this.section.id];
                oldOptions.more_details = this.section.more_details;
                this.$store.commit("multicenter/updateMCOptionList", [oldOptions]);
                return;
            } else {
                return;
            }
        },
    },
};
</script>
<style lang="scss">
.obstetric_qc_preset_data_table_dialog {
    .el-dialog {
        margin-top: 5vh !important;
        height: 90% !important;
        width: 90% !important;
    }
    .select_item {
        text-align: center;
        vertical-align: middle;
    }
    .button_arear {
        width: 100%;
        height: 100px;
        display: -webkit-flex;
        -webkit-flex-wrap: wrap;
        justify-content: center;
        flex-direction: row;
        .general_cancel_button {
            height: 30px;
            padding-top: 7px;
            background: white;
            border: 1px solid black;
            color: black;
        }
        .general_confirm_button {
            height: 30px;
            padding-top: 7px;
        }
        .button_ele {
            width: 200xp;
            margin: 30px;
        }
    }
    .weight_select {
        width: 80px;
        height: 15px;
    }
    .preset_data_table {
        .enabled {
            background: rgb(153, 153, 153, 0.8);
        }
        th,
        td {
            border: 1px solid #bbb;
            font-size: 14px;
            padding: 6px;
            text-align: center;
            word-break: keep-all;
        }
        table {
            color: #333;
            border: 1px solid #bbb;
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 10px;
            .name {
                color: #0000cc;
            }
        }
    }
}
.view_detail_preset_data_table {
    .title {
        font-size: 15px;
        font-weight: 600;
        padding-bottom: 6px;
    }
    .preset_data_table {
        .enabled {
            background: rgb(153, 153, 153, 0.8);
        }
        th,
        td {
            border: 1px solid #bbb;
            font-size: 14px;
            padding: 6px;
            text-align: center;
            word-break: keep-all;
        }
        table {
            color: #333;
            border: 1px solid #bbb;
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 10px;
            .name {
                color: #0000cc;
            }
        }
    }
}
</style>

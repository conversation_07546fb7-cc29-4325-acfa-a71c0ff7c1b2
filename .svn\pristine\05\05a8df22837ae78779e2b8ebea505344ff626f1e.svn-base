import cloneDeep from 'lodash/cloneDeep'
import Vue from 'vue'
const initState ={
    typeIndex:{
        breast: 1,
        abdomen: 2,
        drChest: 3,
        breastSearch: 4,
        obstetrical: 5,
        cardiac:6,
    },
    type:[
        'undefined',
        'breast',
        'abdomen',
        'drChest',
        'breastSearch',
        'obstetrical',
        'cardiac',
    ],
    colors: [
        '#FFFFFF','#00FF00','#0000FF','#FFFF00',
        '#00FFFF','#FF00FF','#808000','#800000','#008000',
        '#800080','#008080',' #000080',
        '#993333','#000099','#003300',
        '#3333CC','#CCCCFF','#F00000','#FFCC00','#0099FF','#33CCCC',
        '#FFCCFF','#33CC33','#CC0000','#000066','#9900CC','#006600','#00CCFF','#66FF33','#FFf000',
        '#FF9D6F','#A6A6D2','#A3D1D1',' #DEDEBE','#D9B3B3','#DAB1D5',
        '#CEFFCE','#FFBFFF','#FFD9EC',' #FFD2D2','#CCFF80','#CCCC33'],
    iworksAbdomenTest: null,
    DrViews:null,
}

export default {
    state: cloneDeep(initState),
    mutations: {
        resetStore(state){
            if(window.vm.$store.state){
                Vue.set(window.vm.$store.state,'aiPresetData',cloneDeep(initState))
            }
        },
        updateAiPresetData(state, data) {
            if(data){
                let keys=Object.keys(data);
                for(let i in keys){
                    let key = keys[i]
                    Vue.set(state,key,data[key])
                }
            }
        }
    },
    actions: {},
    getters: {},
}



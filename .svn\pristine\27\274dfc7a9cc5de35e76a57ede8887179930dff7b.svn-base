import cloneDeep from 'lodash/cloneDeep'
import Vue from 'vue'

const initState = {
    friendApply:[],
    groupApply:[],
    liveCount:[],
}
export default {
    state: cloneDeep(initState),
    mutations: {
        resetStore(state){
            if(window.vm.$store.state){
                Vue.set(window.vm.$store.state,'notifications',cloneDeep(initState))
            }
        },
        addGroupApply(state, valObj) {
            valObj.loading=false
            let same = false
            state.groupApply.forEach(item=>{
                if(valObj.notify_id === item.notify_id){
                    same = true
                }
            })
            !same&&state.groupApply.push(valObj)
        },
        deleteGroupApplyByUidAndCid(state,data){
            let uid = data.uid;
            let cid = data.cid;
            let list=state.groupApply;
            for(let i=list.length-1; i>=0; i--){
                if(cid ==list[i].gid &&  uid== list[i].user_id){
                    list.splice(i,1);
                }
            }
        },
        deleteGroupApplyByCid(state,data){
            let cid = data.cid;
            let list=state.groupApply;
            for(let i=list.length-1; i>=0; i--){
                if(cid ==list[i].gid){
                    list.splice(i,1);
                }
            }
        },
        addFriendApply(state, valObj) {
            valObj.loading=false
            let same = false
            state.friendApply.forEach(item=>{
                if(valObj.notify_id === item.notify_id){
                    same = true
                }
            })
            !same&&state.friendApply.push(valObj)
        },
        deleteFriendApply(state,index){
            state.friendApply.splice(index,1);
        },
        deleteGroupApply(state,index){
            state.groupApply.splice(index,1);
        },
        clearGroupApply(state){
            state.groupApply.splice(0)
        },
        clearFriendApply(state){
            state.friendApply.splice(0)
        },
        updateApplyFriendLocalUrl(state,data){
            let imgObj=data.imgObj
            for(let index=0;index<state.friendApply.length;index++){
                if (imgObj.param.id==state.friendApply[index].param.id) {
                    let item=state.friendApply[index]
                    item.param.avatar_local=data.avatar_local
                    state.friendApply.splice(index,1,item)
                    break;
                }
            }
            // let index=data.index
            // let friend=state.friendApply[index];
            // friend.param.avatar_local=data.avatar_local
            // state.friendApply.splice(index,1,friend)
        },
        updateLiveCount(state,data){
            Vue.set(state,"liveCount",data);
        },
    },
    actions: {},
    getters: {}
}

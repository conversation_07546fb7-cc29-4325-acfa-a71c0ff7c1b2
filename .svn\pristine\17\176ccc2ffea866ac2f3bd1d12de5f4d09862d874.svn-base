import cloneDeep from 'lodash/cloneDeep'
import Vue from 'vue'
const initState ={
    ultrasound_machine_name:'',
    ultrasound_machine_type:'',
    ultrasound_machine_serial:'',
    transferList:[],
    examList:[],
    examTotal:0,
    initTransfer:false,
    initExam:false,
    listKey:{},
    unread:0,
    tabIndex:0,//超声助手tab类型，0为未选择，1为一键转发，2为检查浏览
}
export default {
    state: cloneDeep(initState),
    mutations: {
        resetStore(state){
            if(window.vm.$store.state){
                Vue.set(window.vm.$store.state,'ultrasoundMachine',cloneDeep(initState))
            }
        },
        updateMachine(state, valObj) {
            for (let key in valObj) {
                state[key] = valObj[key];
            }
        },
        destroyMachine(state){
            state.ultrasound_machine_name='';
            state.ultrasound_machine_type='';
            state.ultrasound_machine_serial='';
            state.transferList=[];
            state.examList=[];
            state.examTotal=0;
            state.initTransfer=false;
            state.initExam=false;
            state.unread=0;
            state.listKey={};
            state.tabIndex=0;
        },
        deleteTransferImage(state,data){
            let listIndex=[];
            for(let index in data){
                let list =data[index];
                listIndex.push(index);
                list.sort(function(a,b){
                    return a-b;
                })
                for(let k=0;k<list.length;k++){
                    let j_index=list[k]-k;//删除后j_index向前移
                    state.transferList[index].image_list.splice(j_index,1);
                }

            }
            //某日期下没有图片，整个item删除
            listIndex.sort(function(a,b){
                return a-b;
            })
            let k=0;
            for(let index of listIndex){
                index=index-k;
                if (state.transferList[index].image_list.length==0) {
                    let exam_id=state.transferList[index].exam_id
                    state.transferList.splice(index,1);
                    k++;
                    state.listKey[exam_id]=false;
                }
            }
        },
        updateTransferImageList(state,data){
            if (!state.initTransfer) {
                //未初始化一键转发页面接收到数据不处理，初始化后可以重新获取到
                return ;
            }
            for(let item of data.list){
                if (state.listKey[item.exam_id]) {
                    for(let item of state.transferList){
                        if(item.exam_id==data.list[0].exam_id){
                            item.image_list=item.image_list.concat(data.list[0].image_list)
                        }
                    }
                }else{
                    state.listKey[item.exam_id]=true;
                    state.transferList.push(item)
                }
            }
            // state.list=data.list
        },
        updateExamBrowseList(state,data){
            //获取到检查浏览分批数据后进行排序
            let start_index=data.start_index;
            for(let item of data.examList){
                item.index=start_index;
                start_index+=1;
            }
            let tempArr=data.examList.concat(state.examList);
            tempArr.sort(function(a,b){
                return parseInt(a.index)-parseInt(b.index);
            })
            state.examList=tempArr;
            state.examTotal=data.total_num;
        },
        toggleOpenExamImageList(state,data){
            for(let exam of state.examList){
                if (exam.serial_id==data.serial_id) {
                    exam.open=data.open;
                    break;
                }
            }
        },
        updateExamLocalImage(state,data){
            for(let exam of state.examList){
                if (exam.serial_id==data.serial_id) {
                    for(let img of exam.image_list){
                        if (img.url==data.image_list[0].url) {
                            img.loaded=data.image_list[0].loaded;
                            img.realUrl=data.image_list[0].realUrl;
                            img.msg_type=data.image_list[0].msg_type;
                            img.url2=data.image_list[0].url2;
                            img.exam_id=data.exam_id||data.serial_id;
                            img.path=data.image_list[0].path;
                            break;
                        }
                    }
                    break;
                }
            }
        },
        initImageListLoading(state,data){
            for(let exam of state.examList){
                if (exam.serial_id==data.serial_id) {
                    exam.initImageList=true;
                    break;
                }
            }
        },
        updateExamImageList(state,data){
            for(let exam of state.examList){
                if (exam.serial_id==data.serial_id) {
                    exam.finishImageList=true;
                    for(let img of data.image_list){
                        exam.image_list.push(img)
                    }
                    break;
                }
            }
        },
        updateExamImageInfo(state,data){
            for(let exam of state.examList){
                if (exam.serial_id==data.serial_id) {
                    for(let i=0;i<exam.image_list.length;i++){
                        let img=exam.image_list[i];
                        if (img.img_id==data.image_info.img_id) {
                            img.url=data.image_info.url;
                            img.url_local=data.image_info.url;
                            exam.image_list.splice(i,1,img);
                            break;
                        }
                    }
                    break;
                }
            }
        },
        toggleImageChecked(state,data){
            let checked=state.transferList[data.index].image_list[data.j_index].checked;
            state.transferList[data.index].image_list[data.j_index].checked=!checked;
        }
    },
    actions: {},
    getters: {}
}

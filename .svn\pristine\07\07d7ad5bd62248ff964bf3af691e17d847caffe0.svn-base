<template>
    <div>
        <CommonDialog
            :title="lang.live_setting"
            :show.sync="visible"
            width="500px"
            height="auto"
            append-to-body
            v-bind="$attrs"
            class="reverseControlPanel"
        >
            <div class="control-panel">
                <div class="controlling_tips_container">
                    <p>{{ getControllingDeviceNickName() }}</p>
                    <p>{{ lang.only_training_tips }}</p>
                    <span @click="disconnectRemoteControl">{{ lang.dopplerControl.Disconnect }}</span>
                </div>
                <!-- 顶部按钮 -->
                <div class="top-buttons">
                    <template v-if="controlButtonStatusMap['IMAGE_CMD_FREEZE'].CmdEnable === 1">
                        <el-button
                            type="primary"
                            size="small"
                            :class="[`controlButtons`]"
                            v-if="controlButtonStatusMap['IMAGE_CMD_FREEZE'].CurCommondValue === 1"
                            @click="handlePanelButtonClick('IMAGE_CMD_FREEZE')"
                            >解冻</el-button
                        >
                        <el-button
                            type="primary"
                            size="small"
                            :class="[`controlButtons`]"
                            v-else-if="controlButtonStatusMap['IMAGE_CMD_FREEZE'].CurCommondValue === 0"
                             @click="handlePanelButtonClick('IMAGE_CMD_FREEZE')"
                            >冻结</el-button
                        >
                    </template>
                    <template v-if="controlButtonStatusMap['IMAGE_CMD_SAVEIMG'].CmdEnable === 1">
                        <el-button
                            type="primary"
                            size="small"
                            :class="[`controlButtons`]"
                            v-if="controlButtonStatusMap['IMAGE_CMD_SAVEIMG'].CurCommondValue === 0"
                            >Save</el-button
                        >
                        <el-button
                            type="primary"
                            size="small"
                            :class="[`controlButtons`]"
                            v-else-if="controlButtonStatusMap['IMAGE_CMD_SAVEIMG'].CurCommondValue === 1"
                            >Save</el-button
                        >
                    </template>
                    <template v-if="controlButtonStatusMap['IMAGE_CMD_UPDATE'].CmdEnable === 1">
                        <el-button
                            type="primary"
                            size="small"
                            :class="[`controlButtons`]"
                            v-if="controlButtonStatusMap['IMAGE_CMD_UPDATE'].CurCommondValue === 0"
                            >Update</el-button
                        >
                        <el-button
                            type="primary"
                            size="small"
                            :class="[`controlButtons`]"
                            v-else-if="controlButtonStatusMap['IMAGE_CMD_UPDATE'].CurCommondValue === 1"
                            >Update</el-button
                        >
                    </template>
                    <template v-if="controlButtonStatusMap['IMAGE_CMD_MEASURE'].CmdEnable === 1">
                        <el-button
                            type="primary"
                            size="small"
                            :class="[`controlButtons`]"
                            v-if="controlButtonStatusMap['IMAGE_CMD_MEASURE'].CurCommondValue === 0"
                            >Measure</el-button
                        >
                        <el-button
                            type="primary"
                            size="small"
                            :class="[`controlButtons`]"
                            v-else-if="controlButtonStatusMap['IMAGE_CMD_MEASURE'].CurCommondValue === 1"
                            >Measure</el-button
                        >
                    </template>
                    <template v-if="controlButtonStatusMap['IMAGE_CMD_CALIPER'].CmdEnable === 1">
                        <el-button
                            type="primary"
                            size="small"
                            :class="[`controlButtons`]"
                            v-if="controlButtonStatusMap['IMAGE_CMD_CALIPER'].CurCommondValue === 0"
                            >Caliper</el-button
                        >
                        <el-button
                            type="primary"
                            size="small"
                            :class="[`controlButtons`]"
                            v-else-if="controlButtonStatusMap['IMAGE_CMD_CALIPER'].CurCommondValue === 1"
                            >Caliper</el-button
                        >
                    </template>
                </div>

                <!-- 模式切换 -->
                <div class="mode-switch">
                    <div class="label">模式切换</div>
                    <div class="mode-button-group">
                        <el-button size="small" :class="[`modeButtons`, checkShowModeClass('B')]" @click="handleModeButtonClick('IMAGE_SWITCH_B')">B</el-button>
                        <el-button size="small" :class="[`modeButtons`, checkShowModeClass('C')]" @click="handleModeButtonClick('IMAGE_SWITCH_COLOR')">C</el-button>
                        <el-button size="small" :class="[`modeButtons`, checkShowModeClass('M')]" @click="handleModeButtonClick('IMAGE_SWITCH_M')">M</el-button>
                        <el-button size="small" :class="[`modeButtons`, checkShowModeClass('PW')]" @click="handleModeButtonClick('IMAGE_SWITCH_PW')">PW</el-button>
                        <!-- <el-button size="small" :class="[`controlButtons`]">3D</el-button>
                        <el-button size="small" :class="[`controlButtons`]">4D</el-button> -->
                    </div>
                    <div class="mode-menu_button-group">
                        <el-button
                            size="mini"
                            plain
                            :class="[`mode_menu_button`, currentMode === item ? 'active' : '']"
                            v-for="item in enableModeList"
                            :key="item"
                            @click="handleModeMenuButtonClick(item)"
                            >{{ MODE_NAME_MAP[item] }} Mode</el-button
                        >
                        <!-- <el-button size="mini" plain :class="[`mode_menu_button`,currentModeMenu === 'B'?'active':'']">B</el-button>
                        <el-button size="mini" plain :class="[`mode_menu_button`,currentModeMenu === 'B'?'active':'']">B</el-button> -->
                    </div>
                </div>

                <!-- 控制选项 -->
                <div class="control-options">
                    <!-- 单项 -->
                    <div class="control-item" v-for="(item, index) in controlItems" :key="index">
                        <span class="control-label">{{ item.label }}</span>
                        <div v-if="item.type === 'buttons'" class="control-buttons">
                            <el-button size="mini" icon="el-icon-arrow-left"></el-button>
                            <span class="control-value">{{ item.value }}</span>
                            <el-button size="mini" icon="el-icon-arrow-right"></el-button>
                        </div>
                        <el-slider
                            v-if="item.type === 'slider'"
                            v-model="item.value"
                            :min="item.min"
                            :max="item.max"
                            :step="item.step"
                            show-tooltip
                        />
                    </div>

                    <!-- 开关项 -->
                    <!-- <div class="control-item" v-for="(switchItem, index) in switchItems" :key="`switch-${index}`">
                        <span class="control-label">{{ switchItem.label }}</span>
                        <el-switch v-model="switchItem.value" />
                    </div> -->
                </div>
            </div>
        </CommonDialog>
    </div>
</template>
<script>
import base from "../../lib/base";
import CommonDialog from "../../MRComponents/commonDialog.vue";
import { getLiveRoomObj } from "../../lib/common_base";
import { MODE_NAME_MAP,DOPPLER_BUTTONS} from "@/common/CLiveConferenceWeb/constants/reverseControl";
export default {
    mixins: [base],
    name: "ReverseControlPanel",
    model: {
        prop: "value",
        event: "change",
    },
    props: {
        value: {
            type: Boolean,
            default: false,
        },
        cid: {
            type: [Number, String],
            default: 0,
        },
    },
    watch: {
        value: {
            handler(val) {
                this.visible = val;
                if (val) {
                    this.initDopplerSay();
                }
            },
            immediate: true,
        },
        visible: {
            handler(val) {
                this.$emit("change", val);
            },
        },
    },
    computed: {
        LiveConferenceData() {
            return (
                (this.$store.state.liveConference[this.cid] &&
                    this.$store.state.liveConference[this.cid].LiveConferenceData) ||
                {}
            );
        },
    },
    components: {
        CommonDialog,
    },
    data() {
        return {
            MODE_NAME_MAP,
            visible: false,
            controlItems: [
                { label: "图像质量", type: "buttons", value: 0 },
                { label: "增益", type: "slider", value: 0, min: 0, max: 100, step: 1 },
                { label: "深度", type: "buttons", value: 0 },
                { label: "iBeam", type: "buttons", value: 0 },
                { label: "iClear", type: "buttons", value: 0 },
                { label: "iTouch", type: "buttons", value: 0 },
                { label: "动态范围", type: "slider", value: 0, min: 0, max: 100, step: 1 },
                { label: "灰阶阈值", type: "buttons", value: 0 },
                { label: "轴相关", type: "buttons", value: 0 },
                { label: "平滑", type: "buttons", value: 0 },
            ],
            switchItems: [
                { label: "一键优化", value: false },
                { label: "HDscope", value: false },
            ],
            liveRoom: null,
            currentMode: "",
            enableModeList: [],
            sortModeList: ["B", "Color", "M", "MMark", "PW", "PWMark"],
            currentModeMenu: "",
            controlButtonStatusMap: {
                IMAGE_CMD_FREEZE: {
                    CmdEnable: 0,
                    CurCommondValue: 0,
                },
                IMAGE_CMD_UPDATE: {
                    CmdEnable: 0,
                    CurCommondValue: 0,
                },
                IMAGE_CMD_SAVEIMG: {
                    CmdEnable: 0,
                    CurCommondValue: 0,
                },
                IMAGE_CMD_MEASURE: {
                    CmdEnable: 0,
                    CurCommondValue: 0,
                },
                IMAGE_CMD_CALIPER: {
                    CmdEnable: 0,
                    CurCommondValue: 0,
                },
            },
        };
    },
    created(){
        this.formatControlButtonStatusMap()
    },
    methods: {
        handleClose() {
            this.visible = false;
            if (this.liveRoom && this.liveRoom.CReverseControl) {
                this.liveRoom.CReverseControl.event.off("dopplerSay", this.handleDopplerSay);
            }
        },
        initDopplerSay() {
            this.liveRoom = getLiveRoomObj(this.cid);
            if (this.liveRoom && this.liveRoom.CReverseControl) {
                this.liveRoom.CReverseControl.event.on("dopplerSay", this.handleDopplerSay);
            }
        },
        handleDopplerSay(data) {
            console.error("doppler say", data);
            const command = data.command;
            if (command === "REMOTE_CONTROL_MODECHANGE_NOTIFY") {
                //模式发生变化
                this.currentMode = data.Mode_cur_name;
                this.enableModeList = [];
                data.Mode_List.forEach((item) => {
                    if (MODE_NAME_MAP.hasOwnProperty(item.Mode_name)) {
                        this.enableModeList.push(item.Mode_name);
                    }
                });
                // 根据 sortModeList 的顺序对 enableModeList 进行排序
                this.enableModeList.sort((a, b) => {
                    return this.sortModeList.indexOf(a) - this.sortModeList.indexOf(b);
                });
            } else if (command === "REMOTE_CONTROL_CHANGE_NOTIFICATION") {
                //按钮状态发生变化
                data.CmdSet.forEach((item) => {
                    if (this.controlButtonStatusMap.hasOwnProperty(item.CmdID)) {
                        Object.keys(item).forEach((key) => {
                            this.$set(this.controlButtonStatusMap[item.CmdID], key, item[key]);
                        });
                    } else {
                        this.$set(this.controlButtonStatusMap, item.CmdID, {});
                        Object.keys(item).forEach((key) => {
                            this.$set(this.controlButtonStatusMap[item.CmdID], key, item[key]);
                        });
                    }
                });
            }
        },
        getControllingDeviceNickName() {
            if (this.LiveConferenceData.remoteControlData && this.LiveConferenceData.remoteControlData.nickname) {
                return this.lang.doppler_controlling_device_name.replace(
                    "${name}",
                    this.LiveConferenceData.remoteControlData.nickname
                );
            }
            return this.lang.doppler_controlling_device_name.replace("${name}", "");
        },
        disconnectRemoteControl() {
            this.liveRoom.handleCancelRemoteControl();
            this.visible = false;
        },
        checkShowModeClass(Mode) {
            if (this.enableModeList.includes(`${Mode}Mark`)) {
                return "middle";
            } else if (MODE_NAME_MAP[this.currentMode] === Mode) {
                return "active";
            } else if (this.enableModeList.find((item) => MODE_NAME_MAP[item] === Mode)) {
                return "enable";
            }
        },
        formatControlButtonStatusMap(){
            DOPPLER_BUTTONS.forEach(item=>{
                if (this.controlButtonStatusMap.hasOwnProperty(item.id)) {
                    Object.keys(item).forEach((key) => {
                        this.$set(this.controlButtonStatusMap[item.id], key, item[key]);
                    });
                } else {
                    this.$set(this.controlButtonStatusMap, item.id, {});
                    Object.keys(item).forEach((key) => {
                        this.$set(this.controlButtonStatusMap[item.id], key, item[key]);
                    });
                }
            })

        },
        handlePanelButtonClick(id){
            const params = {
                CmdID:id,
                CmdNumber:new Date().getTime(),
                CmdValue:this.controlButtonStatusMap[id].CurCommondValue,
                OperationType:0
            }
            this.liveRoom.sendRemoteControlCommand(params)
        },
        handleModeButtonClick(id){
            this.handlePanelButtonClick(id)
        },
        handleModeMenuButtonClick(item){
            const id = `IMAGE_CMD_SHIFT_TO_${item.toUpperCase()}`
            const params = {
                CmdID:id,
                CmdNumber:new Date().getTime(),
                CmdValue:1,
                OperationType:0
            }
            this.liveRoom.sendRemoteControlCommand(params)
        }
    },
};
</script>
<style lang="scss">
.reverseControlPanel {
    user-select:none;
    .dialog-content {
        overflow: hidden;
        display: flex;
        flex-direction: column;
        padding: 10px;
    }
    .el-dialog {
        background: #000;
        border: 1px solid #ccc;
    }
    .control-panel {
        width: 100%;
        overflow: hidden;
        background-color: #000;
        color: #fff;
        border-radius: 8px;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        height: 100%;
        .controlling_tips_container {
            padding-top: 10px;
            padding-bottom: 10px;
            margin-top: 10px;
            margin-bottom: 10px;
            padding-left: 5px;
            padding-right: 5px;
            position: relative;
            background: #ccc;
            color: #000;
            span {
                position: absolute;
                right: 10px;
                top: 50%;
                transform: translateY(-50%);
                cursor: pointer;
                color: #407eb6;
            }
        }
        .modeButtons {
            background-color: #5a98cf;
            border-color: #5a98cf;
            color: #fff;
            height: 36px;
            &.active {
                background: #fff;
                color: #000;
            }
            &.middle {
                border: #ffe600 2px dashed;
                box-sizing: border-box;
            }
            &.enable {
                background: #12bb12;
            }
            &:hover {
                background-color: #3498db; /* 按钮悬停效果 */
            }
        }
        .controlButtons {
            color: #fff;
            background-color: #5a98cf;
            border-color: #5a98cf;
            border-radius: 8px;
            padding: 10px 50px;
            text-align: center;
            font-size: 14px;
            margin: 0;
            cursor: pointer;
            &:hover {
                background-color: #3498db; /* 按钮悬停效果 */
            }
        }
        .top-buttons {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 10px;
        }
        .mode-switch {
            .mode-button-group {
                width: 100%;
                display: flex;
                justify-content: space-between;
                margin-bottom: 20px;
                .el-button {
                    min-width: 60px;
                }
            }
            .mode-menu_button-group {
                display: flex;
                justify-content: space-between;
                flex-wrap: wrap;
                .el-button {
                    width: 49%;
                    margin-bottom: 10px;
                    margin-left: 0px;
                    &.active {
                        color: #5a98cf;
                        border: 3px solid #5a98cf;
                    }
                }
            }
            margin-bottom: 10px;
            .label {
                margin-bottom: 10px;
                font-size: 18px;
                font-weight: 600;
            }
            .mode-indicator {
                text-align: center;
                background: #fff;
                color: #000;
                padding: 5px;
                margin: 10px 0;
            }
        }
        .control-options {
            display: flex;
            flex-direction: column;
            gap: 10px;
            flex: 1;
            overflow-y: auto;
            padding-right: 20px;
            scrollbar-color: #5a98cf #e6f1fc; /* 滑块颜色和轨道背景颜色 */ /* 适配 Firefox 的滚动条颜色 */
            scrollbar-width: thin; /* 设置滚动条宽度为“thin” */ /* 适配 Firefox 的滚动条颜色 */
            /* 滚动条的整体样式 */
            &::-webkit-scrollbar {
                width: 8px; /* 滚动条宽度 */
            }

            /* 滚动条的轨道（背景） */
            &::-webkit-scrollbar-track {
                background: #e6f1fc; /* 滚动条轨道背景颜色，浅蓝色 */
                border-radius: 10px; /* 圆角轨道 */
            }

            /* 滚动条的滑块 */
            &::-webkit-scrollbar-thumb {
                background: #5a98cf; /* 滚动条滑块颜色 */
                border-radius: 10px; /* 圆角滑块 */
            }

            /* 滚动条滑块在悬停时的颜色 */
            &::-webkit-scrollbar-thumb:hover {
                background: #407eb6; /* 悬停时更深的蓝色 */
            }
        }
        .control-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 10px 0;
            .control-label {
                width: 100px;
            }
            .el-slider {
                flex: 1;
            }
            .control-buttons {
                display: flex;
                align-items: center;
                gap: 5px;
            }
            .control-value {
                width: 30px;
                text-align: center;
            }
        }
    }
}
</style>

<template>
    <div class="uLinker_instruction_manual_page second_level_page">
            <mrHeader :leftArrow="routeLevels>1">
                <template #title>
                    {{ lang.quick_start_guide }}
                </template>
            </mrHeader>
            <div class="instruction_manual_container">
                <div id="pdfvuer"></div>
            </div>
            <footer v-if="routeLevels === 1">
                <van-button type="primary" block @click="gotoIndexPage">{{lang.next_step_text}}</van-button>
            </footer>
        </div>
</template>

<script>
import Pdfh5 from "pdfh5";
import base from "../lib/base";
import axios from 'axios';
import "pdfh5/css/pdfh5.css";
import { Button} from "vant";
import Panzoom from '@panzoom/panzoom';
import Tool from '@/common/tool'
// 判断是否允许离开路由
let allowLeave = false; // 这里设置为 false，表示禁止离开
export default {
    name:'uLinkerInstructionManual',
    mixins: [base],
    components: {
        VanButton:Button
    },
    beforeRouteLeave(to, from, next) {
    // 在用户尝试离开当前路由时执行逻辑
        if (allowLeave) {
            next(); // 允许离开，继续导航
        } else {
            next(false); // 禁止离开，取消导航
        }
    },
    data() {
        return {
            pdfh5: null,
            version:'',
            languageData:[
                {
                    value:'CN',
                    text:'中文',
                },
                {
                    value:'EN',
                    text:'English'
                },
            ],
            currentLanguage:'',
            routeLevels:0
        };
    },
    computed: {

    },
    created(){
        this.currentLanguage = this.lang.currentLanguage
        const routePath = this.$route.path;
        this.routeLevels = routePath.split('/').length - 1;
        if(this.routeLevels>1){
            allowLeave = true
        }
        console.log('路由层级数：', this.routeLevels);


    },
    mounted() {

        this.loadPdf()
    },
    methods: {
        loadPdf(){
            const server_type = this.$store.state.systemConfig.server_type
            let host = server_type.protocol + server_type.host + server_type.port
            if(host.indexOf('localhost')>-1||host.indexOf('192.168')>-1){
                host = `https://${Tool.getHostConfig().dev}`;
            }
            let fileUrl = host + `/doc/uLinkerQuickStartGuide_V1.0_EN.pdf`
            axios
                .get(fileUrl, {
                    responseType: 'arraybuffer'
                })
                .then(res => {
                    this.pdfh5 = new Pdfh5('#pdfvuer', {
                        data: res.data,
                        lazy:true,
                    });
                    //监听完成事件
                    this.pdfh5.on("complete", function (status, msg, time) {
                        console.log("状态：" + status + "，信息：" + msg + "，耗时：" + time + "毫秒，总页数：" + this.totalNum);
                    });
                    setTimeout(()=>{
                        const panzoomElement = document.querySelector('#pdfvuer');
                        const panzoom = Panzoom(panzoomElement, {
                            maxScale: 2,
                            minScale: 1,
                            contain: 'outside'  // This will ensure the image stays within bounds
                        });
                        let isPinching = false;
                        let lastTapTime = 0;
                        const handleTouchStart = (e) => {
                            if (e.touches.length === 2) {
                                isPinching = true;

                            }
                        };

                        const handleTouchMove = (e) => {
                            if (isPinching) {
                                e.preventDefault(); // Prevent default scrolling during pinch
                            }
                        };

                        const handleTouchEnd = (e) => {
                            if (e.touches.length < 2) {
                                isPinching = false;
                            }
                        };
                        const handleDoubleTap = (e) => {

                            const currentTime = new Date().getTime();
                            const tapLength = currentTime - lastTapTime;
                            if (tapLength < 300 && tapLength > 0) {
                                e.preventDefault();
                                console.log(panzoom.getScale())
                                if (panzoom.getScale() === 1) {
                                    panzoom.zoom(2, { animate: true }); // 放大到2倍
                                } else {
                                    panzoom.zoom(1, { animate: true }); // 缩小到1倍
                                }
                            }
                            lastTapTime = currentTime;
                        };
                        panzoomElement.addEventListener('touchstart', handleTouchStart);
                        panzoomElement.addEventListener('touchmove', handleTouchMove);
                        panzoomElement.addEventListener('touchend', handleTouchEnd);
                        panzoomElement.addEventListener('touchcancel', handleTouchEnd);
                        panzoomElement.addEventListener('click', handleDoubleTap);
                    },0)
                });
        },
        handleChangeMenu(item){
            this.loadPdf()
        },
        gotoIndexPage(){
            allowLeave = true
            window.localStorage.setItem('isReadULinkerManual','1')
            this.$router.replace('/index')
        }
    },
};
</script>
<style lang="scss" scoped>
.uLinker_instruction_manual_page{
    display: flex;
    flex-direction: column;
    height: 100%;
    .instruction_manual_container{
        flex:1;
    }
}
#buttons {
    margin-left: 0 !important;
    margin-right: 0 !important;
}
/* Page content */
.content {
    padding: 16px;
}
#pdfvuer > div {
    margin: 0 !important;
}
</style>

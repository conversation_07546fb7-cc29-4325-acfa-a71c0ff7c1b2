<template>
    <el-dialog
        class="cropper_image_dialog"
        :visible.sync="visible"
        v-bind="$attrs"
        :close-on-click-modal="closeOnClickModal"
        :close-on-press-escape="closeOnPressEscape"
        width="1200px"
        height="auto"
        append-to-body
    >
        <div class="cropper-content">
            <div class="cropper-box">
                <div class="cropper">
                    <vue-cropper
                        ref="cropper"
                        :img="option.img"
                        :outputSize="option.outputSize"
                        :outputType="option.outputType"
                        :info="option.info"
                        :canScale="option.canScale"
                        :autoCrop="option.autoCrop"
                        :autoCropWidth="option.autoCropWidth"
                        :autoCropHeight="option.autoCropHeight"
                        :fixed="option.fixed"
                        :fixedNumber="option.fixedNumber"
                        :full="option.full"
                        :fixedBox="option.fixedBox"
                        :canMove="option.canMove"
                        :canMoveBox="option.canMoveBox"
                        :original="option.original"
                        :centerBox="option.centerBox"
                        :height="option.height"
                        :infoTrue="option.infoTrue"
                        :maxImgSize="option.maxImgSize"
                        :enlarge="option.enlarge"
                        :mode="option.mode"
                        @realTime="realTime"
                        @imgLoad="imgLoad"
                    >
                    </vue-cropper>
                </div>
                <!--底部操作工具按钮-->
                <div class="footer-btn">
                    <div class="scope-btn">
                        <!-- <label class="btn" for="uploads">选择封面</label> -->
                        <!-- <input type="file" id="uploads" style="position:absolute; clip:rect(0 0 0 0);" accept="image/png, image/jpeg, image/gif, image/jpg" @change="selectImg($event)"> -->
                        <el-button size="mini" type="danger" plain icon="el-icon-zoom-in" @click="changeScale(10)"
                            >{{lang.zoom_in}}</el-button
                        >
                        <el-button size="mini" type="danger" plain icon="el-icon-zoom-out" @click="changeScale(-10)"
                            >{{lang.zoom_out}}</el-button
                        >
                        <el-button size="mini" type="danger" plain @click="rotateLeft">↺ {{lang.rotate_left}}</el-button>
                        <el-button size="mini" type="danger" plain @click="rotateRight">↻ {{lang.rotate_right}}</el-button>
                        <el-button size="mini" type="primary" plain icon="el-icon-upload" @click="afterCropper"
                            >{{lang.confirm_txt}}</el-button
                        >
                    </div>
                    <!-- <div class="upload-btn">
          <el-button size="mini" type="success" @click="uploadImg('blob')">上传封面 <i class="el-icon-upload"></i></el-button>
        </div> -->
                </div>
            </div>
            <!--预览效果图-->
            <div class="show-preview">
                <div class="preview" :style="{ width: previews.w + 'px', height: previews.h + 'px' }">
                    <img :src="previews.url" :style="previews.img" />
                </div>
            </div>
        </div>
    </el-dialog>
</template>
<script>
import { VueCropper } from "vue-cropper";
import base from '../lib/base'
export default {
    name: "cropperImgComponents",
    mixins: [base],
    inheritAttrs: true,
    props: {
        closeOnClickModal: {
            type: Boolean,
            default: false,
        },
        closeOnPressEscape: {
            type: Boolean,
            default: false,
        },
        show: {
            type: Boolean,
            default: false,
        },
    },
    components: {
        VueCropper,
    },
    computed: {
        visible: {
            get() {
                return this.show;
            },
            set(val) {
                this.$emit("update:show", val);
            },
        },
    },
    watch: {

    },
    data() {
        return {
            previews: {},
            option: {
                img: "", //裁剪图片的地址
                outputSize: 1, //裁剪生成图片的质量(可选0.1 - 1)
                outputType: "jpeg", //裁剪生成图片的格式（jpeg || png || webp）
                info: true, //图片大小信息
                canScale: true, //图片是否允许滚轮缩放
                autoCrop: true, //是否默认生成截图框
                autoCropWidth: 230, //默认生成截图框宽度
                autoCropHeight: 150, //默认生成截图框高度
                // fixed: true,         //是否开启截图框宽高固定比例
                // fixedNumber: [1.53, 1], //截图框的宽高比例
                full: true, //false按原比例裁切图片，不失真
                fixedBox: false, //固定截图框大小，不允许改变
                canMove: true, //上传图片是否可以移动
                canMoveBox: false, //截图框能否拖动
                original: false, //上传图片按照原始比例渲染
                centerBox: false, //截图框是否被限制在图片里面
                height: true, //是否按照设备的dpr 输出等比例图片
                infoTrue: false, //true为展示真实输出图片宽高，false展示看到的截图框宽高
                maxImgSize: 3000, //限制图片最大宽度和高度
                enlarge: 1, //图片根据截图框输出比例倍数
                mode: "contain", //图片默认渲染方式
            },
        };
    },
    mounted() {},
    methods: {
        // 实时预览函数
        realTime(data) {
            this.previews = data;
        },
        imgLoad(msg) {
            console.log(msg, "imgLoad");
            console.log(this.$refs.cropper)
        },
        //选择图片
        selectImg(e) {
            let file = e.target.files[0];
            if (!/\.(jpg|jpeg|png|JPG|PNG)$/.test(e.target.value)) {
                this.$message({
                    message: "图片类型要求：jpeg、jpg、png",
                    type: "error",
                });
                return false;
            }
            //转化为blob
            let reader = new FileReader();
            reader.onload = (e) => {
                let data;
                if (typeof e.target.result === "object") {
                    data = window.URL.createObjectURL(new Blob([e.target.result]));
                } else {
                    data = e.target.result;
                }
                this.option.img = data;
            };
            //转化为base64
            reader.readAsDataURL(file);
        },
        //图片缩放
        changeScale(num) {
            num = num || 1;
            this.$refs.cropper.changeScale(num);
        },
        //向左旋转
        rotateLeft() {
            this.$refs.cropper.rotateLeft();
        },
        //向右旋转
        rotateRight() {
            this.$refs.cropper.rotateRight();
        },
        preHandleCrossImage(src) {
            let image = new Image();
            // 处理缓存
            // image.src = 'https://rmtus-patient-data-dev.oss-cn-shanghai.aliyuncs.com/PATIENT_DATA/20230119/20230119-***********-579C/284908000200190F34579C/202301190856180010ABD/SingleFrame.jpg' + '?v=' + Math.random();
            // 支持跨域图片
            image.src = src;
            image.crossOrigin = "Anonymous";
            image.onload = () => {
                let cropperBox = document.querySelector(".cropper-drag-box");
                let cropperRect = cropperBox.getBoundingClientRect();
                let cropperRadio = cropperRect.width / cropperRect.height;
                let imageRadio = image.width / image.height;
                if (imageRadio >= cropperRadio) {
                    this.option.autoCropWidth = cropperRect.width;
                    this.option.autoCropHeight = cropperRect.width / imageRadio;
                } else {
                    this.option.autoCropHeight = cropperRect.height;
                    this.option.autoCropWidth = cropperRect.height * imageRadio;
                }
                this.option.mode = `${this.option.autoCropWidth}px ${this.option.autoCropHeight}px`;
                console.log(image);
                this.option.img = this.transBase64FromImage(image);
            };
            image.onerror = (error)=>{
                console.log('image_error',error)
            }
        },
        // 将网络图片转换成base64格式
        transBase64FromImage(image) {
            let canvas = document.createElement("canvas");
            canvas.width = image.width;
            canvas.height = image.height;
            let ctx = canvas.getContext("2d");
            ctx.drawImage(image, 0, 0, image.width, image.height);
            // 可选其他值 image/jpeg
            return canvas.toDataURL("image/png");
        },
        afterCropper() {
            this.$refs.cropper.getCropBlob(async (data) => {
                this.transToFile(data, "cropper", data.type).then((file) => {
                    let cropperImageUrl = window.URL.createObjectURL(data);
                    console.log(data, file);
                    this.$emit("afterCropper", cropperImageUrl, { raw: file, name: `cropper.jpg` });
                    this.visible = false;
                });
            });
        },

        async transToFile(blob, fileName, fileType) {
            return new window.File([blob], fileName, { type: fileType, lastModified: Date.now() });
        },
        //异步onload图片
        onLoadImg(photoUrl) {
            return new Promise(function(resolve, reject) {
                let reader = new FileReader();
                reader.readAsDataURL(photoUrl);
                reader.onload = e => {
                    resolve(e.target["result"]);
                };
            });
        },
        /**
     *
     * @param file
     */
        loadFile(file) {
            if (file instanceof File) {
                this.onLoadImg(file).then(base64 => {
                    this.option.img = base64;
                });
            } else {
                throw new Error("Arguments file is not File");
            }
        },
        /**
     *
     * @param base64
     */
        loadBase64(base64) {
            if (typeof base64 !== "string") {
                throw new Error("Arguments base64 is not string");
            }
            const base = base64.split(",");
            if (!/^data:image\/(.*?);base64$/.test(base[0])) {
                throw new Error("Arguments base64 MIME is not image/*");
            }
            // Base64 Regex @see https://learnku.com/articles/42295
            if (
                !/^[\/]?([\da-zA-Z]+[\/+]+)*[\da-zA-Z]+([+=]{1,2}|[\/])?$/.test(base[1])
            ) {
                throw new Error("Not standard base64");
            }
            this.option.img = base64;
        },
        /**
     *
     * @param imageUrl
     */
        loadImageUrl(url) {
            // this.option.img = url
            this.preHandleCrossImage(url)
        },
    },
};
</script>
<style lang="scss">
.cropper_image_dialog {
    .cropper-content {
        display: flex;
        display: -webkit-flex;
        justify-content: flex-end;
    }
    .cropper-box {
        flex: 1;
        width: 100%;
    }
    .cropper {
        width: auto;
        height: 300px;
    }

    .show-preview {
        flex: 1;
        -webkit-flex: 1;
        display: flex;
        display: -webkit-flex;
        justify-content: center;
    }
    .preview {
        overflow: hidden;
        border: 1px solid #67c23a;
        background: #cccccc;
    }
    .footer-btn {
        margin-top: 20px;
        display: flex;
        display: -webkit-flex;
        justify-content: flex-end;
    }
    .upload-btn {
        flex: 1;
        -webkit-flex: 1;
        display: flex;
        display: -webkit-flex;
        justify-content: center;
    }
    .scope-btn {
        display: flex;
        display: -webkit-flex;
        justify-content: space-between;
        padding-right: 10px;
        flex-wrap: wrap;
        &>button{
            margin-top: 10px;
        }
    }
    .btn {
        outline: none;
        display: inline-block;
        line-height: 1;
        white-space: nowrap;
        cursor: pointer;
        -webkit-appearance: none;
        text-align: center;
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
        outline: 0;
        -webkit-transition: 0.1s;
        transition: 0.1s;
        font-weight: 500;
        padding: 8px 15px;
        font-size: 12px;
        border-radius: 3px;
        color: #fff;
        background-color: #409eff;
        border-color: #409eff;
        margin-right: 10px;
    }
}
</style>

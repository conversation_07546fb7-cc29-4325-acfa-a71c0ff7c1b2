<template>
    <div class="pass-rate-circle">
        <div class="chart-title">总合格率</div>
        <div class="circle-chart-container">
            <div class="circle-chart" ref="circleChart">
                <svg width="100%" height="100%" viewBox="0 0 480 384">
                    <!-- 外圈刻度条（50个刻度） -->
                    <g class="scale-ticks">
                        <line
                            v-for="(tick, index) in scaleTicks"
                            :key="index"
                            :x1="tick.x1"
                            :y1="tick.y1"
                            :x2="tick.x2"
                            :y2="tick.y2"
                            stroke="#E0E6ED"
                            stroke-width="4"
                            stroke-linecap="round"
                        />
                    </g>
                    
                    <!-- 外圈背景（上半圆） -->
                    <path
                        d="M 84 192 A 156 156 0 0 1 396 192"
                        fill="none"
                        stroke="#e6f0ff"
                        stroke-width="12"
                        stroke-linecap="butt"
                    />
                    <!-- 外圈进度（上半圆） -->
                    <path
                        d="M 84 192 A 156 156 0 0 1 396 192"
                        fill="none"
                        stroke="url(#gradient1)"
                        stroke-width="12"
                        stroke-linecap="butt"
                        :stroke-dasharray="halfCircumference"
                        :stroke-dashoffset="halfDashoffset"
                        class="progress-circle"
                    />
                    
                    <!-- 内圈（完整圆圈，无进度） -->
                    <circle
                        cx="240"
                        cy="192"
                        r="126"
                        fill="none"
                        stroke="url(#innerGradient)"
                        stroke-width="4"
                        style="filter: drop-shadow(0 2px 4px rgba(194,194,194,0.45))"
                    />
                    
                    <!-- 渐变定义 -->
                    <defs>
                        <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="0%">
                            <stop offset="0%" style="stop-color:#64BDED;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#64BDED;stop-opacity:1" />
                        </linearGradient>
                        <radialGradient id="innerGradient" cx="49%" cy="12%">
                            <stop offset="0%" style="stop-color:#C4D2FF;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#6D9AF1;stop-opacity:1" />
                        </radialGradient>
                    </defs>
                </svg>
                
                <!-- 刻度标记 -->
                <div class="scale-marks">
                    <div 
                        class="scale-mark scale-0" 
                        :style="scaleMarkPositions['scale-0']">0</div>
                    <div 
                        class="scale-mark scale-25" 
                        :style="scaleMarkPositions['scale-25']">25</div>
                    <div 
                        class="scale-mark scale-50" 
                        :style="scaleMarkPositions['scale-50']">50</div>
                    <div 
                        class="scale-mark scale-75" 
                        :style="scaleMarkPositions['scale-75']">75</div>
                    <div 
                        class="scale-mark scale-100" 
                        :style="scaleMarkPositions['scale-100']">100</div>
                </div>
                
                <!-- 中心数据区域 -->
                <div class="center-data">
                    <div class="center-label">总合格率</div>
                    <div class="center-value">{{ passRate }}%</div>
                    <div class="center-info">报告总数：{{ totalReports }}</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: "PassRateCircle",
    props: {
        passRate: {
            type: Number,
            default: 85
        },
        totalReports: {
            type: Number,
            default: 30000
        },
        // 外圈进度值
        outerProgress: {
            type: Number,
            default: null // 如果为null，则使用passRate
        }
    },
    computed: {
        // 半圆周长
        halfCircumference() {
            return Math.PI * 156;
        },
        // 半圆偏移量
        halfDashoffset() {
            const progress = this.outerProgress !== null ? this.outerProgress : this.passRate;
            return this.halfCircumference - (this.halfCircumference * progress) / 100;
        },
        // 刻度条数据
        scaleTicks() {
            const ticks = [];
            const centerX = 240;
            const centerY = 192;
            const outerRadius = 180; // 外圈半径156 + 24px间距
            const innerRadius = 172; // 刻度线内端
            const totalTicks = 50;
            
            for (let i = 0; i <= totalTicks; i++) {
                const angle = (Math.PI * i) / totalTicks; // 从0到π的角度
                const x1 = centerX + innerRadius * Math.cos(Math.PI - angle);
                const y1 = centerY - innerRadius * Math.sin(Math.PI - angle);
                const x2 = centerX + outerRadius * Math.cos(Math.PI - angle);
                const y2 = centerY - outerRadius * Math.sin(Math.PI - angle);
                
                ticks.push({
                    x1: x1,
                    y1: y1,
                    x2: x2,
                    y2: y2
                });
            }
            
            return ticks;
        },
        // 计算数字标记的精确位置
        scaleMarkPositions() {
            const centerX = 240;
            const centerY = 192;
            const markRadius = 192; // 数字标记距离中心的半径，往外移一点
            const positions = {};
            
            // 计算各个百分比对应的角度和位置
            const percentages = [0, 25, 50, 75, 100];
            percentages.forEach(percent => {
                const angle = Math.PI * (1 - percent / 100); // 从π到0的角度
                const x = centerX + markRadius * Math.cos(angle);
                const y = centerY - markRadius * Math.sin(angle);
                
                // 计算切线角度（垂直于半径方向）
                const tangentAngle = angle - Math.PI / 2; // 切线角度
                const rotationDegrees = (tangentAngle * 180) / Math.PI; // 转换为度数
                
                positions[`scale-${percent}`] = {
                    left: `${x}px`,
                    top: `${y}px`,
                    transform: `translate(-50%, -50%) rotate(${rotationDegrees}deg)`
                };
            });
            
            return positions;
        }
    },
    mounted() {
        // 添加动画效果
        this.$nextTick(() => {
            const progressCircles = this.$el.querySelectorAll('.progress-circle');
            progressCircles.forEach(circle => {
                circle.style.transition = 'stroke-dashoffset 2s ease-in-out';
            });
        });
    }
};
</script>

<style scoped lang="scss">
.pass-rate-circle {
    background: #fff;
    border-radius: 14px;
    padding: 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    height: 480px;
    width: 480px;
    display: flex;
    flex-direction: column;
    
    .chart-title {
        font-family: HarmonyOS_Sans_SC_Bold;
        font-size: 24px;
        font-weight: 700;
        color: #000000;
        margin-bottom: 0;
        text-align: left;
        background: #F5F7FB;
        padding: 14px;
        border-radius: 10px 10px 0 0;
    }
    
    .circle-chart-container {
        flex-grow: 1;
        display: flex;
        justify-content: center;
        align-items: center;
        text-align: center;
        padding: 12px 12px 0 12px;
    }
    
    .circle-chart {
        position: relative;
        width: 480px;
        height: 384px;
        
        svg {
            position: absolute;
            top: 0;
            left: 0;
        }
        
        .scale-marks {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            
            .scale-mark {
                position: absolute;
                font-size: 14px;
                font-weight: 500;
                color: #8c8c8c;
            }
        }
        
        .center-data {
            position: absolute;
            top: 192px;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            
            .center-label {
                font-family: HarmonyOS_Sans_SC_Bold;
                font-size: 24px;
                font-weight: 700;
                line-height: 26px;
                color: #30477A;
                margin-bottom: 10px;
            }
            
            .center-value {
                font-family: HarmonyOS_Sans_SC_Medium;
                font-size: 48px;
                font-weight: 500;
                color: #30477A;
                margin-bottom: 10px;
                line-height: 1;
            }
            
            .center-info {
                font-family: HarmonyOS_Sans_SC_Medium;
                font-size: 14px;
                color: #30477A;
                font-weight: 700;
            }
        }
    }
    
    .progress-circle {
        transition: stroke-dashoffset 2s ease-in-out;
    }
}
</style> 
import Tool from '@/common/tool.js'
import iworksInternational from '@/common/iworksInternational.js'
import {findProtocolViewNode,setProtocolTree,isIworksTest, hasAiAnalyzeResult, isSmartIworks,findProtocolViewNodeByStructId} from '../lib/common_base'
/*
* iworks模块代码
*/
export default {
    data(){
        return {
            setProtocolTree
        }
    },
    methods:{
        getIworksList(conversation){
            conversation.socket.emit('query_iworks_protocol_list',{
                key:{},
                start:0,
                count:36
            },(err,result)=>{
                this.$store.commit('conversationList/updateIworksList',{
                    cid:conversation.id,
                    index:result.index,
                    list:result.list
                })
                for(let item of result.list){
                    this.setIworksProtocol(item.iworks_protocol)
                }
            })
        },
        pushIworks(msg,cid){
            let list=this.conversationList[cid].iworksList.list
            let index=this.conversationList[cid].iworksList.index
            let pos=-1;
            for(let i_index=0;i_index<list.length;i_index++){
                if (list[i_index].protocol_guid==msg.protocol_guid) {
                    pos=i_index;
                    break
                }
            }
            //接收的iworks已在列表中，只更新顺序
            if (pos!=-1) {
                list.splice(pos,1);
            }
            list.unshift(msg);
            this.$store.commit('conversationList/updateIworksList',{
                cid:cid,
                index:index,
                list:list
            })
            this.setIworksProtocol(msg.iworks_protocol)
        },
        setIworksProtocol(protocol){
            let guid=protocol.attributes.GUID;
            let data={
                iworks_protocol_list:{}
            }
            protocol.protocolTree=[this.setProtocolTree(protocol)]
            protocol.protocol_name = protocol.protocolTree[0].label;
            protocol.viewList=this.setViewList(protocol.protocolTree[0],[])
            data.iworks_protocol_list[guid]=protocol;
            this.$store.commit('gallery/setCommentToGallery',data)
            // if (!this.gallery.iworks_protocol_list[guid]) {

            // }
        },
        getIworksProtocolInfo(message, type) {
            let text="";
            if(message && message.iworks_protocol && message.iworks_protocol.attributes) {
                text=message.iworks_protocol.attributes[type]||"";
                if (type == 'name') {
                    const currentLang = window.vm.$store.state.language.currentLanguage === 'CN'?'CN':'EN';
                    const iworksMap = iworksInternational[currentLang];
                    text = iworksMap[text] || text;
                }else if (type === 'CreateDate') {
                    text = Tool.formatJulianOrGregorianToDate(text);
                }
            }
            return text;
        },
        setViewList(node,arr){
            if (node.type==2) {
                arr.push(node)
            }
            if (node.children) {
                for(let child of node.children){
                    this.setViewList(child,arr);
                }
            }
            return arr
        },
        parseIworksImage(exam,protocol_execution_guid){
            exam.iworksImages=[]
            exam.noneIworksImages=[]
            let iworksTestFlag = isIworksTest(exam)
            if(!iworksTestFlag&&exam.imageList&&exam.imageList[0]){
                iworksTestFlag = isIworksTest({iworks_protocol_execution:exam.iworks_protocol_execution,exam_type:exam.imageList[0].exam_type})
            }
            const currentLang = window.vm.$store.state.language.currentLanguage === 'CN'?'CN':'EN';
            const iworksMap = iworksInternational[currentLang];
            for(let item of exam.imageList){
                const guid = protocol_execution_guid || item.protocol_execution_guid
                if (guid&&item.protocol_view_guid) {
                    let protocol=this.gallery.iworks_protocol_list[guid]
                    item.isIworksTest = iworksTestFlag
                    if (!protocol) {
                        item.protocol_view_name = '';
                        exam.noneIworksImages.push(item)
                        continue
                    }
                    let protocolTree=protocol.protocolTree[0]
                    let node=findProtocolViewNode(protocolTree,item.protocol_view_guid)
                    if (node) {
                        item.protocol_view_name=node.StructureID&&iworksMap[node.StructureID]? iworksMap[node.StructureID] : node.label
                        item.protocol_node_id=node.id
                        exam.iworksImages.push(item)
                    }else{
                        item.protocol_view_name=undefined;
                        exam.noneIworksImages.push(item)
                    }

                }else{
                    if(iworksTestFlag&&item.protocol_execution_guid&&isSmartIworks(item) && hasAiAnalyzeResult(item)){
                        //腹部检查-存在ai数据
                        let storeItem = window.vm.$store.state.gallery.commentObj[item.resource_id]||item
                        let ai_analyze_report = storeItem.ai_analyze_report
                        let structure_id = ''
                        if(ai_analyze_report
                            &&ai_analyze_report.clips
                            &&ai_analyze_report.clips[item.resource_id]
                            &&ai_analyze_report.clips[item.resource_id][0]
                            &&ai_analyze_report.clips[item.resource_id][0].clip_id){
                            structure_id = ai_analyze_report.clips[item.resource_id][0].clip_id
                        }
                        let protocol=this.gallery.iworks_protocol_list[item.protocol_execution_guid]||exam.iworks_protocol_execution
                        item.isIworksTest = iworksTestFlag
                        if (!protocol) {
                            exam.noneIworksImages.push(item)
                            continue;
                        }
                        let protocolTree=protocol.protocolTree[0]
                        let node=findProtocolViewNodeByStructId(protocolTree,structure_id)
                        if (node) {
                            item.protocol_node_id=node.id
                            item.LABEL_CEUS=node.LABEL_CEUS
                            item.LABEL_CEUS_HIFR=node.LABEL_CEUS_HIFR
                            item.protocol_view_guid=node.GUID
                            item.protocol_execution_guid=protocol.attributes.GUID
                            item.protocol_view_name=node.StructureID&&iworksMap[node.StructureID]? iworksMap[node.StructureID] : node.label
                            exam.iworksImages.push(item)
                        }else{
                            item.protocol_view_name=undefined;
                            exam.noneIworksImages.push(item)
                        }
                    }else{
                        exam.noneIworksImages.push(item)
                    }
                }
            }
            this.sortIworksImages(exam);
        },
        sortIworksImages(exam){
            if (!exam.iworks_protocol_execution) {
                return ;
            }
            const currentLang = window.vm.$store.state.language.currentLanguage === 'CN'?'CN':'EN';
            const iworksMap = iworksInternational[currentLang];
            let iworksTestFlag = isIworksTest(exam)
            if(!iworksTestFlag&&exam.imageList&&exam.imageList[0]){
                iworksTestFlag = isIworksTest({iworks_protocol_execution:exam.iworks_protocol_execution,exam_type:exam.imageList[0].exam_type})
            }
            let execution_viewList=this.gallery.iworks_protocol_list[exam.iworks_protocol_execution.attributes.GUID].viewList
            let template_viewList=this.gallery.iworks_protocol_list[exam.iworks_protocol.attributes.GUID].viewList
            if (exam.iworksImages.length==0) {
                exam.uploaded_view_number=0;
                exam.all_view_number=execution_viewList.length
                return ;
            }
            var tempObj={}
            for(let img of exam.iworksImages){
                if (!tempObj[img.protocol_view_guid]) {
                    tempObj[img.protocol_view_guid]=img
                    tempObj[img.protocol_view_guid].same_node_image=[]
                    tempObj[img.protocol_view_guid].count=1;
                }else{
                    let count=tempObj[img.protocol_view_guid].count+1;
                    tempObj[img.protocol_view_guid].same_node_image.push(img)
                    if (tempObj[img.protocol_view_guid].gr_id<img.gr_id) {
                        //图片切面展示较新的
                        tempObj[img.protocol_view_guid]=img;
                    }
                    tempObj[img.protocol_view_guid].count=count;
                }
            }
            //合并模板协议和实例协议
            let compound_viewList=[]
            let template_index=0;
            let template_view_guid_ist = []
            for(let index=0;index<execution_viewList.length;index++){
                let execution_item=Object.assign({},execution_viewList[index])
                let deleteList=[];
                execution_item.isCreate=true
                execution_item.isDelete=false
                for(let j_index=template_index;j_index<template_viewList.length;j_index++){
                    let template_item=Object.assign({},template_viewList[j_index])
                    if(template_view_guid_ist.indexOf(template_item.GUID)<0){
                        template_view_guid_ist.push(template_item.GUID)
                    }
                    if (execution_item.id==template_item.id) {
                        execution_item.isCreate=false
                        template_index=j_index+1;
                        compound_viewList=compound_viewList.concat(deleteList)
                        break;
                    }else{
                        template_item.isDelete=true;
                        deleteList.push(template_item)
                    }
                }
                compound_viewList.push(execution_item);
            }
            if (template_index<template_viewList.length) {
                for(let j_index=template_index;j_index<template_viewList.length;j_index++){
                    let template_item=Object.assign({},template_viewList[j_index])
                    template_item.isDelete=true;
                    compound_viewList.push(template_item)
                }
            }
            let uploaded_view_number=0;
            let result=[]
            for(let view of compound_viewList){
                let item =tempObj[view.GUID]
                if (item&&!view.isDelete) {
                    uploaded_view_number++
                    item.isCreate=view.isCreate
                    item.isDelete=view.isDelete
                    item.isIworksTest=iworksTestFlag
                    result.push(item)
                }else{
                    if(template_view_guid_ist.indexOf(view.GUID)>-1){
                        result.push({
                            msg_type:this.systemConfig.msg_type.Frame,
                            url:'',
                            url_local:'',
                            protocol_view_name: view.StructureID&&iworksMap[view.StructureID]? iworksMap[view.StructureID] : view.label,
                            protocol_view_guid:view.GUID,
                            group_id:this.cid,
                            resource_id:view.GUID,
                            loaded:true,
                            isDelete:view.isDelete,
                            isCreate:view.isCreate,
                            isIworksTest:iworksTestFlag,
                        })
                    }
                }
            }
            exam.uploaded_view_number=uploaded_view_number;
            exam.all_view_number=execution_viewList.length
            exam.iworksImages=result;
        },
    }
}

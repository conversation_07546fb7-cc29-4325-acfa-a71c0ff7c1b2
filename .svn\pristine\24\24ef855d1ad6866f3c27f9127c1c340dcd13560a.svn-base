<template>
    <div class="tool-mid-box">
        <div
            v-for="item in filterMenu(descriptions.applianceName, 'first')"
            :key="item.values"
            class="tool-mid-box_item"
        >
            <div v-if="item.hasColor">
                <el-popover
                    placement="right"
                    trigger="manual"
                    popper-class="tool-box-popover"
                    v-model="item.isShowPopover"
                    v-show="item.hasColor"
                >
                    <div class="tool-box-palette-box-container">
                        <tool-box-palette-box
                            :displayStroke="item.hasStroke"
                            :currentMemberState="currentMemberState"
                            :color="item.color"
                            :type="item.name"
                            v-on="$listeners"
                            @setColor="setColor"
                            ref="paletteBox"
                        ></tool-box-palette-box>
                    </div>
                    <div slot="reference" :class="[isSelected === item.name ? 'active' : '', 'tool-box-cell-box-top']">
                        <el-tooltip
                            class="item"
                            effect="dark"
                            :content="lang[$root.currentLanguage][item.name]"
                            placement="right"
                            v-model="item.showToolTips"
                        >
                            <div class="tool-box-cell" @click.prevent="(event) => clickAppliance(event, item.name)">
                                <img :src="item.icon" />
                            </div>
                        </el-tooltip>
                    </div>
                </el-popover>
            </div>
            <div v-else>
                <template v-if="item.name === 'more'">
                    <el-popover
                        placement="right"
                        trigger="manual"
                        popper-class="tool-second_menu-popover"
                        v-model="item.isShowMoreMenu"
                    >
                        <div class="tool-second_menu">
                            <div
                                v-for="item in filterMenu(descriptions.applianceName, 'second')"
                                :key="item.values"
                                class="tool-mid-box_item"
                            >
                                <div v-if="item.hasColor">
                                    <el-popover
                                        placement="right"
                                        trigger="manual"
                                        popper-class="tool-box-popover"
                                        v-model="item.isShowPopover"
                                        v-show="item.hasColor"
                                    >
                                        <div class="tool-box-palette-box-container">
                                            <tool-box-palette-box
                                                :displayStroke="item.hasStroke"
                                                :currentMemberState="currentMemberState"
                                                :color="item.color"
                                                :type="item.name"
                                                v-on="$listeners"
                                                @setColor="setColor"
                                                ref="paletteBox"
                                            ></tool-box-palette-box>
                                        </div>
                                        <div
                                            slot="reference"
                                            :class="[isSelected === item.name ? 'active' : '', 'tool-box-cell-box-top']"
                                        >
                                            <el-tooltip
                                                class="item"
                                                effect="dark"
                                                :content="lang[$root.currentLanguage][item.name]"
                                                placement="right"
                                                v-model="item.showToolTips"
                                            >
                                                <div
                                                    class="tool-box-cell"
                                                    @click.prevent="(event) => clickAppliance(event, item.name)"
                                                >
                                                    <img :src="item.icon" />
                                                </div>
                                            </el-tooltip>
                                        </div>
                                    </el-popover>
                                </div>
                                <div v-else>
                                    <div
                                        :class="[isSelected === item.name ? 'active' : '', 'tool-box-cell-box-top']"
                                        :key="item.values"
                                    >
                                        <el-tooltip
                                            class="item"
                                            effect="dark"
                                            :content="lang[$root.currentLanguage][item.name]"
                                            placement="right"
                                            v-model="item.showToolTips"
                                        >
                                            <div
                                                class="tool-box-cell"
                                                @click.prevent="(event) => clickAppliance(event, item.name)"
                                            >
                                                <img :src="item.icon" />
                                            </div>
                                        </el-tooltip>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div
                            :class="[isSelected === item.name ? 'active' : '', 'tool-box-cell-box-top']"
                            :key="item.values"
                            slot="reference"
                        >
                            <el-tooltip
                                class="item"
                                effect="dark"
                                :content="lang[$root.currentLanguage][item.name]"
                                placement="right"
                                v-model="item.showToolTips"
                            >
                                <div class="tool-box-cell" @click.prevent="(event) => clickAppliance(event, item.name)">
                                    <img :src="item.icon" />
                                </div>
                            </el-tooltip>
                        </div>
                    </el-popover>
                </template>
                <template v-else>
                    <div
                        :class="[isSelected === item.name ? 'active' : '', 'tool-box-cell-box-top']"
                        :key="item.values"
                    >
                        <el-tooltip
                            class="item"
                            effect="dark"
                            :content="lang[$root.currentLanguage][item.name]"
                            placement="right"
                            v-model="item.showToolTips"
                        >
                            <div class="tool-box-cell" @click.prevent="(event) => clickAppliance(event, item.name)">
                                <img :src="item.icon" />
                            </div>
                        </el-tooltip>
                    </div>
                </template>
            </div>
        </div>
        <slot></slot>
    </div>
</template>

<script>
import ToolBoxPaletteBox from "./ToolBoxPaletteBox";
import selector from "../static/image/selector.svg";
import selectorActive from "../static/image/selector-active.svg";
import pen from "../static/image/pencil.svg";
import penActive from "../static/image/pencil-active.svg";
import text from "../static/image/text.svg";
import textActive from "../static/image/text-active.svg";
import eraser from "../static/image/eraser.svg";
import eraserActive from "../static/image/eraser-active.svg";
import ellipse from "../static/image/ellipse.svg";
import ellipseActive from "../static/image/ellipse-active.svg";
import rectangle from "../static/image/rectangle.svg";
import rectangleActive from "../static/image/rectangle-active.svg";
import straight from "../static/image/straight.svg";
import straightActive from "../static/image/straight-active.svg";
import arrow from "../static/image/arrow.svg";
import arrowActive from "../static/image/arrow-active.svg";
import laserPointer from "../static/image/laserPointer.svg";
import laserPointerActive from "../static/image/laserPointer-active.svg";
import hand from "../static/image/hand.svg";
import handActive from "../static/image/hand-active.svg";
import clear from "../static/image/clear.svg";
import mask from "../static/image/mask.svg";
import clicker from "../static/image/clicker.svg";
import clickerActive from "../static/image/clicker-active.svg";
import graphical from "../static/image/graphical.svg";
import graphicalActive from "../static/image/graphical-active.svg";
import clean from "../static/image/clean.svg";
import cleanActive from "../static/image/clean-active.svg";
import close from "../static/image/close.svg";
import closeActive from "../static/image/close-active.svg";
import more from "../static/image/more.svg";
import { lang } from "../common/languages";
export default {
    name: "ToolBox",
    props: {
        currentMemberState: {
            type: Object,
            default: () => {
                return {};
            },
            require: true,
        },
    },
    components: {
        ToolBoxPaletteBox,
    },

    watch: {
        "currentMemberState.currentApplianceName": {
            handler(val) {
                this.changeIcon(val);
            },
            immediate: true,
            deep: true,
        },
    },
    data() {
        return {
            lang,
            selector,
            selectorActive,
            pen,
            penActive,
            text,
            textActive,
            eraser,
            eraserActive,
            ellipse,
            ellipseActive,
            rectangle,
            rectangleActive,
            straight,
            straightActive,
            arrow,
            arrowActive,
            laserPointer,
            laserPointerActive,
            hand,
            handActive,
            mask,
            strokeEnable: false,
            close,
            closeActive: false,
            isSelected: "",
            descriptions: {
                applianceName: {
                    clicker: {
                        name: "clicker",
                        icon: clicker,
                        iconActive: clickerActive,
                        // hasColor: false,
                        hasStroke: false,
                        hideInMenu: true,
                        showToolTips: false,
                        clickMenuHide:true
                    },
                    selector: {
                        name: "selector",
                        icon: selector,
                        iconActive: selectorActive,
                        // hasColor: false,
                        hasStroke: false,
                        hideInMenu: true,
                        showToolTips: false,
                        clickMenuHide:true
                    },
                    pencil: {
                        name: "pencil",
                        icon: pen,
                        iconActive: penActive,
                        hasColor: true,
                        hasStroke: true,
                        isShowPopover: false,
                        color: [252, 58, 63],
                        showToolTips: false,
                    },
                    text: {
                        name: "text",
                        icon: text,
                        iconActive: textActive,
                        hasColor: true,
                        hasStroke: false,
                        isShowPopover: false,
                        color: [252, 58, 63],
                        hideInMenu: true,
                        showToolTips: false,
                    },
                    eraser: {
                        name: "eraser",
                        icon: eraser,
                        iconActive: eraserActive,
                        // hasColor: false,
                        hasStroke: false,
                        hideInMenu: true,
                        showToolTips: false,
                        clickMenuHide:true
                    },
                    shape: {
                        name: "shape",
                        icon: graphical,
                        iconActive: graphicalActive,
                        hasColor: true,
                        hasStroke: true,
                        isShowPopover: false,
                        color: [252, 58, 63],
                        hideInMenu: true,
                        showToolTips: false,
                    },
                    laserPointer: {
                        name: "laserPointer",
                        icon: laserPointer,
                        iconActive: laserPointerActive,
                        // hasColor: false,
                        hasStroke: false,
                        showToolTips: false,
                    },
                    more: {
                        name: "more",
                        icon: more,
                        hasColor: false,
                        hasStroke: false,
                        isShowMoreMenu: false,
                        showToolTips: false,
                    },
                    clear: {
                        name: "clear",
                        icon: clean,
                        iconActive: clean,
                        hasColor: false,
                        hasStroke: false,
                        showToolTips: false,
                    },
                    close: {
                        name: "close",
                        icon: close,
                        iconActive: closeActive,
                        hasColor: false,
                        hasStroke: false,
                        showToolTips: false,
                    },
                },
            },
        };
    },
    mounted() {
        this.$nextTick(() => {
            document.addEventListener("mousedown", this.handlerClickOutSide);
        });
    },
    beforeDestroy() {
        document.removeEventListener("mousedown", this.handlerClickOutSide);
    },
    methods: {
        changeIcon(val) {
            let shapeTypeArr = ["ellipse", "rectangle", "straight", "arrow"];
            let unsetArr = ["clear", "close", "more"];
            if (shapeTypeArr.includes(val)) {
                this.isSelected = "shape";
            } else if (!unsetArr.includes(val)) {
                this.isSelected = val;
            }
        },
        clickAppliance(eventTarget, applianceName) {
            this.descriptions.applianceName[applianceName].showToolTips = false;
            if(this.descriptions.applianceName[applianceName].clickMenuHide){
                this.descriptions.applianceName["more"].isShowMoreMenu = false;
            }
            Object.keys(this.descriptions.applianceName).forEach((item) => {
                if (item !== applianceName) {
                    this.descriptions.applianceName[item].isShowPopover = false;
                }
            });
            if (applianceName !== "more" && !this.descriptions.applianceName[applianceName].hideInMenu) {
                this.descriptions.applianceName["more"].isShowMoreMenu = false;
            } else {
                this.descriptions.applianceName[applianceName].isShowMoreMenu =
                    !this.descriptions.applianceName[applianceName].isShowMoreMenu;
            }
            if (applianceName === "clear") {
                this.$emit("cleanCurrentScene");
            } else if (applianceName === "shape") {
                this.$refs.paletteBox[0].setShapeType();
            } else if (applianceName === "close") {
                this.$emit("closeToolBar");
            } else {
                this.$emit("setMemberState", { currentApplianceName: applianceName });
            }
            if (this.descriptions.applianceName[applianceName].hasColor) {
                this.$emit("setMemberState", { strokeColor: this.descriptions.applianceName[applianceName].color });
                if (applianceName === this.isSelected) {
                    console.error(applianceName, this.isSelected, this.descriptions.applianceName[applianceName]);
                    this.descriptions.applianceName[applianceName].isShowPopover =
                        !this.descriptions.applianceName[applianceName].isShowPopover;
                } else {
                    // this.descriptions.applianceName[applianceName].isShowPopover = true;
                }
            }

            this.changeIcon(applianceName);
        },
        clickoutside(e) {
            Object.keys(this.descriptions.applianceName).forEach((item) => {
                this.descriptions.applianceName[item].isShowPopover = false;
            });
            this.descriptions.applianceName["more"].isShowMoreMenu = false;
        },
        setColor(color) {
            this.descriptions.applianceName[this.isSelected].color = color;
            this.$emit("setMemberState", { strokeColor: color });
        },
        getEventPath(event) {
            var path = [];
            var node = event.target;

            while (node !== document) {
                path.push(node);
                node = node.parentNode;
            }

            path.push(document);

            return path.reverse();
        },
        handlerClickOutSide(event) {
            console.log(event);
            let path = event.composedPath ? event.composedPath() : this.getEventPath(event);
            let classNames = path.map(function (node) {
                return node.className;
            });
            let isTarget = false;
            classNames.forEach((item) => {
                if (item && item.indexOf("tool-") > -1) {
                    isTarget = true;
                }
            });
            if (!isTarget) {
                this.clickoutside();
            }
        },
        closePopover() {
            this.clickoutside();
        },
        filterMenu(menu, type = "first") {
            let obj = {};
            if (type === "first") {
                Object.keys(menu).forEach((key) => {
                    if (!menu[key].hideInMenu) {
                        obj[key] = menu[key];
                    }
                });
            } else {
                Object.keys(menu).forEach((key) => {
                    if (menu[key].hideInMenu) {
                        obj[key] = menu[key];
                    }
                });
            }
            return obj;
        },
    },
};
</script>

<style lang="scss" scoped>
// .tool-mid-box {
//   height: 32px;
//   display: flex;
//   border-radius: 4px;
//   justify-content: space-between;
//   padding-left: 6px;
//   padding-right: 6px;
//   background-color: white;
// }

.tool-mid-box {
    display: flex;
    flex-direction: column;
    border-radius: 4px;
    background-color: #4d4d4d;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 4px;
    padding-top: 4px;
    box-shadow: 0 8px 24px 0 rgba(0, 0, 0, 0.08);
    .tool-mid-box_item {
        padding-bottom: 4px;
        padding-top: 4px;
        &:first-child {
        }
    }
}

.tool-box-cell {
    width: 30px;
    height: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    color: #ffffff !important;
    cursor: pointer;
    font-size: 14px;
    img {
        width: 20px;
        height: 20px;
    }
    p {
        margin-top: 5px;
        &.active {
            color: #00c59d;
        }
    }
}

// .tool-box-cell-box {
//   width: 32px;
//   height: 32px;
//   margin-left: 4px;
//   margin-right: 4px;
//   user-select: none;
//   cursor: pointer;
//   background-color: white;
// }

.tool-box-cell-box-top {
    width: 40px;
    height: 30px;
    user-select: none;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 2px;
    &:hover {
        background: #00c59d;
    }
    &.active {
        background: #00c59d;
    }
}

.tool-box-cell-step-two {
    height: 2px;
    border-radius: 1px;
    margin-top: -4px;
    margin-left: auto;
    margin-right: auto;
}

.tool-box-cell-step-two {
    height: 2px;
    border-radius: 1px;
    margin-top: -4px;
    margin-left: auto;
    margin-right: auto;
}
.tool-second_menu-popover {
}
.tool-second_menu {
}
</style>
<style lang="scss">
.tool-second_menu-popover {
    &.el-popover {
        .popper__arrow {
            &::after {
                border-right-color: #4d4d4d !important;
            }
        }
        display: flex;
        flex-direction: column;
        border-radius: 4px;
        background-color: #4d4d4d;
        justify-content: space-between;
        align-items: center;
        padding: 4px 0;
        box-shadow: 0 8px 24px 0 rgba(0, 0, 0, 0.08);
        min-width: unset;
        border: 0;
        .tool-mid-box_item {
            padding-bottom: 4px;
            padding-top: 4px;
            &:first-child {
            }
        }
    }
}
</style>

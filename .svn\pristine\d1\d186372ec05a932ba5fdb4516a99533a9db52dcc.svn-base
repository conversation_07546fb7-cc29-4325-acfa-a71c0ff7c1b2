<template>
    <div class="svg-loader-container" :class="customClass" ref="svgLoaderContainer" v-html="svgContent"></div>
</template>

<script>
import axios from "axios";

export default {
    props: {
        url: {
            type: String,
            required: true,
        },
        customClass: String,
    },
    data() {
        return {
            svgContent: "",
        };
    },
    mounted() {
        this.loadSvg();
    },
    watch: {
        url() {
            this.loadSvg();
        },
    },
    methods: {
        async getSvgByUrl(url) {
            return new Promise((resolve, reject) => {
                const xhr = new XMLHttpRequest();
                xhr.open("GET", url, true);
                xhr.onload = () => {
                    if (xhr.status >= 200 && xhr.status < 300) {
                        resolve(xhr.responseText);
                    } else {
                        reject(new Error(`Request failed: ${xhr.statusText}`));
                    }
                };
                xhr.onerror = () => reject(new Error("Network error"));
                xhr.send();
            });
        },
        async loadSvg() {
            try {
                let svg = "";
                if (this.url) {
                    const response = await this.getSvgByUrl(this.url);
                    svg = response;
                }
                svg = svg.replace(/fill="[^"]*"/g, "");
                svg = svg.replace(/stroke="[^"]*"/g, "");
                this.svgContent = svg;
                // this.$nextTick(() => {
                //     const svgElement = this.$refs.svgLoaderContainer.querySelector("svg");
                //     if (svgElement) {
                //         svgElement.setAttribute("role", "img");
                //         svgElement.classList.add("svg-content");
                //     }
                // });
            } catch (error) {
                console.error("Error loading SVG:", error);
            }
        },
    },
};
</script>

<style scoped>
.svg-loader-container {
    display: inline-block;
    line-height: 0; /* 消除底部间隙 */
}

.svg-loader-container /deep/ .svg-content {
    width: 100%;
    height: 100%;
    fill: currentColor; /* 关键：使颜色继承自父元素 */
    stroke: currentColor;
}
</style>

<template>
    <van-nav-bar
        :title="title"
        :left-arrow="leftArrow"
        :fixed="fixed"
        :z-index="zIndex"
        :border="border"
        :placeholder="placeholder"
        @click-left="onClickLeft"
        @click-right="onClickRight"
        :safe-area-inset-top="false"
        v-bind="$attrs"
        class="mr-nav-bar"
    >
        <template #title>
            <slot name="title"></slot>
        </template>
        <template #left>
            <slot name="left"></slot>
        </template>
        <template #right>
            <slot name="right"></slot>
        </template>
    </van-nav-bar>
</template>
<script>
import { NavBar } from "vant";
import base from "../lib/base";
export default {
    name: "mrHeader",
    mixins: [base],
    components: {
        [NavBar.name]: NavBar,
    },
    props: {
        title: {
            type: String,
            default: "",
        },
        leftText: {
            type: String,
            default: "",
        },
        rightText: {
            type: String,
            default: "",
        },
        leftArrow: {
            type: Boolean,
            default: true,
        },
        fixed: {
            type: Boolean,
            default: false,
        },
        zIndex: {
            type: [String, Number],
            default: 1,
        },
        border: {
            type: Boolean,
            default: true,
        },
        placeholder: {
            type: <PERSON>olean,
            default: false,
        },
    },
    methods: {
        onClickLeft() {
            if (this.$listeners["click-left"]) {
                this.$emit("click-left");
            } else {
                this.back();
            }
        },
        onClickRight() {
            this.$emit("click-right");
        },
    },
};
</script>
<style scoped lang="scss">
.mr-nav-bar {
    background-color: #00c59d;
    color: #fff;
    height: 2.95rem;
    ::v-deep {
        .van-nav-bar__content {
            height: 2.95rem !important;
            color: #fff;
        }
        .van-nav-bar__title {
            color: #fff;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0 auto;
            font-size: 0.95rem;
        }
        .van-nav-bar__left {
            color: #fff;
            font-size: 1.2rem;
            .van-icon {
                color: #fff;
                font-size: 1.2rem;
            }
        }
        .van-nav-bar__right {
            color: #fff;
            font-size: 0.95rem;
        }
    }
}
</style>

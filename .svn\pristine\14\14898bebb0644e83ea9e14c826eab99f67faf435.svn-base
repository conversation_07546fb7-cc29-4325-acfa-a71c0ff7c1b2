import {Logger} from '@/common/console.js'

class CommunicationMngLogger {
    static instance = null;

    constructor() {
        if (CommunicationMngLogger.instance) {
            return CommunicationMngLogger.instance;
        }

        this.log = function ({ message, data }) {
            Logger.save({
                message,
                eventType: `communication_mng_log`,
                data
            });
        };
        this.error = function ({ message, data }) {
            Logger.saveError({
                message,
                eventType: `communication_mng_error`,
                data
            });
        };

        CommunicationMngLogger.instance = this;
    }

    static getInstance() {
        if (!CommunicationMngLogger.instance) {
            CommunicationMngLogger.instance = new CommunicationMngLogger();
        }
        return CommunicationMngLogger.instance;
    }
}

export const logger = CommunicationMngLogger.getInstance();
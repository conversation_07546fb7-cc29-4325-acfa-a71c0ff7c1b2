/**
 * @description 构造函数
 * @constructor
 */
import Tool from '@/common/tool.js'
import requestManager from '@/common/CommunicationMng/requestManager';
import CommonBridge from './CommonBridge'
import CMonitorWallPushBridge from './CMonitorWallPushBridge'
import CMonitorWallPlayBridge from './CMonitorWallPlayBridge'
import CefQueryBridge from './CefQueryBridge'
import CLiveConferenceBridge from './CLiveConferenceBridge'
import CWhiteBoardBridge from './CWhiteBoardBridge'
import CMobileBridge from './CMobileBridge'
import CReverseControlBridge from './CReverseControlBridge'
import CTEAirBridge from './CTEAirBridge'
import { Base64 } from 'js-base64';
import { designatedClientType,clientTypeMap } from '../../../config/clientType'
var Toast
function DEBUG_TO_SERVER(msg, data) {
    console.log('DEBUG_TO_SERVER',msg, data)
    // if (window.main_screen) {
    //     window.main_screen.gateway.emit('debug', msg, data)
    // }
}
var wechat_group_info = {}
var download_single = false
var download_multi = false
function CWorkstationCommunicationMng() {}
CWorkstationCommunicationMng.DEBUG_TO_SERVER = DEBUG_TO_SERVER
CWorkstationCommunicationMng.init= function(){
    return new Promise((resolve,reject)=>{
        const startTime = Date.now();
        const maxWaitTime = 3000; // 3秒超时

        function tryInit() {
            if (window.cefQuery) {
                window.browse_type = 'CEF'
                window.vm.$store.commit('globalParams/updateGlobalParams', {
                    isCef: true,
                })
                CWorkstationCommunicationMng.queryAppVersion()
                CWorkstationCommunicationMng.getLicenceInfo()
                resolve(window.browse_type)
            } else if ('undefined' != typeof webkitMng) {
                window.browse_type = 'WEBKIT'
                CWorkstationCommunicationMng.queryAppVersion()
                resolve(window.browse_type)
            } else if (window.webkit
                && window.webkit.messageHandlers
                && window.webkit.messageHandlers.js2App_query
                && 'undefined' == typeof window.JSInterface) {
                window.browse_type = 'MUI_IOS'
                CWorkstationCommunicationMng.queryAppVersion()
                CWorkstationCommunicationMng.getAppNewVersionInfo()
                resolve(window.browse_type)
            } else if ('undefined' != typeof window.JSInterface
                && window.JSInterface.GetClientBrowseType
                && window.JSInterface.GetClientBrowseType()=='MUI_ANROID') {
                window.browse_type = 'MUI_ANROID'
                CWorkstationCommunicationMng.queryAppVersion()
                CWorkstationCommunicationMng.getAppNewVersionInfo()
                resolve(window.browse_type)
            } else if (window.clientType == 5) {
                if (window.mui.os.ios) {
                    window.browse_type = 'MUI_IOS_BROWSER'
                } else if (window.mui.os.android) {
                    window.browse_type = 'MUI_ANROID_BROWSER'
                }else{
                    window.browse_type = 'MUI_MOBILE_BROWSER'
                }
                resolve(window.browse_type)
            } else if (window.clientType == 1) {
                window.browse_type = 'PC_CUSTOM_BROWSER'
                resolve(window.browse_type)
            } else {
                // 检查是否超时
                if (Date.now() - startTime < maxWaitTime) {
                    // 100ms 后重试
                    setTimeout(tryInit, 100);
                } else {
                    // 超过5秒后仍然失败，返回 UNKNOW
                    window.browse_type = 'UNKNOW'
                    resolve(window.browse_type)
                }
            }
        }

        // 开始第一次尝试
        tryInit();
    })
}
CWorkstationCommunicationMng.query = function (str, success, failure) {
    if (-1 == str.indexOf('Navigation')) {
        console.log('[event] CWorkstationCommunicationMng.query')
        if(str.length < 1000){
            DEBUG_TO_SERVER('CWorkstationCommunicationMng.query', str)
        } else {
            DEBUG_TO_SERVER('CWorkstationCommunicationMng.query', str.split(':')[0])
        }
    }
    if (window.cefQuery) {
        if (-1 == str.indexOf('Navigation')) {
            console.log('CWorkstationCommunicationMng.query', str)
        }
        window.cefQuery({
            request: str,
            onSuccess: function (response) {
                if (success) {
                    success(response)
                }
            },
            onFailure: function (error_code, error_message) {
                if (failure) {
                    failure(error_code, error_message)
                }
            },
        })
    } else if ('undefined' != typeof window.webkitMng) {
        window.webkitMng.query(str)
    } else if (window.webkit
        && window.webkit.messageHandlers
        && window.webkit.messageHandlers.js2App_query
        && 'undefined' == typeof window.JSInterface) {
        if(str.length < 1000){
            console.log('start.postMessage:',str)
        } else {
            console.log('start.postMessage:',str.split(':')[0])
        }
        window.webkit.messageHandlers.js2App_query.postMessage({body: str});

    } else if ('undefined' != typeof window.JSInterface
                && window.JSInterface.GetClientBrowseType
                && window.JSInterface.GetClientBrowseType()=='MUI_ANROID') {
        console.log(str)
        window.JSInterface.AppMethod(str)
    } else if (window.clientType == 5) {
        //浏览器访问不处理
    }
    // else{
    //     setTimeout(()=>{
    //         this.query(str, success, failure)
    //     },500)
    // }
}


CWorkstationCommunicationMng.queryAppVersion = function () {
    //查询版本信息
    this.query('QueryAppVersion')
}

CWorkstationCommunicationMng.AppVersion = function (json_str) {
    console.log(' ***************************** AppVersion', json_str)
    //提示版本信息
    var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str

    json.local_mac = json.local_mac && json.local_mac.replace(/:/g, '-')
    window.app_info = json
    window.vm.$store.commit('device/updateDeviceInfo', {
        mac: json.local_mac,
        device_id:json.local_mac
    })
    requestManager.handleResponse('updateVersion', json)
    if (window.main_screen) {
        window.main_screen.controller.emit('bind_scan_room_user')
    }
}

CWorkstationCommunicationMng.getAppNewVersionInfo = function () {
    //查询最新版本和当前版本信息，以及是否强制更新的信息
    this.query('getAppNewVersionInfo')
}
CWorkstationCommunicationMng.NotifyGetAppNewVersionInfo = function (json_str) {
    console.log(' ***************************** NotifyGetAppNewVersionInfo', json_str)
    //提示版本信息
    //{new_version:***, cur_version:***, action:0,1,2,3}  action=1需要强制更新 2非强制更新

    var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str

    // var new_version = parseInt(json.new_version)
    // var cur_version = parseInt(json.cur_version)
    var need_update = false
    if (json.action ==2||json.action ==1) {
        need_update = true
    }
    let force = json.action ==1?true:false
    const app_update_info = {
        new_version: json.new_version,
        cur_version: json.cur_version,
        action:json.action,
        need_update: need_update,
        force,
    }
    window.vm.$store.commit('globalParams/updateGlobalParams',{
        app_update_info
    })
    requestManager.handleResponse('updateAppNewVersionInfo_minePage', app_update_info)

}

CWorkstationCommunicationMng.forceUpdateApp = function (json) {
    //强制更新app
    this.query('forceUpdateApp:'+ JSON.stringify(json))
}

////////超声直播/////////////

CWorkstationCommunicationMng.stopUltrasoundDesktop = function (json) {
    json.start_catch_type = window.vm.$store.state.systemConfig.start_catch_type.UltrasoundDesktop
    this.query('StopCatch:' + JSON.stringify(json))
}

CWorkstationCommunicationMng.NotifyStopCatch = function (json_str) {
    console.log('[event] CWorkstationCommunicationMng.NotifyStopCatch')
    console.log(JSON.parse(json_str))

    var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
    requestManager.handleResponse('notifyStopUltrasoundDesktop', json)
    //如何停止电视墙 todo
}

CWorkstationCommunicationMng.startPush = function (json) {
    console.log('[event] CWorkstationCommunicationMng.startPush')
    var systemConfig = window.vm.$store.state.systemConfig
    json.enable_voice_stream = systemConfig.realtimeModeInfo.enable_voice_stream
    console.log(json)
    this.query('StartPush:' + JSON.stringify(json))
}

CWorkstationCommunicationMng.stopPush = function (json) {
    console.log('[event] CWorkstationCommunicationMng.stopPush')
    console.log(json)

    this.query('StopPush:' + JSON.stringify(json))
}

CWorkstationCommunicationMng.NotifyStartPush = function (json_str) {
    console.log('[event] CWorkstationCommunicationMng.NotifyStartPush')
    console.log(json_str)

    var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str

    if (this.getCurrentScanRoom()) {
        if (20000 == json.error) {
            //app端音频初始化失败,不处理,仅仅记录日志
            DEBUG_TO_SERVER('[error] app音频初始化失败')
            json.error = 0
        }
        this.getCurrentScanRoom().notifyStartPush(json)
    }
}

CWorkstationCommunicationMng.NotifyStopPush = function (json_str) {
    console.log('[event] CWorkstationCommunicationMng.NotifyStopPush')
    console.log(json_str)

    var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
    if (this.getCurrentScanRoom()) {
        this.getCurrentScanRoom().notifyStopPush(json)
    }
}

//本地存储
CWorkstationCommunicationMng.RequestStartStorageConsultationFile = function () {
    console.log('RequestStartStorageConsultationFile')
    // if (window.main_screen) {
    //     CScanRoom.StorageConsultationFile = true
    //     var scan_room = this.getCurrentScanRoom()
    //     if (scan_room && scan_room.enableCatch()) {
    //         scan_room.startStorageConsultationFile({})
    //     } else {
    //         var error_info = ''
    //         var error_code = ''
    //         if (scan_room) {
    //             error_info = scan_room.errorInfoOfEnableCatch()
    //             error_code = scan_room.errorCodeOfEnableCatch()
    //         } else {
    //         }

    //         CWorkstationCommunicationMng.notifyStartStorageConsultationFile({
    //             error: 1,
    //             type: 11,
    //             start_catch_type: ConversationConfig.start_catch_type.StorageConsultationFile,
    //             error_code: error_code,
    //             error_info: error_info,
    //         })
    //     }
    // } else {
    //     CWorkstationCommunicationMng.notifyStartStorageConsultationFile({
    //         error: 1,
    //         type: 11,
    //         start_catch_type: ConversationConfig.start_catch_type.StorageConsultationFile,
    //         error_code: 'no_login',
    //         error_info: window.vm.$store.state.language.not_login_and_fail_to_start_rt_video,
    //     })
    // }
}

CWorkstationCommunicationMng.RequestStopStorageConsultationFile = function () {
    console.log('RequestStopStorageConsultationFile')
    // if (window.main_screen) {
    //     CScanRoom.StorageConsultationFile = false
    //     if (this.getCurrentScanRoom()) {
    //         this.getCurrentScanRoom().stopStorageConsultationFile({})
    //     }
    // }
}

CWorkstationCommunicationMng.notifyStartStorageConsultationFile = function (json) {
    // this.query('NotifyStartStorageConsultationFile:' + JSON.stringify(json))
}

CWorkstationCommunicationMng.notifyStopStorageConsultationFile = function (json) {
    // this.query('NotifyStopStorageConsultationFile:' + JSON.stringify(json))
}

//电视墙
CWorkstationCommunicationMng.startMonitorWall = function (json) {
    //TODO:
}

CWorkstationCommunicationMng.stopMonitorWall = function (json) {
    //TODO:
}

CWorkstationCommunicationMng.enterVideoWall = function (json) {
    this.query('StartTVPlayer:' + JSON.stringify(json))
}

CWorkstationCommunicationMng.exitVideoWall = function (json) {
    this.query('StopTVPlayer:' + JSON.stringify(json))
}


CWorkstationCommunicationMng.RestoreByShutdown = function (json_str) {
    window.main_screen&&window.main_screen.controller.emit('restore_by_shutdown')
}

CWorkstationCommunicationMng.NotifyWaitCloseVideoWall = function (json_str) {
    console.log('CWorkstationCommunicationMng.NotifyWaitCloseVideoWall')
    console.log(json_str)

    var json = JSON.parse(json_str)
    window.main_screen.controller.emit('wait_close_monitor_wall', json)
}

CWorkstationCommunicationMng.ZoomScanRoomFromVideoWall = function (json_str) {
    var json = JSON.parse(json_str)
    window.main_screen.controller.emit('zoom_scan_room', json)
}

//=======================================Mobile=========================================

CWorkstationCommunicationMng.hideStartupView = function () {
    console.log('CWorkstationCommunicationMng.hideStartupView', '')
    this.query('HideStartupView')
}

CWorkstationCommunicationMng.showStartupView = function () {
    console.log('CWorkstationCommunicationMng.showStartupView', '')
    this.query('ShowStartupView')
}

CWorkstationCommunicationMng.queryEnterWebIMCount = function () {
    this.query('QueryEnterWebIMCount')
}

CWorkstationCommunicationMng.NotifyEnterWebIMCount = function (json_str) {
    console.log('CWorkstationCommunicationMng.NotifyEnterWebIMCount', json_str)
    var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
    var webimCountEntered = json.webimCountEntered
    if (webimCountEntered && webimCountEntered > 1) {
        //CMainScreenUI.restoreUserUI();
        //window.vm.eventBus.$emit("restoreUserUI",json_str);
    }
    setTimeout(function () {
        CWorkstationCommunicationMng.hideStartupView()
    }, 500)
}
CWorkstationCommunicationMng.enterUltrasoundDesktop = function (json) {
    console.log('CWorkstationCommunicationMng.enterUltrasoundDesktop',json)
    var systemConfig = window.vm.$store.state.systemConfig
    json.network_environment = systemConfig.serverInfo.network_environment
    // json.start_catch_type = systemConfig.start_catch_type.UltrasoundDesktop

    json.enable_playback = systemConfig.serverInfo.EnablePlayback

    if (json.is_starter) {
        json.enable_video_time_shift = false
    } else {
        json.enable_video_time_shift = systemConfig.serverInfo.EnableVideoTimeShift
    }

    json.video_server_type = systemConfig.serverInfo.video_server_type
    json.record_video_server_type = systemConfig.serverInfo.record_video_server_type
    if(json.start_catch_type!=7||'MUI_IOS_BROWSER' == window.browse_type){
        this.query('StartPlay:' + JSON.stringify(json))
    }

}
// APP通知 由APP关闭播放器后的事件
CWorkstationCommunicationMng.notifyStopPlay = function(json){
    window.vm.eventBus.$emit('notifyStopPlay')
}
CWorkstationCommunicationMng.exitUltrasoundDesktop = function (json) {
    console.log('CWorkstationCommunicationMng.exitUltrasoundDesktop')
    var systemConfig = window.vm.$store.state.systemConfig
    json.start_catch_type = systemConfig.start_catch_type.UltrasoundDesktop
    this.query('StopPlay:' + JSON.stringify(json))
}

CWorkstationCommunicationMng.resetApp = function () {
    if ( window.browse_type != 'MUI_IOS' &&  window.browse_type != 'MUI_ANROID'){
        return
    } else{
        this.query('ResetApp')
    }

}

CWorkstationCommunicationMng.switchVideo = function (json) {
    console.log('CWorkstationCommunicationMng.switchVideo')
    var systemConfig = window.vm.$store.state.systemConfig
    //console.log(systemConfig);
    json.network_environment = systemConfig.serverInfo.network_environment
    json.start_catch_type = systemConfig.start_catch_type.UltrasoundDesktop

    json.enable_playback = systemConfig.serverInfo.EnablePlayback
    json.enable_video_time_shift = systemConfig.serverInfo.EnableVideoTimeShift

    json.video_server_type = systemConfig.serverInfo.video_server_type
    json.record_video_server_type = systemConfig.serverInfo.record_video_server_type

    this.query('SwitchVideo:' + JSON.stringify(json))
}
CWorkstationCommunicationMng.enterGalleryMode = function () {
    this.query('EnterGalleryMode')
}

CWorkstationCommunicationMng.exitGalleryMode = function () {
    this.query('ExitGalleryMode')
}

CWorkstationCommunicationMng.changeOrientation = function () {
    this.query('ChangeOrientation')
}
CWorkstationCommunicationMng.SwitchPlayerFullScreenStatus = function (json) { //{is_full_screen:true}
    this.query('SwitchPlayerFullScreenStatus:' + JSON.stringify(json))
}

CWorkstationCommunicationMng.PlayerStateChanged = function (json_str) {
    console.log('CWorkstationCommunicationMng.PlayerStateChanged', json_str)
    if ( window.browse_type != 'MUI_IOS' &&  window.browse_type != 'MUI_ANROID'){
        return
    }

    var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
    if ('tap' == json.player_state) {
        //点击关闭


        //json: {
        //    error_code: 0 正常； 101 地址格式错误（空也包括在里面）； 102 打开失败
        //    type：xxx,
        //    cid:xxx,
        //    img_id:xxxx
        //}
    } else if ('swipe' == json.player_state) {
        if (json.state_value) {
            //Next
            //CMainScreenUI.mui_previewImage_slider('next');
        } else {
            //Prev
            //CMainScreenUI.mui_previewImage_slider('prev');
        }

        //json: {
        //    state_value:
        //}
    }
}

CWorkstationCommunicationMng.PlayerError = function (json_str) {
    console.log('CWorkstationCommunicationMng.PlayerError', json_str)
    if ( window.browse_type != 'MUI_IOS' &&  window.browse_type != 'MUI_ANROID'){
        return
    }

    //json: {
    //    error_code: 101 地址为空 102 打开失败
    //    type：xxx,
    //    cid:xxx,
    //    img_id:xxxx
    //}

    // var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str;

    //CMainScreenUI.mui_previewImage_close(json);
}

CWorkstationCommunicationMng.setWebrtcIceServer = function (json) {
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.setWebrtcIceServer ', json)
    this.query('setWebrtcIceServer:' + JSON.stringify(json))
}

CWorkstationCommunicationMng.SwitchSound = function (json_str) {
    if ( window.browse_type != 'MUI_IOS' &&  window.browse_type != 'MUI_ANROID'){
        return
    }

    // var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str;
    // var id = json.id;
    // if (id && controllerMap[id]) {
    //     if (controllerMap[id]) {
    //         controllerMap[id].switchSound();
    //     }
    // } else {
    //     console.log("[error] CWorkstationCommunicationMng.SwitchSound");
    // }
}

CWorkstationCommunicationMng.SwitchSoundStatus = function (json) {
    if ( window.browse_type != 'MUI_IOS' &&  window.browse_type != 'MUI_ANROID'){
        return
    }

    this.query('SwitchSoundStatus:' + JSON.stringify(json))
}

CWorkstationCommunicationMng.startAudioSession = function (json) {
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.startAudioSession ', json)
    this.query('StartAudioSession:' + JSON.stringify(json))
}

CWorkstationCommunicationMng.NotifyStartAudioSession = function (json_str) {
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.NotifyStartAudioSession ', json_str)
    var json = json_str //JSON.parse(json_str);
    var id = json.cid

    if (id && 0 >= id) {
    } else if (id && 0 < id) {
        if (window.main_screen.conversation_list[id]) {
            json.is_starter = 1
            window.main_screen.conversation_list[id].notifyStartAudioSession(json)
        } else {
            console.log('[error] CWorkstationCommunicationMng.NotifyStartAudioSession')
        }
    }
}

CWorkstationCommunicationMng.NotifyAudioSessionError = function (json_str) {
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.NotifyAudioSessionError ', json_str)
    var json = json_str //JSON.parse(json_str);
    var id = json.cid

    if (id && 0 >= id) {
    } else if (id && 0 < id) {
        if (window.main_screen.conversation_list[id]) {
            window.main_screen.conversation_list[id].NotifyAudioSessionError(json)
        } else {
            console.log('[error] CWorkstationCommunicationMng.NotifyAudioSessionError')
        }
    }
}

CWorkstationCommunicationMng.enterAudioSession = function (json) {
    console.log('CWorkstationCommunicationMng.enterAudioSession ', json)
    this.query('EnterAudioSession:' + JSON.stringify(json))
}

CWorkstationCommunicationMng.NotifyEnterAudioSession = function (json_str) {
    console.log('CWorkstationCommunicationMng.NotifyEnterAudioSession ', json_str)
    var json = json_str //JSON.parse(json_str);
    var id = json.cid
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.NotifyEnterAudioSession ', {
        id: json.cid,
        error: json.error,
        errorinfo: json.errorinfo,
    })
    if (id && 0 >= id) {
    } else if (id && 0 < id) {
        if (window.main_screen.conversation_list[id]) {
            json.is_starter = 0
            window.main_screen.conversation_list[id].notifyEnterAudioSession(json)
        } else {
            console.log('[error] CWorkstationCommunicationMng.NotifyEnterAudioSession')
        }
    }
}

CWorkstationCommunicationMng.exitAudioSession = function (json) {
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.exitAudioSession ', json)
    this.query('ExitAudioSession:' + JSON.stringify(json))
}

CWorkstationCommunicationMng.NotifyExitAudioSession = function (json_str) {
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.NotifyExitAudioSession ', json_str)
    var json = json_str //JSON.parse(json_str);
    var id = json.cid

    if (id && 0 >= id) {
    } else if (id && 0 < id) {
        if (window.main_screen.conversation_list[id]) {
            window.main_screen.conversation_list[id].notifyExitAudioSession(json)
        } else {
            console.log('[error] CWorkstationCommunicationMng.NotifyExitAudioSession')
        }
    }
}

CWorkstationCommunicationMng.stopAudioSession = function (json) {
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.stopAudioSession ', json)
    this.query('StopAudioSession:' + JSON.stringify(json))
}

CWorkstationCommunicationMng.NotifyStopAudioSession = function (json_str) {
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.NotifyStopAudioSession ', json_str)
    var json = json_str //JSON.parse(json_str);
    var id = json.cid

    if (id && 0 >= id) {
    } else if (id && 0 < id) {
        if (window.main_screen.conversation_list[id]) {
            window.main_screen.conversation_list[id].notifyStopAudioSession(json)
        } else {
            console.log('[error] CWorkstationCommunicationMng.NotifyStopAudioSession')
        }
    }
}

/*
 * ios的ice连接状态通知
 */
CWorkstationCommunicationMng.NotifyIceConnectionStateChange = function (json_str) {
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.NotifyIceConnectionStateChange', json_str)
    var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
    var state = json.state
    console.log('------ NotifyIceConnectionStateChange: ' + state)
}

CWorkstationCommunicationMng.getWechatGroupInfo = function (data) {
    //浏览器唤起APP传参数
    console.log('CWorkstationCommunicationMng.getWechatGroupInfo')
    const json = Tool.formatUrl(data.url)
    wechat_group_info = json
    var account = window.localStorage.getItem('account') || ''
    var password = window.localStorage.getItem('password') || ''
    console.log(account != '' && password != '')
    console.log(account)
    console.log(password)
    alert(data);
    if (window && window.main_screen && window.main_screen.gateway && window.main_screen.gateway.check) {
        //当前用户已经登录
        console.log('is still connectted to network')
        window.vm.eventBus.$emit('wechatSendGroupInfo', json)
        wechat_group_info = {}
    } else if (account != '' && password != '') {
        var currentPath = window.vm.$router.currentRoute.fullPath
        console.log(currentPath)

        var pos = currentPath.indexOf('?')
        if (pos >= 0) {
            currentPath = currentPath.substring(0, pos)
        }
        console.log(currentPath)

        if (currentPath == '/index') {
            window.vm.eventBus.$emit('wechatSendGroupInfo', json)
        } else if (currentPath == '/login') {
            window.vm.eventBus.$emit('wechatSendGroupInfoToLogin', json)
        }
    } else {
        //no user login
        if (json.from === 'weChat') {
            window.setTimeout(function () {
                CWorkstationCommunicationMng.wechatLogin()
            }, 200)
        }
    }
}

/*
 * 安卓微信图像分享
 */
CWorkstationCommunicationMng.shareImageToWechat = function (json) {
    console.log('CWorkstationCommunicationMng.shareImageToWechat')
    console.log(json)
    this.query('shareImageToWechat:' + JSON.stringify(json))
}

/*
 * 安卓微信图像分享反馈信息
 */
CWorkstationCommunicationMng.NotifyWechatShareImageInfo = function (json) {
    console.log('CWorkstationCommunicationMng.NotifyWechatShareImageInfo')
    console.log(json)
    var that = this

    if (json.errCode == 0) {
        Toast(window.vm.$store.state.language.share_to_wechat_succeed)
    } else if (json.errCode == -1) {
        //普通错误类型
        Toast(window.vm.$store.state.language.wechat_regular_wrong_type)
    } else if (json.errCode == -2) {
        //用户点击取消并返回
        Toast(window.vm.$store.state.language.wechat_user_tap_cancel_and_back)
    } else if (json.errCode == -3) {
        //发送失败
        Toast(window.vm.$store.state.language.send_failed)
    } else if (json.errCode == -4) {
        //授权失败
        Toast(window.vm.$store.state.language.auth_fail)
    } else if (json.errCode == -5) {
        //微信不支持
        Toast(window.vm.$store.state.language.wechat_is_not_supported)
    } else if (json.errCode == -10) {
        //微信不支持
        Toast(window.vm.$store.state.language.file_does_not_exist)
    } else {
        //未知错误
        Toast(window.vm.$store.state.language.Unknown_error + '(' + json.errCode + ')')
    }
}

/*
 * 微信登录
 */
CWorkstationCommunicationMng.wechatLogin = function () {
    console.log('CWorkstationCommunicationMng.wechatLogin')

    this.query('wechatLogin')
}

/*
 * 返回微信用户相关信息
 */
CWorkstationCommunicationMng.NotifyWechatUserInfo = function (json) {
    console.log('CWorkstationCommunicationMng.NotifyWechatUserInfo')
    console.log(json)
    var that = this

    if (json.errCode == 0) {
        if (wechat_group_info.cid == undefined) {
            window.vm.eventBus.$emit('wechatAccessToken', json, {})
        } else {
            window.vm.eventBus.$emit('wechatAccessToken', json, wechat_group_info)
            wechat_group_info = {}
        }
    } else if (json.errCode == -1) {
        //普通错误类型
        Toast(window.vm.$store.state.language.wechat_regular_wrong_type)
    } else if (json.errCode == -2) {
        //用户点击取消并返回
        Toast(window.vm.$store.state.language.wechat_user_tap_cancel_and_back)
    } else if (json.errCode == -3) {
        //发送失败
        Toast(window.vm.$store.state.language.send_failed)
    } else if (json.errCode == -4) {
        //授权失败
        Toast(window.vm.$store.state.language.auth_fail)
    } else if (json.errCode == -5) {
        //微信不支持
        Toast(window.vm.$store.state.language.wechat_is_not_supported)
    } else if (json.errCode == -100) {
        //微信不支持
        Toast(window.vm.$store.state.language.wechat_no_wechat_installed)
    } else {
        //未知错误
        Toast(window.vm.$store.state.language.unknown_error + '(' + json.errCode + ')')
    }
}
/*
 * 苹果登录
 */
CWorkstationCommunicationMng.appleLogin = function () {
    console.log('CWorkstationCommunicationMng.appleLogin')

    this.query('appleLogin')
}
/*
 * 返回微信用户相关信息
 */
CWorkstationCommunicationMng.NotifyAppleLoginInfo = function (json) {
    // console.error('CWorkstationCommunicationMng.NotifyAppleLoginInfo',json)
    console.log(json)
    if (json.errCode === 0) {
        requestManager.handleResponse('NotifyAppleLoginInfo', json.data)
    } else if (json.errCode == 1000) {
        //普通错误类型
        Toast(window.vm.$store.state.language.wechat_regular_wrong_type)
    } else if (json.errCode == 1001) {
        //用户点击取消并返回
        Toast(window.vm.$store.state.language.wechat_user_tap_cancel_and_back)
    } else if (json.errCode == 1002) {
        //发送失败
        Toast(window.vm.$store.state.language.send_failed)
    } else if (json.errCode == 1004 || json.errCode == 1003) {
        //授权失败
        Toast(window.vm.$store.state.language.auth_fail)
    } else {
        //未知错误
        Toast(window.vm.$store.state.language.unknown_error + '(' + json.errCode + ')')
    }

    // var that = this

    // if (json.errCode == 0) {
    //     if (wechat_group_info.cid == undefined) {
    //         window.vm.eventBus.$emit('wechatAccessToken', json, {})
    //     } else {
    //         window.vm.eventBus.$emit('wechatAccessToken', json, wechat_group_info)
    //         wechat_group_info = {}
    //     }
    // } else if (json.errCode == -1) {
    //     //普通错误类型
    //     Toast(window.vm.$store.state.language.wechat_regular_wrong_type)
    // } else if (json.errCode == -2) {
    //     //用户点击取消并返回
    //     Toast(window.vm.$store.state.language.wechat_user_tap_cancel_and_back)
    // } else if (json.errCode == -3) {
    //     //发送失败
    //     Toast(window.vm.$store.state.language.send_failed)
    // } else if (json.errCode == -4) {
    //     //授权失败
    //     Toast(window.vm.$store.state.language.auth_fail)
    // } else if (json.errCode == -5) {
    //     //微信不支持
    //     Toast(window.vm.$store.state.language.wechat_is_not_supported)
    // } else if (json.errCode == -100) {
    //     //微信不支持
    //     Toast(window.vm.$store.state.language.wechat_no_wechat_installed)
    // } else {
    //     //未知错误
    //     Toast(window.vm.$store.state.language.unknown_error + '(' + json.errCode + ')')
    // }
}
CWorkstationCommunicationMng.startRecordSound = function (json) {
    this.query('StartRecordSound:' + JSON.stringify(json))
}
CWorkstationCommunicationMng.stopRecordSound = function (json) {
    this.query('StopRecordSound:' + JSON.stringify(json))
}
CWorkstationCommunicationMng.NotifySoundRecord = function (json_str) {
    console.log('NotifySoundRecord', json_str)
    var json = json_str //JSON.parse(json_str);
    // var id = json.cid;
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.NotifySoundRecord', {
        duration: json.duration,
        length: json.url.length,
    })
    requestManager.handleResponse('notifySoundRecord', json_str)
}
CWorkstationCommunicationMng.PlayingAudio = function () {
    this.query('PlayingAudio')
}
CWorkstationCommunicationMng.StopPlayingAudio = function () {
    this.query('StopPlayingAudio')
}

/*
 * 切换声音状态，非静音还是静音
 */
CWorkstationCommunicationMng.switchVoiceState = function (json) {
    this.query('switchVoiceState:' + JSON.stringify(json))
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.switchVoiceState', json)
}

//检测二维码的有效性
CWorkstationCommunicationMng.checkQRCode = function (json_str) {
    try {
        var json = JSON.parse(json_str)
        return json
    } catch (e) {
        return null
    }
}
// js通知APP超声机器的信息
CWorkstationCommunicationMng.notifyUltrasoundMachineInfo = function (json_str) {
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.notifyUltrasoundMachineInfo', json_str)
    var json = CWorkstationCommunicationMng.checkQRCode(json_str)
    if (json) {
        json.user_account = window.vm.$store.state.user.login_name || 'visitor'
        json.user_nickname = window.vm.$store.state.user.login_name || 'visitor'

        this.query('NotifyUltrasoundMachineInfo:' + JSON.stringify(json))
        // if (window.vm.$store.state.user.login_name) {

        // }else {
        //     Toast(window.vm.$store.state.language.init_not_ready);
        // }
    } else {
        Toast(window.vm.$store.state.language.invalid_qrcode)
    }
}

//APP通知js已连接超声机器
CWorkstationCommunicationMng.NotifyStartUltrasoundMachineAssistant = function (json_str) {
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.NotifyStartUltrasoundMachineAssistant', json_str)
    //连接后销毁之前的连接数据
    window.vm.$store.commit('ultrasoundMachine/destroyMachine')
    var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
    json.isConnecting = true
    window.vm.$store.commit('ultrasoundMachine/updateMachine', json)
    var uid = window.vm.$store.state.user.uid
    if (uid) {
        window.vm.$router.push(`/index/ultrasound_machine?version=${window.version}`)
    } else {
        window.vm.$router.push(`/scan_device/ultrasound_machine?version=${window.version}`)
    }
}

//APP通知js已断开超声机器的连接
CWorkstationCommunicationMng.NotifyStopUltrasoundMachineAssistant = function () {
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.NotifyStopUltrasoundMachineAssistant', '')
    window.vm.$store.commit('ultrasoundMachine/destroyMachine')
    requestManager.handleResponse('exitMachinePage')
}

// js通知APP查询网络类型
CWorkstationCommunicationMng.queryMobileNetType = function () {
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.queryMobileNetType')
    this.query('queryMobileNetType')
}

//APP通知js网络类型
CWorkstationCommunicationMng.NotifyQueryMobileNetType = function (json_str) {
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.NotifyQueryMobileNetType', json_str)
    console.log('CWorkstationCommunicationMng.NotifyQueryMobileNetType ', json_str)
    var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
    var net_type = json.net_type //(int, -1 无网络， 0 移动网络，1  WIFI
    var scanner = window.vm.$store.state.scanner
    if (scanner.scanner_str && '' != scanner.scanner_str) {
        //处于扫码过程
        requestManager.handleResponse('scanner_notify_mobile_net_type', { net_type: net_type })
    }
}

//js向App查询超声机器图像列表
CWorkstationCommunicationMng.queryUltrasoundMachineImageList = function () {
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.queryUltrasoundMachineImageList', '')
    this.query('QueryUltrasoundMachineImageList')
}

//APP通知js更新超声图像列表
CWorkstationCommunicationMng.NotifyUpdateUltrasoundMachineImageList = function (json_str) {
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.NotifyUpdateUltrasoundMachineImageList', json_str)
    console.log('get new machine image list', json_str)
    var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
    if (json.error) {
        Toast('NotifyUpdateUltrasoundMachineImageList error')
    } else {
        for (let item of json.list) {
            for (let img of item.image_list) {
                img.url_local = item.exam_path + '/' + img.url
                img.msg_type = img.img_type
                img.checked = false
                img.isTransferFile = true
            }
        }
        window.vm.$store.commit('ultrasoundMachine/updateTransferImageList', json)
    }
}

//js通知App本地存储超声机器图像
CWorkstationCommunicationMng.notifyUltrasoundMachineLocalStorageImages = function (json) {
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.notifyUltrasoundMachineLocalStorageImages', '')

    this.query('UltrasoundMachineLocalStorageImages:' + JSON.stringify(json))
}

//js通知App废弃超声机器图像
CWorkstationCommunicationMng.notifyUltrasoundMachineDestroyImages = function (json) {
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.notifyUltrasoundMachineDestroyImages', '')

    this.query('UltrasoundMachineDestroyImages:' + JSON.stringify(json))
}

//js通知App断开超声机器连接
CWorkstationCommunicationMng.notifyDisconnectFromDoppler = function () {
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.notifyDisconnectFromDoppler', '')

    this.query('NotifyDisconnectFromDoppler')
}
//js通知app上传超声图像
CWorkstationCommunicationMng.addExamToMobile = function (data) {
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.addExamToMobile', data)
    console.log('addExamToMobile%%%%%%%%%%%%%', data)
    let serverInfo = window.vm.$store.state.systemConfig.serverInfo
    if (serverInfo) {
        var json =  Tool.generateConsultationKeyForPcClient()
        json.SessionId = data.SessionId
        json.TaskId = data.TaskId ? data.TaskId : ''
        json.ImageList = data.ImageList ? data.ImageList : []
        json.DataSource = data.DataSource ? data.DataSource : 0

        this.query('AddExam:' + JSON.stringify(json))
    } else {
        console.log('[error] CWorkstationCommunicationMng.addExamToMobile')
    }
}
CWorkstationCommunicationMng.QueryExamIsValid = function (json_str) {
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.QueryExamIsValid', json_str)
    var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
    json.isValid = 1
    this.ExamIsValid(json)
}

CWorkstationCommunicationMng.ExamIsValid = function (data) {
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.ExamIsValid', data)
    var json = { ExamId: data.ExamId, SessionId: data.SessionId, IsValid: data.isValid }
    this.query('ExamIsValid:' + JSON.stringify(json))
}

CWorkstationCommunicationMng.QueryImageIsExist = function (json_str) {
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.QueryImageIsExist', json_str)
    var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
    // var id = json.SessionId;
    var imgList = []
    for (var i in json.ImageList) {
        var img_id = json.ImageList[i]
        var img = {}
        img[img_id] = false
        imgList.push(img)
    }

    var param = {}
    param.SessionId = json.SessionId
    param.ExamId = json.ExamId
    param.Result = imgList
    this.ImageIsExist(param)
}

CWorkstationCommunicationMng.ImageIsExist = function (data) {
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.ImageIsExist', data)
    this.query('ImageIsExist:' + JSON.stringify(data))
}
//app通知Js准备上传超声图像
CWorkstationCommunicationMng.InitTransferState = function (json_str) {
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.InitTransferState', json_str)
    console.info('InitTransferState', json_str)
    requestManager.handleResponse('initMachineTransfer', json_str)
}
//app通知Js更新上传超声图像进度
CWorkstationCommunicationMng.NotifyProgress = function (json_str) {
    console.info('NotifyProgress', json_str)
    requestManager.handleResponse('updateMachineTransfer', json_str)
}
//app通知Js完成上传超声图像
CWorkstationCommunicationMng.UpdateTransferState = function (json_str) {
    console.info('UpdateTransferState', json_str)
    requestManager.handleResponse('finishMachineTransfer', json_str)
}
//js向App查询本地病人库图像列表
CWorkstationCommunicationMng.queryLocalPatientDataImageList = function () {
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.queryLocalPatientDataImageList', '')
    this.query('QueryLocalPatientDataImageList')
}

//APP通知js更新本地病人库图像列表
CWorkstationCommunicationMng.NotifyUpdateLocalPatientDataImageList = function (json_str) {
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.NotifyUpdateLocalPatientDataImageList', json_str)

    var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
    // CMainScreenUI.notifyUpdateLocalPatientDataImageList(json);
    if (json.error) {
        Toast('NotifyUpdateLocalPatientDataImageList error')
    } else {
        for (let item of json.list) {
            for (let img of item.image_list) {
                img.url_local = item.exam_path + '/' + img.url
                img.msg_type = img.img_type
                img.isLocalPatientFile = true
            }
        }
        window.vm.$store.commit('localPatients/updatePatientImageList', json)
    }
    console.log('CWorkstationCommunicationMng.NotifyUpdateLocalPatientDataImageList', json)
}

//js通知App废弃本地病人库图像
CWorkstationCommunicationMng.notifyLocalPatientDataDestroyImages = function (json) {
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.notifyLocalPatientDataDestroyImages', '')

    this.query('LocalPatientDataDestroyImages:' + JSON.stringify(json))
}
//js通知App获取检查浏览列表
CWorkstationCommunicationMng.ULinkQueryExamListEx = function (json) {
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.ULinkQueryExamListEx', '')
    this.query('ULinkQueryExamListEx:' + JSON.stringify(json))
    // let examList=[
    //     {
    //         patient_id:201901021021282,
    //         serial_id:201901021021282,
    //         patient_name:'bob',
    //         patient_sex:1,
    //         patient_age:22,
    //         patient_age_unit:21003,
    //         exam_time:'2018-10-08',
    //         exam_mode:'小器官',
    //         image_list:[]
    //     },
    //     {
    //         patient_id:1234567,
    //         serial_id:1234567,
    //         patient_name:'vicky',
    //         patient_sex:2,
    //         patient_age:22,
    //         patient_age_unit:21003,
    //         exam_time:'2018-10-03',
    //         exam_mode:'SMP',
    //         image_list:[]
    //     }
    // ];
    // setTimeout(()=>{
    //     this.ULinkNotifyExamListEx({
    //         examList:examList,
    //         total_num:100
    //     });
    // },200)
}
//APP通知js更新检查浏览列表
CWorkstationCommunicationMng.ULinkNotifyExamListEx = function (json_str) {
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.ULinkNotifyExamListEx', json_str)
    var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
    console.log('CWorkstationCommunicationMng.ULinkNotifyExamListEx', json)
    if (json.error) {
        Toast('ULinkNotifyExamListEx error')
    } else {
        for (let item of json.examList) {
            item.initImageList = false //初始化数据标记,仅点击一次
            item.finishImageList = false //收到数据，隐藏loading
            item.open = false
        }
        window.vm.$store.commit('ultrasoundMachine/updateExamBrowseList', json)
    }
}
//js通知App获取检查浏览下缩略图列表
CWorkstationCommunicationMng.ULinkQueryImageListEx = function (json) {
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.ULinkQueryImageListEx', '')
    this.query('ULinkQueryImageListEx:' + JSON.stringify(json))
    // let ImageList={
    //     patient_id:1234567,
    //     serial_id:1234567,
    //     image_list:[{
    //         img_id:'123abc',
    //         img_type:3,
    //     },
    //     {
    //         img_id:'123abcc',
    //         img_type:3,
    //     }]
    // }
    // setTimeout(()=>{
    //     this.ULinkNotifyThumbImageListEx1(ImageList);
    // },200)
}
//APP通知js更新检查浏览缩略图列表
CWorkstationCommunicationMng.ULinkNotifyThumbImageListEx1 = function (json_str) {
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.ULinkNotifyThumbImageListEx1', json_str)
    var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
    console.log('CWorkstationCommunicationMng.ULinkNotifyThumbImageListEx1', json)
    if (json.error) {
        Toast('ULinkNotifyThumbImageListEx1 error')
    } else {
        for (let img of json.image_list) {
            img.loaded = false
            img.loading = false
            img.isExamFile = true
            img.msg_type = img.img_type
            img.url_local = 'static/resource/images/placeholder.png'
        }
        window.vm.$store.commit('ultrasoundMachine/updateExamImageList', json)
    }
    // let img={
    //     serial_id:1234567,
    //     image_info:{
    //         img_id:'123abc',
    //         url:'stylesheets/images/portrait/24/24_1543993794525.jpg'
    //     }
    // }
    // setTimeout(()=>{
    //     this.ULinkNotifyThumbImageListEx2(img)
    // },100)
}
//APP通知js更新检查浏览缩略图列表url
CWorkstationCommunicationMng.ULinkNotifyThumbImageListEx2 = function (json_str) {
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.ULinkNotifyThumbImageListEx2', json_str)
    var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
    console.log('CWorkstationCommunicationMng.ULinkNotifyThumbImageListEx2', json)
    if (json.error) {
        Toast('ULinkNotifyThumbImageListEx2 error')
    } else {
        window.vm.$store.commit('ultrasoundMachine/updateExamImageInfo', json)
    }
}
//js通知APP获取检查图像大图
CWorkstationCommunicationMng.ULinkLoadExamImageList = function (json) {
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.ULinkLoadExamImageList', '')
    this.query('ULinkLoadExamImageList:' + JSON.stringify(json))
    // for(let image of json.image_list){
    //     image.url2='http://consult-lan-test.mindray.com:80/PATIENT_DATA/20181221/20181221-***********-6E04/071122000074FE48276E04/201812212211070001ABD/SingleFrame.jpg'
    // }
    // setTimeout(()=>{
    //     this.ULinkNotifyExamImageList(json)
    // },200)
}
//APP通知js更新检查图像大图
CWorkstationCommunicationMng.ULinkNotifyExamImageList = function (json_str) {
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.notifyExamBrowseList', json_str)
    var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
    console.log('CWorkstationCommunicationMng.ULinkNotifyExamImageList', json)
    if (json.error) {
        Toast('ULinkNotifyExamImageList error')
        for (let item of json.image_list) {
            item.realUrl = 'static/resource/images/slt_err.png'
            item.loaded = false
        }
        window.vm.$store.commit('ultrasoundMachine/updateExamLocalImage', json)
        requestManager.handleResponse('updateLoadedDcm', json)
    } else {
        for (let item of json.image_list) {
            if (item.img_type == 4) {
                console.log('ULinkNotifyExamImageList realUrl4', item.url2)
                item.realUrl = item.url
            } else {
                console.log('ULinkNotifyExamImageList realUrl3', item.url2)
                item.realUrl = item.url2
            }
            item.loaded = true
            item.msg_type = item.img_type
        }
        window.vm.$store.commit('ultrasoundMachine/updateExamLocalImage', json)
        requestManager.handleResponse('updateLoadedDcm', json)
        // requestManager.handleResponse('setExamLocalImage',json);
    }
}

//js通知App上传超声机器图像到OSS
CWorkstationCommunicationMng.requestUploadAttachmentToOSS = function (json) {
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.requestUploadAttachmentToOSS', json)
    let serverInfo = window.vm.$store.state.systemConfig.serverInfo
    json.bucket = serverInfo.oss_attachment_server.bucket
    json.endpoint = serverInfo.oss_attachment_server.endpoint
    json.soa = serverInfo.oss_attachment_server.soa
    json.seo = serverInfo.oss_attachment_server.seo
    json.dst_path = serverInfo.oss_attachment_server.sub_dir + '/' + json.dst_path + '/' + json.file_name
    json.progress_step = 5

    this.query('RequestUploadAttachmentToOSS:' + JSON.stringify(json))
}
//App通知js OSS上传进度
CWorkstationCommunicationMng.notifyUploadAttachmentProgressToOSS = function (json_str) {
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.notifyUploadAttachmentToOSS', json_str)
    var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
    requestManager.handleResponse('updateProgressOSS', json)
    console.log('CWorkstationCommunicationMng.notifyUploadAttachmentToOSS', json_str)
    // if (cid && controllerMap[cid]) {
    //     if (controllerMap[cid]) {
    //         controllerMap[cid].notifyUploadAttachmentToOSSByH5Plus(json);
    //     }
    // } else {
    //     DEBUG_TO_SERVER("CWorkstationCommunicationMng.notifyUploadAttachmentToOSS Fail", json);
    // }
}
CWorkstationCommunicationMng.HttpUploadTarget = function (json) {
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.HttpUploadTarget', json)
    this.query('HttpUploadTarget:' + JSON.stringify(json))
}
CWorkstationCommunicationMng.HttpNotifyProgress = function (json_str) {
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.HttpNotifyProgress', json_str)
    var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
    requestManager.handleResponse('HttpNotifyProgress', json)
}
CWorkstationCommunicationMng.QueryUnreadMsg = function () {
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.QueryUnreadMsg')
    requestManager.handleResponse('getUnreadMsgNum')
}
CWorkstationCommunicationMng.NotifyUnreadMsg = function (json) {
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.NotifyUnreadMsg', json)
    //json: {msgNum : 5}
    this.query('NotifyUnreadMsg:' + JSON.stringify(json))
}

CWorkstationCommunicationMng.WechatScanQRConnect = function (json_str) {
    console.log('[event] CWorkstationCommunicationMng.WechatScanQRConnect')
    console.log(json_str)

    var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
    var str = ''
    if (json.uri) {
        var pos = json.uri.indexOf('?')
        if (pos != -1) {
            str = json.uri.substr(pos + 1)
        }
    }
    var params = {}
    if (str) {
        var strs = str.split('&')
        for (let i = 0; i < strs.length; i++) {
            params[strs[i].split('=')[0]] = unescape(strs[i].split('=')[1])
        }
    }

    if (1 == json.is_wake_up) {
        //app被唤醒的
        console.log('--------------------------------- wake up ---------------------------------')
        requestManager.handleResponse('ScanQRConnect', params)
    } else {
        //app是新启动的
        //app是新启动应该只调用一次，防止接口调用两次导致后面的流程出错
        if (1 == window.callOneTime_appNewStart_WechatScanQRConnect) {
            console.log('----------- call more than one time for appNewStart to call WechatScanQRConnect -----')
            return
        }
        window.callOneTime_appNewStart_WechatScanQRConnect = 1

        console.log('---------------------------------app new start---------------------------------')
        console.log('new_start_app: ' + window.vm.$store.state.scanner.new_start_app)
        if (params.act == 'con') {
            if (1 == window.vm.$store.state.scanner.new_start_app) {
                requestManager.handleResponse('ScanQRConnect', params)
            } else {
                window.vm.$store.commit('scanner/initScanner')
                window.vm.$store.commit('scanner/updateScanner', {
                    new_start_app: 1,
                    scanner_str: JSON.stringify(params),
                })
            }
        }
    }
}

//app前后台切换的通知
CWorkstationCommunicationMng.AppSwitchFrontOrBackGround = function (json_str) {
    console.log('[event] CWorkstationCommunicationMng.AppSwitchFrontOrBackGround')
    console.log(json_str)
}
CWorkstationCommunicationMng.navigationShutDown = function () {
    this.query('NavigationShutDown')
}

CWorkstationCommunicationMng.navigationMinimize = function () {
    this.query('NavigationMinimize')
}

CWorkstationCommunicationMng.navigationShowNormalOrMaximize = function () {
    this.query('NavigationShowNormalOrMaximize')
}
CWorkstationCommunicationMng.return = function () {
    this.query('Return')
}
////////切换语言/////////////
CWorkstationCommunicationMng.switchLanguage = function (language) {
    this.query('SwitchLanguage:' + language)
}
CWorkstationCommunicationMng.navigationMouseDown = function (json) {
    this.query('NavigationMouseDown:' + JSON.stringify(json))
}

CWorkstationCommunicationMng.navigationMouseMove = function (json) {
    this.query('NavigationMouseMove:' + JSON.stringify(json))
}

CWorkstationCommunicationMng.navigationMouseUp = function (json) {
    this.query('NavigationMouseUp:' + JSON.stringify(json))
}
CWorkstationCommunicationMng.navigationShutDown = function () {
    this.query('NavigationShutDown')
}
/*
 * 打开本地目录
 */
CWorkstationCommunicationMng.openDirectory = function () {
    this.query('openDirectory')
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.openDirectory')
}

/*
 * 获取打开的目录
 */
CWorkstationCommunicationMng.NotifyChoosedDirectory = function (json_str) {
    var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.NotifyChoosedDirectory', json)
    var choose_dir = json.choose_dir

    requestManager.handleResponse('NotifyOpenDirectory', choose_dir)
}
/*
 * 导出图片
 */
CWorkstationCommunicationMng.exportImage = function (json, cb) {
    if (download_single || download_multi) {
        Toast(window.vm.$store.state.language.export_running)
        return
    }
    this.query('exportImage:' + JSON.stringify(json))
    download_single = true
    cb && cb()
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.exportImage', json)
}

CWorkstationCommunicationMng.exportImageInDefaultPath = function (json, cb) {
    if (download_single || download_multi) {
        Toast(window.vm.$store.state.language.export_running)
        return
    }
    this.query('exportImageInDefaultPath:' + JSON.stringify(json))
    download_multi = true
    cb && cb()
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.exportImageInDefaultPath', json)
}

CWorkstationCommunicationMng.exportMultiFileList = function (json, cb) {
    if (download_single || download_multi) {
        Toast(window.vm.$store.state.language.export_running)
        return
    }
    this.query('exportMultiFileList:' + JSON.stringify(json))
    download_multi = true
    cb && cb()
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.exportMultiFileList', json)
}

/*
 * 取消导出图片
 */
CWorkstationCommunicationMng.CancelExportImage = function (json) {
    this.query('CancelExportImage:' + JSON.stringify(json))
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.CancelExportImage', json)
}
/*
 * 通知图片导出进度
 */
CWorkstationCommunicationMng.notifyExportImageProgress = function (json_str) {
    var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.notifyExportImageProgress', json)

    if (download_single) {
        json.type = 'single'
        requestManager.handleResponse('notifyDownloadProcess', json)
        if (json.isAllFinished) {
            download_single = false
        }
    }
    if (download_multi) {
        json.type = 'multi'
        requestManager.handleResponse('notifyDownloadProcess', json)
        if (json.isAllFinished) {
            download_multi = false
        }
    }
    if (json.error) {
        download_single = false
        download_multi = false
    }
    // CMainScreenUI.notifyExportImageProgress(json);
}
//文件保存进度通知
CWorkstationCommunicationMng.NotifyFileDownloadFinish = function (json_str) {
    console.log('[event] CWorkstationCommunicationMng.NotifyFileDownloadFinish')
    console.log(json_str)
    var json = JSON.parse(json_str)
    var data = CWorkstationCommunicationMng.parseURL(json.Url)
    data.percent = 100
    data.url = json.Url
    window.vm.eventBus.$emit('appNotifyFileDownloadPercent', data)
}

CWorkstationCommunicationMng.NotifyFileDownloadPercent = function (json_str) {
    console.log('[event] CWorkstationCommunicationMng.NotifyFileDownloadPercent')
    console.log(json_str)

    var json = JSON.parse(json_str)
    if (json.Progress < 100) {
        var data = CWorkstationCommunicationMng.parseURL(json.Url)
        data.percent = json.Progress
        data.url = json.Url
        window.vm.eventBus.$emit('appNotifyFileDownloadPercent', data)
    }
}

CWorkstationCommunicationMng.QuipApp = function (language) {
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.QuipApp', language)
    this.query('QuipApp')
}
CWorkstationCommunicationMng.DisplayDrImage = function (json) {
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.DisplayDrImage', json)
    this.query('DisplayDrImage:' + JSON.stringify(json))
}
/*
 *  启动本地桌面
 */
CWorkstationCommunicationMng.startLocalDesktop = function (json) {
    var systemConfig = window.vm.$store.state.systemConfig
    json.start_catch_type = systemConfig.start_catch_type.LocalDesktop
    window.CWorkstationCommunicationMng.starCatch(json)
}
/*
 *  启动摄像头桌面
 */
CWorkstationCommunicationMng.startCameraDesktop = function (json) {
    var systemConfig = window.vm.$store.state.systemConfig
    json.start_catch_type = systemConfig.start_catch_type.CameraDesktop
    window.CWorkstationCommunicationMng.starCatch(json)
}
CWorkstationCommunicationMng.starCatch = function (json) {
    var systemConfig = window.vm.$store.state.systemConfig
    json.network_environment = systemConfig.serverInfo.network_environment
    json.video_server_addr = systemConfig.serverInfo.VideoServerAddr
    json.video_server_port = systemConfig.serverInfo.VideoServerPort
    json.rtmp_video_server_port = systemConfig.serverInfo.RtmpVideoServerPort
    //json.rtmp_video_server_channel = systemConfig.serverInfo.RtmpVideoServerChannel;
    if (json.record_mode == 1) {
        json.rtmp_video_server_channel = systemConfig.serverInfo.RtmpVideoServerChannel
    } else {
        json.rtmp_video_server_channel = systemConfig.serverInfo.RtmpVideoServerChannelNoRecord
    }
    json.rtmp_video_server_cdn = systemConfig.serverInfo.RtmpVideoServerCDN
    json.rtmp_video_server = systemConfig.serverInfo.RtmpVideoServer

    json.enable_playback = systemConfig.serverInfo.EnablePlayback
    json.enable_video_time_shift = systemConfig.serverInfo.EnableVideoTimeShift
    json.enable_video_stream_preview = systemConfig.serverInfo.enable_video_stream_preview

    json.video_server_type = systemConfig.serverInfo.video_server_type
    json.record_video_server_type = systemConfig.serverInfo.record_video_server_type
    json.enable_camera = systemConfig.realtimeModeInfo.SecondaryFlowPushEnable
    json.enable_voice_stream = systemConfig.realtimeModeInfo.enable_voice_stream

    json.enable_edge_push = systemConfig.serverInfo.enable_edge_push
    json.rtmp_video_server_edge_pull = systemConfig.serverInfo.rtmp_video_server_edge_pull
    json.rtmp_video_server_edge_push = systemConfig.serverInfo.rtmp_video_server_edge_push
    this.query('StartCatch:' + JSON.stringify(json))
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.StartCatch', json)
}
CWorkstationCommunicationMng.NotifyStartCatch = function (json_str) {
    console.log('[event] CWorkstationCommunicationMng.NotifyStartCatch')
    console.log(json_str)
    var systemConfig = window.vm.$store.state.systemConfig
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.NotifyStartCatch', json_str)
    //consultation_id、type、url
    var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
    var id = json.consultation_id
    if (id && 0 < id) {
        if (window.main_screen.conversation_list[id]) {
            if (20000 == json.error) {
                //app端音频初始化失败,不处理,仅仅记录日志
                DEBUG_TO_SERVER('[error] app音频初始化失败')
                json.error = 0
            }
            if (systemConfig.start_catch_type.UltrasoundDesktop == json.start_catch_type) {
                requestManager.handleResponse('notifyStartUltrasoundDesktop', json)
            } else if (systemConfig.start_catch_type.MonitorWall == json.start_catch_type) {
                //已废弃
                // controllerMap[id].notifyStartMonitorWall(json);
            } else if (systemConfig.start_catch_type.CameraVideoEx == json.start_catch_type) {
                requestManager.handleResponse('notifyStartCatchEx', json)
            } else if (systemConfig.start_catch_type.LocalDesktop == json.start_catch_type) {
                requestManager.handleResponse('notifyStartUltrasoundDesktop', json)
            } else if (systemConfig.start_catch_type.CameraDesktop == json.start_catch_type) {
                requestManager.handleResponse('notifyStartUltrasoundDesktop', json)
            } else {
                console.log('[error] CWorkstationCommunicationMng.NotifyStartCatch error start_catch_type:' + json.start_catch_type)
            }
        } else {
            console.log('[error] CWorkstationCommunicationMng.NotifyStartCatch')
        }
    }
}

CWorkstationCommunicationMng.QueryRealTimeMode = function () {
    this.query('QueryRealTimeMode')
}
CWorkstationCommunicationMng.NotifyQueryRealTimeMode = function (json_str) {
    console.log('[event] CWorkstationCommunicationMng.NotifyQueryRealTimeMode')
    console.log(json_str)

    DEBUG_TO_SERVER('CWorkstationCommunicationMng.NotifyQueryRealTimeMode', json_str)
    var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
    json.enable_voice_stream = json.enable_voice_stream || 0
    window.vm.$store.commit('systemConfig/updateSystemConfig', { realtimeModeInfo: json })
}
//关闭桌面直播
CWorkstationCommunicationMng.QueryPatientInformation = function (data) {
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.QueryPatientInformation', data)
    var json = { cid: data.cid, start_catch_type: data.start_catch_type,}
    this.query('QueryPatientInformation:' + JSON.stringify(json))
    //var json_str = {
    //    error:0,
    //    cid:3,
    //    patient_name:"kongzima",
    //    patient_sex:0,
    //    patient_age:46,
    //    patient_age_unit:21004,
    //    patient_id:"dghhafdasfgaseggad",
    //}
    //CWorkstationCommunicationMng.NotifyPatientInformation(json_str);
}
CWorkstationCommunicationMng.NotifyPatientInformation = function (json_str) {
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.NotifyPatientInformation', json_str)
    var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
    console.log(json)
    //现在没有这种情况了
    if (json.cid <= 0) {
        if (window.main_screen) {
            window.main_screen.controller.emit('notify_query_patient_information', json)
        }
        //cMainScreenController.NotifyQueryPatientInformation(json);
        return
    }
    var patient_info = {}
    if (json.error == 0) {
        patient_info = {
            start_catch_type: json.start_catch_type,

            patient: {
                patient_name: json.patient_name,
                patient_sex: json.patient_sex,
                patient_age: json.patient_age,
                patient_age_unit: json.patient_age_unit,
            },
        }

        if (json.patient_exam_id) {
            patient_info.patient.patient_id = json.patient_exam_id
        } else if (json.patient_id) {
            patient_info.patient.patient_id = json.patient_id
        }
    } else if (json.error == 1) {
        console.log('[error] NotifyPatientInformation Get Patient Info Failed')
        patient_info = {
            start_catch_type: json.start_catch_type,
            patient: null,
        }
    }

    var id = json.cid
    if (id && id > 0) {
        if (window.main_screen.conversation_list[id]) {
            window.vm.eventBus.$emit('notifyPatientInformationToConversation', patient_info)
        } else {
            console.log('[error] CWorkstationCommunicationMng.NotifyPatientInformation')
        }
    }
}
CWorkstationCommunicationMng.stopLocalDesktop = function (json) {
    var systemConfig = window.vm.$store.state.systemConfig
    json.start_catch_type = systemConfig.start_catch_type.LocalDesktop
    this.query('StopCatch:' + JSON.stringify(json))
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.StopCatch', json)
}
CWorkstationCommunicationMng.stopCameraDesktop = function (json) {
    var systemConfig = window.vm.$store.state.systemConfig
    json.start_catch_type = systemConfig.start_catch_type.CameraDesktop
    this.query('StopCatch:' + JSON.stringify(json))
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.StopCatch', json)
}
//控制手势视频的窗口打开or隐藏
//参数：{show:0or1}
CWorkstationCommunicationMng.CtrlGestureVideoWindow = function (json) {
    this.query('CtrlGestureVideoWindow:' + JSON.stringify(json))
}
CWorkstationCommunicationMng.NotifyCtrlGestureVideoWindow = function (json_str) {
    console.log('[event] CWorkstationCommunicationMng.NotifyCtrlGestureVideoWindow')
    console.log(json_str)
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.NotifyCtrlGestureVideoWindow', json_str)
    // var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str;
}
//push rtmp voice msg
CWorkstationCommunicationMng.pushVoiceRtmpMsg = function (json) {
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.PushVoiceRtmpMsg', json)

    this.query('PushVoiceRtmpMsg:' + JSON.stringify(json))
}
//notify push rtmp voice msg
CWorkstationCommunicationMng.NotifyPushVoiceRtmpMsg = function (json_str) {
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.NotifyPushVoiceRtmpMsg', json_str)
    var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
    var id = json.cid
    if (id && 0 >= id) {
    } else if (id && 0 < id) {
        if (window.main_screen.conversation_list[id]) {
            window.vm.eventBus.$emit('NotifyPushVoiceRtmpMsg', json)
            // controllerMap[id].NotifyPushVoiceRtmpMsg(json);
        }
    }
}
/*
 *  切换会议采集源
 */
CWorkstationCommunicationMng.switchConference = function (json) {
    var systemConfig = window.vm.$store.state.systemConfig
    var serverInfo = systemConfig.serverInfo
    //cid,start_catch_type 指定采集源头
    json.network_environment = serverInfo.network_environment
    json.video_server_addr = serverInfo.VideoServerAddr
    json.video_server_port = serverInfo.VideoServerPort
    json.rtmp_video_server_port = serverInfo.RtmpVideoServerPort
    //json.rtmp_video_server_channel = serverInfo.RtmpVideoServerChannel;
    if (json.record_mode == 1) {
        json.rtmp_video_server_channel = serverInfo.RtmpVideoServerChannel
    } else {
        json.rtmp_video_server_channel = serverInfo.RtmpVideoServerChannelNoRecord
    }
    json.rtmp_video_server_cdn = serverInfo.RtmpVideoServerCDN
    json.rtmp_video_server = serverInfo.RtmpVideoServer

    json.enable_playback = serverInfo.EnablePlayback
    json.enable_video_time_shift = serverInfo.EnableVideoTimeShift
    json.enable_video_stream_preview = serverInfo.enable_video_stream_preview

    json.video_server_type = serverInfo.video_server_type
    json.record_video_server_type = serverInfo.record_video_server_type
    this.query('SwitchConference:' + JSON.stringify(json))
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.SwitchConference', json)
}
CWorkstationCommunicationMng.NotifySwitchConference = function (json_str) {
    console.log('[event] CWorkstationCommunicationMng.NotifySwitchConference')
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.NotifySwitchConference', json_str)

    var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
    var id = json.consultation_id
    if (id && 0 >= id) {
    } else if (id && 0 < id) {
        if (window.main_screen.conversation_list[id]) {
            window.vm.eventBus.$emit('NotifySwitchConference', json)
        }
    }
}
CWorkstationCommunicationMng.showRealTimeVideo = function (json) {
    this.query('ShowRealTimeVideo:' + JSON.stringify(json))
}

CWorkstationCommunicationMng.hideRealTimeVideo = function (json) {
    this.query('HideRealTimeVideo:' + JSON.stringify(json))
}
CWorkstationCommunicationMng.parseURL = function (str) {
    var json = {}
    var pos = str.indexOf('?')
    if (pos > -1) {
        var param_str = str.substr(pos + 1)
        var param = param_str.split('&')
        for (var i in param) {
            var arr = param[i].split('=')
            for (var j in arr) {
                if (2 == arr.length) {
                    json[arr[0]] = arr[1]
                }
            }
        }
    }
    return json
}
CWorkstationCommunicationMng.initServerConfig = function (serverInfo) {
    var json = {
        network_environment: serverInfo.network_environment,
    }
    this.query('InitServerConfig:' + JSON.stringify(json))
}
CWorkstationCommunicationMng.initUserConfig = function (json) {
    this.query('InitUserConfig:' + JSON.stringify(json))
}
////////上传超声图像///////////
CWorkstationCommunicationMng.addExam = function (cid) {
    if (Tool.ifAppWorkstationClientType(window.clientType)) {
        //工作站
    } else if (Tool.ifAppConsultationClientType(window.clientType) && window.enable_istation) {
        //开启IStation功能
    } else {
        return
    }
    let serverInfo = window.vm.$store.state.systemConfig.serverInfo
    if (serverInfo) {
        var json =  Tool.generateConsultationKeyForPcClient()
        json.SessionId = parseInt(cid)

        this.query('AddExam:' + JSON.stringify(json))
    } else {
        console.log('[error] CWorkstationCommunicationMng.addExam')
    }
}
////////实时超声（实时存储超声图像）/////////////
CWorkstationCommunicationMng.realtimeUltrasound = function (param) {
    let serverInfo = window.vm.$store.state.systemConfig.serverInfo
    if (serverInfo) {
        var json =  Tool.generateConsultationKeyForPcClient()
        json.SessionId = param.consultation_id

        json.top = param.top
        json.left = param.left
        json.width = param.width
        json.height = param.height
        this.query('RealtimeUltrasound:' + JSON.stringify(json))
    } else {
        console.log('[error] CWorkstationCommunicationMng.realtimeUltrasound')
    }
}
CWorkstationCommunicationMng.NotifyExitRealtimeUltrasound = function () {
    window.vm.$store.commit('globalParams/updateGlobalParams', {
        realtime_ultrasound_mode:false
    })
}
CWorkstationCommunicationMng.exitRealtimeUltrasoundMode = function () {
    this.query('ExitRealtimeUltrasound')
}
CWorkstationCommunicationMng.cancelTransfer = function (json) {
    this.query('CancelTransferTask:' + json.ImgId)
}
CWorkstationCommunicationMng.ExitApp = function () {
    //todo
    //设备检测使用到这个方法
}
CWorkstationCommunicationMng.getAppPhotoLibraryAccess = function () {
    console.log('getAppPhotoLibraryAccess')
    this.query('getAppPhotoLibraryAccess:')
}
CWorkstationCommunicationMng.NotifyPhotoLibraryAccess = function (access_code) {
    console.log('[event] CWorkstationCommunicationMng.NotifyPhotoLibraryAccess')
    console.log(access_code)
    window.vm.eventBus.$emit('NotifyPhotoLibraryAccess', access_code)
}
//DR通知自动转发
CWorkstationCommunicationMng.NotifyNewExamImages = function (json_str) {
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.NotifyNewExamImages', json_str)
    console.info('CWorkstationCommunicationMng.NotifyNewExamImages', json_str)
    var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
    window.vm.eventBus.$emit('notifyNewExamImages', json)
}
////////上传超声图像///////////
CWorkstationCommunicationMng.addExamImages = function (cid, image_list) {
    if (Tool.ifAppWorkstationClientType(window.clientType)) {
        //工作站
    } else if (Tool.ifAppConsultationClientType(window.clientType)) {
        //开启IStation功能
    } else {
        return
    }
    let serverInfo = window.vm.$store.state.systemConfig.serverInfo
    if (serverInfo) {
        var json = {}
        var json =  Tool.generateConsultationKeyForPcClient()
        json.SessionId = parseInt(cid)
        json.ImageList = image_list || []

        this.query('AddExamImages:' + JSON.stringify(json))
    } else {
        console.log('[error] CWorkstationCommunicationMng.AddExamImages')
    }
}

//语音异常,将数据上报给App图标上显示
//  int errorSource: 10表示语音
//  int errorCode; 来源于AVLiveError,如果值为0,则所有的语音错误均消失
//  QString errorString;
//  int isError;  1表示开启错误,0表示关闭错误
CWorkstationCommunicationMng.ReportAVLiveError = function (json) {
    json.errorSource = 10
    this.query('ReportAVLiveError:' + JSON.stringify(json))
}



////////////////////////////////// rtc Begin////////////////////////////////
CWorkstationCommunicationMng.StartRTCAudioSession = function (data) {
    data.action = 'StartRTCAudioSession'
    this.query('JoinRTCRoom:' + JSON.stringify(data))

    //temp test
    //data.error = 0;
    //this.NotifyJoinRTCRoom(data);
}

CWorkstationCommunicationMng.EnterRTCAudioSession = function (data) {
    data.action = 'EnterRTCAudioSession'
    this.query('JoinRTCRoom:' + JSON.stringify(data))

    //temp test by wwh
    //data.error = 0;
    //this.NotifyJoinRTCRoom(data);
}

CWorkstationCommunicationMng.NotifyJoinRTCRoom = function (json_str) { //PC端不使用该逻辑 此通知在NotifyPublishRTC之前，确保成功由NotifyPublishRTC提供
    // if(!Tool.ifPcClientType(window.clientType)){
    console.info('[event] CWorkstationCommunicationMng.NotifyJoinRTCRoom', json_str)
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.NotifyJoinRTCRoom', json_str)
    var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str

    var id = json.cid

    if (id && 0 >= id) {
    } else if (id && 0 < id) {
        if (window.main_screen.conversation_list[id]) {
            if ('StartRTCAudioSession' == json.action) {
                json.is_starter = 1
                window.main_screen.conversation_list[id].notifyStartAudioSession(json)
            } else if ('EnterRTCAudioSession' == json.action) {
                json.is_starter = 0
                window.main_screen.conversation_list[id].notifyEnterAudioSession(json)
            } else {
                console.log('[error] CWorkstationCommunicationMng.JoinRTCRoom error action:' + json.action)
            }
        } else {
            console.log('[error] CWorkstationCommunicationMng.JoinRTCRoom')
        }
    }
    // }

}
CWorkstationCommunicationMng.NotifyPublishRTC = function (json_str) { //手机APP使用该逻辑
    // console.log('[event] CWorkstationCommunicationMng.NotifyPublishRTC', json_str)
    // DEBUG_TO_SERVER('CWorkstationCommunicationMng.NotifyPublishRTC', json_str)
    // var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str

    // var id = json.cid
    // json.error = json.isPublished?0:1
    // if (id && 0 >= id) {
    // } else if (id && 0 < id) {
    //     if (window.main_screen.conversation_list[id]) {
    //         if ('StartRTCAudioSession' == json.action) {
    //             json.is_starter = 1
    //             window.main_screen.conversation_list[id].notifyStartAudioSession(json)
    //         } else if ('EnterRTCAudioSession' == json.action) {
    //             json.is_starter = 0
    //             window.main_screen.conversation_list[id].notifyEnterAudioSession(json)
    //         } else {
    //             console.log('[error] CWorkstationCommunicationMng.JoinRTCRoom error action:' + json.action)
    //         }
    //     } else {
    //         console.log('[error] CWorkstationCommunicationMng.JoinRTCRoom')
    //     }
    // }
}
CWorkstationCommunicationMng.StopRTCAudioSession = function (data) {
    data.action = 'StopRTCAudioSession'
    this.query('LeaveRTCRoom:' + JSON.stringify(data))

    //temp test by wwh
    //data.error = 0;
    //this.NotifyLeaveRTCRoom(data);
}

CWorkstationCommunicationMng.ExitRTCAudioSession = function (data) {
    data.action = 'ExitRTCAudioSession'
    this.query('LeaveRTCRoom:' + JSON.stringify(data))

    //temp test by wwh
    //data.error = 0;
    //this.NotifyLeaveRTCRoom(data);
}

CWorkstationCommunicationMng.CloseRTCAudioResource = function (data) {
    data.action = 'CloseRTCAudioResource'
    this.query('LeaveRTCRoom:' + JSON.stringify(data))
}

CWorkstationCommunicationMng.NotifyLeaveRTCRoom = function (json_str) {
    console.info('[event] CWorkstationCommunicationMng.NotifyLeaveRTCRoom', json_str)
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.NotifyLeaveRTCRoom ', json_str)
    var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str

    var id = json.cid

    if (id && 0 >= id) {
    } else if (id && 0 < id) {
        if (window.main_screen.conversation_list[id]) {
            if ('StopRTCAudioSession' == json.action) {
                json.is_starter = 1
                window.main_screen.conversation_list[id].notifyDisconnectRTCVoiceServer(json)
            } else if ('ExitRTCAudioSession' == json.action) {
                json.is_starter = 0
                window.main_screen.conversation_list[id].notifyDisconnectRTCVoiceServer(json)
            } else if ('CloseRTCAudioResource' == json.action) {
                //do nothing
            }
        } else {
            console.log('[error] CWorkstationCommunicationMng.NotifyLeaveRTCRoom')
        }
    }
}

//param:{mute:true or false, cid:that.cid}
CWorkstationCommunicationMng.MuteLocalMic = function (data) {
    this.query('MuteLocalMic:' + JSON.stringify(data))

    //temp test by wwh
    //data.error = 0;
    //this.NotifyMuteLocalMic(data);
}

//param:{mute:true, cid:that.cid, error:0}
CWorkstationCommunicationMng.NotifyMuteLocalMic = function (json_str) {
    console.log('[event] CWorkstationCommunicationMng.NotifyMuteLocalMic', json_str)
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.NotifyMuteLocalMic ', json_str)
    var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str

    var id = json.cid

    if (id && 0 >= id) {
    } else if (id && 0 < id) {
        if (window.main_screen.conversation_list[id]) {
            window.main_screen.conversation_list[id].notifyMuteLocalMic(json)
        } else {
            console.log('[error] CWorkstationCommunicationMng.NotifyMuteLocalMic')
        }
    }
}

//APP返回音量大小
//params: volume, isMic, cid
CWorkstationCommunicationMng.NotifyRTCAudioVolume = function (json_str) {
    //console.log("[event] CWorkstationCommunicationMng.NotifyRTCAudioVolume");
    //console.log(json_str);

    var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str

    var id = json.cid
    if (id && id > 0) {
        if (window.main_screen.conversation_list[id]) {
            var data = window.main_screen.conversation_list[id].notifyRTCAudioVolume(json)
        } else {
            console.log('[error] CWorkstationCommunicationMng.NotifyRTCAudioVolume')
        }
    }
}

CWorkstationCommunicationMng.NotifyRTCInfo = function (json_str) {
    console.log('*****************************************************')
    console.log('[event] CWorkstationCommunicationMng.NotifyRTCInfo')
    console.log(json_str)
}

//APP返回网络异常
//params: upQuality, downQuality, uid,cid
//AliRtcNetworkQualityBad= 3, //网络差，视频卡顿严重，音频能正常沟通
//AliRtcNetworkQualityVeryBad= 4, //网络极差，基本无法沟通
//AliRtcNetworkQualityDisconnect = 5, //网络中断
//AliRtcNetworkQualityUnknow = 6, //未知
CWorkstationCommunicationMng.NotifyRTCNetworkQuality = function (json_str) {
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.NotifyRTCNetworkQuality ', json_str)
    console.log('[event] CWorkstationCommunicationMng.NotifyRTCNetworkQuality ', json_str)
}

////////////////////////////////// rtc End ////////////////////////////////

///////////////////////////////// audio device Begin //////////////////////
CWorkstationCommunicationMng.GetAudioDeviceName = function () {
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.GetAudioDeviceName')
    this.query('GetAudioDeviceName') //app提供的接口
    let serverInfo = window.vm.$store.state.systemConfig.serverInfo
    if ('Aliyun' == serverInfo.rtc_voice_type) {
        this.query('GetAudioDeviceNameFromAliyun') //aliyun提供的接口
    }
}

CWorkstationCommunicationMng.NotifyGetAudioDeviceName = function (json_str) {
    console.log('[event] CWorkstationCommunicationMng.NotifyGetAudioDeviceName', json_str)
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.NotifyGetAudioDeviceName ', json_str)
}

CWorkstationCommunicationMng.NotifyGetAudioDeviceNameFromAliyun = function (json_str) {
    console.log('[event] CWorkstationCommunicationMng.NotifyGetAudioDeviceNameFromAliyun', json_str)
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.NotifyGetAudioDeviceNameFromAliyun ', json_str)
}

CWorkstationCommunicationMng.SetDefaultAudioDevice = function (json) {
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.SetDefaultAudioDevice', json)
    this.query('SetDefaultAudioDevice:' + JSON.stringify(json))
}

//output 0 or 1; input 0 or 1;(0 means succ, 1 means failed)
CWorkstationCommunicationMng.NotifySetDefaultAudioDevice = function (json_str) {
    console.log('[event] CWorkstationCommunicationMng.NotifySetDefaultAudioDevice')
    console.log(json_str)
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.NotifySetDefaultAudioDevice', json_str)

    //暂时不处理，只是记录日志
}

////////////////////////////////  audio device End ////////////////////////

////////////////////////////////  手机控制 Begin ////////////////////////
CWorkstationCommunicationMng.SaveSingleFrame = function (json) {
    this.query('SaveSingleFrame:' + JSON.stringify(json))

    //this.NotifySaveSingleFrame({error: 0});//temp test ass
}

CWorkstationCommunicationMng.NotifySaveSingleFrame = function (json_str) {
    console.log('[event] CWorkstationCommunicationMng.NotifySaveSingleFrame')
    console.log(json_str)

    DEBUG_TO_SERVER('CWorkstationCommunicationMng.NotifySaveSingleFrame', json_str)
    var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str

    if (window.main_screen) {
        window.main_screen.controller.emit('notify_save_single_frame', json)
    }
    //cMainScreenController.NotifySaveSingleFrame(json);
}

CWorkstationCommunicationMng.SaveMultiFrame = function (json) {
    this.query('SaveMultiFrame:' + JSON.stringify(json))

    //this.NotifySaveMultiFrame({error: 0, is_start:json.is_start});//temp test ass
}

CWorkstationCommunicationMng.NotifySaveMultiFrame = function (json_str) {
    console.log('[event] CWorkstationCommunicationMng.NotifySaveMultiFrame')
    console.log(json_str)

    DEBUG_TO_SERVER('CWorkstationCommunicationMng.NotifySaveMultiFrame', json_str)
    var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str

    if (window.main_screen) {
        window.main_screen.controller.emit('notify_save_multi_frame', json)
    }
    //cMainScreenController.NotifySaveMultiFrame(json);
}

//手柄通知存单帧
CWorkstationCommunicationMng.HandShankSaveSingleFrame = function (json_str) {
    console.log('[event] CWorkstationCommunicationMng.HandShankSaveSingleFrame')
    console.log(json_str)

    DEBUG_TO_SERVER('CWorkstationCommunicationMng.HandShankSaveSingleFrame', json_str)
    var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str

    json.trigger = window.vm.$store.state.systemConfig.ctrl_device_type.HandShank
    //cMainScreenController.ctrlDeviceSaveSingleFrame(json);
}

//手柄通知存多帧
CWorkstationCommunicationMng.HandShankSaveMultiFrame = function (json_str) {
    console.log('[event] CWorkstationCommunicationMng.HandShankSaveMultiFrame')
    console.log(json_str)

    DEBUG_TO_SERVER('CWorkstationCommunicationMng.HandShankSaveMultiFrame', json_str)
    var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str

    json.trigger = window.vm.$store.state.systemConfig.ctrl_device_type.HandShank
    //cMainScreenController.ctrlDeviceSaveMultiFrame(json);
}

//通知手柄存储结果
CWorkstationCommunicationMng.NotifyHandShankSaveSingleFrame = function (json) {
    this.query('NotifyHandShankSaveSingleFrame:' + JSON.stringify(json))
}

CWorkstationCommunicationMng.NotifyHandShankSaveMultiFrame = function (json) {
    this.query('NotifyHandShankSaveMultiFrame:' + JSON.stringify(json))
}

CWorkstationCommunicationMng.ShutdownBox = function () {
    this.query('ShutdownBox')
}
CWorkstationCommunicationMng.getPatientExamList = function (json) {
    this.query('GetPatientExamList:' + JSON.stringify(json))

    // this.NotifyPatientFileList({error:0,file_list:[]});//temp test ass
}
CWorkstationCommunicationMng.NotifyPatientFileList = function (json_str) {
    var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
    if (window.main_screen) {
        window.main_screen.controller.emit('notify_patient_file_list', json)
    }
    //cMainScreenController.NotifyPatientFileList(json);
}
CWorkstationCommunicationMng.UpdateDeviceInfo = function (json) {
    this.query('UpdateDeviceInfo:' + JSON.stringify(json))

    //this.NotifyUpdateDeviceInfo(json);//temp test ass
}

CWorkstationCommunicationMng.NotifyUpdateDeviceInfo = function (json_str) {
    console.log('[event] CWorkstationCommunicationMng.NotifyUpdateDeviceInfo')
    console.log(json_str)

    DEBUG_TO_SERVER('CWorkstationCommunicationMng.NotifyUpdateDeviceInfo', json_str)
    var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str

    if (window.main_screen) {
        window.main_screen.controller.emit('notify_update_device_info', json)
    }
    //cMainScreenController.NotifyUpdateDeviceInfo(json);
}

CWorkstationCommunicationMng.GetDeviceID = function (json) {
    if (!Tool.ifAppClientType(window.clientType)) {
        if (json) {
            json.device_id = 0
            this.NotifyGetDeviceID(JSON.stringify(json))
        } else {
            this.NotifyGetDeviceID(JSON.stringify({ device_id: 0 }))
        }

        return
    }
    this.query('GetDeviceID:' + JSON.stringify(json))
    //this.NotifyGetDeviceID({device_id: "111"});//temp test ass todo
}

CWorkstationCommunicationMng.NotifyGetDeviceID = function (json_str) {
    console.log('[event] CWorkstationCommunicationMng.NotifyGetDeviceID')
    console.log(json_str)
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.NotifyGetDeviceID', json_str)
    var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
    /*if ("undefined" == typeof CMainScreenController){ //处于登陆界面
        NotifyGetDeviceID(json);
    }*/
    window.vm.eventBus.$emit('NotifyGetDeviceID', json)
}

CWorkstationCommunicationMng.GetDeviceInfo = function () {
    this.query('GetDeviceInfo')

    //this.NotifyGetDeviceInfo({device_id: "*********", cur_session_id: 369, cur_session_type:0, ftp_isAnonymous:0, ftp_account:"wwh", ftp_password:111, ftp_path:"apollo", ftp_port:22});//temp test ass
}

CWorkstationCommunicationMng.NotifyGetDeviceInfo = function (json_str) {
    console.log('[event] CWorkstationCommunicationMng.NotifyGetDeviceInfo')
    console.log(json_str)

    DEBUG_TO_SERVER('CWorkstationCommunicationMng.NotifyGetDeviceInfo', json_str)
    var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str

    if (window.main_screen) {
        window.main_screen.controller.emit('notify_get_device_info', json)
    }
    //cMainScreenController.NotifyGetDeviceInfo(json);
}

CWorkstationCommunicationMng.SetPatientInfo = function (json) {
    this.query('SetPatientInfo:' + JSON.stringify(json))

    //this.NotifySetPatientInfo(json);//temp test ass
}

CWorkstationCommunicationMng.NotifySetPatientInfo = function (json_str) {
    console.log('[event] CWorkstationCommunicationMng.NotifySetPatientInfo')
    console.log(json_str)

    DEBUG_TO_SERVER('CWorkstationCommunicationMng.NotifySetPatientInfo', json_str)
    var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str

    if (window.main_screen) {
        window.main_screen.controller.emit('notify_set_patient_info', json)
    }
    //cMainScreenController.NotifySetPatientInfo(json);
}

////////////////////////////////  手机控制 End ////////////////////////

//////////////////////////////////DR////////////////////////////////
CWorkstationCommunicationMng.queryIStationInfo_DR = function () {
    this.query('QueryIStationInfo_DR')

    //CWorkstationCommunicationMng.IStationInfo_DR(JSON.stringify({Show:1}));
}

CWorkstationCommunicationMng.IStationInfo_DR = function (json_str) {
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.IStationInfo_DR', json_str)
    var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
    requestManager.handleResponse('IStationInfo_DR', json)
}
/*
 * 会议有语音请求消息来到时，通知app
 *
 */
CWorkstationCommunicationMng.conferenceMsgNotify = function (json) {
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.conferenceMsgNotify', json)

    this.query('ConferenceMsgNotify:' + JSON.stringify(json))
}

CWorkstationCommunicationMng.addDownloadTasks = function (json) {
    console.log('[event] CWorkstationCommunicationMng.addDownloadTasks', json)
    this.query('AddDownloadTasks:' + JSON.stringify(json))
}

CWorkstationCommunicationMng.NotifyDownloadTask = function (json_str) {
    console.log('[event] CWorkstationCommunicationMng.NotifyDownloadTask', json_str)
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.NotifyDownloadTask', json_str)

    var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
    console.log(json)

    window.main_screen.controller.emit('notify_download_task', json)
}
////////////////////////////////////////////////////////////////////

CWorkstationCommunicationMng.setCatchOption = function (json) {
    this.query('SetCatchOption:' + JSON.stringify(json))
}

CWorkstationCommunicationMng.setQueryStartupOptionTimer = function () {
    var that = this
    that.clearQueryStartupOptionTimer()
    that.query_startup_option_timer = setTimeout(function () {
        that.NotifyStartupOption(JSON.stringify({}))
    }, 1000)
}

CWorkstationCommunicationMng.clearQueryStartupOptionTimer = function () {
    var that = this
    if (that.query_startup_option_timer) {
        clearTimeout(that.query_startup_option_timer)
        that.query_startup_option_timer = null
    }
}

CWorkstationCommunicationMng.QueryStartupOption = function () {
    if (!Tool.ifAppClientType(window.clientType)) {
        this.NotifyStartupOption(JSON.stringify({}))
        return
    }

    this.setQueryStartupOptionTimer()
    this.query('QueryStartupOption')
}

CWorkstationCommunicationMng.NotifyStartupOption = function (json_str) {
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.NotifyStartupOption', json_str)
    console.log(json_str)
    var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
    this.clearQueryStartupOptionTimer()

    if (json.param) {
        var regexp = /^([0-9a-zA-Z_\-\+\/])+$/i
        if (regexp.test(json.param)) {
            json.param = Base64.decode(json.param)
        }
        json.param = decodeURIComponent(json.param)
    }

    window.vm.eventBus.$emit('notifyStartupOption', json.param)
}

CWorkstationCommunicationMng.ClearStartupOption = function () {
    if (!Tool.ifAppClientType(window.clientType)) {
        return
    }

    this.query('ClearStartupOption')
}

//实时视频下显示语音图标
//参数：{type：voice_operate_type, cid:cid}
CWorkstationCommunicationMng.ShowVoiceOperatorIcon = function (json) {
    this.query('ShowVoiceOperatorIcon:' + JSON.stringify(json))
}

//实时视频下操作语音图标,app通知cef
//参数：{type：voice_operate_type, cid:cid}
CWorkstationCommunicationMng.NotifyVoiceOperator = function (json_str) {
    console.log('[event] CWorkstationCommunicationMng.NotifyVoiceOperator')
    console.log(json_str)

    DEBUG_TO_SERVER('CWorkstationCommunicationMng.NotifyVoiceOperator', json_str)
    var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
    var cid = json.cid
    if (cid && window.main_screen.conversation_list[cid]) {
        if (window.main_screen.conversation_list[cid]) {
            window.main_screen.conversation_list[cid].NotifyVoiceOperator(json)
        }
    } else {
        DEBUG_TO_SERVER('CWorkstationCommunicationMng.NotifyVoiceOperator Fail', json)
        console.log('NotifyVoiceOperator Fail !')
    }
}

CWorkstationCommunicationMng.clientNotice = function (json) {
    this.query('clientNotice:' + JSON.stringify(json))
}

CWorkstationCommunicationMng.QueryAppDeviceInfo = function () {
    if (!Tool.ifAppClientType(window.clientType)) {
        return
    }

    this.query('QueryAppDeviceInfo')

    //CWorkstationCommunicationMng.NotifyAppDeviceInfo(JSON.stringify({device_id:"11.22.33"})); //Temp
}

CWorkstationCommunicationMng.NotifyAppDeviceInfo = function (json_str) {
    console.log('[event] CWorkstationCommunicationMng.NotifyAppDeviceInfo')
    console.log(json_str)

    if (!Tool.ifAppClientType(window.clientType)) {
        return
    }

    DEBUG_TO_SERVER('CWorkstationCommunicationMng.NotifyAppDeviceInfo', json_str)
    var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str

    var u = navigator.userAgent
    let if_ios = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) ? true : false

    let platform = null
    if (if_ios) {
        platform = 'ios'
    } else {
        platform = json.pushPlatform||'android'
    }

    let user_id = window.vm.$store.state.user.uid
    let language = window.localStorage.getItem('lang') || 'CN'

    if (window.main_screen && platform && user_id) {
        window.main_screen.controller.emit('push_device_info', {
            device_type: window.vm.$store.state.systemConfig.clientType,
            device_platform: platform,
            device_id: json.device_id,
            user_id: user_id,
            language: language,
        })
    }
}

CWorkstationCommunicationMng.NotifyImportExamImageParameter = function (json_str) {
    console.log('[event] CWorkstationCommunicationMng.NotifyImportExamImageParameter')
    console.log(json_str)

    DEBUG_TO_SERVER('CWorkstationCommunicationMng.NotifyImportExamImageParameter', json_str)
    var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str

    window.vm.eventBus.$emit('notify_clip_parameter', json)
}

CWorkstationCommunicationMng.OpenNewWindow = function (json) {
    this.query('OpenNewWindow:' + JSON.stringify(json))
}

CWorkstationCommunicationMng.CloseNewWindow = function () {
    this.query('CloseNewWindow')
}

CWorkstationCommunicationMng.NotifyBiExit = function () {
    console.log('[event] CWorkstationCommunicationMng.NotifyBiExit')

    window.vm.eventBus.$emit('notify_bi_exit')
}

/////////////////////////////////////////// 独立工作站交互接口 begin ////////////////////////////////////////

//通知是否要显示转发对话框 {flag:0 or 1}
CWorkstationCommunicationMng.NotifyStandaloneWorkstationShareExamInfo = function (json_str) {
    var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
    console.log('[event] CWorkstationCommunicationMng.NotifyStandaloneWorkstationShareExamInfo')
    console.log(json)

    if (json.flag && window.main_screen) {
        window.main_screen.controller.emit('NotifyStandaloneWorkstationShareExamInfo', json)
    }
}

CWorkstationCommunicationMng.ClearStandaloneWorkstationShareExamInfo = function () {
    if (!Tool.ifAppClientType(window.clientType)) {
        return
    }

    this.query('ClearStandaloneWorkstationShareExamInfo')
}

CWorkstationCommunicationMng.QueryStandaloneWorkstationShareExamInfo = function () {
    if (!Tool.ifAppClientType(window.clientType)) {
        return
    }

    this.query('QueryStandaloneWorkstationShareExamInfo')

    //CWorkstationCommunicationMng.NotifyStandaloneWorkstationShareExamInfo(JSON.stringify({flag:1})); //temp test
}

CWorkstationCommunicationMng.SendStandaloneWorkstationShareExamInfo = function (cid) {
    let serverInfo = window.vm.$store.state.systemConfig.serverInfo
    if (serverInfo) {
        var json =  Tool.generateConsultationKeyForPcClient()
        json.SessionId = parseInt(cid)
        this.query('SendStandaloneWorkstationShareExamInfo:' + JSON.stringify(json))

        CWorkstationCommunicationMng.ClearStandaloneWorkstationShareExamInfo() //清除待发送标记
    } else {
        console.log('[error] CWorkstationCommunicationMng.SendStandaloneWorkstationShareExamInfo')
    }
}
/////////////////////////////////////////// 独立工作站交互接口 end ////////////////////////////////////////

// 写剪切板
CWorkstationCommunicationMng.setClipboard = function (json) {
    this.query('setClipboard:' + JSON.stringify(json))
}
// 获取剪切板
CWorkstationCommunicationMng.getClipboard = function () {
    this.query('getClipboard')
}
//剪切板读取成功回调
CWorkstationCommunicationMng.NotifyClipboard = function (json_str) {
    var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
    console.error('NotifyClipboard',json)
    let data = null
    if (Tool.isBase64(json.str)) {
        data = Base64.decode(json.str)
    } else {
        data = json.str
    }
    requestManager.handleResponse('NotifyClipboard', data)
}
//查询Licence信息
CWorkstationCommunicationMng.getLicenceInfo = function () {
    if (!Tool.ifAppWorkstationClientType(window.clientType)) {
        return
    }

    this.query('GetLicenceInfo')
}
//通知Licence信息
CWorkstationCommunicationMng.NotifyLicenceInfo = function (json_str) {
    if (!Tool.ifAppWorkstationClientType(window.clientType)) {
        return
    }

    var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
    window.license_info = json
}
//查询Licence信息
CWorkstationCommunicationMng.importLicence = function () {
    if (!Tool.ifAppWorkstationClientType(window.clientType)) {
        return
    }

    this.query('ImportLicence')
}
//是否停止自身的摄像头推流，但是不关闭直播 {pasue : 1}
CWorkstationCommunicationMng.PauseCameraStreaming = function (json) {
    this.query('PauseCameraStreaming:' + JSON.stringify(json))
}
//直接关闭APP
CWorkstationCommunicationMng.destroyApp = function () {
    this.query('destroyApp')
}
//询问APP获取摄像头信息
CWorkstationCommunicationMng.GetCameraDevice = function () {
    this.query('GetCameraDevice')
}
//APP通知前端获取摄像头信息 //PC
CWorkstationCommunicationMng.NotifyGetCameraDevice = function (json) {
    console.log('NotifyGetCameraDevice',json)
    requestManager.handleResponse('getCameraDevice', json)
}
//通知APP设置播放的摄像头 //PC
CWorkstationCommunicationMng.SetCameraDevice = function (json) {
    this.query('SetCameraDevice:' + JSON.stringify(json))
}
//请求app打开前台保活服务: From:mobile
CWorkstationCommunicationMng.startForegroundService = function(json){
    console.log('startForegroundService')
    this.query("startForegroundService:" + JSON.stringify(json));
}
//请求app关闭前台保活服务: From:mobile
CWorkstationCommunicationMng.stopForegroundService = function(json){
    console.log('stopForegroundService')
    this.query("stopForegroundService:" + JSON.stringify(json));
}
//客户端通知分辨率发生变化: From:PC
CWorkstationCommunicationMng.NotifyScreenChanged = function(json){
    DEBUG_TO_SERVER('CWorkstationCommunicationMng.NotifyScreenChanged', json)
    requestManager.handleResponse('NotifyScreenChanged',json)
}
//尝试调用OTA更新: From:mobile
CWorkstationCommunicationMng.CheckULinkerOSVersionInfo = function(){
    this.query("CheckULinkerOSVersionInfo:" + JSON.stringify({}));
}
//js-->app 打开APP系统设置
CWorkstationCommunicationMng.LinkToSystemNetworkSetting= function(){
    this.query("LinkToSystemNetworkSetting:" + JSON.stringify({}));
}
//js-->app 打开APP流量设置
CWorkstationCommunicationMng.linkToSystemDataTrafficSetting= function(){
    this.query("linkToSystemDataTrafficSetting:" + JSON.stringify({}));
}
// js-->app  打开APP内置浏览器
CWorkstationCommunicationMng.OpenNativeBrowser= function(){
    this.query("OpenNativeBrowser:" + JSON.stringify({}));
}
/////////////////////////////////////////// 血站-专家 文件交互 ////////////////////////////////////////
// APP-->JS  APP通知Js请求上传文件
CWorkstationCommunicationMng.NotifySendFileToConversation= function(json){
    console.log('NotifySendFileToConversation', json)
    requestManager.handleResponse('DealNotifySendFileToConversation', json)
}
// JS-->APP JS通知APP上传文件
CWorkstationCommunicationMng.SendFileToOSS = function (json) {
    // console.log('SendFileToOSS', json)
    this.query('SendFileToOSS:' + JSON.stringify(json))
}
//APP-->JS app通知Js更新上传文件进度
CWorkstationCommunicationMng.NotifySendFileProgress = function (json) {
    console.log('NotifySendFileProgress', json)
    requestManager.handleResponse('UpdateSendFileToConversation', json)
}
//通知APP更改窗口大小
CWorkstationCommunicationMng.ResizeBrowser = function (json) {
    this.query('ResizeBrowser:' + JSON.stringify(json))
}
//通知播放器关闭
CWorkstationCommunicationMng.NotifyPlayerClose = function (json) {
    console.log('endVideoCommit', json)
    requestManager.handleResponse('endVideoCommit', json)
    requestManager.handleResponse('NotifyPlayerClose', json)
}
//通知APP更改语言
CWorkstationCommunicationMng.SetLanguage = function (json) {
    this.query('SetLanguage:' + JSON.stringify(json))
}
// js-->app  关闭所有播放器
CWorkstationCommunicationMng.ResetAllMedia= function(){
    this.query("ResetAllMedia:" + JSON.stringify({}));
}
CommonBridge(CWorkstationCommunicationMng)
CefQueryBridge(CWorkstationCommunicationMng)
CLiveConferenceBridge(CWorkstationCommunicationMng)
CMonitorWallPlayBridge(CWorkstationCommunicationMng)
CMonitorWallPushBridge(CWorkstationCommunicationMng)
CWhiteBoardBridge(CWorkstationCommunicationMng)
CMobileBridge(CWorkstationCommunicationMng)
CReverseControlBridge(CWorkstationCommunicationMng)
CTEAirBridge(CWorkstationCommunicationMng)
export default CWorkstationCommunicationMng

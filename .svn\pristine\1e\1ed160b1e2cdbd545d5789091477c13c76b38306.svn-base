<template>
	<el-dialog
      class="task_mng_page"
      :title="lang.task_manager.title"
      :visible="true"
      :close-on-click-modal="false"
      width="90%"
      :modal="false"
      :before-close="back">
        <div class="container" v-loading="loading">
            <el-tabs v-model="activeName">
                <el-tab-pane :label="lang.action_clip_text" name="media_transfer">
                    <div class="data_panel">
                        <table>
                            <thead>
                                <tr>
                                    <!--th>{{lang.task_manager.media_transfer.task_id}}</th-->
                                    <th>{{lang.task_manager.media_transfer.image_source}}</th>
                                    <th>{{lang.task_manager.media_transfer.image_name}}</th>
                                    <th>{{lang.task_manager.media_transfer.status}}</th>
                                    <th>{{lang.task_manager.media_transfer.progress}}</th>
                                    <th>{{lang.scan_room_sort_by_creation_ts}}</th>
                                    <th>{{lang.operation}}</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="(item,index) of task_list" :key="index">
                                    <!--td>{{item.task_id}}</td-->
                                    <th>{{item.image_source || ""}}</th>
                                    <td>{{item.image_name || ""}}</td>
                                    <td>{{statusText(item.status)}}</td>
                                    <td>{{item.progress || 0}}%</td>
                                    <td>{{formatTime(item.creation_ts)}}</td>
                                    <td>
                                        <el-button :disabled="!ifSuccess(item.status)" @click="reviewMediaTransfer(item)" type="primary" size="mini">{{lang.view_btn}}</el-button>
                                        <el-button :disabled="!ifSuccess(item.status) || !ifImport(item)" @click="importMediaTransfer(item)" type="primary" size="mini">{{lang.task_manager.media_transfer.operation_import}}</el-button>
                                        <el-button :disabled="!ifSuccess(item.status)" @click="sendtoMediaTransfer(item)" type="primary" size="mini">{{lang.transmit_title}}</el-button>
                                        <el-button :disabled="!ifSuccess(item.status)" @click="downloadMediaTransfer(item)" type="primary" size="mini">{{lang.download_title}}</el-button>
                                        <el-button @click="deleteMediaTransfer(item)" type="primary" size="mini">{{lang.action_delete_text}}</el-button>
                                    </td>
                                </tr>
                            </tbody>
                       </table>
                   </div>
                </el-tab-pane>
            </el-tabs>
            <el-dialog
                class="download_image_dialog"
                :title="lang.download_title"
                :visible="isShowDownloadDialog"
                :close-on-click-modal="false"
                width="40%"
                :append-to-body="true"
                :modal="false"
                :before-close="closeDownloadDialog">
                <div class="container clearfix">
                    <div class="item">
                        <div class="title">{{lang.download_name}}</div>
                        <div class="form_item">
                            <el-input v-model="downloadFileName"></el-input>
                        </div>
                    </div>
                    <div class="item">
                        <div class="title">{{lang.download_directory}}</div>
                        <div class="form_item">
                            <el-input v-model="downloadDirectory" :disabled="true"></el-input>
                        </div>
                        <el-button @click="openChooseDirectory" class="choose_directory" type="success" size="small">{{lang.download_choose_btn}}</el-button>
                    </div>
                    <div class="item fr">
                        <el-button @click="downloadSubmit" type="primary" size="small">{{lang.download_title}}</el-button>
                    </div>
                </div>
            </el-dialog>
        </div>
        <router-view></router-view>
    </el-dialog>
</template>
<script>
import base from '../lib/base'
import {formatString} from '../lib/common_base'
import Tool from '@/common/tool'
export default {
    mixins: [base],
    name: 'taskManager',
    components: {},
    data(){
        return {
            activeName:"media_transfer",
            task_list:[],
            loading:false,

            isShowDownloadDialog:false,
            downloadFileName:'',
            downloadDirectory:'',
        }
    },
    computed:{

    },
    mounted(){
        this.$nextTick(()=>{
            let that = this;

            that.task_list = this.$store.state.taskList.media_transfer_tasks;
            that.queryTasks();
        });
    },
    methods:{
        statusText(status) {
            return this.lang.task_manager.media_transfer.status_list[status] || "";
        },
        ifSuccess(status) {
            return 3 == status ? true :false;
        },
        ifImport(item) {
            return item.original_sender_id == this.user.id;
        },
        queryTasks () {
            let that = this;

            let controller = window.main_screen.controller;
            if (controller) {
                that.loading = true;
                let timer = setTimeout(function () {
                    that.loading = false;
                    that.$message.error(that.lang.task_manager.media_transfer.error.query_task_error);
                },20000);

                controller.emit("query_media_transfer_tasks", {}, function (err, result) {
                    that.loading = false;
                    clearTimeout(timer);

                    if (err) {
                        that.$message.error(that.lang.task_manager.media_transfer.error.query_task_error);
                    } else {
                        that.$store.commit('taskList/initMediaTransferTasks',result.list);
                    }
                });
            } else {
                that.$message.error(that.lang.task_manager.media_transfer.error.query_task_error);
            }

        },
        reviewMediaTransfer(item) {
            console.log(item)
            var that = this;
            var image_copy=Object.assign({}, item.image);
            image_copy.file_id = 1;
            image_copy.url_local = image_copy.url;
            image_copy.loaded = true;

            that.$store.commit('gallery/setGallery',{
                list:[image_copy],
                openFile:image_copy,
            })
            that.$nextTick(()=>{
                that.$router.push(that.$route.fullPath+'/gallery')
            })
        },
        importMediaTransfer(item) {
            window.current_media_transfer_task = item;
            this.$router.push(this.$route.fullPath+'/exam_manager');
        },
        sendtoMediaTransfer(item) {
            var that = this;
            this.$root.eventBus.$emit('openTransmit',{
                callback:function (data) {
                    that.startSendtoMediaTransfer(item, data);
                }
            })
        },
        downloadMediaTransfer(item) {
            if (!this.globalParams.isCef) {
                this.$message.error(this.lang.use_app_tip);
                return;
            }

            window.current_media_transfer_task = item;
            this.isShowDownloadDialog = true;
            this.downloadFileName = item.image.image_name;
        },
        deleteMediaTransfer(item) {
            let that = this;

            let controller = window.main_screen.controller;
            if (controller) {
                that.loading = true;
                let timer = setTimeout(function () {
                    that.loading = false;
                    that.$message.error(that.lang.task_manager.media_transfer.error.delete_task_error);
                },20000);
                controller.emit("delete_media_transfer_tasks", {task_id:item.task_id}, function (err, result) {
                    that.loading = false;
                    clearTimeout(timer);

                    if (err) {
                        that.$message.error(that.lang.task_manager.media_transfer.error.delete_task_error);
                    } else {
                    }
                });
            } else {
                that.$message.error(that.lang.task_manager.media_transfer.error.delete_task_error);
            }
        },
        startSendtoMediaTransfer(task, target) {
            var that = this;
            let controller = window.main_screen.controller;
            if (controller) {
                that.loading = true;
                let timer = setTimeout(function () {
                    that.loading = false;
                    that.$message.error(that.lang.exam_manager.error.sendto_error)
                },20000);



                var option = {
                    task_id: task.task_id,

                    target: {}
                };
                if (target.cid) {
                    option.target.type = 2,
                    option.target.cid = target.cid;
                } else if (target.id) {
                    option.target.type = 1,
                    option.target.fid = target.id;
                } else {
                    that.$message.error(that.lang.exam_manager.error.sendto_target_invalid);
                    return;
                }

                controller.emit("sendto_media_transfer_tasks", {list:[option]}, function (err, result) {
                    that.loading = false;
                    clearTimeout(timer);

                    if (err) {
                        that.$message.error(that.lang.exam_manager.error.sendto_error);
                    } else {
                        let tip = formatString(that.lang.exam_manager.sendto_success_tip, {1:target.subject})
                        that.$message.success(tip);
                    }
                });
            } else {
                that.$message.error(that.lang.exam_manager.error.sendto_error);
            }
        },
        closeDownloadDialog() {
            this.isShowDownloadDialog = false;
        },
        openChooseDirectory(){
            Tool.createCWorkstationCommunicationMng({
                name: "openDirectory",
                emitName: "NotifyOpenDirectory",
                params: {},
                timeout: null,
            }).then((res) => {
                this.downloadDirectory = res
            })
        },
        downloadSubmit(){
            if(this.downloadFileName==""){
                this.$message.error(this.lang.download_image_name);
                return;
            }
            if (this.downloadFileName.indexOf(" ") >=0) {
                this.$message.error(this.lang.download_image_space);
                return;
            }
            if (this.downloadDirectory=='') {
                this.$message.error(this.lang.download_directory_empty);
                return;
            }

            let url = window.current_media_transfer_task.image.url
            let src = url.replace(window.current_media_transfer_task.image.poster, "");
            let dest=this.downloadDirectory + '/' + this.downloadFileName;
            let image_type = window.current_media_transfer_task.image.msg_type;
            var json_img_url = {src: src, dest: dest, image_type: image_type};
            var imgUrlList = [];
            imgUrlList.push(json_img_url);
            var json = {
                exportLocalAddr: this.downloadDirectory,
                imgUrlList: imgUrlList
            }
            window.CWorkstationCommunicationMng.exportImage(json,()=>{
                this.isShowDownloadDialog=false;
            });
        },
    }
}
</script>
<style lang="scss">
.task_mng_page{
    &.el-dialog__wrapper{
        .el-dialog{
            height:80% !important;
            margin-top:10vh !important;
            .container{
                height:98%;
                display: flex;
                flex-direction: column;

                .el-tabs{
                    height:100%;
                    .el-tabs__content{
                        height:calc(100% - 55px);
                        .el-tab-pane{
                            height:100%;
                            display: flex;
                            flex-direction: column;
                        }
                    }
                }

                .data_panel{
                    flex:1;
                    overflow:auto;

                    th,td{
                        border:1px solid #bbb;
                        font-size: 14px;
                        padding: 6px 6px;
                        text-align: left;
                    }
                    table{
                        color:#333;
                        border:1px solid #bbb;
                        width:100%;
                        border-collapse: collapse;
                        margin-bottom:10px;
                    }
                }
            }
        }
    }
    .el-input,.el-select,.el-date-editor{
        height: 100%;
        font-size: 16px;
        .el-input__inner{
            height: 100%;

        }
        .el-input__icon,.el-range-separator{
            line-height:28px;
        }
    }
    .el-range-editor{
        padding:0 10px;
        width: 320px !important;
    }
}

</style>

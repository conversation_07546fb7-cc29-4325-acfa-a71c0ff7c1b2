<template>
    <div class="live_room_container" v-show="showLiveRoom">
        <div v-if="isConferenceJoining" class="full_loading_spinner starting_rt_video_cover van_loading_spinner">
            <van-loading color="#00c59d" />
            <span class="tip">{{ lang.starting_rt_video }}</span>
        </div>
        <div v-if="isReconnectChannel" class="full_loading_spinner starting_rt_video_cover van_loading_spinner">
            <van-loading color="#00c59d" />
            <span class="tip">{{ lang.live_conference_reconnecting }}</span>
        </div>
        <LiveOperation
            :channelId="channelId"
            :liveRoom="liveRoom"
        ></LiveOperation>
        <ManageVideoMember
            v-if="showManageVideoMember"
            :channelId="channelId"
            :liveRoom="liveRoom"
        > </ManageVideoMember>
    </div>
</template>
<script>
import base from "../../lib/base"
import CLiveRoom from "@/common/CLiveConferencePUMCH/CLiveRoomNative";
import Tool from "@/common/tool";
import ManageVideoMember from "./manageVideoMember.vue";
import LiveOperation from "./liveOperation";
import { Loading,Toast } from "vant";
import { cloneDeep } from "lodash";
export default {
    mixins: [base],
    props: {
        groupTitle: {
            type: String,
            default: "",
        },
    },
    name: "LiveRoomComponent",
    components: {
        ManageVideoMember,
        LiveOperation,
        VanLoading: Loading,
    },
    data() {
        return {
            event: {},
            liveRoom: {},
            showLiveRoom: false,
            isConferenceJoining: false,
            isReconnectChannel: false,
            from: "",
            channelId: 0,
            appId: "",
            mainInfo:{
                token:'',
                uid:0
            },
            auxInfo:{
                token:'',
                uid:0
            },
            isSender:1,
            showManageVideoMember:true,
            socketAddress:''
        };
    },
    computed: {
        isTEAir() {
            return this.$store.state.device.isTEAir;
        },
    },
    created() {
        window.livingStatus = 0
    },
    mounted() {},
    methods: {
        async initLiveRoomObj(channelId) {
            if (window.CLiveRoom) {
                if (!window.CLiveRoom[channelId]) {
                    window.CLiveRoom[channelId] = new CLiveRoom({
                        channelId,
                    });
                }
            } else {
                window.CLiveRoom = {};
                window.CLiveRoom[channelId] = new CLiveRoom({
                    channelId,
                });
            }
            this.liveRoom = window.CLiveRoom[channelId];
            console.error(this.liveRoom)
            // 监听 liveRoom.data 的变化
            this.observeDataChanges();
            this.liveRoom.event.off("HandleNotifyLeaveChannelAux");
            this.liveRoom.event.on("HandleNotifyLeaveChannelAux", this.HandleNotifyLeaveChannelAux);

            this.liveRoom.event.off("HandleNotifyJoinChannelAux");
            this.liveRoom.event.on("HandleNotifyJoinChannelAux", this.HandleNotifyJoinChannelAux);

            this.liveRoom.event.off("HandleDisconnectAux");
            this.liveRoom.event.on("HandleDisconnectAux", this.HandleDisconnectAux);

            this.liveRoom.event.off("clickLeaveChannel");
            this.liveRoom.event.on("clickLeaveChannel", this.clickLeaveChannel);
            this.$root.eventBus.$off("startJoinRoom").$on("startJoinRoom", this.startJoinRoom);
        },
        startJoinRoom: Tool.debounce(
            async function (
                {
                    mainInfo={
                        token:'',
                        uid:0
                    },
                    auxInfo={
                        token:'',
                        uid:0
                    },
                    channelId=0,
                    appId="",
                    socketAddress=''
                },
                callback
            ) {
                if (channelId === 0 || window.livingStatus === 1 || window.livingStatus === 2) {
                    return;
                }
                console.log('startJoinRoom',mainInfo,auxInfo,channelId,appId)
                this.showLiveRoom = true
                this.channelId = channelId;
                this.appId = appId;
                this.mainInfo = mainInfo;
                this.auxInfo = auxInfo;
                this.isConferenceJoining = true;
                this.socketAddress = socketAddress;
                window.livingStatus = 1
                try {
                    await this.initLiveRoomObj(channelId);
                    await this.joinChannel()
                    this.isConferenceJoining = false;
                    this.showManageVideoMember = true;
                    callback && callback(true);
                    window.livingStatus = 2
                } catch (error) {
                    window.livingStatus = 0
                    console.error(error);
                    this.isConferenceJoining = false;
                    callback && callback(false, error);
                }
            },
            1000,
            true
        ),
        clickLeaveChannel() {
            this.liveRoom.LeaveChannelAux("normal")
            // Tool.openMobileDialog({
            //     message: '是否确认关闭直播',
            //     showRejectButton: true,
            //     confirmButtonText: this.lang.confirm_btn,
            //     rejectButtonText: this.lang.cancel_btn,
            //     confirm: () => {
            //         this.liveRoom.LeaveChannelAux("normal");
            //     },
            //     reject: () => {

            //     },
            //     close: () => {},
            // });
        },
        HandleNotifyJoinChannelAux() {
        },
        HandleNotifyLeaveChannelAux({}) {
            Tool.closeAllDialog();
            this.isConferenceJoining = false;
            this.showLiveRoom = false
            this.$emit("leaveChannelAux");
            setTimeout(() => {
                this.unbindDataChanges();
            }, 0);
            window.livingStatus = 0
        },
        HandleDisconnectAux() {
            this.isConferenceJoining = false;
        },
        observeDataChanges() {
            // 防抖函数用于批量替换 roomUserMap
            const debouncedUpdateRoomUserMap = Tool.debounce(() => {
                // 防抖函数内部执行深拷贝，确保拷贝最新的 roomUserMap
                // const newRoomUserMap = cloneDeep(this.liveRoom.data.roomUserMap);
                this.$store.commit("livingData/setRoomUserMapData", {
                    channelId: this.channelId,
                    data: { roomUserMap: this.liveRoom.data.roomUserMap },
                });
            }, 50); // 设置防抖时间为 50ms，可以根据需求调整
            // 代理 liveRoom.data 对象
            const originData = cloneDeep(this.liveRoom.data);
            this.liveRoom.data = Tool.deepReactive(originData, (target, key, value, action, path) => {
                // 同步数据到 Store
                if (path.includes("roomUserMap")) {
                    if (action === "set" || action === "delete") {
                        debouncedUpdateRoomUserMap();
                    }
                } else {
                    if (action === "set") {
                        this.$store.commit("livingData/setLiveRoomData", { channelId: this.channelId, data: { path, value } });
                    } else if (action === "delete") {
                        // 处理删除操作
                        this.$store.commit("livingData/deleteLiveRoomData", { channelId: this.channelId, data: { path } });
                    }
                }
                if (this.liveRoom.data) {
                    if (this.liveRoom.data.joinedAux && this.liveRoom.data.losing_connect_server) {
                        this.isReconnectChannel = true;
                    } else {
                        this.isReconnectChannel = false;
                    }
                    if (this.liveRoom.data.joinedAux && this.liveRoom.data.losing_connect_server) {
                        this.isReconnectChannel = true;
                    } else {
                        this.isReconnectChannel = false;
                    }
                }
            });
            // 初始同步一次数据
            this.$store.commit("livingData/replaceLiveRoomData", {
                channelId: this.channelId,
                data: cloneDeep(this.liveRoom.data),
            });
        },
        unbindDataChanges() {
            this.liveRoom.data = cloneDeep(this.liveRoom.data);
        },
        joinChannel(){
            return new Promise(async(resolve,reject)=>{
                try {
                    if (!this.globalParams.functionsStatus.live) {
                        console.log('区域配置关闭了直播功能')
                        return reject(false)
                    }
                    if(!this.liveRoom){
                        console.error('liveRoom不存在')
                        return reject('初始化失败，请稍后尝试')
                    }

                    try {
                        Tool.initNativeAgoraSdk(this.appId).then(()=>{
                            if(this.mainInfo.uid&&this.auxInfo.uid){ //同时有主辅流，先让辅流进
                                this.liveRoom.JoinChannelAux({ //辅流先进房间
                                    channelId:this.channelId,//房间名
                                    uid:this.auxInfo.uid,
                                    isSender:this.isSender,
                                    token:this.auxInfo.token,
                                    socketAddress:this.socketAddress
                                }).then((joinAuxRes)=>{
                                    if(joinAuxRes&&this.mainInfo.uid){
                                        this.liveRoom.JoinChannelMain({// 后主流加入
                                            channelId:this.channelId,//房间名
                                            uid:this.mainInfo.uid,
                                            token:this.mainInfo.token,
                                        }).then(()=>{
                                            return resolve(true)
                                        }).catch((error)=>{
                                            return reject(error)
                                        })
                                    }
                                }).catch((error)=>{
                                    return reject(error)
                                })


                            }else{
                                if(this.auxInfo.uid){
                                    this.liveRoom.JoinChannelAux({
                                        channelId:this.channelId,//房间名
                                        uid:this.auxInfo.uid,
                                        isSender:this.isSender,
                                        token:this.auxInfo.token,
                                        socketAddress:this.socketAddress
                                    }).then(()=>{
                                        return resolve(true)
                                    }).catch((error)=>{
                                        return reject(error)
                                    })

                                }
                                if(this.mainInfo.uid){
                                    this.liveRoom.JoinChannelMain({
                                        channelId:this.channelId,//房间名
                                        uid:this.mainInfo.uid,
                                        token:this.mainInfo.token,
                                    }).then(()=>{
                                        return resolve(true)
                                    }).catch((error)=>{
                                        return reject(error)
                                    })
                                }

                            }
                        }).catch((error)=>{
                            console.error(error)
                            return reject(error)
                        })
                    } catch(error) {
                        return reject(error)

                    }
                } catch (error) {
                    return reject(error)
                }


            })


        }
    },
};
</script>
<style lang="scss" scoped>
.live_room_container{
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 20;
    display: flex;
    flex-direction: column;
}
.van_loading_spinner {
    display: flex;
    justify-content: center;
    align-items: center;

    .van-loading__spinner {
        width: 2.8rem;
        height: 2.8rem;
    }
}
.starting_rt_video_cover {
    background-color: rgba(128, 128, 128, 0.8);
    z-index: 999;
    .tip {
        margin-top: 3rem;
        width: 80%;
        text-align: center;
    }
}
</style>

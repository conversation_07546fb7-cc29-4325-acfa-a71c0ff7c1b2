<template>
  <div class="patient_info_page">
      <div class="exam_list_wrap">
        <div class="exam_item" v-for="(exam,i) of examList" :key="i">
          <div class="upload_info clearfix" @click="toggleExam(exam,i)">
            <div class="patient_avatar fl">
                <mr-avatar :url="getLocalAvatar(exam.sender_nickname[0])" :key="exam.sender_nickname[0].avatar"></mr-avatar>
            </div>
            <div class="upload_info_right fl">
              <div class="patient_nickname">
                <p>
                  <span v-for="(sender,senderIndex) of exam.sender_nickname" :key="senderIndex">
                    {{sender.hospital_name?sender.hospital_name+'-'+sender.nickname:sender.nickname}}
                  </span>
              </p>
              </div>
              <p class="upload_datetime">{{lang.submission_time}}：{{formatTime(exam.upload_ts)}}</p>
            </div>
            <i v-show="!exam.openState" class="icon iconfont icondown fr"></i>
            <i v-show="exam.openState" class="icon iconfont iconup fr"></i>
          </div>
          <div class="patient_info_wrap">
            <div class="patient_info">
              <p>{{lang.patient_name}}：{{exam.patientInfo.patient_name}}</p>
              <p>{{lang.exam_type}}：{{lang.exam_types[exam.exam_type]}}</p>
              <p>{{lang.patient_age}}：{{exam.patientInfo.patient_age}}</p>
            </div>
            <div class="patient_info">
              <p>{{lang.patient_sex}}：{{lang.sex[exam.patient_sex]}}</p>
              <p>{{lang.exam_time}}：{{formatTime(exam.patient_series_datetime)}}</p>
              <p>{{lang.case_status}}：{{lang.exam_status[exam.statusInfo.status]}}</p>
            </div>
            <div class="patient_info">
              <p>{{lang.group_by}}：{{getExamGroupSubject(exam)}}</p>
              <p v-if="user.role!=role.review">{{lang.numberOfImages}}：{{exam.count}}</p>
              <p></p>
            </div>
          </div>
          <div class="patient_examlist clearfix" v-show="exam.openState">
            <div v-loading="!exam.initImageList" class="loading" v-show="!exam.initImageList"></div>
            <div class="gallery-image-list" v-if="exam.image_list">
              <div v-for="(img,imgIndex) of exam.image_list" class="gallery-image" @click="openGallery(img,imgIndex,exam)" :key="imgIndex">
                  <div>
                      <p v-if="img.protocol_view_name" class="view_name">{{img.protocol_view_name}}</p>
                      <img v-if="img.url" :src="img.error_image||img.url"  class="file_image" @error="setErrorImage(img)">
                      <img v-else src="static/resource_pc/images/default.png" class="file_image">
                      <i v-if="img.msg_type==systemConfig.msg_type.Cine||img.msg_type==systemConfig.msg_type.Video" class="icon iconfont iconvideo_fill_light"></i>
                  </div>
              </div>
            </div>
            <el-button type="primary" @click="openGallery(exam.image_list[0],0,exam)">{{getBtnText(user.role, exam)}}</el-button>
          </div>
        </div>
      </div>
  </div>
</template>

<script>
import base from '../../../lib/base'
import examServer from '../../../service/multiCenterService.js'
import {getExamGroupSubject,deDuplicatingImg,getLocalAvatar} from '../../../lib/common_base'
export default {
    name: 'PatientInfo',
    mixins:[base],
    components: {},
    props:{
        examList:{
            type:Array,
            default() {
                return []
            }
        },
    },
    data() {
        return {
            getLocalAvatar,
        }
    },
    computed: {
        currentMulticenter(){
            return this.$store.state.multicenter.currentMulticenter||{}
        },
        currentConfig(){
            return this.$store.state.multicenter.currentConfig;
        },
        exam_status(){
            return this.currentConfig.exam_status
        },
        role(){
            return this.currentConfig.role
        },
    },
    mounted(){
    },
    created() {
    },
    methods: {
        toggleExam(exam, index){
            if(!exam.initImageList){
                examServer.getExamDetail({
                    mcID:this.currentMulticenter.id,
                    protocolGUID:exam.protocol_guid,
                    statusID:exam.statusInfo.id,
                }).then(res => {
                    if(res.data.error_code==0){
                        exam.caseData=res.data.data.caseData;
                        exam.image_list=deDuplicatingImg(res.data.data.image_list);
                        exam.process=res.data.data.process;
                        exam.topic=res.data.data.topic;
                    }
                    exam.initImageList=true;
                })
            }
            exam.openState=!exam.openState
        },
        openGallery(img,index,exam){
            this.$emit('openGallery',img,index,exam)
        },
        getExamGroupSubject(exam) {
            return getExamGroupSubject(exam)
        },
        getBtnText(role, exam) {
            if(exam.status === this.exam_status.reject) {
                return this.lang.view_btn
            }
            switch(role) {
            case 1: //普通管理员
            case 5: //超管
                return this.lang.view_btn
            case 2: // 分配者
                if(exam.status === this.exam_status.submited) {
                    return this.lang.assign_btn
                }else{
                    return this.lang.view_btn
                }
            default:
                return this.lang.view_btn
            }

        }
    },
}
</script>
<style lang="scss">
.patient_info_page{
  .exam_list_wrap{
    padding:0 5px;
    .exam_item{
      position:relative;
      padding: 10px 20px;
      margin-bottom:10px;
      background-color: #fff;
      border-radius: 0.3rem;
      box-shadow: 0.1rem 0.1rem 0.2rem rgba(140,152,155,0.7);
      .upload_info{
        font-size:.8rem;
        color:#333;
        cursor:pointer;
        border-bottom:1px solid #dbdbdb;
        .patient_avatar{
          float: left;
          width:40px;
          height:40px;
          margin-right:10px;
          margin-bottom:10px;
          img{
            border-radius: 100%;
            height:100%;
            width:100%;
          }
        }
        .upload_info_right{
          margin-left:10px;
          .patient_nickname,.upload_datetime{
            padding:3px 0;
          }
          .patient_nickname {
            p>span{
              vertical-align: middle;
            }
            .same_annotate,.case_integrity,.no_same_annotate,.no_case_integrity {
              display: inline-block;
              padding: 5px 10px;
              border-radius: 5px;
              background: #3ecb3e;
              font-size: 12px;
              color: #fff;
              margin-left: 8px;
            }
            .no_same_annotate,.no_case_integrity {
              background-color:#ee5f5f;
            }
          }

          .upload_datetime{
            font-size: 0.4rem;
            color: #999;
          }
        }
      }
      .icondown,.iconup{
          position: absolute;
          right: 10px;
          top: 22px;
          line-height:1;
          font-size: 26px;
      }
      .patient_info_wrap{
        display: flex;
        flex-direction: column;
        margin-bottom:10px;
        font-size: 0.7rem;
        line-height:1.2rem;
        color: rgb(101,109,112);
        .patient_info{
          display: flex;
          margin-top:10px;
          p{
            flex:1;
          }
          .exam_time{
            flex:2;
          }
        }
      }
      .patient_examlist{
        .loading{
          height:40px;
        }
        .gallery-image-list{
            padding-top: 10px;
            display: flex;
            flex-wrap:wrap;
            .gallery-image{
                margin-right: 6px;
                margin-bottom: 6px;
                width: 140px;
                height: 110px;
                background-color: #000;
                position: relative;
                cursor: pointer;
                display: flex;
                align-items: center;
                border-radius:0.2rem;
                .view_name{
                    color: #fff;
                    position: absolute;
                    top: 6px;
                    z-index: 9;
                    left: 2px;
                    transform: none;
                    font-size: 14px;
                    white-space: normal;
                    text-align: left;
                }
                img{
                    max-width: 100%;
                    max-height: 100%;
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                }
                .icon{
                    position: absolute;
                    bottom: 0;
                    color: #fff;
                    font-size: 24px;
                    line-height: 1;
                    left: 0;
                }
            }
        }
        .patient_exam_item{
          float: left;
          margin-right: 6px;
          margin-bottom: 6px;
          width: 140px;
          height: 110px;
          background-color: #000;
          position: relative;
          cursor: pointer;
          border-radius: 0.2rem;
          img{
            max-width: 100%;
            max-height: 100%;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
          }
        }
      }
    }
    .md_gallery{
      .operation_btns{
        position: absolute;
        top: 20px;
        text-align:center;
      }

      .reject_dialog{
          display:flex;
          flex-direction:column;
          width:100%;
          .reject_btn{
              width: 80px;
              align-self: flex-end;
              margin-top: 20px;
          }
      }
    }
  }
}
</style>

<template>
<div>
    <el-dialog
      class="export_file_page"
      :title="lang.export_image"
      :visible="true"
      :close-on-click-modal="false"
      width="690px"
      :modal="false"
      :before-close="back">
        <div class="container">
            <div class="file_list clearfix">
                <el-checkbox-group v-model="checkedList">
                    <div v-for="(item,index) in fileList" class="file_item" :key="index">
                        <el-checkbox :label="index">
                            <img :src="item.url" class="file_img">
                        </el-checkbox>
                    </div>
                </el-checkbox-group>
            </div>
            <div class="btns">
                <el-button v-show="checkAll" type="primary" @click="selectNone">{{lang.cancel_select_all}}</el-button>
                <el-button v-show="!checkAll" type="primary" @click="selectAll">{{lang.select_all}}</el-button>
                <el-button type="primary" @click="exportSubmit">{{lang.export_comment}}</el-button>
            </div>
        </div>
    </el-dialog>
</div>

</template>
<script>
import base from '../lib/base'
export default {
    mixins: [base],
    name: 'ExportFilePc',
    components: {
    },
    data(){
        return {
            checkedList:[],
        }
    },
    computed:{
        conversation(){
            return this.conversationList[this.$route.params.cid]||{}
        },
        galleryObj(){
            return this.conversation.galleryObj
        },
        fileList(){
            let arr=[]
            for(let item of this.galleryObj.gallery_list){
                if (item.msg_type==this.systemConfig.msg_type.OBAI||item.msg_type==this.systemConfig.msg_type.Frame||item.msg_type==this.systemConfig.msg_type.Cine) {
                    arr.push(item)
                }
            }
            return arr
        },
        checkAll(){
            if (this.checkedList.length>0&&this.checkedList.length==this.fileList.length) {
                return true
            }else{
                return false
            }
        }
    },
    mounted(){
        this.$nextTick(()=>{
            this.selectAll();
        })
    },
    methods:{
        selectAll(){
            let index=0;
            this.checkedList=[]
            for(let item of this.fileList){
                this.checkedList.push(index);
                index++
            }
        },
        selectNone(){
            this.checkedList=[]
        },
        exportSubmit(){
            if (this.checkedList.length==0) {
                this.$message.error(this.lang.export_no_select)
                return ;
            }
            let downLoadUrlList=[]
            for(let index of this.checkedList){
                let item=this.fileList[index]
                let url=this.getDownloadUrl(item);
                if(!url){
                    console.error(url,item)
                }else{
                    let surl=window.location.origin;
                    if (url.indexOf('http')!=0) {
                        url=surl+'/'+url;
                    }
                    downLoadUrlList.push({src:url})
                }
            }
            this.$root.eventBus.$emit("createDownLoadTask",{
                downLoadUrlList:downLoadUrlList,
                needsInputFileName:false
            })
            this.back()
        }

    }
}
</script>
<style lang="scss">
.export_file_page{
    .container{
        height:100%;
        display: flex;
        flex-direction: column;
        .file_list{
            flex: 1;
            overflow: auto;
            .file_item{
                width: 120px;
                float: left;
                position: relative;
                margin: 4px;
                height:90px;
                background:#000;
                .el-checkbox{
                    width:100%;
                    height:100%;
                    .el-checkbox__input{
                        position: absolute;
                        right: 4px;
                        bottom: 4px;
                        .el-checkbox__inner:after{
                            transition:none;
                        }
                    }
                    .el-checkbox__label{
                        padding:0;
                    }
                }
                .file_img{
                    max-width:100%;
                    max-height:100%;
                    position:absolute;
                    top:50%;
                    left:50%;
                    transform:translate(-50%,-50%)
                }
            }
        }
        .btns{
            margin:10px;
            text-align:right;
        }
    }
    .process_modal{
        position:absolute;
        z-index:2;
        background:rgba(256,256,256,.7);
        width: 100%;
        top: -36px;
        left: 0;
        bottom: 0;
        padding:20px;
        .el-progress{
            top:50%;
        }
    }
}
.el-message-box__message{
    word-break:break-all;
}
</style>

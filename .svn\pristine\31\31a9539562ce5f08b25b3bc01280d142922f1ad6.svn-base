import axios from 'axios'

// const NETWORK_ERROR_MESSAGE='网络异常'
const SERVER_ERROR_MESSAGE='系统异常'
// const UNEXPETED_STATUS=-1
// const EXPETED_STATUS=1
// const UNLOGIN_STATUS=100
// const SUCCESS_STATUS=0
// const COMMON_STATUS=1000

const api=axios.create({
    headers:{
        'Content-Type':'application/json'
    },
    timeout:1000*60*5,
    transformRequest:[(data)=>{
        if(!data){
            return ;
        }
        return JSON.stringify(data.data)
    }],
    transformResponse:[(data)=>{
        if(data){
            try{
                data=JSON.parse(data)
            }catch(e){
                console.log(SERVER_ERROR_MESSAGE)
            }
        }
        return data;
    }]
})
api.interceptors.request.use(req => {
    if (localStorage.token) {
        req.headers.token =localStorage.token;
    }
    return req;
}, error => {
    return Promise.reject(error)
})
// api.interceptors.response.use(
//     (response)=>{
//         const status=typeof response.data.status!=='undefined' ? response.data.status:UNEXPETED_STATUS
//         if(typeof status !=='number' || status!==SUCCESS_STATUS){
//             if(status===UNLOGIN_STATUS){
//                 alert('登录失效')
//                 //return redirect
//             }
//             if(status > COMMON_STATUS){
//                 alert(response.data.msg||SERVER_ERROR_MESSAGE)
//             }
//             return Promise.reject(response.data)
//         }
//         return response.data||{}
//     },
//     (err)=>{
//         return Promise.reject({
//             msg:NETWORK_ERROR_MESSAGE,
//             status:UNEXPETED_STATUS
//         })
//     }
// )
export default api

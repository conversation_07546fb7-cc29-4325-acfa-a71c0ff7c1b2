<template>
    <div class="login_form_container">
        <div class="login_types">
            <div class="login_type_item" @click="changeLoginType(1)">
                <img v-if="loginType === 1 || loginType === 2" src="static/resource_activity/images/login_mobile_active.png">
                <img v-else src="static/resource_activity/images/login_mobile.png">
                <p>{{lang.verification_login}}</p>
            </div>
            <div class="login_type_item" @click="changeLoginType(3)">
                <img v-if="loginType === 3" src="static/resource_activity/images/login_account_active.png">
                <img v-else src="static/resource_activity/images/login_account.png">
                <p>{{lang.password_login}}</p>
            </div>
            <p class="qrcode_title">{{lang.qrcode_login}}</p>
            <p class="qrcode_description">{{lang.scan_login_title}}</p>
            <login-qr-code :callback="saveUserInfo"></login-qr-code>
            <p class="download_tip" @click="isShowDownload = true">{{lang.download_tip}}</p>
        </div>
        <div class="login_forms">
            <p class="welcome_title">{{lang.ecology_welcome}}</p>
            <div class="validate_forms" v-if="loginType === 1 || loginType === 2">
                <div class="validate_tabs">
                    <div class="validate_item" :class="{active:loginType===1}" @click="changeLoginType(1)">{{lang.mobile_login}}</div>
                    <div class="validate_item" :class="{active:loginType===2}" @click="changeLoginType(2)">{{lang.email_login}}</div>
                </div>
                <div v-if="loginType===1" class="mobile_login" @keyup.enter="loginOrRegister">
                    <p class="register_tip">{{lang.login_or_register_mobile}}</p>
                    <MobileInternational :type="2" :mobile.sync="mobile_number" :nationalCode.sync="international_code" ref='mobile_international'></MobileInternational>
                    <div class="verify_item">
                        <el-input v-model="verifyCode" :placeholder="lang.sms_verification_code" maxlength="6"></el-input>
                        <span v-show="counter==0" class="code_btn" @click="loginByTracelessValid" v-loading="isValidating||requesting">{{lang.forget_password_getcode}}</span>
                        <span v-show="counter!=0" class="code_btn ban">{{counter}}s</span>
                    </div>
                </div>
                <div v-else-if="loginType===2" class="email_login" @keyup.enter="loginOrRegister">
                    <p class="register_tip">{{lang.login_or_register_email}}</p>
                    <div  class="verify_item">
                        <el-input v-model="email" :placeholder="lang.register_email"></el-input>
                    </div>
                    <div class="verify_item">
                        <el-input v-model="verifyCode" :placeholder="lang.email_verification_code" maxlength="6"></el-input>
                        <span v-show="counter==0" class="code_btn" @click="loginByTracelessValid" v-loading="isValidating||requesting">{{lang.forget_password_get_email_code}}</span>
                        <span v-show="counter!=0" class="code_btn ban">{{counter}}s</span>
                    </div>
                </div>
            </div>
            <div v-else-if="loginType===3" class="account_login" @keyup.enter="loginByTracelessValid">
                <div class="verify_item">
                    <el-input v-model="login_form.name" :placeholder="lang.register_account_or_mobile" autocomplete="new-password"></el-input>
                </div>
                <div class="verify_item">
                    <el-input type="password" v-model="login_form.pwd" :placeholder="lang.register_password" autocomplete="new-password"></el-input>
                </div>
            </div>
            <div class="login_form_checkbox">
                <div class="agree_privacy_protocol">
                    <el-checkbox v-model="isAgreePrivacyPolicy"></el-checkbox>
                    <span class="privacy_text">
                        <span class="privacy_link" @click="goToPrivacyPolicy">《{{lang.ultrasync_privacy_protocol}}》</span>
                    </span>
                </div>
                <span v-if="loginType === 1 || loginType === 2" @click="loginOrRegister" class="login_btn common_btn" v-loading="logging||isAutoLogin">{{lang.login_title}}</span>
                <span v-if="loginType === 3" @click="loginByTracelessValid" class="login_btn common_btn" v-loading="isValidating||logging||isAutoLogin">{{lang.login_title}}</span>
                <div class="checkbox_row">
                    <el-checkbox class="remember" v-model="remember" @click="remember=!remember">{{lang.auto_login}}</el-checkbox>
                    <span v-if="loginType === 3" class="go_to_btn" @click="showForgetDialog">{{lang.to_forget_password}}</span>
                </div>
            </div>
        </div>
        <div v-if="isShowDownload" class="download_panel">
            <div class="back_row">
                <img  @click="isShowDownload = false" src="static/resource_activity/images/back.png">
                <p  @click="isShowDownload = false">{{lang.back_to_login}}</p>
            </div>
            <p class="download_title">{{lang.download_tip}}</p>
            <div class="download_way">
                <div class="download_way_item">
                    <p class="download_way_title">App</p>
                    <div class="download_way_logo">
                        <img :src="`static/resource_activity/images/zcm_${PROJECT_NOV}_${serverInfo.build_version}.png`" class="app_qrcode">
                    </div>
                    <!-- <p class="download_version">V{{serverInfo.installer_version}}</p> -->
                    <p class="download_way_tip">{{lang.scan_to_download}}</p>
                </div>
                <div class="download_way_item">
                    <p class="download_way_title">Windows</p>
                    <div class="download_way_logo">
                        <img class="pc_logo" src="static/resource_activity/images/logo.png">
                    </div>
                    <p class="download_version">V{{serverInfo.installer_version}}</p>
                    <a :href='serverInfo.installer_url' class="download_btn" target="_self">{{lang.download_title}}</a>
                </div>
            </div>
        </div>
        <div class="forget_password_panel" v-if="isShowForgetPassword">
            <div class="back_row">
                <img  @click="isShowForgetPassword = false" src="static/resource_activity/images/back.png">
                <p  @click="isShowForgetPassword = false">{{lang.back_to_login}}</p>
            </div>
            <p class="forget_password_title">{{lang.modify_password_text}}</p>
            <forget-password-form ></forget-password-form>
        </div>

        <two-factor-authentication
        :isShowVerify.sync="isShowVerify"
        :cellphone="cellphone"
        :email="email"
        :token="token"
        :isLoading="logging"
        :verifyCallback="loginByVerify"
        :bindingCallback="loginByBinding"
        :customClass="'login_form'"
        ref="twoFactorAuthentication"
        ></two-factor-authentication>
        <ValidateCaptcha name="LoginForm" ref='validateCaptcha'></ValidateCaptcha>
    </div>


</template>

<script>
// import base from '../lib/base'
import service from '@/common/service/service.js'
import Tool from '@/common/tool.js'
import CWorkstationCommunicationMng from '@/common/CommunicationMng/index'
// import {parseImageListToLocal,handleAfterLogin} from '../lib/common_base'
import twoFactorAuthentication from '@/components/login/twoFactorAuthentication.vue'
import MobileInternational from '@/components/login/mobileInternational.vue'
import LoginQrCode from '@/components/login/loginQrCode.vue'
import ForgetPasswordForm from '@/components/login/forgetPasswordForm.vue'
import ValidateCaptcha from '@/components/login/ValidateCaptcha.vue'
import { goPrivacyPolicy } from '../lib/common_base'
export default {
    mixins: [],
    name: 'LoginForm',
    components: {
        twoFactorAuthentication,
        MobileInternational,
        LoginQrCode,
        ForgetPasswordForm,
        ValidateCaptcha,
    },
    data(){
        return {
            login_form:{
                name:'',
                pwd:'',
                device_id:'',
                token:'',
                mobile_phone:"",
                sms_verification_code:'',
                sms_verification_code_index:'',

                verification_code:"",
                verification_code_uuid:"",
            },
            remember:false,
            logging:false,
            errorPwd:'',//用于保存错误的原密码
            isShowVerify:false,
            token:'',
            mobile_number:'',
            cellphone:'',
            email:'',
            loginType:1,//1手机登录，2邮箱登录,3账号登录,4扫码登录
            international_code:'',
            verifyCode:'',
            countTimer:null,
            counter:0,
            submitType:1,//1登录，2注册
            isShowForgetPassword:false,
            isAutoLogin:false,
            requesting:false, //发送获取验证码的请求中
            isAgreePrivacyPolicy:false,//是否同意隐私政策
            isValidating:false,
            isShowDownload: false,
            PROJECT_NOV:process.env.VUE_APP_PROJECT_NOV,
        }
    },
    computed:{
        lang(){
            return this.$store.state.language
        },
        systemConfig(){
            return this.$store.state.systemConfig
        },
        serverInfo() {
            return this.systemConfig.serverInfo
        },
        isShowSMSIdentify() {
            return this.serverInfo.enable_sms_identification;
        },
        isShowBackBtn(){
            return this.isShowForgetPassword
        }
    },
    destroyed(){
    },
    mounted(){
        this.$nextTick(()=>{
            let account=window.localStorage.getItem('account')||''
            let pwd=window.localStorage.getItem('password')
            this.login_form.name=account;
            if (pwd) {
                this.remember=true;
                this.login_form.pwd=pwd
            }
            this.$root.eventBus.$off('changeLoginType').$on('changeLoginType',this.changeLoginType)
            this.$root.eventBus.$off('loginWithToken').$on('loginWithToken',this.loginWithToken)
            setTimeout(()=>{
                //延迟自动登录，防止闪烁
                this.autoLogin();
            },300)
        })
    },
    methods:{
        loginOrRegister(afsCode){
            if (this.submitType===1) {
                //登录
                if (this.loginType===1||this.loginType===2) {
                    this.loginByCode()
                } else if (this.loginType===3) {
                    this.loginV2(afsCode);
                }
            } else if (this.submitType===2) {
                //注册
                if (this.loginType===1||this.loginType===2) {
                    this.register()
                }
            }
        },
        loginV2(afsCode){
            if (!this.validateLoginForm()) {
                return ;
            }
            this.logging = true
            this.errorPwd=this.login_form.pwd
            service.encryptPassword({
                pwd:this.login_form.pwd
            }).then((res)=>{
                if (res.data.error_code===0) {
                    const tokenParams={
                        loginName:this.login_form.name,
                        password:res.data.data.encryptStr,
                        language:window.localStorage.getItem('lang')||'CN',
                        clientType:this.systemConfig.clientType,
                        deviceId:'',
                        afsCode:afsCode
                    }
                    service.getLoginToken(tokenParams).then((res)=>{
                        this.logging=false
                        if (res.data.error_code===0) {
                            const result=res.data.data;
                            this.twoFactorLogin(result);
                        }
                    }).catch((e)=>{
                        this.$message.error(e);
                        this.logging=false
                    })
                }else{
                    this.logging=false
                }
            }).catch((e)=>{
                this.$message.error(e);
                this.logging=false
            })
        },
        twoFactorLogin(result){
            const needBind=result.cellphone==''&&result.email==''&&this.isShowSMSIdentify;
            if (result.userOutOfTrail) {
                //账号超过试用期，先去输推荐码
                const isRemember=this.remember?1:0
                // this.$router.push(`/login/referral_code?token=${result.token}&isAutoLogin=1&isRemember=${isRemember}`)
                setTimeout(()=>{
                    this.$message.error(this.lang.userOutOfTrail);
                },300)
            }else if (result.need_code_login||needBind) {
                this.verifyWithToken(result);
            }else if (result.need_pwd_login) {
                this.$message.error(this.lang.login_need_password);
                this.loginType=3;
            }else{
                this.loginWithToken(result.token);
            }
        },
        loginWithToken(token){
            this.logging=true
            service.loginByToken({
                token:token,
                deviceInfo:{
                    device_id:''
                }
            }).then((res)=>{
                if (res.data.error_code===0) {
                    const user=res.data.data;
                    // parseImageListToLocal([user],'avatar')
                    // this.setDefaultImg([user])
                    // handleAfterLogin(user,this.remember);
                    this.saveUserInfo(user,this.remember)
                }else{
                    window.localStorage.setItem('unifiedPlatformToken','')
                }
                setTimeout(()=>{
                    this.logging=false
                    this.isAutoLogin = false
                },1000)
            })
        },
        saveUserInfo(user,remember){
            user.fromLogin=true
            this.$store.commit('dynamicGlobalParams/updateToken',user.new_token)
            if (remember) {
                window.localStorage.setItem('unifiedPlatformToken',user.new_token)
            }else{
                window.localStorage.setItem('unifiedPlatformToken','')
            }
            this.$store.commit('user/updateUser', user);
            this.$router.replace("/unifiedPlatformIndex");
        },
        verifyWithToken(loginRes){
            this.token=loginRes.token;
            this.cellphone=loginRes.cellphone;
            this.email=loginRes.email;
            this.$nextTick(()=>{
                this.isShowVerify=true;
                this.$refs.twoFactorAuthentication.init();
            })
        },
        loginByVerify(verifyCode,accountType){
            this.logging=true
            service.loginByToken({
                token:this.token,
                accountType:accountType,
                code:verifyCode,
                deviceInfo:{
                    device_id:''
                }
            }).then((res)=> {
                this.logging=false
                if (res.data.error_code===0) {
                    const user=res.data.data;
                    // parseImageListToLocal([user],'avatar')
                    // this.setDefaultImg([user])
                    // handleAfterLogin(user,this.remember);
                    this.saveUserInfo(user,this.remember)
                }else{
                }
            })
        },
        showForgetDialog(){
            this.isShowForgetPassword=true;
            // this.$root.eventBus.$emit('openForgetPasswordDialog');
        },
        loginByBinding(verifyCode,accountType,account,countryCode){
            this.logging=true
            service.loginAndBindAccount({
                account:account,
                token:this.token,
                accountType:accountType,
                code:verifyCode,
                countryCode:countryCode
            }).then((res)=> {
                this.logging=false
                if (res.data.error_code===0) {
                    const user=res.data.data;
                    // parseImageListToLocal([user],'avatar')
                    // this.setDefaultImg([user])
                    // handleAfterLogin(user,this.remember);
                    this.saveUserInfo(user,this.remember);
                }else{
                }
            })
        },
        changeLoginType(loginType){
            console.log('changeLoginType',loginType)
            this.loginType=loginType;
            this.isShowForgetPassword=false;
        },
        async getVerifyCode(accountType, afsCode){
            this.requesting = true
            let verifyType='getVerityCodeToMobile';
            let checkParams={};
            let params={};
            if (accountType==='mobile') {
                verifyType='getVerityCodeToMobile';
                checkParams={
                    account:this.mobile_number,
                    accountType:'mobile',
                    countryCode:this.international_code,
                }
                params={
                    mobile:this.mobile_number,
                    afsCode:afsCode,
                    countryCode:this.international_code,
                }
            }else{
                verifyType='getVerityCodeToEmail';
                checkParams={
                    account:this.email,
                    accountType:'email'
                }
                params={
                    email:this.email,
                    afsCode:afsCode,
                }
            }
            const checkResult=await service.checkAccount(checkParams);
            if (checkResult.data.error_code===0) {
                if (checkResult.data.data.existStatus) {
                    this.submitType=1;
                    params.type='login'
                }else{
                    this.submitType=2;
                    params.type='register'
                }
            }
            service[verifyType](params).then((res)=>{
                this.requesting = false;
                if (res.data.error_code===0) {
                    this.display_count_down();
                }
            })
        },
        display_count_down(){
            if (!this.countTimer) {
                this.counter=89;
                this.countTimer = setInterval(()=>{
                    this.counter--;
                    if (this.counter == 0){
                        this.counter = 0;
                        clearInterval(this.countTimer);
                        this.countTimer = null;
                    }
                }, 1000);
            }
        },
        loginByCode(){
            if(!this.isAgreePrivacyPolicy){
                this.$message.error(this.lang.please_agree_privacy_policy);
                return
            }
            let params={
                code:this.verifyCode,
                language:window.localStorage.getItem('lang')||'CN',
                clientType:this.systemConfig.clientType,
                deviceId:''
            }
            if (this.loginType===1) {
                const mobileValidate=this.$refs.mobile_international.validate()
                if (!mobileValidate.pass){
                    this.$message.error(mobileValidate.tip)
                    return;
                }
                if (this.verifyCode.length===0){
                    const tip='login_sms_verification_code_empty';
                    this.$message.error(this.lang[tip]);
                    return ;
                }
                params.account=this.mobile_number;
                params.countryCode=this.international_code;
                params.accountType='mobile';
            }else if (this.loginType===2){
                if (!Tool.isEmail(this.email)) {
                    this.$message.error(this.lang.email_is_invalid_input_again);
                    return;
                }
                if (this.verifyCode.length===0){
                    const tip='email_verification_code_empty';
                    this.$message.error(this.lang[tip]);
                    return ;
                }
                params.account=this.email;
                params.accountType='email';
            }
            this.logging=true;
            service.getLoginTokenByCode(params).then((res)=>{
                if (res.data.error_code===0) {
                    const result=res.data.data;
                    this.twoFactorLogin(result);
                }
                this.logging=false
            }).catch((e)=>{
                this.$message.error(e);
                this.logging=false
            })
        },
        register(){
            let registerParams={}
            if (this.loginType==1) {
                const mobileValidate=this.$refs.mobile_international.validate()
                if (!mobileValidate.pass){
                    this.$message.error(mobileValidate.tip)
                    return;
                }
                registerParams.accountType='mobile';
                registerParams.countryCode=this.international_code;
                registerParams.account=this.mobile_number;
                registerParams.clientType=this.systemConfig.clientType;
                if (this.verifyCode.length===0){
                    const tip='login_sms_verification_code_empty';
                    this.$message.error(this.lang[tip]);
                    return ;
                }
                registerParams.code=this.verifyCode;
            }else if (this.loginType==2) {
                registerParams.accountType='email';
                registerParams.account=this.email;
                registerParams.clientType=this.systemConfig.clientType;
                if (this.verifyCode.length===0){
                    const tip='email_verification_code_empty';
                    this.$message.error(this.lang[tip]);
                    return ;
                }
                registerParams.code=this.verifyCode;
            }
            this.registerV2(registerParams);
        },
        registerV2(params){
            this.logging = true;
            service.registerV2(params).then((res)=> {
                this.logging = false;
                if (res.data.error_code===0) {
                    this.logging = true;
                    if (this.loginType==1||this.loginType==2) {
                        window.localStorage.setItem('isShowSetOrganization',1);
                        this.loginWithToken(res.data.data.token)
                        return ;
                    }
                }else{
                }
            })
        },
        validateLoginForm(){
            let result=true;
            if(this.login_form.name.length === 0){
                result=false;
                this.$message.error(this.lang.login_account_empty)
            } else if(this.login_form.pwd.length===0){
                result=false;
                this.$message.error(this.lang.login_password_empty);
            } else if(!this.isAgreePrivacyPolicy){
                result=false;
                this.$message.error(this.lang.please_agree_privacy_policy);
            }
            return result;
        },
        async handleValidateCaptcha(loginType){
            try {
                if(this.isValidating){
                    return
                }
                this.isValidating = true
                const afsCode = await this.$refs['validateCaptcha'].validateCaptcha()
                if(this.loginType === 3){
                    this.loginOrRegister(afsCode)
                }else if(this.loginType === 1){
                    this.getVerifyCode('mobile',afsCode)
                }else if(this.loginType===2){
                    this.getVerifyCode('email',afsCode)
                }
                this.isValidating = false
            } catch (error) {
                this.isValidating = false
            }
        },
        loginByTracelessValid:Tool.debounce(function () {
            if(this.loginType === 3){
                if (!this.validateLoginForm()) {
                    return ;
                }
            }
            if(this.loginType === 1){
                const mobileValidate=this.$refs.mobile_international.validate()
                if (!mobileValidate.pass){
                    this.$message.error(mobileValidate.tip);
                    return;
                }
                if(!this.isAgreePrivacyPolicy){
                    this.$message.error(this.lang.please_agree_privacy_policy);
                    return;
                }
            }
            if(this.loginType===2){
                if (!Tool.isEmail(this.email)) {
                    this.$message.error(this.lang.email_is_invalid_input_again);
                    return;
                }
                if(!this.isAgreePrivacyPolicy){
                    this.$message.error(this.lang.please_agree_privacy_policy);
                    return;
                }
            }
            this.handleValidateCaptcha(this.loginType)
        }, 500,true),
        autoLogin(){
            if (this.logging) {
                //其他途径在尝试登录时，不自动登录
                return ;
            }
            console.log('[event] loginform autoLogin');
            const loginToken=window.localStorage.getItem('unifiedPlatformToken')||''
            const language=window.localStorage.getItem('lang')||'CN'
            var that=this;
            if (loginToken!=='') {
                this.autoLoginAction({
                    action:'loginByToken',
                    loginToken:loginToken,
                })
            }
        },
        autoLoginAction(params){
            this.isAutoLogin=true;
            this.remember=true;
            this.logging=true;
            this.isAgreePrivacyPolicy = true
            this.loginWithToken(params.loginToken)
        },
        backToLogin(){
            this.isShowForgetPassword = false
        },
        goToPrivacyPolicy(){
            goPrivacyPolicy()
        },
    }
}
</script>
<style lang="scss">
.login_form_container{
    position: relative;
    flex: 1;
    display: flex;
    flex-direction: row;
    .login_types{
        width: 360px;
        background-color: #f2f5fa;
        padding:38px 32px;
        .login_type_item{
            display: flex;
            height: 80px;
            border-bottom:1px solid #e1e1e1;
            align-items: center;
            cursor: pointer;
            img{
                width: 56px;
                height: 56px;
            }
            p{
                margin-left: 16px;
                color: #000;
                font-weight: bold;
                font-size: 20px;
            }
        }
        .qrcode_title{
            margin-top: 36px;
            font-size: 20px;
            font-weight: bold;
            color: #3b3c3d;
            line-height: 22px;
            padding-left:72px;
        }
        .qrcode_description{
            font-size: 16px;
            line-height: 22px;
            padding-left:72px;
            margin: 8px 0;
        }
        .download_tip{
            font-size: 16px;
            line-height: 22px;
            padding-left:72px;
            color: #3875fa;
            margin: 8px 0;
            cursor: pointer;
        }
        .qrcode_card{
            width: 214px;
            padding-left: 72px;
            .other_login_way,.download_btn{
                display: none;
            }
            .refresh{
                left: 72px;
                transform: none;
                color: #3875fa;
            }
        }
    }
    .login_forms{
        flex:1;
        padding: 40px 56px;
        .welcome_title{
            font-size: 24px;
            font-weight: bold;
            color: rgba(0,0,0,.8);
            line-height: 32px;
            margin-bottom: 14px;
        }
        .validate_forms{
            .validate_tabs{
                display: flex;
                margin-bottom: 30px;
                .validate_item{
                    font-size: 16px;
                    line-height: 22px;
                    border-bottom:4px solid transparent;
                    cursor: pointer;
                    padding-top:16px;
                    padding-bottom: 12px;
                    &:first-child{
                        margin-right: 34px;
                    }
                    &.active{
                        font-weight: bold;
                        border-color:#3875fa;
                    }
                }
            }
        }
        .account_login{
            padding-top:58px;
        }
        .mobile_login,.email_login{
            .register_tip{
                font-size: 16px;
                line-height: 20px;
                margin-left: 12px;
                color: rgba(0,0,0,.4);
                margin-bottom: 8px;
            }
        }
        .login_form_checkbox{
            .checkbox_row{
                display: flex;
                justify-content: space-between;
                .go_to_btn{
                    font-size: 16px;
                    line-height: 1;
                    cursor: pointer;
                }
            }
            .remember{
            }
            .agree_privacy_protocol{
                margin-bottom: 8px;
                margin-right: 0;
                display: flex;
                align-items: center;
                .el-checkbox{
                    margin-right: 8px;
                }
                .privacy_text{
                    font-size: 16px;
                    color: #424242;
                    line-height: 1;
                    .privacy_link{
                        color: #3875fa;
                        cursor: pointer;
                        text-decoration: none;
                        transition: color 0.3s ease;
                        &:hover{
                            color: #2c5ce6;
                            text-decoration: underline;
                        }
                    }
                }
            }
        }
        .verify_item{
            position: relative;
            margin-bottom: 40px;
            display: flex;
            .code_btn{
                border:1px solid #3875fa;
                color: #3875fa;
                line-height: 40px;
                font-size: 16px;
                min-width: 128px;
                margin-left: 16px;
                text-align: center;
                border-radius:6px;
                cursor: pointer;
            }
            .el-input{
                flex:1;
            }
            .ban{
                background-color:#f5f7f9;
                border-color:#dfe0e7;
                color: #c4c6c7;
                pointer-events: none;
                cursor: not-allowed;
            }
        }
    }
    .download_panel{
        position:absolute;
        width: 100%;
        height: 100%;
        background-color:#fff;
        z-index: 2;
        padding:40px;
        cursor: pointer;
        .back_row{
            display: flex;
            align-items: center;
            img{
                width: 24px;
                height: 24px;
            }
            p{
                font-size: 16px;
                line-height: 20px;
                margin-left: 10px;
            }
        }
        .download_title{
            margin-top: 30px;
            text-align: center;
            font-size: 24px;
            line-height: 32px;
            font-weight: bold;
        }
        .download_way{
            display: flex;
            justify-content: center;
            margin-top: 57px;
            .download_way_item{
                text-align: center;
                width: 264px;
                .download_way_title{
                    font-size: 20px;
                    font-weight: bold;
                    line-height: 24px;
                    margin-bottom: 32px;
                }
                .download_way_logo{
                    width: 160px;
                    height: 160px;
                    margin: 0 auto;
                    display: flex;
                    align-items:center;
                    justify-content: center;
                    background-image:url('/static/resource_activity/images/windows.png');
                    img{
                        width: 160px;
                        height: 160px;
                    }
                    .pc_logo{
                        width: 96px;
                        height: auto;
                    }
                }
                .download_version{
                    font-size: 16px;
                    line-height: 20px;
                    margin: 16px 0 10px;
                }
                .download_way_tip{
                    font-size: 16px;
                    line-height: 20px;
                    margin-top: 20px;
                }
                .download_btn{
                    border:1px solid #3875fa;
                    color: #3875fa;
                    line-height: 40px;
                    font-size: 16px;
                    min-width: 160px;
                    text-align: center;
                    border-radius:6px;
                    cursor: pointer;
                    display: inline-block;
                }
            }
        }
    }
    .forget_password_panel{
        position:absolute;
        width: 100%;
        height: 100%;
        background-color:#fff;
        z-index: 2;
        padding:40px;
        cursor: pointer;
        .back_row{
            display: flex;
            align-items: center;
            img{
                width: 24px;
                height: 24px;
            }
            p{
                font-size: 16px;
                line-height: 20px;
                margin-left: 10px;
            }
        }
        .forget_password_dialog{
            width: 328px;
            padding:0;
            position:relative;
            top: 0;
            left: 0;
            margin: 0 auto;
            .forget_item{
                margin-bottom: 40px;
                .requery_sms_identification{
                    font-size: 16px;
                    color: #3875fa;
                    line-height: 40px;
                }
            }
        }
        .forget_password_title{
            margin-top: 24px;
            font-size: 24px;
            font-weight: bold;
            line-height: 32px;
            text-align: center;
            margin-bottom: 40px;
        }
        .forget_password_email{
            margin-bottom: 40px;
            display: block;
        }
        .toggle_validate{
            font-size: 16px;
            color: #333;
        }
    }
    .mobile_international{
        margin-bottom: 40px;
        .el-select{
            top: 1px;
            left: 1px;
            height: 38px;
            .el-input__inner{
                height: 38px;
                border:none;
            }
        }
    }
    .el-input__inner{
        border:1px solid #999;
        border-radius:6px;
        caret-color:#3875fa;
    }
    .el-input.is-active .el-input__inner, .el-input__inner:focus{
        border:1px solid #3875fa;
    }
}
</style>

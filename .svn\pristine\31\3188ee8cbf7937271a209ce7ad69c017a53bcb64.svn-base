<template>
    <div class="video_container">
        <video
            ref="videoPlayer"
            class="video-js"
            playsinline
            controls
            crossorigin="anonymous"
            :poster="poster"
            :key="playerKey"
        >
            <!-- 不直接设置 source，而是通过 HLS.js 动态加载 -->
        </video>
    </div>
</template>

<script>
import Plyr from "plyr";

export default {
    name: "AutoVideoPlayer",
    props: {
        videoSrc: {
            type: String,
            required: true,
        },
        poster: {
            type: String,
            default: "", // 默认没有封面
        },
        autoPlay: {
            type: Boolean,
            default: true,
        },
        id: {
            type: [String, Number],
            default: () => `video-player-${Date.now()}-${Math.floor(Math.random() * 1000)}`
        }
    },
    data() {
        return {
            playerKey: this.generatePlayerKey(),
            player: null,
            hls: null,  // 添加 hls 实例存储
            hasEmittedError: false,  // 防止重复触发错误事件
        }
    },
    mounted() {
        this.$nextTick(() => {
            this.initPlayer();
        });
    },
    beforeDestroy() {
        if (this.player) {
            this.player.destroy();
        }
        if (this.hls) {
            this.hls.destroy();
        }
    },
    methods: {
        generatePlayerKey() {
            return `${this.id}-${this.videoSrc}`;
        },
        initPlayer() {
            if (!this.$refs.videoPlayer || this.player) {
                return;  // 如果播放器已经初始化，直接返回
            }

            // 初始化 Plyr
            this.player = new Plyr(this.$refs.videoPlayer, {
                controls: ["play-large", "play", "current-time", "progress", "fullscreen"],
            });

            // 添加播放事件监听
            this.setupVideoEventListeners();

            // 首次加载视频
            this.loadVideo(this.videoSrc);
        },
        async loadVideo(src) {
            if (!this.player || !this.$refs.videoPlayer) {
                return;
            }

            // 重置错误状态
            this.hasEmittedError = false;
            const videoElement = this.$refs.videoPlayer;

            // 处理 HLS 视频
            if (src.endsWith(".m3u8")) {
                try {
                    // 动态引入 Hls.js
                    const { default: Hls } = await import(/* webpackPrefetch: true */ 'hls.js');

                    if (Hls.isSupported()) {
                        if (!this.hls) {
                            this.hls = new Hls();
                        }

                        // 添加 HLS 错误事件监听
                        this.hls.on(Hls.Events.ERROR, (event, data) => {
                            console.error('HLS error:', event, data);
                            if (data.fatal && !this.hasEmittedError) {
                                this.hasEmittedError = true;
                                this.$emit('error', { type: 'hls', event, data });
                            }
                        });

                        this.hls.loadSource(src);
                        this.hls.attachMedia(videoElement);
                        this.hls.on(Hls.Events.MANIFEST_PARSED, () => {
                            if (this.autoPlay) {
                                videoElement.play();
                            }
                        });
                    }
                } catch (error) {
                    console.error('Failed to load HLS:', error);
                    if (!this.hasEmittedError) {
                        this.hasEmittedError = true;
                        this.$emit('error', { type: 'hls_init', error });
                    }
                }
            } else {
                // 普通视频直接加载
                videoElement.src = src;

                // 添加错误事件监听
                videoElement.addEventListener("error", (event) => {
                    console.error('Video load error:', src, event);
                    if (!this.hasEmittedError) {
                        this.hasEmittedError = true;
                        this.$emit('error', event);
                    }
                });

                videoElement.addEventListener("loadedmetadata", () => {
                    if (this.autoPlay) {
                        videoElement.play();
                    }
                });
            }
        },
        // 设置视频事件监听器 - 只处理基础的播放器事件
        setupVideoEventListeners() {
            if (!this.player) {
                return;
            }

            // 监听播放事件
            this.player.on('play', () => {
                this.$emit('play');
            });

            // 监听暂停事件
            this.player.on('pause', () => {
                this.$emit('pause');
            });

            // 监听结束事件
            this.player.on('ended', () => {
                this.$emit('ended');
            });

            // 监听时间更新事件
            this.player.on('timeupdate', () => {
                this.$emit('timeupdate', {
                    currentTime: this.player.currentTime,
                    duration: this.player.duration
                });
            });

            // 监听错误事件
            this.player.on('error', (error) => {
                console.error('Video error:', error);
                if (!this.hasEmittedError) {
                    this.hasEmittedError = true;
                    this.$emit('error', { error });
                }
            });
        },
        // 暴露播放器控制方法
        play() {
            if (this.player) {
                this.player.play();
            }
        },
        pause() {
            if (this.player) {
                this.player.pause();
            }
        },
        // 获取播放器状态
        getPlayerState() {
            if (!this.player) {
                return null;
            }

            return {
                playing: this.player.playing,
                currentTime: this.player.currentTime,
                duration: this.player.duration,
                volume: this.player.volume,
                muted: this.player.muted
            };
        }
    }
};
</script>

<style scoped lang="scss">
.video_container {
    width: 100%;
    height: 100%;
    ::v-deep {
        .plyr--video {
            width: 100%;
            height: 100%;
        }
    }
}
</style>

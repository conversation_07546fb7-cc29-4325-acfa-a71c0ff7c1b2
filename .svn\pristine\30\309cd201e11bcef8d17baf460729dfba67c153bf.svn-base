<template>
    <div class="remote_ultrasound data_center">
        <div class="header_panel">
            <img class="logo" src="static/resource_statistic/images/logo.png">
            <div class="router_bar">
                <p class="app_name">{{lang.app_name}}</p>
                <p class="router_title" @click.stop="isSHowMenu = true">{{lang.statistic.large_billboard}}</p>
                <img @click.stop="isSHowMenu = true" class="router_dorp_down" src="static/resource_statistic/images/quick_time_dorp_down.png">
                <div class="router_menu" v-if="isSHowMenu">
                    <div class="router_item active">{{lang.statistic.large_billboard}}</div>
                    <div @click="toStatistic" class="router_item" :class="{disabled:disabledStatistic}">{{lang.statistic.statistic_report}}</div>
                </div>
            </div>
            <img @click.stop="isShowTheme = true" class="title_dropdown" src="static/resource_statistic/images/title_dropdown.png">
            <p class="bi_title">{{lang.statistic.BI_title_demo}}</p>
            <div class="theme_menu" v-show="isShowTheme">
                <router-link to="/remote_ultrasound_data_center" replace class="theme_item">{{lang.statistic.BI_title}}</router-link >
                <router-link to="/mis_data_demo" replace class="theme_item">{{lang.statistic.BI_title_demo}}</router-link >
                <router-link to="/dr_data_center" replace class="theme_item">{{lang.statistic.BI_DR_title}}</router-link >
                <router-link to="/dr_data_demo" replace class="theme_item active">{{lang.statistic.BI_title_dr_demo}}</router-link >
            </div>
            <div class="tool_bar">
                <div class="quick_time" @click.stop="showQuickTime">
                    {{lang.statistic.time_map[DRGlobalSetting.quickTime]}}
                    <div class="quick_time_menu" v-show="isShowQuickTime">
                        <div 
                            v-for="item in quickModificationTimeList"
                            @click.stop="changeQuickTime(item.param)"
                            class="item"
                            :class="{active:DRGlobalSetting.quickTime === item.param}"
                            :key="item.param">
                            {{lang.statistic.time_map[item.param]}}
                        </div>
                    </div>
                </div>
                <img  @click.stop="showQuickTime" class="quick_time_dorp_down" src="static/resource_statistic/images/quick_time_dorp_down.png">
                <img @click="openSettingDialog" class="data_setting" src="static/resource_statistic/images/bi_setting.png">
                <p class="bi_showtime">{{time}}
                </p>
            </div>
        </div>
        <div class="body_panel">
            <div class="left_panel">
                <div class="top_block">
                    <div class="draggable_block" v-loading="loadingLiveData">
                        <div class="statistic_card">
                            <div class="block_title">
                                <p class="title_content statistic_title">{{lang.statistic.statistic_data_title}}</p>
                            </div>
                            <template v-if="DRGlobalSetting.statisticGroup === 'group'">
                                <div class="count_item">
                                    <p class="label">{{lang.statistic.liveDuration}}</p>
                                    <p class="value">{{ parseInt(liveDataObj.liveDuration/60) }}</p>
                                </div>
                                <div class="count_item">
                                    <p class="label">{{lang.statistic.liveCount}}</p>
                                    <p class="value">{{ liveDataObj.liveCount }}</p>
                                </div>
                                <div class="count_item">
                                    <p class="label">{{lang.statistic.iworksUseTimes}}</p>
                                    <p class="value">{{ liveDataObj.iworksUseTimes }}</p>
                                </div>
                                <div class="count_item">
                                    <p class="label">{{lang.statistic.iworksUserCount}}</p>
                                    <p class="value">{{ liveDataObj.iworksUserCount }}</p>
                                </div>
                            </template>
                            <template v-else>
                                <div class="count_item">
                                    <p class="label">{{lang.statistic.image_count}}</p>
                                    <p class="value">{{examSummaryObj.imageCount}}</p>
                                </div>
                                <div class="count_item">
                                    <p class="label">{{lang.statistic.exam_count}}</p>
                                    <p class="value">{{ examSummaryObj.examCount }}</p>
                                </div>
                            </template>  
                        </div>
                    </div>
                    <div class="draggable_block"
                        id="draggableBlock1"
                        draggable="true"
                        @dragstart="handleDragStart"
                        @dragover="handleDragOver"
                        @drop="handleDrop">
                        <DraggableCard
                            v-if="statsList.block1"
                            ref="block1"
                            :block="statsList.block1"
                            :query="query"
                            :quickOptions="quickOptions"
                            :globalSetting="DRGlobalSetting"
                            :random="true"
                            @emitBlockData="emitBlockData"
                        ></DraggableCard>
                    </div>
                </div>
                <div class="draggable_block"
                    id="draggableBlock2"
                    draggable="true"
                    @dragstart="handleDragStart"
                    @dragover="handleDragOver"
                    @drop="handleDrop">
                    <DraggableCard
                        v-if="statsList.block2"
                        ref="block2"
                        :block="statsList.block2"
                        :query="query"
                        :quickOptions="quickOptions"
                        :globalSetting="DRGlobalSetting"
                        :random="true"
                        @emitBlockData="emitBlockData"
                    ></DraggableCard>
                </div>
            </div>
            <div class="center_panel">
                <!-- <div class="quick_toggle_bar">
                    <div @click="toggleGroup('group')" class="quick_toggle_left" :class="{active:DRGlobalSetting.statisticGroup === 'group'}">{{lang.statistic.statistic_group}}</div>
                    <div @click="toggleGroup('device')" class="quick_toggle_right" :class="{active:DRGlobalSetting.statisticGroup === 'device'}">{{lang.statistic.statistic_device}}</div>
                </div> -->
                <div class="top_block">
                    <BIMapChart @getData="getMapData"  ref="map_chart"/>
                </div>
                <div class="draggable_block"
                    id="draggableBlock3"
                    draggable="true"
                    @dragstart="handleDragStart"
                    @dragover="handleDragOver"
                    @drop="handleDrop">
                    <DraggableCard
                        v-if="statsList.block3"
                        ref="block3"
                        :block="statsList.block3"
                        :query="query"
                        :globalSetting="DRGlobalSetting"
                        :quickOptions="quickOptions"
                        :random="true"
                        @emitBlockData="emitBlockData"
                    ></DraggableCard>
                </div>
            </div>
            <div class="right_panel">
                <div class="top_block">
                    <div class="draggable_block">
                        <div class="statistic_card">
                            <div class="block_title">
                                <p class="title_content statistic_title">{{lang.statistic.exam_distribution}}</p>
                            </div>
                            <BIPieChart :data="overallDistribution" @togglePieData="handdleTogglePieData" :type="this.pieType" :adultTotal="adultTotal" :childTotal="childTotal"/>
                        </div>
                    </div>
                </div>
                <div class="draggable_block"
                    id="draggableBlock4"
                    draggable="true"
                    @dragstart="handleDragStart"
                    @dragover="handleDragOver"
                    @drop="handleDrop">
                    <DraggableCard
                        v-if="statsList.block4"
                        ref="block4"
                        :block="statsList.block4"
                        :query="query"
                        :quickOptions="quickOptions"
                        :globalSetting="DRGlobalSetting"
                        :random="true"
                        @emitBlockData="emitBlockData"
                    ></DraggableCard>
                </div>
            </div>
        </div>
        <div class="setting_dialog_wrapper" v-show="showSettingDialog">
            <div class="setting_dialog">
                <section class="setting_title">
                    <span class="title_content">{{lang.statistic.BI_setting_title}}</span>
                    <img @click="closeSettingDialog" class="close_icon" src="static/resource_statistic/images/close.png">
                </section>
                <section class="setting_container">
                    <section class="setting_header">
                        <label class="setting_header_title">{{lang.statistic.statistic_time}}</label>
                        <button
                            v-for="item in quickModificationTimeList"
                            :key="item.param"
                            class="setting_button"
                            :class="{active:item.param === basicOptions.quickTime}"
                            @click="changeSettingQuickTime(item.param)"
                            >{{ lang.statistic.time_map[item.param] }}</button
                        >
                        <el-date-picker
                            class="date_picker"
                            v-model="basicOptions.period"
                            @change="handelChangeRange"
                            size="small"
                            :clearable="false"
                            type="monthrange"
                            range-separator="-"
                            :default-time="['00:00:00', '00:00:00']"
                        >
                        </el-date-picker>
                        <!-- <section class="group_search_box" v-if="isAdmin">
                            <el-autocomplete
                              v-model="basicOptions.subject"
                              clearable
                              :fetch-suggestions="querySearchAsync"
                              :placeholder="lang.statistic.search_group_name"
                              size="small"
                              @select="handleSelect"
                              @change="handleChange"
                            ></el-autocomplete>
                        </section> -->
                        <section class="setting_show_type">
                            <label class="">{{lang.statistic.show_type}}</label>
                            <div class="show_type_item" @click="basicOptions.showType = 1" :class="{active:basicOptions.showType ===1}">
                                <img src="static/resource_statistic/images/show_type_1.png">
                            </div>
                            <div class="show_type_item" @click="basicOptions.showType = 0" :class="{active:basicOptions.showType ===0}"><img src="static/resource_statistic/images/show_type_0.png"></div>
                        </section>
                    </section>
                    <!-- <section class="statistic_type">
                        <div @click="handleClickTab('group')" class="statistic_item" :class="{active:basicOptions.statisticGroup === 'group'}">{{lang.statistic.statistic_group}}</div>
                        <div @click="handleClickTab('device')" class="statistic_item" :class="{active:basicOptions.statisticGroup === 'device'}">{{lang.statistic.statistic_device}}</div>
                    </section> -->
                    <section v-if="basicOptions.statisticGroup === 'group'" class="setting_content">
                        <section class="left">
                            <section class="setting_content_box">
                                <h2>{{lang.statistic.user_related}}</h2>
                                <div class="check_item" v-for="item of settingGroupList.user_related" @click="toggleCheck(item)" :key="item" :class="{disabled:isOptionsMax&&!options[item].checked}">
                                    <img v-if="options[item].checked" src="static/resource_statistic/images/checkbox_checked.png">
                                    <img v-else src="static/resource_statistic/images/checkbox.png">
                                    <p>{{lang.statistic[item]}}</p>
                                </div>
                            </section>
                            <section class="setting_content_box">
                                <h2>{{lang.statistic.content_related}}</h2>
                                <div class="check_item" v-for="item of settingGroupList.content_related" @click="toggleCheck(item)" :key="item" :class="{disabled:isOptionsMax&&!options[item].checked}">
                                    <template v-if="options[item]">
                                        <img v-if="options[item].checked" src="static/resource_statistic/images/checkbox_checked.png">
                                        <img v-else src="static/resource_statistic/images/checkbox.png">
                                        <p>{{lang.statistic[item]}}</p>
                                    </template>
                                </div>
                            </section>
                        </section>
                        <section class="center">
                            <section class="setting_content_box">
                                <h2>{{lang.statistic.group_related}}</h2>
                                <div class="check_item" v-for="item of settingGroupList.group_related" @click="toggleCheck(item)" :key="item" :class="{disabled:isOptionsMax&&!options[item].checked}">
                                    <template v-if="options[item]">
                                        <img v-if="options[item].checked" src="static/resource_statistic/images/checkbox_checked.png">
                                        <img v-else src="static/resource_statistic/images/checkbox.png">
                                        <p>{{lang.statistic[item]}}</p>
                                    </template>
                                </div>
                            </section>
                            <section class="setting_content_box">
                                <h2>{{lang.statistic.iworks_protocol}}</h2>
                                <div class="check_item" v-for="item of settingGroupList.iworks_protocol" @click="toggleCheck(item)" :key="item" :class="{disabled:isOptionsMax&&!options[item].checked}">
                                    <img v-if="options[item].checked" src="static/resource_statistic/images/checkbox_checked.png">
                                    <img v-else src="static/resource_statistic/images/checkbox.png">
                                    <p>{{lang.statistic[item]}}</p>
                                </div>
                            </section>
                        </section>
                        <section class="right">
                            <section class="setting_content_box">
                                <h2>{{lang.statistic.device_related}}</h2>
                                <div class="check_item" v-for="item of settingGroupList.device_related" @click="toggleCheck(item)" :key="item" :class="{disabled:isOptionsMax&&!options[item].checked}">
                                    <img v-if="options[item].checked" src="static/resource_statistic/images/checkbox_checked.png">
                                    <img v-else src="static/resource_statistic/images/checkbox.png">
                                    <p>{{lang.statistic[item]}}</p>
                                </div>
                            </section>
                        </section>
                    </section>
                    <section v-else-if="basicOptions.statisticGroup === 'device'" class="setting_content">
                        <section class="left">
                            <section class="setting_content_box">
                                <h2>{{lang.statistic.device_types.doppler}}</h2>
                                <div class="check_item" v-for="item of settingDeviceList.doppler" @click="toggleCheck(item)" :key="item" :class="{disabled:isOptionsMax&&!options[item].checked}">
                                    <img v-if="options[item].checked" src="static/resource_statistic/images/checkbox_checked.png">
                                    <img v-else src="static/resource_statistic/images/checkbox.png">
                                    <p>{{lang.statistic[item]}}</p>
                                </div>
                            </section>
                        </section>
                    </section>
                </section>
                <section class="setting_footer">
                    <button class="setting_cancel" @click="handleSettingCancel">{{lang.statistic.cancel_btn}}</button>
                    <button class="setting_submit" @click="handleSettingConfrim">{{lang.statistic.confirm_btn}}</button>
                </section>
            </div>
        </div>
    </div>
</template>
<script type="text/javascript">
import base from '../../lib/base'
import _ from "lodash";
import request from '../../service/service'
import moment from 'moment';
import DraggableCard from "../../components/DraggableCard";
import BIPieChart from "../../components/DRPieChart/index_new.vue";
import '../../common/scss/BI.scss'
import BIMapChart from "../../components/BIMapChart/index_new.vue";
export default {
    mixins: [base],
    name: "RemoteUltrasoundDataCenter",
    components: { DraggableCard,BIPieChart,BIMapChart},
    data() {
        return {
            time:'',
            loadingLiveData:false,
            onDraggingElement: null,
            maxOptions: 4,
            query:{
                dataFrom:'',
                id:'',
                subject:'',
            },
            DRGlobalSetting:{
                quickTime:'6M',
                period:[],
                showType:1,
                statisticGroup:'group', // 统计类别，group：数据统计 device：设备统计
            },
            pieType:'adultData',
            basicOptions: {
                dataFrom:'',
                id:'',
                subject:'',
                quickTime:'6M',
                period: [], // 统计时间 [起(Date), 终(Date)]
                showType: 1, // 0 -> 曲线图 | 1 -> 柱状图
                statisticGroup: 'group',
            },
            allShowTypes: [
                {
                    id: 0, // 0 -> 曲线图 | 1 -> 柱状图
                },
                {
                    id: 1,
                },
            ],
            liveDataObj:{
                iworksUserCount:7586,
                iworksUseTimes:12050,
                liveCount:9577,
                liveDuration:89665689,
            },
            examSummaryObj:{
                examCount:0,
                imageCount:0,
            },
            statsList: {
                block1: null,
                block2: null,
                block3: null,
                block4: null,
            },
            defaultDataItem: ['liveCount','liveUserTimes','examIncreased','imageIncreased'], //数据统计默认选中项
            defaultDeviceItem: ['deviceInstallTime','deviceUtilizationRate','examCountOfOrganization','deviceStatus'],
            adultTotal:0,
            childTotal:0,
            // 超声检查总体分布情况
            overallDistribution: [
                { value: 0 , key:'P01' },
                { value: 0 , key:'P02'},
                { value: 0 , key:'P03'},
                { value: 0 , key:'P04'},
                { value: 0 , key:'P05'},
                { value: 0 , key:'P06'},
                { value: 0 , key:'P07'},
                { value: 0 , key:-1},
            ],
            options:{
                // 用户相关
                userIncreased: { title: "userIncreased", showType:1, checked:false }, // 新增用户
                userTotal: { title: "userTotal", showType:1, checked:false , hiddenCount:true }, // 累计用户
                userActive: { title: "userActive", showType:1, checked:false }, // 活跃用户
                // 群/群落
                groupActive: { title: "groupActive", showType:1, checked:false }, // 活跃群数
                groupIncreased: { title: "groupIncreased", showType:1, checked:false}, // 新增群数
                groupTotal: { title: "groupTotal", showType:1, checked:false, hiddenCount:true,}, // 累计群数
                // 直播
                liveCount: { title: "liveCount", showType:1, checked:false }, // 直播场数
                liveUserTimes: { title: "liveUserTimes", showType:1, checked:false }, // 直播参与人数
                liveDuration: { title: "liveDuration", showType:1, checked:false }, // 直播时长
                // 设备
                iSyncIncreased: { title: "iSyncIncreased", showType:1, checked:false }, // 新增isync 数
                iSyncTotal: { title: "iSyncTotal", showType:1, checked:false, hiddenCount:true }, // 累计isync 数
                ulinkerIncreased: { title: "ulinkerIncreased", showType:1, checked:false }, // 新增灵珂数
                ulinkerTotal: { title: "ulinkerTotal", showType:1, checked:false, hiddenCount:true }, // 累计灵珂数
                dopplerIncreased: { title: "dopplerIncreased", showType:1, checked:false }, // 新增超声数
                dopplerTotal: { title: "dopplerTotal", showType:1, checked:false, hiddenCount:true }, // 累计超声数
                // 内容
                examIncreased: { title: "examIncreased", showType:1, checked:false }, // 新增检查数
                imageIncreased: { title: "imageIncreased", showType:1, checked:false }, // 新增图像数
                videoIncreased: { title: "videoIncreased", showType:1, checked:false }, // 新增视频数
                breastAI: { title: "breastAI", showType:1, checked:false }, // 佳佳老师
                library: { title: "library", showType:1, checked:false,}, // 图书馆
                // iWorks协议（暂时定义）
                iworksUserCount: { title: "iworksUserCount", showType:1, checked:false }, // 扫查规范使用人数
                iworksUseTimes: { title: "iworksUseTimes", showType:1, checked:false }, // 扫查规范使用次数
                //specCompliance: { title: "扫查规范遵从度", showType:1, checked:false }, // 扫查规范遵从度
                // 设备统计
                deviceInstallTime: { title: "deviceInstallTime", showType:1, checked:false, hiddenCount:true },
                deviceUtilizationRate: { title: "deviceUtilizationRate", showType:1, checked:false, hiddenCount:true },
                examCountOfOrganization: { title: "examCountOfOrganization", showType:1, checked:false, hiddenCount:true },
                deviceFailureTip: {title: "deviceFailureTip", showType:1, checked:false},
                examCountOfDevice: { title: "examCountOfDevice", showType:1, checked:false, hiddenCount:true },
                deviceStatus: { title: "deviceStatus", showType:1, checked:false, hiddenCount:true },
                probeUsageRate:{title:"probeUsageRate",showType:1, checked:false,hiddenCount:true}
            },
            quickModificationTimeList: [
                {
                    param: "3M",
                },
                {
                    param: "6M",
                },
                {
                    param: "1Y",
                },
                {
                    param: "TY",
                },
            ],
            isShowQuickTime: false,
            isShowTheme:false,
            isSHowMenu:false,
            isAdmin:false,
            disabledStatistic:false,
            showSettingDialog:false,
            settingGroupList:{
                user_related:['userActive','userIncreased','userTotal'],
                content_related:['examIncreased','imageIncreased','videoIncreased','breastAI','library'],
                group_related:['groupActive','groupIncreased','groupTotal','liveCount','liveUserTimes','liveDuration'],
                iworks_protocol:['iworksUserCount','iworksUseTimes'],
                device_related:['iSyncIncreased','iSyncTotal','ulinkerIncreased','ulinkerTotal','dopplerIncreased','dopplerTotal'],
            },
            settingDeviceList:{
                doppler:['deviceInstallTime','deviceUtilizationRate','examCountOfOrganization','deviceFailureTip','examCountOfDevice','deviceStatus','probeUsageRate']
            }
        }
    },
    created() {
        this.query = JSON.parse(window.localStorage.getItem('stat_query'))
        if(this.query.dataFrom === 'global') {
            this.isAdmin = true;
        }else{
            // 非全局不可见 , 删除相关配置
            delete this.options.groupTotal;
            delete this.options.library;
            this.settingGroupList.content_related.splice(4)
            this.settingGroupList.group_related.splice(2,1)
        }
    },
    computed:{
        checkedOptionsLength() {
            let count = 0;
            for (const key in this.options) {
                if (this.options.hasOwnProperty(key) && this.options[key].checked === true) {
                    count++;
                }
            }
            return count;
        },
        isOptionsMax() {
            return this.checkedOptionsLength >= this.maxOptions;
        },
        quickOptions(){
            const target =this.DRGlobalSetting.statisticGroup === 'device'?this.settingDeviceList:this.settingGroupList;
            const checkedBlocks = [];
            const result = []
            for(let key in this.statsList){
                const block = this.statsList[key]
                if (block) {
                    checkedBlocks.push(block.title);
                }
            }
            for(let key in target){
                const list =target[key];
                list.forEach((item)=>{
                    if(checkedBlocks.indexOf(item) === -1){
                        result.push(item);
                    }
                })
            }
            return result
        }
    },
    mounted() {
        //默认获取最近6月数据
        this.getTime();
        //初始化本地存储
        this.storageInitialize();
        this.initMenu();
        this.$nextTick(() => {
            this.handleQuickModificationTimeChange();
        });
        window.addEventListener('click',()=>{
            this.isSHowMenu = false;
            this.isShowTheme = false;
            this.isShowQuickTime = false;
        })
    },
    methods:{
        getTime() {
            const date = new Date();
            this.time = `${moment().format('YYYY-MM-DD HH:mm')} ${this.getWeek()}`;
            setInterval(() => {
                const date = new Date();
                this.time = `${moment().format('YYYY-MM-DD HH:mm')} ${this.getWeek()}`;
            }, 1000);
        },
        getWeek() {
            return this.lang.statistic.weeks[new Date().getDay()];
        },
        storageInitialize() {
            // 获取全局配置和
            if (localStorage.getItem("DRGlobalSettingDemo")) {
                try {
                    this.DRGlobalSetting = JSON.parse(localStorage.getItem("DRGlobalSettingDemo"));
                    this.DRGlobalSetting.period[0] = new Date(this.DRGlobalSetting.period[0])
                    this.DRGlobalSetting.period[1] = new Date(this.DRGlobalSetting.period[1])
                } catch (e) {
                    console.error("缓存中图表类型有错误数据，已将对应数据初始化. Detail: ", e);
                    localStorage.removeItem("DRGlobalSettingDemo");
                    this.storageInitialize();
                }
            } else {
                localStorage.setItem("DRGlobalSettingDemo", JSON.stringify(this.DRGlobalSetting));
            }
            if (localStorage.getItem("DRStatsListDemo")) {
                try {
                    let statsList = JSON.parse(localStorage.getItem("DRStatsListDemo"));
                    for(let key in statsList){
                        const block = statsList[key]
                        if (block && !this.options[block.title]) {
                            statsList[key] = null;
                        }
                    }
                    this.statsList = statsList;
                } catch (e) {
                    console.error("缓存中图表类型有错误数据，已将对应数据初始化. Detail: ", e);
                    localStorage.removeItem("MISStatsList");
                    this.storageInitialize();
                }
            } else {
                this.setDefaultStatsList();
                localStorage.setItem("DRStatsListDemo", JSON.stringify(this.statsList));
            }
        },
        setDefaultStatsList(){
            let target = this.defaultDataItem;
            if(this.DRGlobalSetting.statisticGroup === 'device'){
                target = this.defaultDeviceItem;
            }
            target.forEach((item,index)=>{
                this.statsList[`block${index+1}`] = _.cloneDeep(this.options[item]);
            })
        },
        handleQuickModificationTimeChange() {
            if (this.DRGlobalSetting.quickTime !== "") {
                this.adjustPeriod(this.DRGlobalSetting.quickTime,this.DRGlobalSetting);
            }
            this.renderAll();
        },
        renderAll(){
            // this.renderLiveData();
            this.renderPieData();
        },
        // 获取几个月前的1号0点时间
        getDateOneOfMonth(num) {
            const time = moment().set('date', 1).subtract(num-1, 'month');
            return new Date(new Date(time).setHours(0,0,0,0));
        },
        adjustPeriod(opt,target) {
            const end = new Date();
            end.setHours(0, 0, 0, 0);
            let start = new Date();
            // this.DRGlobalSetting.quickTime = opt;
            console.log(opt)
            try {
                switch (opt) {
                case "TY": {
                    start.setUTCHours(0, 0, 0, 0);
                    start.setUTCMonth(0);
                    start.setUTCDate(1);
                    break;
                }
                case '3M': {
                    start = this.getDateOneOfMonth(3)
                    break;
                }
                case '6M': {
                    start = this.getDateOneOfMonth(6)
                    break;
                }
                case '1Y': {
                    start = this.getDateOneOfMonth(12)
                    break;
                }
                default: {
                    throw `Option error with an unknown parameter ${opt}`;
                }
                }
                // start.setHours(0, 0, 0, 0);
                target.period = [start, end];
            } catch (e) {
                console.error("An error occured. Details:", e);
                this.$message.error("错误日期参数，设置失败");
                return;
            }
        },
        getRangeTime(){
            const startTime = new Date(moment(this.DRGlobalSetting.period[0]).subtract(0, 'days')).setHours(0, 0, 0, 0);
            const endTime = new Date(moment(this.DRGlobalSetting.period[1]).set('date', 1).add(1, 'month')).getTime() - 1;
            return {
                startTime,
                endTime
            }
        },
        handleDragStart(e) {
            this.onDraggingElement = e.currentTarget;
        },
        handleDragOver(e) {
            e.preventDefault();
        },
        handleDrop(e) {
            let dropElement = e.currentTarget;
            if (this.onDraggingElement != null && this.onDraggingElement != dropElement) {
                let dropId = dropElement.id.slice(-6).toLowerCase();
                let dragId = this.onDraggingElement.id.slice(-6).toLowerCase();
                let temp = this.statsList[dropId];
                this.statsList[dropId] = this.statsList[dragId];
                this.statsList[dragId] = temp;
            }
            localStorage.setItem("DRStatsListDemo", JSON.stringify(this.statsList));
        },
        showQuickTime() {
            this.isShowQuickTime = true;
        },
        changeQuickTime(time) {
            this.isShowQuickTime = false;
            this.DRGlobalSetting.quickTime = time;
            this.handleQuickModificationTimeChange();
            this.renderBlocks();
            localStorage.setItem("DRGlobalSettingDemo", JSON.stringify(this.DRGlobalSetting));
        },
        async initMenu() {
            if(this.query.dataFrom !== 'global') {
                const item = await request.getDataFromItem({dataFrom:this.query.dataFrom, id:this.query.id});
                this.$store.commit('globalParams/updateGlobalParams',{
                    targetInfo:item.data.data
                })
                //群普通成员进BI看板不能看云端统计
                if (this.query.dataFrom === 'group' && !item.data.data.isAdminOrOwner) {
                    this.disabledStatistic = true;
                }
            }
        },
        toStatistic(){
            if (!this.disabledStatistic) {
                this.$router.replace({
                    path: '/index/live',
                    query: {}
                })
            } 
        },
        // dialog操作
        openSettingDialog() {
            this.showSettingDialog = true;
            this.basicOptions = Object.assign(_.cloneDeep(this.query),_.cloneDeep(this.DRGlobalSetting));
            for(let key in this.options){
                // 重置所有选中
                this.options[key].checked = false;
                this.options[key].showType = this.basicOptions.showType;
            }
            for(let key in this.statsList){
                const block = this.statsList[key]
                if (block) {
                    this.options[block.title].checked = true;
                }
            }
        },
        closeSettingDialog() {
            this.showSettingDialog = false;
        },
        handelChangeRange(value,value2){
            const startDate = new Date(this.basicOptions.period[0]);
            const endDate = new Date(this.basicOptions.period[1]);
            this.basicOptions.quickTime = '';
            if (endDate.valueOf()-startDate.valueOf()>= 365*24*60*60*1000) {
                this.$message.error(this.lang.statistic.time_range_tip)
                this.basicOptions.period = _.cloneDeep(this.period);
            }
        },
        async querySearchAsync(queryStr,cb){
            const arr=[]
            if (this.basicOptions.subject) {
                const result = await request.getSubjectData({
                    subject:this.basicOptions.subject
                })
                for(let item of result.data.data){
                    arr.push({
                        value:`${item.id}:${item.subject}(${item.adminInfo.nickname})`,
                        id:item.id,
                    })
                }
            }
            cb(arr)
        },
        handleSelect(item){
            this.basicOptions.dataFrom = 'group';
            this.basicOptions.id = item.id;
            console.log('handleSelect',this.basicOptions)
        },
        handleChange(value){
            console.log('value',value)
            if (!value) {
                this.basicOptions.dataFrom = 'global';
                this.basicOptions.id = -1;
                this.basicOptions.subject = '';
            }
        },
        handleClickTab(name){
            this.basicOptions.statisticGroup = name;
            this.setDefaultDetailOption();
        },
        setDefaultDetailOption(){
            let target = this.defaultDataItem;
            if (this.basicOptions.statisticGroup === 'device') {
                target = this.defaultDeviceItem;
            }
            for (const key in this.options) {
                this.options[key].checked = false;
            }
            for(let key of target){
                this.options[key].checked = true;
            }
        },
        handleSettingCancel() {
            this.showSettingDialog = false;
        },
        handleSettingConfrim() {
            console.log(this.query,this.basicOptions);
            this.query.dataFrom = this.basicOptions.dataFrom;
            this.query.id = this.basicOptions.id;
            this.query.subject = this.basicOptions.subject;
            this.DRGlobalSetting.quickTime = this.basicOptions.quickTime;
            this.DRGlobalSetting.period = this.basicOptions.period;
            this.DRGlobalSetting.showType = this.basicOptions.showType;
            this.DRGlobalSetting.statisticGroup = this.basicOptions.statisticGroup;
            this.statsList = {
                block1: null,
                block2: null,
                block3: null,
                block4: null,
            };
            let index = 1;
            for(let key in this.options){
                if (this.options[key].checked === true) {
                    this.options[key].showType = this.basicOptions.showType;
                    this.statsList[`block${index}`] = _.cloneDeep(this.options[key]);
                    index++;
                }
            }
            localStorage.setItem("DRStatsListDemo", JSON.stringify(this.statsList));
            localStorage.setItem("DRGlobalSettingDemo", JSON.stringify(this.DRGlobalSetting));
            this.renderAll();
            this.showSettingDialog = false;
        },
        changeSettingQuickTime(time) {
            this.basicOptions.quickTime = time;
            this.adjustPeriod(time,this.basicOptions)
        },
        toggleCheck(name) {
            const isChecked = this.options[name].checked;
            if (this.checkedOptionsLength < this.maxOptions || isChecked) {
                this.options[name].checked = !isChecked;
            }
        },
        emitBlockData(title,newBlock,save = true){
            console.log('emitBlockData',newBlock)
            for(let key in this.statsList){
                let block = this.statsList[key]
                if (block && block.title === title) {
                    this.statsList[key] = newBlock;
                    break;
                }
            }
            if (save) {
                localStorage.setItem("DRStatsListDemo", JSON.stringify(this.statsList));
            }
        },
        async renderPieData(){
            this.loadingPieData = true;
            const result = {
                data:{
                    error_code:0,
                    data:{}
                }
            }
            this.loadingPieData = false;
            let data = result.data.data;
            result.data.data={
                "adultTotal": 1379,
                "childTotal": 13759,
                "adultData": {
                    "P01": 322,
                    "P02": 225,
                    "P03": 12,
                    "P04": 142,
                    "P05": 43,
                    "P06": 192,
                    "P07": 89,
                },
                "childData": {
                    "P01": 4552,
                    "P02": 195,
                    "P03": 2433,
                    "P04": 963,
                    "P05": 4210,
                    "P06": 365,
                    "P07": 1752,
                }
            }
            this.tempPieData = result;
            this.setPieData();
        },
        handdleTogglePieData(type){
            this.pieType = type;
            this.setPieData();
        },
        setPieData(){
            const result = this.tempPieData;
            for(let item of this.overallDistribution){
                item.value = 0;
            }
            this.adultTotal = 0;
            this.childTotal = 0
            const typeArr = ['P01','P02','P03','P04','P05','P06','P07'];
            const map = result.data.data[this.pieType]
            for(let key in map){
                if (typeArr.indexOf(key)>-1) {
                    for(let item of this.overallDistribution){
                        if(item.key == key){
                            item.value = map[key];
                        }
                    }
                }else{
                    this.overallDistribution[7].value +=map[key];
                }
            }
            this.adultTotal = result.data.data.adultTotal;
            this.childTotal = result.data.data.childTotal;
        },
        renderBlocks(){
            if(this.$refs.block1){
                this.$refs.block1.updateChart();
            }
            if(this.$refs.block2){
                this.$refs.block2.updateChart();
            }
            if(this.$refs.block3){
                this.$refs.block3.updateChart();
            }
            if(this.$refs.block4){
                this.$refs.block4.updateChart();
            }
        },
        async getMapData(data){
            if (data.regionCode === 'china') {
                data.regionCode = 86
            }
            const result = await request.getMapData({
                startTime:this.getRangeTime().startTime,
                endTime:this.getRangeTime().endTime,
                dataFrom:this.query.dataFrom,
                id:this.query.id,
                regionCode:data.regionCode,
                regionType:data.regionType,
                type:this.DRGlobalSetting.statisticGroup,
            })
            for(let key in result.data.data){
                let randomHasData = Math.random()>0.5?true:false;
                if (['110000','330000','350000','350400'].indexOf(key)>-1) {
                    // 北京，浙江，福建，福建三明必须有数据
                    randomHasData=true;
                }
                if (['710000'].indexOf(key)>-1) {
                    // 台湾不显示
                    randomHasData=false;
                }
                result.data.data[key]['2'] = 0
                result.data.data[key]['6'] = 0
                result.data.data[key]['9'] = 0
                if (randomHasData) {
                    result.data.data[key]['11'] = parseInt(Math.random()*500)
                }
                console.log('key',key,result.data.data[key])
            }
            data.callback(result.data); 
        },
        toggleGroup(type){
            this.DRGlobalSetting.statisticGroup = type;
            this.setDefaultStatsList();
            this.renderAll();
        }
    }
}
</script>
<style lang="scss" scoped>
</style>
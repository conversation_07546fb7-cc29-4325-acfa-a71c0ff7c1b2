<template>
    <div class="login_container">
        <div><cookies-notification /></div>
        <div class="header">
            <img class="logo" src="static/resource_activity/images/logob.png">
            <span class="title">{{lang.ecology_title}}</span>
            <div class="menus">
                <el-dropdown trigger="click">
                    <div class="nation_info">
                        <img class="nation_icon" src="static/resource_activity/images/nation.png">
                        <span class="nickname">{{lang.choose_language}}</span>
                    </div>
                    <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item :class="{ 'highlight_item': lang.currentLanguage === 'CN' }" @click.native="changeLanguage('CN')">简体中文</el-dropdown-item>
                        <el-dropdown-item :class="{ 'highlight_item': lang.currentLanguage === 'EN' }" @click.native="changeLanguage('EN')">English</el-dropdown-item>
                        <el-dropdown-item :class="{ 'highlight_item': lang.currentLanguage === 'ES' }" @click.native="changeLanguage('ES')">Español</el-dropdown-item>
                        <el-dropdown-item :class="{ 'highlight_item': lang.currentLanguage === 'RU' }" @click.native="changeLanguage('RU')">Русский язык</el-dropdown-item>
                        <el-dropdown-item :class="{ 'highlight_item': lang.currentLanguage === 'PTBR' }" @click.native="changeLanguage('PTBR')">Português</el-dropdown-item>
                    </el-dropdown-menu>
                </el-dropdown>
            </div>
        </div>
        <div class="login_card">
            <img class="login_bg" src="static/resource_activity/images/login_bg.png">
            <div class="login_card_content">
                <login-form  element-loading-text="" element-loading-background="#fff" v-show="showLoginForm" ref="loginForm"></login-form>
                <router-view></router-view>
            </div>
        </div>
        <div class="footer">
            <span class="protocol" @click="toProtocol">{{lang.privacy_policy}}</span>
            <span class="copyright">{{lang.copyright}}©2024</span>
            <a target="_blank" href="https://beian.miit.gov.cn/#/Integrated/index" class="licence" >粤ICP备05083646号-4</a>
        </div>
    </div>
</template>
<script>
import service from '../../service/service'
// import base from '../../lib/base'
import LoginForm from '../../components/loginForm.vue'
import cookiesNotification from '@/components/login/cookiesNotification.vue'
import { goPrivacyPolicy } from '../../lib/common_base'
export default {
    mixins: [],
    name: 'Login_PC',
    components: {
        cookiesNotification,
        LoginForm,
    },
    data(){
        return {
            showLoginForm:false,
        }
    },
    computed:{
        lang(){
            return this.$store.state.language
        },
        serverInfo() {
            return this.systemConfig.serverInfo
        },
    },
    beforeCreate(){
    },
    created(){
        window.clientType= 1;
        this.$store.commit('systemConfig/updateSystemConfig',{clientType:window.clientType});

        // 获取环境配置
        this.getEnvConfig().then(() => {
            // 获取登录配置
            service.query_login_config().then((res)=>{
                this.showLoginForm = true
                this.$store.commit('systemConfig/updateSystemConfig',{
                    serverInfo:res.data
                })
            })
        })
    },
    mounted(){
        this.$nextTick(()=>{
        })
    },
    destroyed(){
    },

    methods:{
        getEnvConfig(){
            return new Promise((resolve)=>{
                service.getEnvConfig().then((res)=>{
                    console.log("getEnvConfig:", res);
                    // 将getEnvConfig返回的数据存储到store中
                    if (res && res.data) {
                        // 统一存储envConfig数据
                        this.$store.commit("systemConfig/updateSystemConfig", {
                            envConfig: res.data.data
                        });
                    }
                    resolve(res)
                }).catch(e=>{
                    console.warn("getEnvConfig failed, but continuing with initialization:", e)
                    // 即使失败也resolve，确保不影响后续流程
                    resolve(null)
                })
            })
        },
        toProtocol(){
            goPrivacyPolicy()
        },
        changeLanguage(lang){
            window.localStorage.setItem('lang',lang)
            window.location.reload();
        }
    }
}
</script>
<style lang="scss">
.login_container{
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: auto;
    background-color: #e3e6eb;
    user-select: none;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    font-family: MicrosoftYaHei;
    &~.el-message{
        min-width: 300px;
    }
    .header{
        width: 100%;
        height: 56px;
        display: flex;
        align-items: center;
        font-size: 16px;
        .logo{
            width: 136px;
            height: 40px;
            margin-top: 8px;
            margin-left: 32px;
        }
        .title{
            padding-left:16px;
            border-left: 1px solid #999;
            margin-left: 50px;
            line-height: 16px;
        }
        .menus{
            flex:1;
            display: flex;
            height: 100%;
            align-items: center;
            justify-content: flex-end;
            padding-right:56px;
            .nation_info{
                display: flex;
                align-items: center;
                cursor: pointer;
                line-height: 1;
                padding-left: 16px;
                border-left: 1px solid #c1c1c1;
                .nation_icon{
                    width: 20px;
                    height: 20px;
                    margin-right: 8px;
                }
                .nickname{
                    font-size: 16px;
                    color: #000;
                }
            }
        }
    }
    .login_card{
        flex:1;
        position: relative;
        display: flex;
        flex-direction: row;
        align-items: center;
        // padding:20px;
        .login_bg{
            // width: 872px;
            // height: 760px;
        }
        .login_card_content{
            width: 800px;
            min-height: 568px;
            background:#fff;
            box-shadow:0 0 10px 5px rgba(0,0,0,0.05);
            border-radius:10px;
            display: flex;
            overflow: hidden;
        }
        .register_version{
            position: absolute;
            right: 6px;
            bottom: 0;
            color: #999;
            font-size: 14px;
        }
    }
    .footer{
        height: 80px;
        font-size: 16px;
        line-height: 1;
        display: flex;
        align-items: center;
        color: #3b3c3d;
        .protocol{
            cursor: pointer;
            color: #3875fa;
            &:hover{
                color: #2c5ce6;
                text-decoration: underline;
            }
        }
        .copyright{
            padding-left:16px;
            border-left: 1px solid #999;
            margin-left: 16px;
        }
        .licence{
            color: #3b3c3d;
            margin-left: 60px;
        }
    }
    .el-form-item{
        margin-bottom: 0px;
    }
    .el-form-item__error{
        padding-top: 1px;
    }
    .common_btn{
        font-size: 18px;
        line-height: 1;
        background:#3875fa;
        color:#fff;
        text-align: center;
        display: block;
        line-height: 40px;
        border-radius: 6px;
        cursor: pointer;
        margin: 17px 0;
    }
    .ban_btn{
        background:rgba(81,193,165,0.8);
        pointer-events: none;
        cursor: not-allowed;
    }
    .el-checkbox .el-checkbox__input.is-checked .el-checkbox__inner{
        background-color: #3875fa;
        border-color: #3875fa;
    }
    .el-checkbox{
        display: flex;
        align-items: center;
        padding: 2px 0;
        .el-checkbox__input{
            height: 16px;
        }
        .el-checkbox__inner{
            width: 16px;
            height: 16px;
            border-color:#9a9a9a;
        }
        .el-checkbox__inner::after{
            top: 2px;
            left: 6px;
        }
    }
    .el-checkbox__label{
        font-size: 16px;
        color: #424242;
        padding-left: 8px;
        line-height: 1;
    }
    .el-checkbox__input.is-checked+.el-checkbox__label{
        color: #424242;
    }
    .mobile_field{
        input{
            padding-left: 90px;
        }
    }
    &~.el-message-box__wrapper .el-message-box{
        width: 300px;
    }
    .notify-bar{
        flex-shrink: 0;
        position: absolute;
        left: 50px;
        right: 50px;
        top: 20px;
        z-index: 2;
    }
}
#cookiesNotificationContainer{
    .el-dialog{
        width: 80%;
        .el-button--primary{
            background-color:#3875fa;
            border-color:#3875fa;
        }
    }
}
.el-dropdown-menu__item:focus, .el-dropdown-menu__item:not(.is-disabled):hover{
    background:#f0f0f0;
    color: #666;
}
.el-dropdown-menu__item.highlight_item{
    color: #3875fa;
}
</style>

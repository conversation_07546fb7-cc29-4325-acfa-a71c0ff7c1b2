<template>
	<div>
        <div>
            <el-button @click="logout">注销</el-button>
        </div>
        <table border="1">
            <tr>
                <th>用户id</th>
                <th>ip地址</th>
                <th>业务类型</th>
                <th>请求状态</th>
                <th>日期</th>
            </tr>
            <tr v-for="(item, index) in dataList" :key="index">
                <th>{{item.userid}}</th>
                <th>{{item.ip}}</th>
                <th>{{item.description}}</th>
                <th>{{formatStatus(item)}}</th>
                <th>{{formatTime(item)}}</th>
            </tr>
        </table>
        <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-size="pageSize"
            :page-sizes="pageSizeList"
            layout="total, sizes, prev, pager, next, jumper"
            :total="count"
        ></el-pagination>
	</div>
</template>
<script>
import service from '../service/service'
import moment from 'moment'
export default {
    mixins: [],
    name: 'IndexPage',
    components: {},
    data(){
        return {
            dataList: [],
            currentPage: 1,
            totalPage: 0,
            nextPage: 1,
            pageSize: 20,
            count: 0,
            pageSizeList: [20, 50, 100],
        }
    },
    mounted(){
        this.$nextTick(()=>{
            this.getList();
        })
    },
    methods: {
        handleCurrentChange(page) {
            this.currentPage = page;
            this.getList();
        },
        handleSizeChange(pageSize) {
            this.pageSize = pageSize;
            this.currentPage = 1;
            this.getList();
        },
        getList() {
            console.log(this.page)
            service.list({page: this.currentPage, pageSize: this.pageSize}).then((res)=>{
                if (res.data.error_code === 0) {
                    this.dataList = res.data.data.data;
                    this.totalPage = res.data.data.totalPage;
                    this.count = res.data.data.count;
                    this.currentPage = res.data.data.currentPage
                } else {
                    this.$message.error(res.data.error_msg)
                    if (res.data.error_msg === "登陆令牌错误") {
                        this.$router.replace('/login')
                    }
                }
            });
        },
        formatStatus(row) {
            if (row.status === 0) {
                return '请求中'
            }
            if (row.status === 1) {
                return '成功'
            }
            if (row.status === 2) {
                return '失败'
            }
        },
        formatTime(row) {
            return moment(row.created_at).format('YYYY-MM-DD HH:mm:ss z')
        },
        logout(){
            service.logout()
            this.$router.replace('/login')
        }
    },

}
</script>
<style lang="scss">
</style>

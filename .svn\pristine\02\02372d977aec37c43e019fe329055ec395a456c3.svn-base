<template>
    <div class="completed_exam_list">
        <van-search
            :placeholder="lang.homework_search_key"
            v-model="searchKey"
            @input="debounceSearch"
            ></van-search>
        <van-list
          v-model="loadingPage"
          :finished="finished"
          :finished-text="lang.no_more_text"
          @load="onLoad"
        >
            <div class="cloud_exam_item" v-for="exam of examList" :key="exam._id">
                <div class="exam_infomation">
                    <span v-if="exam.status === 3" class="unread"></span>
                    <div class="exam_type_icon">
                        <template v-if="exam.assignmentInfo.paperInfo.contentType">
                            <img class="title_icon" :src="`static/resource/images/homework_type${exam.assignmentInfo.paperInfo.contentType}.png`">
                            <p>{{lang['homework_type'+exam.assignmentInfo.paperInfo.contentType]}}</p>
                        </template>
                        <template v-else>
                            <img class="title_icon" src="static/resource/images/homework_type5.png">
                            <p>{{lang.homework_type5}}</p>
                        </template>
                    </div>
                    <div class="exam_detail" >
                        <div class="exam_title">
                            <p>{{exam.assignmentInfo.paperInfo.title}}</p>
                        </div>
                        <div class="exam_description">
                            <p v-if="exam.status === 3 || exam.status === 4">{{lang.paper_results}}：<span>{{exam.score}}{{lang.point_tip}}</span>/{{exam.assignmentInfo.paperInfo.score}}{{lang.point_tip}}</p>
                            <p v-else>{{lang.paper_results}}：<span>{{lang.correcting}}</span></p>
                            <p>{{lang.deadline}}：{{exam.dueTime | showData}}</p>
                        </div>
                        <div class="exam_description">
                            <p>{{lang.paper_duration}}：{{exam.useTime | useTime}}</p>
                            <p>{{lang.submission_time}}：{{exam.submitAt | showData}}</p>
                        </div>
                    </div>
                </div>
                <div class="exam_btns">
                    <van-button class="gradient_btn" :disabled="!exam.assignmentInfo.resultCanChecked || (exam.status!==3&&exam.status!==4)"  @click="ExamDetail(exam._id)">{{lang.view_details}}</van-button>
                    <van-button class="primary_btn" @click="openStatistics(exam)" >{{lang.paper_statistics}}</van-button>
                </div>
            </div>
        </van-list>
    </div>
</template>
<script>
import base from '../../lib/base';
import {formatDurationTime} from '../../lib/common_base';
import service from '../../service/service.js'
import moment from 'moment';
import { List , Button, Search } from 'vant';
import Tool from '@/common/tool.js'
export default {
    mixins: [base],
    name: 'cloudExamList',
    components: {
        vanList:List,
        vanButton:Button,
        vanSearch:Search
    },
    data(){
        return {
            cid:0,
            queryForm:{
                pageNo:1,
                pageSize:20,
            },
            examListTotal:20,
            examList:[],
            loadingPage:false,
            finished:false,
            searchKey:'',
            debounceSearch:null,
        }
    },
    computed:{
    },
    filters:{
        useTime(duration){
            return formatDurationTime(duration)
        },
        showData(ts){
            return moment(ts).format("YYYY-MM-DD HH:mm")
        }
    },
    created(){
        this.cid = parseInt(this.$route.params.cid);
        this.debounceSearch = Tool.debounce(this.refreshCompletePaper,600);
    },
    mounted(){
        this.$root.eventBus.$off("refreshCompletePaper").$on("refreshCompletePaper",this.refreshCompletePaper);
        this.fetchData()
    },
    watch:{
        
    },
    methods:{
        onLoad() {
            this.queryForm.pageNo = this.queryForm.pageNo += 1;
            this.fetchData()
        },
        refreshCompletePaper(){
            this.examList = [];
            this.queryForm.pageNo = 1;
            this.fetchData()
        },
        fetchData(){
            this.loadingPage = true;
            service.getFinishedList({
                gid:this.cid,
                page:this.queryForm.pageNo,
                pageSize:this.queryForm.pageSize,
                searchKey:this.searchKey,
            }).then(res=>{
                this.loadingPage = false;
                if (res.data.error_code === 0) {
                    this.examListTotal = res.data.data.total;
                    this.examList = this.examList.concat(res.data.data.data);
                    if (this.examList.length === this.examListTotal) {
                        this.finished = true;
                    }else{
                        this.finished = false;
                    }
                    // 检查是否有status=3的作业，如果有则更新全局状态
                    const status3Exams = this.examList.filter(item => item.status === 3);
                    const correctedCount = status3Exams.length;
                    
                    if (this.cid === 0) {
                        this.$store.commit("homework/updateHomework", {
                            globalCorrected: correctedCount > 0 ? correctedCount : 0
                        });
                    } else {
                        let obj = {};
                        if (correctedCount > 0) {
                            obj[this.cid] = status3Exams;
                        } else {
                            obj[this.cid] = undefined;
                        }
                        this.$store.commit("homework/updateCorrected", obj);
                    }
                }
            })
        },
        enterExam(exam){
            // this.$store.commit('homework/setCurrentAssignment',exam)
            this.$router.push({
                path: `${this.$route.path}/exam/2/${exam._id}`,
                query: this.$route.query,
            });
        },
        ExamDetail(id){
            // 查找当前作业
            const exam = this.examList.find(item => item._id === id);
            console.log('当前cid:', this.cid);
            if (exam && exam.status === 3) {
                // 手动更新本地状态
                exam.status = 4;
                if(isNaN(this.cid)){
                    // 更新全局状态
                    const remainingCorrectedCount = this.examList.reduce((count, item) => count + (item.status === 3 ? 1 : 0), 0);
                    this.$store.commit("homework/updateHomework", {
                        globalCorrected: remainingCorrectedCount
                    });
                    //从conversationCorrected中删除当前作业
                    for (const conversationId in this.$store.state.homework.conversationCorrected) {
                        const conversationExams = this.$store.state.homework.conversationCorrected[conversationId];
                        if (conversationExams && Array.isArray(conversationExams)) {
                            // 检查当前会话是否包含此作业
                            const examIndex = conversationExams.findIndex(item => item._id === id);
                            if (examIndex !== -1) {
                                // 排除当前作业
                                const updatedExams = [...conversationExams];
                                updatedExams.splice(examIndex, 1);
                                
                                // 更新会话状态
                                let obj = {};
                                if (updatedExams.length > 0) {
                                    obj[conversationId] = updatedExams;
                                } else {
                                    obj[conversationId] = undefined;
                                }
                                this.$store.commit("homework/updateCorrected", obj);
                            }
                        }
                    }
                }else if(this.cid !== 0){
                    // 更新会话状态
                    let obj = {};
                    const status3Exams = this.examList.filter(item => item.status === 3); //群里的未读已批改数量
                    if (status3Exams.length > 0) {
                        obj[this.cid] = status3Exams;
                    } else {
                        obj[this.cid] = undefined;
                    }
                    this.$store.commit("homework/updateCorrected", obj);
                    // 将globalCorrected减1但不小于0
                    const currentGlobalCorrected = this.$store.state.homework.globalCorrected;
                    if (currentGlobalCorrected !== undefined && currentGlobalCorrected > 0) {
                        this.$store.commit("homework/updateHomework", {
                            globalCorrected: currentGlobalCorrected - 1
                        });
                    }
                }
            }
            this.$router.push({
                path: `${this.$route.path}/exam/4/${id}`,
                query: this.$route.query,
            });
        },
        openStatistics(exam){
            this.$store.commit('homework/setCurrentPaper',exam.assignmentInfo);
            this.$router.push({
                path: `${this.$route.path}/exam_statistics/${exam.assignmentID}`,
                query: this.$route.query,
            });
        },
    },
    destroyed(){
    },

}

</script>
<style lang="scss">
.completed_exam_list{
    
}
</style>

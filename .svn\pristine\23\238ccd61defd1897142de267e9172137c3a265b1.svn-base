<template>
    <div class="create_multicenter_page">
        <h1 class="create_title">{{lang.create_multicenter}}</h1>
        <div class="create_detail">
            <multicenter-detail ref="multicenterDetail"></multicenter-detail>
        </div>
        <div class="create_footer">
            <el-button class="close_create" v-if="type === 1" @click="back" size="large" type="default">{{lang.cancel_btn}}</el-button>
            <el-button class="close_create" v-if="type === 2" @click="deleteApply" size="large" type="default">{{lang.action_delete_text}}</el-button>
            <el-button class type="primary" @click="apply" v-if="type === 1">{{lang.apply_btn}}</el-button>
            <el-button class type="primary" @click="apply" v-if="type === 2">{{lang.reapply_btn}}</el-button>
        </div>
    </div>
</template>
<script>
import base from '../../../lib/base'
import service from '../../../service/multiCenterService.js'
import multicenterDetail from '../../../components/genericMulticenter/multicenterDetail.vue'
export default {
    mixins:[base],
    components:{multicenterDetail},
    name: 'create_generic_multicenter',
    data(){
        return {
            type:parseInt(this.$route.params.type),
        }
    },
    computed: {
        currentMulticenter() {
            return this.$store.state.multicenter.currentMulticenter || {};
        },
    },
    mounted(){
        if (this.type === 2) {
            this.$refs.multicenterDetail.initDetail(this.currentMulticenter,true);
        }
    },
    updated(){
    },
    methods:{
        apply(){
            const data = this.$refs.multicenterDetail.getDetailData();
            if (data) {
                let method = 'applyMulticenter';
                let params = data;
                if (this.type ===2) {
                    method = 'updateMulticenter';
                    params = {
                        mcID: this.currentMulticenter.id,
                        data:data
                    }
                }
                service[method](params).then((res)=>{
                    if (res.data.error_code == 0) {
                        this.$message.success(this.lang.operate_success)
                        this.$root.eventBus.$emit('refreshMultiCenterList')
                        this.back();
                    }
                })
            }
        },
        deleteApply(){
            this.$MessageBox.confirm(this.lang.delete_apply_multicenter_tips, this.lang.tip_title, {
                confirmButtonText: this.lang.confirm_txt,
                callback: action => {
                    if(action==='confirm'){
                        service.deleteApplyMulticenter({mcID:this.currentMulticenter.id}).then((res)=>{
                            if (res.data.error_code == 0) {
                                this.$message.success(this.lang.operate_success)
                                this.$root.eventBus.$emit('refreshMultiCenterList')
                                this.back();
                            }
                        })
                    }
                }
            });

        }
    }
}
</script>
<style lang="scss">
.create_multicenter_page{
    position: absolute;
    width: 100%;
    height: 95%;
    overflow: auto;
    top: 0;
    left: 0;
    z-index: 2;
    background: #fff;
    display: flex;
    flex-direction: column;
    .create_title{
        text-align: center;
        margin-bottom: 40px;
    }
    .create_detail{
        flex:1;
    }
    .create_footer{
        text-align: center;
        .close_create{
            margin-right: 40px;
        }
        button{
            padding: 12px 50px;
        }
    }
}
</style>

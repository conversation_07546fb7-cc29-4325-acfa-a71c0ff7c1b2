import cloneDeep from 'lodash/cloneDeep'
import Vue from 'vue'
import Tool from '@/common/tool.js'


const initState ={
    list:[],
    lastMessage:{},
    unreadMap:{}
}
export default {
    state: cloneDeep(initState),
    mutations: {
        resetStore(state){
            if(window.vm.$store.state){
                Vue.set(window.vm.$store.state,'chatList',cloneDeep(initState))
            }
        },
        addChatList(state, obj) {
            obj.unread=0
            state.list.push(obj);
            Vue.set(state.lastMessage,obj.cid,{})
        },
        addAndTopChat(state,obj){
            obj.unread=0
            state.list.unshift(obj)
            Vue.set(state.lastMessage,obj.cid,{})
        },
        setTopChat(state,cid){
            if(!cid || cid ==='0'){
                return
            }
            // console.error(msg)
            for(let i=0;i<state.list.length;i++){
                let item=state.list[i]
                if (String(item.cid)===String(cid)) {
                    //接收到消息置顶聊天
                    item.last_message_ts=new Date().valueOf()
                    state.list.splice(i,1)
                    state.list.unshift(item);
                    break;
                }
            }
        },
        initChatList(state,arr){
            let oldList = [...state.list];
            state.list.splice(0);
            for(let item of arr){
                item.unread=0
                item.clamped=false;
                item.message={}
                state.list.push(item)
                if (!state.lastMessage[item.cid]) {
                    //重连时不重置最近消息
                    Vue.set(state.lastMessage,item.cid,{})
                }
            }
        },
        clearChatList(state){
            state.list.splice(0)
        },
        setLastMessage(state,obj){
            for(let chat of state.list){
                if (chat.cid==obj.cid) {
                    obj.message.showTime=Tool.getShowTime(obj.message)
                    Vue.set(state.lastMessage,chat.cid,obj.message)
                    break;
                }
            }
        },
        addMessage(state,msg){
            for(let i=0;i<state.list.length;i++){
                let item=state.list[i]
                if (msg.group_id==item.cid ) {
                    let message=state.lastMessage[item.cid]
                    if (message && message.gmsg_id && msg.gmsg_id < message.gmsg_id) {
                        break;
                    }
                    //接收到消息置顶聊天
                    msg.showTime=Tool.getShowTime(msg)
                    item.last_message_ts=msg.send_ts?new Date(msg.send_ts).getTime():new Date().valueOf()
                    Vue.set(state.lastMessage,item.cid,msg)
                    state.list.splice(i,1)
                    state.list.unshift(item);
                    break;
                }
            }
        },
        addMessageNoSort(state,msg){
            for(let i=0;i<state.list.length;i++){
                let item=state.list[i]
                if (msg.group_id==item.cid ) {
                    let message=state.lastMessage[item.cid]
                    if (message && message.gmsg_id && msg.gmsg_id <= message.gmsg_id) {
                        break;
                    }
                    //接收到消息置顶聊天
                    msg.showTime=Tool.getShowTime(msg)
                    item.last_message_ts=msg.send_ts?new Date(msg.send_ts).getTime():new Date().valueOf()
                    Vue.set(state.lastMessage,item.cid,msg)
                    break;
                }
            }
        },
        sortChatList(state){
            state.list.sort((a, b)=>{
                if(a.last_message_ts&&b.last_message_ts){
                    return b.last_message_ts - a.last_message_ts;
                }else{
                    return 0
                }
            })
        },
        updateFriendToChatList(state,user){
            for(let i=0;i<state.list.length;i++){
                let chat=state.list[i]
                if(chat.fid==user.id){
                    chat.avatar=user.avatar
                    if(chat.hasOwnProperty('nickname')){
                        chat.nickname = user.nickname
                    }
                    chat.subject=user.nickname
                    chat.state=user.state
                    state.list.splice(i,1,chat)
                    // break;
                }
                if (state.lastMessage[chat.cid].sender_id==user.id) {
                    state.lastMessage[chat.cid].nickname=user.nickname
                }
            }
        },
        updateUnReadMap(state,data){
            if(Array.isArray(data)){
                data.forEach(item=>{
                    Vue.set(state.unreadMap,Number(item.cid),item.unReadCount)
                })
            }
        },
        addUnread(state,{group_id}){
            if(!state.unreadMap.hasOwnProperty(Number(group_id))){
                Vue.set(state.unreadMap,Number(group_id),0)
            }
            state.unreadMap[Number(group_id)]++
        },
        clearUnread(state,group_id){
            //清除未读标记
            Vue.set(state.unreadMap,Number(group_id),0)
        },
        updateSubjectToChatList(state,data){
            for(let i=0;i<state.list.length;i++){
                let chat=state.list[i]
                if(chat.cid==data.cid){
                    chat.subject=data.subject
                    state.list.splice(i,1,chat)
                    break;
                }
            }
        },
        deleteChatList(state, data){
            for(let i=0;i<state.list.length;i++){
                let chat=state.list[i]
                if(chat.cid==data.cid){
                    state.list.splice(i,1);
                    break;
                }
            }
        },
        setMention(state,data){
            //清除未读标记
            for(let chat of state.list){
                if(chat.cid==data.group_id){
                    chat.mentionStatus=true;
                    break;
                }
            }
        },
        clearMention(state,cid){
            //清除未读标记
            for(let chat of state.list){
                if(chat.cid==cid){
                    chat.mentionStatus=false;
                    break;
                }
            }
        },
        updateMuteToChatList(state,data){
            for(let i=0;i<state.list.length;i++){
                let chat=state.list[i]
                if(chat.cid==data.cid){
                    chat.is_mute=data.is_mute
                    state.list.splice(i,1,chat)
                    break;
                }
            }
        },
        updateChatAvatarLocalUrl(state,data){
            let imgObj=data.imgObj
            console.log('updateChatAvatarLocalUrl',data)
            for(let index=0;index<state.list.length;index++){
                if (imgObj.cid==state.list[index].cid) {
                    let item=state.list[index]
                    // item.avatar_local=data.avatar_local
                    if (data.avatar) {
                        item.avatar=data.avatar
                    }
                    state.list.splice(index,1,item)
                    break;
                }
            }
        },
        updateLastChatMessageByResourceDelete(state,obj){
            if(obj.cid&&state.lastMessage[obj.cid]){
                if(obj.data){
                    if(obj.data.resource_id === state.lastMessage[obj.cid].resource_id){
                        Vue.set(state.lastMessage[obj.cid],'msg_type',obj.data.msg_type)
                    }
                }
            }
        }
    },
    actions: {},
    getters: {}
}

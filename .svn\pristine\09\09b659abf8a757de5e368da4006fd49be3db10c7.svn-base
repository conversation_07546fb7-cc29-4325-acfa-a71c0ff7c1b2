<template>
    <div>
        <!-- <el-dialog
            class="groupset_setting_page"
            :title="lang.edit_group_set"
            :visible="true"
            :close-on-click-modal="false"
            :append-to-body="true"
            width="640px"
            :modal="false"
            v-loading="loadingDetail"
            :before-close="back"> -->
            <CommonDialog
            class="groupset_setting_page"
            :title="lang.edit_group_set"
            :show.sync="visible"
            :close-on-click-modal="false"
            :append-to-body="true"
            width="640px"
            :modal="false"
            v-loading="loadingDetail"
            @closed="back"
            :footShow="false"
            >
                <div>
                    <div class="groupset_info_wrap">
                        <div class="groupset_avatar" @click="modifyAvatar">
                            <mr-avatar :url="getLocalAvatar(currentGroupset)" :key="currentGroupset.avatar" :radius="100"></mr-avatar>
                        </div>
                        <div class="groupset_info">
                            <div class="groupset_subject">
                                <el-input v-model="subject" maxlength="50" v-loading="loadingSubject" @blur="changeSubject"></el-input>
                            </div>
                            <div>
                                <el-input v-model="description" maxlength="300" v-loading="loadingDescription" @blur="changeDescription" :placeholder="lang.no_description_tip"></el-input>
                            </div>
                        </div>
                    </div>
                    <div class="groupset_attendee">
                        <p class="">{{lang.groupset_navbar}}</p>
                        <div class="groupset_attendee_list">
                            <div class="groupset_attendee_item" v-for="group of currentGroupset.groupList" :key="group.id" @click="openGroup(group)">
                                <mr-avatar :url="getLocalAvatar(group)" :key="group.avatar" :radius="80"></mr-avatar>
                                <p class="groupset_subject longwrap" :title="remarkMap[group.fid]||group.subject">{{remarkMap[group.fid]||group.subject}}</p>
                            </div>
                            <div class="groupset_attendee_item">
                                <i class="iconfont iconuser-round-add" @click="addGroupsetAttendee"></i>
                            </div>
                            <div class="groupset_attendee_item">
                                <i class="iconfont iconuser-delete" @click="deleteGroupsetAttendee"></i>
                            </div>
                        </div>
                    </div>
                </div>
        </CommonDialog>
        <!-- </el-dialog> -->
    </div>

</template>
<script>
import base from '../lib/base'
import {parseSingleChat,getLocalAvatar} from '../lib/common_base'
import CommonDialog from "../MRComponents/commonDialog.vue";

export default {
    mixins: [base],
    name: 'groupsetSetting',
    components: {
        CommonDialog
    },
    data(){
        return {
            getLocalAvatar,
            visible: false,
            loadingDetail:false,
            loadingSubject:false,
            loadingDescription:false,
            subject:'',
            description:'',
            tempGid:0,
        }
    },
    computed:{
        groupset_id(){
            let id=this.$route.params.groupset_id;
            return id
        },
        details(){
            return this.$store.state.groupset.details
        },
        currentGroupset(){
            let detail=this.details[this.groupset_id]
            if (!detail||!detail.hasOwnProperty('groupList')) {
                detail={}
                this.initDetail()
            }
            this.subject=detail.subject;
            this.description=detail.description;
            return detail
        },
        remarkMap(){
            return this.$store.state.friendList.remarkMap;
        }
    },
    mounted(){
        this.$nextTick(() => {
            this.visible = true;
        });
    },
    methods:{
        initDetail(){
            if (this.groupset_id==0||this.tempGid==this.groupset_id) {
                return ;
            }
            this.loadingDetail=true;
            this.tempGid=this.groupset_id;
            window.main_screen.getGroupsetDetail({
                groupSetID:this.groupset_id
            },(data)=>{
                this.loadingDetail=false;
                console.log('data',data)
                if (data.error_code==0) {
                    data.data.groupList=parseSingleChat(data.data.groupList)
                    data.data.type=3
                    this.$store.commit('groupset/updateGroupsetDetail',data.data)
                }
            })
        },
        addGroupsetAttendee(){
            const id=this.groupset_id;
            this.$router.push(`/main/index/chat_window/0/groupset_wall/${id}/edit_groupset/2/2`)
        },
        deleteGroupsetAttendee(){
            const id=this.groupset_id;
            this.$router.push(`/main/index/chat_window/0/groupset_wall/${id}/edit_groupset/3/2`)
        },
        changeSubject(){
            if (this.subject=='') {
                this.subject=this.currentGroupset.subject;
            } else if (this.subject==this.currentGroupset.subject) {
                return ;
            } else{
                let parmas={
                    groupSetID:this.currentGroupset.id,
                    subject:this.subject,
                }
                this.loadingSubject=true;
                window.main_screen.updateGroupset(parmas,(data)=>{
                    this.loadingSubject=false
                    console.log('data',data)
                    if (data.error_code==0) {
                        this.$store.commit('groupset/updateGroupsetDetail',data.data)
                    }
                })
            }
        },
        changeDescription(){
            if (this.description==this.currentGroupset.description) {
                return ;
            }
            let parmas={
                groupSetID:this.currentGroupset.id,
                description:this.description,
            }
            this.loadingDescription=true;
            window.main_screen.updateGroupset(parmas,(data)=>{
                this.loadingDescription=false
                console.log('data',data)
                if (data.error_code==0) {
                    this.$store.commit('groupset/updateGroupsetDetail',data.data)
                }
            })
        },
        modifyAvatar(){
            const id=this.groupset_id;
            this.$router.push(`/main/index/chat_window/0/groupset_wall/${id}/groupset_avatar`)
        },
        openGroup(group){
            this.openConversation(group.id,6)
        },
    }
}
</script>
<style lang="scss">
.groupset_setting_page{
    .el-dialog__body{
        display:flex;
        flex-direction: column;
        .groupset_info_wrap{
            display: flex;
            padding: 20px 10px;
            .groupset_avatar{
                margin-right: 20px;
                cursor: pointer;
            }
            .groupset_info{
                flex:1;
                .groupset_subject{
                    width: 300px;
                    margin-bottom: 10px;
                    margin-top: 10px;
                }
            }
        }
        .groupset_attendee{
            .groupset_attendee_list{
                display: flex;
                flex-wrap: wrap;
            }
            .groupset_attendee_item{
                width: 100px;
                text-align: center;
                cursor: pointer;
                margin: 10px;
                .avatar_wrapper{
                    margin:0 auto;
                }
                .groupset_subject{
                    font-size: 16px;
                    margin-top: 10px;
                }
                .iconfont{
                    font-size: 60px;
                    color: #666;
                }
            }
        }
    }
}
</style>

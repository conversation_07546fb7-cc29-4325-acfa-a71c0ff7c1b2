<template>
    <div v-if="isShowTransmit" class="transmit_page third_level_page">
        <mrHeader>
            <template #title>
                {{title}}
            </template>
        </mrHeader>
        <div class="transmit_container">
            <p v-show="choosenChatList.length>0"  class="share_risk_content">{{lang.share_risk_content}}</p>
            <div class="search_container">
                <input type="text" :placeholder="lang.search" @input="search" v-model="searchText" />
            </div>
            <div :class="[checkOnlyList?'quick_only_box':'quick_multiple_box']">
                <div v-show="choosenChatList.length>0" class="quick_recent_item">
                    <p class="title">{{lang.recent_chat_text}} </p>
                    <div class="transmit_list">
                        <div v-for="(chatItem,index) of choosenChatList" @click="submit(chatItem,1)" class="transmit_item clearfix" :key="index">
                            <mr-avatar :url="getLocalAvatar(chatItem)" :origin_url="chatItem.avatar" :showOnlineState="false" :key="chatItem.avatar"></mr-avatar>
                            <p class="fl transmit_subject">{{getSubject(chatItem)}}</p>
                        </div>
                    </div>
                </div>
                <div v-show="choosenFriendList.length>0" class="quick_recent_item">
                    <p class="title">{{lang.contact_text}}</p>
                    <div class="transmit_list">
                        <div v-for="(chatItem,index) of choosenFriendList" @click="submit(chatItem,2)" class="transmit_item clearfix" :key="index">
                            <mr-avatar :url="getLocalAvatar(chatItem)" :origin_url="chatItem.avatar" :showOnlineState="false" :key="chatItem.avatar"></mr-avatar>
                            <p class="fl transmit_subject">{{chatItem.nickname}}</p>
                        </div>
                    </div>
                </div>
                <div v-show="choosenGroupList.length>0" class="quick_recent_item">
                    <p class="title">{{lang.group}}</p>
                    <div class="transmit_list">
                        <div v-for="(chatItem,index) of choosenGroupList" @click="submit(chatItem,3)" class="transmit_item clearfix" :key="index">
                            <mr-avatar :url="getLocalAvatar(chatItem)" :origin_url="chatItem.avatar" :showOnlineState="false" :key="chatItem.avatar"></mr-avatar>
                            <p class="fl transmit_subject">{{chatItem.subject}}</p>
                        </div>
                    </div>
                </div>
            </div>
            <p v-if="choosenChatList.length==0&&choosenFriendList.length==0&&choosenGroupList.length==0" class="no_search_data">
                {{lang.no_search_data}}
            </p>
        </div>
    </div>
</template>
<script>
import base from '../lib/base'
import Tool from '@/common/tool'
import {cloneDeep} from 'lodash'
import {getLocalAvatar} from "../lib/common_base"
export default {
    mixins: [base],
    name: 'TransmitComponent',
    components: {},
    data(){
        return {
            getLocalAvatar,
            isShowTransmit:false,
            choosenChatList:[],
            choosenFriendList:[],
            choosenGroupList:[],
            // plainChatList: [],
            searchText:'',
            callback:'',
            disableChat:false,
            disableFriend:false,
            disableGroup:false,
        }
    },
    beforeDestroy(){
    },
    mounted(){
        this.$nextTick(()=>{
            this.$root.eventBus.$off('initTransmit').$on('initTransmit',this.initTransmit);
            this.$root.eventBus.$off('destroyTransmit').$on('destroyTransmit',this.destroyTransmit);
            this.$root.eventBus.$off('setTransmitCallback').$on('setTransmitCallback',this.setTransmitCallback);
        })
    },
    computed:{
        title(){
            let title=this.$route.meta.title||'transmit_title'
            return this.lang[title];
        },
        chatList(){
            return this.filterEnableList(this.$store.state.chatList.list)
        },
        friendList(){
            return this.filterEnableList(this.$store.state.friendList.list)
        },
        groupList(){
            return this.filterEnableList(this.$store.state.groupList)
        },
        remarkMap(){
            return this.$store.state.friendList.remarkMap;
        },
        checkOnlyList(){
            let chatNum = this.choosenChatList.length>0?1:0
            let friendNum = this.choosenFriendList.length>0?1:0
            let groupNum = this.choosenGroupList.length>0?1:0
            if(( chatNum+ friendNum + groupNum) === 1){
                return true
            }else{
                return false
            }
        }
    },
    methods:{
        getSubject(item){
            if(item.service_type===0){
                return item.subject
            }else if(item.service_type===this.systemConfig.ServiceConfig.type.FileTransferAssistant){
                return this.lang.file_transfer_assistant
            }else{
                return item.subject
            }
        },
        initTransmit(){
            this.choosenChatList=this.setChatListRemark(this.chatList);
            this.callback=this.$route.meta.callback
            this.disableChat=this.$route.meta.disableChat || false
            this.disableFriend=this.$route.meta.disableFriend || false
            this.disableGroup=this.$route.meta.disableGroup || false
            this.isShowTransmit=true;
            this.search();
        },
        destroyTransmit(){
            this.searchText='';
            this.choosenChatList=[];
            this.choosenFriendList=[];
            this.choosenGroupList=[];
            // this.plainChatList = []
            this.isShowTransmit=false;
        },
        chatListFilter(list){
            let new_list = []
            new_list=list.reduce((k,v)=>{
                if(v.service_type!==this.systemConfig.ServiceConfig.type.AiAnalyze&&v.service_type!==this.systemConfig.ServiceConfig.type.DrAiAnalyze){ //文件传输助手不允许转发
                    k.push(v)
                }
                return k
            },[])
            return new_list
        },
        search(){
            // 去除首尾空格
            let keyword = this.searchText.trim();
            // 初始化搜索结果数组
            this.choosenChatList = [];
            this.choosenFriendList = [];
            this.choosenGroupList = [];

            // 空关键词：显示所有数据
            if(keyword === ''){
                if(!this.disableChat) {
                    // 使用 setChatListRemark 来处理备注替换以及过滤
                    this.choosenChatList = this.chatListFilter(this.chatList);
                }
                if(!this.disableFriend) {
                    // 对好友列表，先把 alias 替换到 nickname
                    const processedFriends = this.friendList.map(friend => {
                        return Object.assign({}, friend, { nickname: friend.alias || friend.nickname });
                    });
                    // 先使用chatListFilter过滤，然后再额外过滤掉文件传输助手
                    const filteredFriends = this.chatListFilter(processedFriends).filter(friend =>
                        friend.service_type !== this.systemConfig.ServiceConfig.type.FileTransferAssistant
                    );
                    this.choosenFriendList = filteredFriends;
                }
                if(!this.disableGroup) {
                    this.choosenGroupList = this.chatListFilter(this.groupList);
                }
            } else {
                // 非空关键词：先转为小写用于不区分大小写的匹配
                let lowerKeyword = keyword.toLowerCase();

                if(!this.disableChat) {
                    // 处理会话列表（同时进行备注替换）
                    const processedChats = this.chatList.map(chat => {
                        let clone = Object.assign({}, chat);
                        let alias = this.remarkMap[clone.fid];
                        if(alias){
                            clone.subject = alias;
                        }
                        return clone;
                    });
                    const filteredChats = processedChats.filter(chat => {
                        return this.getSubject(chat).toLowerCase().indexOf(lowerKeyword) !== -1;
                    });
                    this.choosenChatList = this.chatListFilter(filteredChats);
                }

                if(!this.disableFriend) {
                    // 处理好友列表
                    const processedFriends = this.friendList.map(friend => {
                        return Object.assign({}, friend, { nickname: friend.alias || friend.nickname });
                    });
                    const filteredFriends = processedFriends.filter(friend => {
                        return friend.nickname.toLowerCase().indexOf(lowerKeyword) !== -1;
                    });
                    // 先使用chatListFilter过滤，然后再额外过滤掉文件传输助手
                    this.choosenFriendList = this.chatListFilter(filteredFriends).filter(friend =>
                        friend.service_type !== this.systemConfig.ServiceConfig.type.FileTransferAssistant
                    );
                }

                if(!this.disableGroup) {
                    // 处理群组列表
                    const filteredGroups = this.groupList.filter(group => {
                        return group.subject.toLowerCase().indexOf(lowerKeyword) !== -1;
                    });
                    this.choosenGroupList = this.chatListFilter(filteredGroups);
                }
            }
        },
        submit(item,type){
            let that=this;
            let target={

            }
            if (type==1) {
                target.subject=item.subject
                target.cid=item.cid
                if(item.is_single_chat){
                    target.from = 'friend'
                    target.uid = item.fid
                }else{
                    target.from = 'group'
                }
            }else if(type==2){
                target.subject=item.nickname
                target.id=item.id
                target.from = 'friend'
                target.uid = item.id
            }else if(type==3){
                target.subject=item.subject
                target.cid=item.id
                target.from = 'group'
            }

            let tip=this.$route.meta.tip||'transmit_comfirm_msg'
            let message=this.lang[tip]+target.subject;
            Tool.openMobileDialog(
                {
                    message: message,
                    showRejectButton:true,
                    confirm:()=>{
                        if (!item.cid&&item.nickname) {
                        //从联系人点击的单聊，从chatlist中寻找cid
                            for(let chatItem of this.chatList){
                                if (item.id==chatItem.fid) {
                                    target.cid=chatItem.cid;
                                    break;
                                }
                            }
                        }else if(!item.cid&&item.is_single_chat==0){
                            target.cid=item.id;
                        }else{
                            target.cid=item.cid;
                        }
                        that.$root.eventBus.$emit(that.callback,target)

                        that.back();
                    }
                }
            )
        },
        isGroupMember(group, groupList) {
            // debugger
            if(group.type !== 2) { // 不是群，直接false
                return false
            }
            for(const item of groupList) {
                if(item.id == group.cid) {
                    return true
                }
            }
            return false
        },
        isFriend(person, friendList) {
            // debugger
            if(person.type !== 1) { // 不是单聊，直接false
                return false
            }
            for(const item of friendList) {
                if(item.id == person.fid) {
                    return true
                }
            }
            return false
        },
        // filterGroupset(ochatList, filter) {
        //     let chatList = cloneDeep(ochatList)
        //     let filterObj = filter || {}
        //     for(const item of chatList) {
        //         if(item.service_type===100){
        //             continue
        //         }
        //         // 如果是群落，深度遍历
        //         if(item.type === 3 && item.list.length > 0) {
        //             for(const chat of item.list) {
        //                 if(chat.cid in filterObj){ // 去重
        //                     continue
        //                 }
        //                 if(chat.type === 3 && item.list.length > 0) { // 如果还是群落，则继续深度遍历
        //                     this.filterGroupset(chat.list, filterObj)
        //                 }else{
        //                     const isGroupMember = this.isGroupMember(chat, this.groupList)
        //                     const isFriend = this.isFriend(chat, this.friendList)
        //                     if(isGroupMember || isFriend) {
        //                         this.plainChatList.push(chat)
        //                         filterObj[chat.cid] = ""
        //                     }
        //                 }
        //             }
        //         }else{
        //             // 单聊或群聊，判断自己是否属于该群群成员
        //             if(item.cid in filterObj){ // 去重
        //                 continue
        //             }
        //             const isGroupMember = this.isGroupMember(item, this.groupList)
        //             const isFriend = this.isFriend(item, this.friendList)
        //             if(isGroupMember || isFriend) {
        //                 this.plainChatList.push(item)
        //                 filterObj[item.cid] = ""
        //             }

        //         }

        //     }
        //     return this.plainChatList
        // },
        back() {
            this.$router.back()
            // this.plainChatList = []
        },
        setChatListRemark(chatList) {
            let arr = [];
            for(let chat of chatList){
                let chatItem=Object.assign({},chat);
                let alias = this.remarkMap[chatItem.fid]
                if (alias) {
                    chatItem.subject = alias;
                }
                if(chatItem.service_type===this.systemConfig.ServiceConfig.type.AiAnalyze||chatItem.service_type===this.systemConfig.ServiceConfig.type.DrAiAnalyze){
                    //不可以转发给小麦同学
                    continue
                }
                arr.push(chatItem);
            }
            return arr;
        },
        filterEnableList(list){
            return list.filter(item=>(item.user_status!==this.systemConfig.userStatus.Destroy))
        },
        setTransmitCallback(data){
            this.$route.meta.callback = data.callback || this.$route.meta.callback;
            this.$route.meta.comfirm_msg = data.comfirm_msg || '';
        },
    }
}

</script>
<style lang="scss">
.transmit_page{
    z-index:910;
    color:#333;
    .transmit_container{
        background:#fff;
        flex: 1;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        .share_risk_content{
            font-size: 0.6rem;
            background: #fef1ef;
            color: #ff675c;
            padding: .4rem .6rem;
        }
        .title{
            font-size: .7rem;
            line-height: 1.6;
            background-color: #dfe3e6;
            padding: .2rem .5rem;
            color: #000;
            margin-bottom: .2rem;
        }
        .search_container{
            padding:0.6rem;
            input{
                width: 100%;
                height: 100%;
                display: block;
                font-size: 0.8rem;
                padding: 0.3rem 0.8rem;
                box-sizing: border-box;
                border: none;
                background: #f2f6f9;
                border-radius: 0.8rem;
            }
        }
        .quick_only_box{
            flex: 1;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            padding: 0 .4rem .3rem;
            .quick_recent_item{
                flex: 1;
                overflow: hidden;
                display: flex;
                flex-direction: column;
                .transmit_list{
                    overflow: auto;
                }
            }
        }
        .quick_multiple_box{
            flex: 1;
            overflow: auto;
        }
        .transmit_list{
            flex: 1;
            .transmit_item{
                padding: 0.3rem;
                display:flex;
                align-items: center;
                &>p{
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    overflow: hidden;
                    font-size:0.9rem;
                    line-height: 3rem;
                    color: #666;
                    border-bottom: 1px solid #efefef;
                    margin-left: .8rem;
                    flex:1;
                }
            }
        }
        .no_search_data{
            padding: 0.3rem;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%,-50%);
            width: 100%;
            text-align: center;
        }
    }
}
</style>

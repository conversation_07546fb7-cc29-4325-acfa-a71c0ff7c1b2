<template>
    <div
        class="line_chart"
        ref="line_chart_dom"
        :style="{
            width: width + '%',
            height: height + '%',
        }"
    ></div>
</template>

<script>
import base from '../../lib/base'
import _ from "lodash";
import * as echarts from "echarts";
export default {
    mixins: [base],
    name: "bi_line_chart",
    data: function () {
        return {
            lineCharts: {},
        };
    },
    props: {
        data: {
            required: true,
            type: Object,
            default:()=>{
                return {
                    xAxis: {
                        data: [],
                    },
                    yAxis: {},
                    series: [
                        {
                            data: [],
                        },
                    ],
                }
            }
        },
        width: {
            type: Number,
            default: 100,
        },
        height: {
            type: Number,
            default: 100,
        },
    },
    watch: {
        data: {
            handler: function () {
                this.updateLineChart();
            },
            deep: true,
        },
    },
    computed: {},
    mounted() {
        this.initLineChart();
        window.addEventListener("resize", () => {
            this.lineCharts.resize();
        });
    },
    beforeD<PERSON>roy() {
        this.lineCharts.dispose();
    },
    methods: {
        getOptions() {
            const lang = this.lang;
            let option = {
                tooltip: {
                    trigger: "axis",
                    formatter: "{b}: {c}",
                    confine: true,
                    textStyle: {
                        fontFamily: "MyriadPro-Regular",
                    },
                },
                grid: {
                    containLabel: true,
                    left: 20,
                    right: 20,
                    top: 24,
                    bottom: 0,
                },
                xAxis: {
                    type: "category",
                    boundaryGap: false,
                    alignTicks: true,
                    axisLine: {
                        show: true,
                        lineStyle: {
                            color: "#354657",
                        },
                    },
                    axisTick: {
                        show: true,
                        alignWithLabel: true,
                        interval: 0,
                    },
                    splitLine: {
                        show: true,
                        lineStyle: {
                            color: "#354657",
                        },
                    },
                    axisLabel: {
                        show: true,
                        color: "#c6c6c6",
                        interval: 0,
                        fontFamily: "MyriadPro-Regular",
                    },
                },
                yAxis: {
                    type: "value",
                    boundaryGap: false,
                    minInterval: 1,
                    axisTick: {
                        show: false,
                    },
                    axisLine: {
                        show: true,
                        lineStyle: {
                            color: "#354657",
                        },
                    },
                    axisLabel: {
                        color: "#c6c6c6",
                        fontFamily: "MyriadPro-Regular",
                    },
                    splitLine: {
                        lineStyle: {
                            color: "#354657",
                            type: "dotted",
                        },
                    },
                    nameTextStyle: {
                        color: "#c6c6c6",
                        align: "left",
                        fontSize: 16,
                        fontFamily: "MyriadPro-Regular",
                    },
                },
                series: [
                    {
                        type: "line",
                        smooth: true,
                        symbol: "circle",
                        lineStyle: {
                            color: "#6d78da",
                        },
                        itemStyle: {
                            color: "#fc675d",
                        },
                        universalTransition: true,
                        markPoint: {
                            data: [
                                {
                                    type: "max",
                                    name: lang.statistic.max_tip,
                                    itemStyle: {
                                        color: "#fc675d",
                                    },
                                },
                                {
                                    type: "min",
                                    name: lang.statistic.min_tip,
                                    itemStyle: {
                                        color: "#fc675d",
                                    },
                                },
                            ],
                            symbol: "path://m7,19.5c0,-4.890410423278809,4.109589576721191,-9,9,-9l166,0c4.890411376953125,0,9,4.109589576721191,9,9l0,60c0,4.890411376953125,-4.109588623046875,9,-9,9l-166,0c-4.890410423278809,0,-9,-4.109588623046875,-9,-9l0,-60Z",
                            symbolSize: (value) => {
                                let _value = value ? [value.toString().length * 8, 15] : [25, 15];
                                if (_value[0] < 25) {
                                    _value[0] = 25;
                                }
                                return _value;
                            },
                            symbolOffset: [0, "-100%"],
                            label: {
                                position: ["50%", "5%"],
                                color: "#fff",
                                align: "center",
                            },
                        },
                        areaStyle: {
                            color: {
                                type: "linear",
                                x: 0,
                                y: 0,
                                x2: 0,
                                y2: 1,
                                colorStops: [
                                    {
                                        offset: 0,
                                        color: "#6d78da",
                                    },
                                    {
                                        offset: 1,
                                        color: "rgba(33,40,56,0.5)",
                                    },
                                ],
                            },
                        },
                    },
                ],
            };
            option.xAxis = Object.assign(option.xAxis, this.data.xAxis);
            option.yAxis = Object.assign(option.yAxis, this.data.yAxis);
            option.series[0] = Object.assign(option.series[0], this.data.series[0]);
            return option;
        },
        initLineChart() {
            let option = this.getOptions();
            this.lineCharts = echarts.init(this.$refs["line_chart_dom"], null, { renderer: "svg" });
            this.lineCharts.setOption(option);
        },
        updateLineChart() {
            let option = this.getOptions();
            this.lineCharts.setOption(option);
        },
    },
};
</script>

<style scoped>
.line_chart {
    overflow: hidden;
}
</style>

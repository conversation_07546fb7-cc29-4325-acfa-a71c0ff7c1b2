<template>
    <div class="club_repository_page second_level_page" >
        <mrHeader>
            <template #title>
                {{ clubNames[clubName] }}
            </template>
        </mrHeader>
        <div class="repository_content" v-loading="isLoading" >
            
            <div class="search_btn">
                <!-- <i class="iconfont svg_icon_search icon-magnifier"></i> -->

                <!-- @focus="focusInput()"
                    @blur="blurInput()"
                    @keyup.enter="searchInput"
                    @click="focusInput()" -->
                <van-field
                    ref="search_input"
                    clearable 
                    @blur="blurInput()"
                    @focus="focusInput()"
                    @keyup.enter="searchInput"
                    @click="focusInput()" 
                    v-model="filters.searchInputKey"
                >
                    <template #right-icon>
                        <template v-if="!filters.focus_input && !filters.searchInputKey">
                            <i class="iconfont icon-search svg_icon_search">
                            </i>
                            <div class="icon_desc">{{ lang.search }}</div>
                        </template>
                    </template>
                </van-field>
            </div>
            <van-pull-refresh v-model="isRefresh" :disabled="refresh_disable" @refresh="onRefresh" style="flex:1;">
                <div class="type" ref="main_post_content">
                    <div class="swipe_posts">
                        <van-swipe :autoplay="3000" :height="'10rem'">
                            <van-swipe-item
                                v-for="(item, i) in filters.swipe_posts"
                                :key="item.id"
                                :default="i"
                                class="swipe_posts_van"
                                @click="viewRepositoryPost({ id: item.pid })"
                            >
                                <div class="swipe_posts_item">
                                    <img v-lazy="item.url" />
                                </div>
                            </van-swipe-item>
                        </van-swipe>
                    </div>
                    <div class="hot_type">
                        <div class="row" >
                            <div
                                class="hot_type_item"
                                v-for="(item,index) in filters.hot_types"
                                :key="index"
                                v-show="item.type === clubName"
                                :class="{'is_active':item.is_active}"
                                @click="changeHotTypeByIndex(item)"
                            >
                                <i class="iconfont" :class="item.icon"> </i>
                                <div class="hot_type_name">
                                    {{item.en}}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="filter_type">
                        <div class="filter_type_list" ref="filter_type_scroll" >
                            <!-- <span
                                class="filter_type_item"
                                :class="{ 'is-active': item.is_checked }"
                                v-for="item in filters.condition.category"
                                :key="'category' + item.id"
                                @click="filedChange(item, 'category')"
                            >
                                {{ item.name }}</span
                            > -->
                            <span
                                class="filter_type_item"
                                v-for="item in filters.condition.tag.filter(v=>v.in_pop)"
                                :class="{ 'is-active': item.is_checked }"
                                :key="'tag' + item.id"
                                @click="filedChange(item, 'tag')"
                            >
                                {{ item.name }}
                            </span >
                                
                        </div>
                        <div class="filter_type_icon">
                            <i class="icon iconfont icon-filter " @click="toggleCondition"></i>
                        </div>
                    </div>
                    <div class="result_type">
                        <div class="result_type_tips">
                            {{
                                lang.total_count_of_post.replace("${name}", total > 99 ? "99+" : total)
                            }}
                        </div>
                        <div class="result_type_shuaxin" @click="handleReset()">
                            <i class="icon iconfont icon-shuaxin icon_shuxin_filter" ></i>
                            {{ lang.register_reset_btn }}
                        </div>
                    </div>
                    <div class="current_post" v-loading="filters.isSearchLoading" >
                        <template v-if="posts.length == 0 && !filters.isSearchLoading">
                            <div class="current_post_empty">
                                <div class="empty-icon">
                                    <i class="icon iconfont icon-no_data_post"></i>
                                </div>
                                <div>
                                    {{ lang.sorry_no_post_dat_with_tag }}
                                </div>
                            </div>
                        </template>
                        <template v-else>
                            <van-list
                                ref="loadmore"
                                :immediate-check="false"
                                @load="loadMorePosts"
                                v-model="filters.isLoadingMore"
                                :finished="posts.length >= total"
                                :loading-text="lang.bottom_loading_text"
                                :class="'current_post'"
                                offset="10"
                            >
                                <div
                                    class="current_post_item"
                                    v-for="(post, i) in posts"
                                    :key="'post' + i"
                                    @click="viewRepositoryPost(post)"
                                >
                                    <div class="post_image">
                                        <img
                                            :src="getPostImage(post)"
                                            :class="{ default_image: getPostImage(post) == defaultImage }"
                                        />
                                    </div>
                                    <div class="post_title" v-html="markTitle(post.title)"></div>
                                    <div class="post_like">
                                        <i class="iconfont icon-eye_post post_like_item">{{
                                            post.post_more_details.postviews > 99
                                                ? "99+"
                                                : post.post_more_details.postviews
                                        }}</i>
                                        <i class="iconfont icon-like_post post_like_item">{{
                                            post.post_more_details.postlikes > 99
                                                ? "99+"
                                                : post.post_more_details.postlikes
                                        }}</i>
                                    </div>
                                </div>
                            </van-list>
                        </template>
                    </div>
                    <div v-if="posts.length >= total && posts.length>0" class="no-more-text">
                        {{lang.no_more_text}}
                    </div>
                </div>
            </van-pull-refresh>
        </div>
        <club-filter
            ref="case_fliter"
            :clubName="clubName"
            :filters="filters"
            :isShowCondition.sync="filters.isShowCondition"
            @handleback="handleback()"
        ></club-filter>
        <!-- <div v-for="post in posts" :key="post.id" >
            <div >
                {{ post.title.rendered }}
            </div>
            {{getImage(post)}}
            <img :src="getImage(post)" class="post_thumbnail" v-if="getImage(post)"/>
        </div> -->
        <router-view></router-view>
    </div>
</template>
<script>
import base from "../../lib/base";
import clubBase from "../../lib/clubBase";
import service from "../../service/service.js";
import clubFilter from "@/module/ultrasync/components/clubFilter.vue";
import { cloneDeep, trim, uniqBy } from "lodash";
import Tool from "@/common/tool.js";
import {clubNames} from '@/common/tools/mappings.js'
import { Toast, Swipe, SwipeItem, List, Field, PullRefresh, Loading } from "vant";
export default {
    mixins: [base,clubBase],
    name: "club_page",
    components: {
        clubFilter,
        VanSwipe: Swipe,
        VanSwipeItem: SwipeItem,
        VanList: List,
        vanField: Field,
        VanPullRefresh: PullRefresh,
    },
    data() {
        return {
            clubNames,
            refresh_disable:false,
            scroll_top: 0,
            isLoading: false,
            isRefresh: false,
            defaultImage:"",
            clubName:''
        };
    },
    beforeDestroy(){
        this.filters = cloneDeep(this.default_filters);
        this.$store.commit('clubData/updateClubData',{posts:[],total:0})
    },
    computed: {
        posts(){
            return this.$store.state.clubData.posts
        },
        total(){
            return this.$store.state.clubData.total
        },
    },
    created(){
        this.clubName = this.$route.params.club_name;
        this.filters = cloneDeep(this.default_filters);
        let club_server = this.$store.state.systemConfig.serverInfo.library_server;
        let ajaxServer = club_server.protocol+club_server.addr+':' + club_server.port +"/club/"
        this.defaultImage = ajaxServer + 'wp-content/themes/library_fliter/assets/img/default-thumbnail.png'
    },
    mounted() {
        let scroll = this.$refs.main_post_content;
        if(scroll){
            scroll.addEventListener('scroll', ()=>{
                this.scroll_top = scroll.scrollTop;
                if(this.scroll_top==0){
                    this.refresh_disable = false
                }else{
                    this.refresh_disable = true
                }
            })
        }
        this.isLoading = true;
        this.filters.isSearchLoading = true;
        setTimeout(async () =>{
            if(this.$store.state.clubData.club_token){
                this.startRender(true);
            }else{
                let external_token=await this.getExternalToken();
                if(external_token){
                    await this.getClubToken(external_token);
                    this.startRender(true);
                }else{
                    this.isLoading = false;
                }
            }
        },0)
    },
    methods: {
        onRefresh() {
            this.startRender(true);
        },
        startRender(reload) {
            let that = this;
            (async (reload) => {
                let function_list = []
                if(that.getListDataFromStore('swipe_posts')&&!reload){
                    that.filters.swipe_posts = that.getListDataFromStore('swipe_posts');
                }else{
                    const fc_get_swipe_posts = async ()=>{
                        that.filters.swipe_posts = await that.getPostSlideImages();
                        that.$store.commit('clubData/updateClubData',{swipe_posts:that.filters.swipe_posts});
                        return true ;
                    }
                    function_list.push(fc_get_swipe_posts())
                }
                
                if(that.getListDataFromStore('tags')&&!reload){
                    that.filters.condition["tag"] = that.getListDataFromStore('tags');
                    that.filters.hot_types = that.getListDataFromStore('hot_tags');
                }else{
                    const fc_get_all_tags = async (resolve)=>{
                        let tags = await that.getAllTags();
                        that.filters.condition["tag"] = cloneDeep(tags);
                        let hot_tags =  cloneDeep(that.filters.hot_types).reduce((h, item) => {
                            item.is_active =  false;
                            h.push(item)
                            return h;
                        },[]);
                        hot_tags[0].is_active = true;
                        that.$store.commit('clubData/updateClubData',{tags,hot_tags}) ;
                        return true ;
                    }
                    function_list.push(fc_get_all_tags())

                }
                that.isRefresh = false;
                if(that.getListDataFromStore('init_posts')&&!reload){
                    let init_posts = that.getListDataFromStore('init_posts');
                    that.$store.commit('clubData/updateClubData',{posts:init_posts.posts,total:init_posts.total})
                }else{
                    const fc_get_posts = async (resolve)=>{
                        let new_hot_posts = await that.search(true);
                        that.$store.commit('clubData/updateClubData',{init_posts:{posts:that.posts,total:that.total}}) ;
                        return true ;
                    }
                    function_list.push(fc_get_posts())
                }
                if(function_list.length > 0){
                    const result = await  Promise.all(function_list)
                }
                that.isLoading = false;
                that.filters.isSearchLoading = false;
            })(reload);
        },
        viewRepositoryPost(post) {
            const path = this.$route.path
            this.$router.push(`${path}/post/${post.id}`);
        },
        focusInput() {
            this.$refs.search_input.focus();
            this.filters.focus_input = true;
        },
        async blurInput(flag) {

            this.filters.focus_input = false;
            await this.search(true);
        },
        async searchInput() {
            this.$refs.search_input.blur();
        },
        async loadMorePosts() {
            if(this.posts.length >= this.total && this.posts.length>0){
                this.filters.isSearchLoading = false;
                return 
            }
            if(!this.filters.isShowCondition){
                this.filters.isLoadingMore = true;
                await this.search(false);
                this.filters.isLoadingMore = false;
            }
        },
        getPostImage(post) {
            let url = this.defaultImage;
            // if (post._embedded && post._embedded["wp:featuredmedia"]) {
            //     url = post._embedded["wp:featuredmedia"][0]?.source_url;
            // }
            
            if (post.post_more_details && post.post_more_details.post_thumbnail_url) {
                url =  post.post_more_details.post_thumbnail_url;
            }
            
            return url;
        },
        
        async changeHotTypeByIndex(type) {
            //更改推荐后-选中是否处理
            this.filters.hot_types.forEach((item, i) => {
                item.is_active = false;
            });
            type.is_active = true
            await this.search(true);
            return;
        },
        getImage(post) {
            if (post._embedded && post._embedded["wp:featuredmedia"] && post._embedded["wp:featuredmedia"].length > 0) {
                return post._embedded["wp:featuredmedia"][0].source_url;
            }
            return "";
        },
        //显示过滤
        toggleCondition() {
            if (!this.filters.isSearchLoading) {
                this.filters.isShowCondition = !this.filters.isShowCondition;
            }
        },

        async handleback(newCondition,need_search) {
            this.$set(this.filters, "condition", newCondition);
            if(need_search){
                let new_posts = await this.search(true);
            }
        },
        async handleReset() {
            let condition = {};
            this.filters.searchInputKey = "";
            for (let key in this.filters.condition) {
                let list = this.filters.condition[key];
                condition[key] = this.filters.condition[key].reduce((h, v) => {
                    v.is_checked = false;
                    h.push(v);
                    return h;
                }, []);
            }
            await this.handleback(condition,true);
        },
        async filedChange(field, parent, gparent) {
            var condition = cloneDeep(this.filters.condition);
            if (gparent) {
                condition[gparent] = condition[gparent] || {};
                condition[gparent][parent] = condition[gparent][parent] || [];
                condition[gparent][parent] = this.filters.condition[gparent][parent].reduce((h, v) => {
                    if (v.id == field.id) {
                        v.is_checked = !v.is_checked;
                    }
                    h.push(v);
                    return h;
                }, []);
            } else {
                condition[parent] = condition[parent] || [];
                condition[parent] = this.filters.condition[parent].reduce((h, v) => {
                    if (v.id == field.id) {
                        v.is_checked = !v.is_checked;
                    }
                    h.push(v);
                    return h;
                }, []);
            }
            await this.handleback(condition,true);
        },
        markTitle(title){
            if(this.filters.searchInputKey){
                const highlight = this.filters.searchInputKey
                let replaceString = '<span style="color:#00c59d;">' + highlight + '</span>' // 高亮替换v-html值
                return title.split(highlight).join(replaceString)
            }else{
                return title
            }
        }
    },
};
</script>
<style >
.van-swipe__indicator--active{
    background-color:#00C59D;
}
</style>
<style lang="scss">

.club_repository_page {
   
    .post_thumbnail {
        width: 50px;
        height: 50px;
    }
    .repository_content {
        // flex: 1;
        margin: 0rem 0.25rem 0rem 0.25rem;
        height: 100%;
        overflow: auto;
        display: flex;
        flex-direction: column;
       
        .search_btn {
            height: 1.6rem;
            line-height: 1.6rem;
            text-align: center;
            background-color: rgba(0, 197, 157, 0.16);
            color: rgb(179, 179, 179);
            font-size: 0.7rem;
            border-radius: 0.7rem;
            background: #efefef;
            margin: 0.4rem 0rem 0.7rem 0rem;
            span,
            i {
                vertical-align: middle;
            }
            .van-cell {
                padding: 0;
                padding-top: 0.2rem;
            }
            .van-field__body {
                height: 1.5rem;
                color: #A7BCBA;
                border-radius: 0.7rem;
                background: #F2F6F9;
                display: flex;
                align-items: center;
                justify-content: center;
                input {
                    text-align: center;
                }
                .icon_desc {
                    color: red;
                    line-height: 1.5rem;
                    color: #8b8b8b;
                    padding-top: 0.2rem;
                    padding-left: 0.5rem;
                }
                .svg_icon_search {
                    color: rgb(0, 197, 157);
                    float: center;
                    font-size: 0.7rem;
                    padding-top: 0.4rem;
                }
                .van-field__right-icon {
                    color: #969799;
                    width: 100%;
                    display: flex;
                    justify-content: center;
                }
            }
            .van-field__body:focus {
                .svg_icon_search {
                    display: none;
                }
            }
        }
        .type {
            flex: 2;
            overflow-y: auto;
            overflow-x: hidden;
            display: flex;
            flex-direction: column;
            margin-bottom: 0.5rem;
            height: 100%;
            .swipe_posts {
                height: 10rem;
                margin: 0rem 0rem 0.2rem 0rem;
                background-color: #373535;
                border-radius: 0.5rem;
                .van-swipe {
                    border-radius: 0.5rem;
                }
                .swipe_posts_item {
                    background: black;
                    width: 100%;
                    height: 10rem;
                    text-align: center;
                    img {
                        height: 100%;
                        text-align: center;
                    }
                }
            }
            .hot_type {
                display: flex;
                justify-content: space-between;
                flex-wrap: wrap;
                flex-direction: column;
                padding-top: 0.5rem;
                margin: 0.2rem 0rem 0.2rem 0rem;
                .row {
                    display: flex;
                    margin-bottom: 0.5rem;
                    justify-content: space-around;
                    flex-wrap: wrap;
                    flex-direction: row;
                    width: 100%;
                    .hot_type_item {
                        width: 25%;
                        display: flex;
                        justify-content: flex-start;
                        flex-direction: column;
                        align-items: center;
                        font-size: 1.5rem;
                        min-width: 1.5rem;
                        color: #7a7a7a;
                        i {
                            font-size: 1.3rem;
                        }
                        .hot_type_name {
                            padding: 0 0.15rem; //   display:flex;
                            justify-content: center;
                            font-size: 0.6rem;
                            text-align: center;
                        }
                    }
                    .is_active {
                        color: #00c59dc7;
                    }
                }
            }
            .filter_type {
                margin: 0 0rem 0 0rem;
                padding-top: 0.5rem;
                border-top: 0.01px solid #e3e3e3;
                display: flex;
                height: 1.5rem;
                .filter_type_list {
                    flex: 2;
                    overflow-y: hidden;
                    overflow-x: auto;
                    white-space: nowrap;
                    display: flex;
                    flex-direction: row;
                    .filter_type_item {
                        background-color: rgb(242,246,249);
                        color: rgb(155,168,172);
                        padding:0.2rem;;
                        margin: 0 0.2rem 0 0;
                        border-radius: 0.3rem;
                        font-size: 0.65rem;
                        height: 1.2rem;
                        display: flex;
                        justify-items: center;
                        align-items: center;
                    }
                    .is-active {
                        color: #00c59d;
                    }
                }
                .filter_type_icon {
                    width: 1.5rem;
                    text-align: right;
                    color:rgb(101,109,112);
                }
            }
            .result_type {
                display: flex;
                justify-content: space-between;
                margin: 0.5rem 0rem 0.3rem 0rem;
                height: 1rem;
                font-size: 0.7rem;
                .result_type_tips {
                    color: #C7CFD1;
                }
                .result_type_shuaxin {
                    color: #00c59d;
                    font-size: 0.7rem;
                    .icon_shuxin_filter {
                        padding-right: 0.2rem;
                        font-size: 0.7rem;
                    }
                }
            }

            .current_post {
                flex: 2;
                position: relative;
                // padding: 0.25rem 1.5rem 0.4rem 1.5rem;
                padding: 0.25rem 0rem 0.4rem 0rem;
                display: flex;
                flex-direction: row;
                flex-wrap: wrap;
                justify-content: space-between;
                .van-list__loading {
                    width: 100%;
                }
              
                .loading-mask{
                    margin-top:0rem !important;
                }
                .current_post_empty {
                    color: #9CA8AD;
                    width: 100%;
                    height: 100%;
                    display: flex;
                    font-size: 0.65rem;
                    align-items: center;
                    flex-direction: column;
                    .empty-icon {
                        margin-top: 0.5rem;
                        margin-bottom: 0.8rem;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        height: 4rem;
                        width: 4rem;
                        border-radius: 1.9rem;
                        .icon {
                            font-size: 3rem;
                            color: #a2a2a2;
                        }
                    }
                }
                .current_post_item {
                    display: flex;
                    flex-direction: column;
                    width: 48%;
                    height: 11rem;
                    margin-bottom: 0.6rem;
                    align-items: center;
                    background: #ececec;
                    border-radius: 0.4rem;
                    .default_image {
                        width: 4rem;
                        height: 4rem;
                    }
                    .post_image {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        background: black;
                        border-radius: 0.4rem;
                        height: 7rem;
                        width: 100%;
                        img {
                            border-radius: 0.4rem;
                            text-align: center;
                            max-width: 100%;
                            max-height: 7rem;
                        }
                    }
                    .post_title {
                        width: calc(100% - 0.8rem);
                        text-align: left;
                        padding-left: 0.6rem;
                        padding-right: 0.8rem;
                        padding-top: 0.4rem;
                        font-size: 0.75rem;
                        height: 2.3rem;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        -webkit-line-clamp: 2;
                        display: -webkit-box;
                        -webkit-box-orient: vertical;
                    }
                    .post_like {
                        width: calc(100% - 0.8rem);
                        height: 1.5rem;
                        text-align: left;
                        font-size: 0.7rem;
                        padding-left: 0rem;
                        .post_like_item {
                            margin-right: 0.4rem;
                            color: #757575;
                            padding-right: 0.1rem;
                        }
                    }
                }
                .loading-mask.van_loading_spinner .van-loading__spinner {
                    opacity: 1;
                }
            }
            .no-more-text{
                color: #969799;
                font-size: 14px;
                line-height: 50px;
                text-align: center;
                /* top: 2rem; */
                margin-top: -1.5rem;
            }
        }
    }
}
</style>

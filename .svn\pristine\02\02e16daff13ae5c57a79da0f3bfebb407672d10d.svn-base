
export default function ProxyData(obj, cb) {

    if (typeof obj === 'object') {

        for (let key in obj) {
            if (typeof obj[key] === 'object') {
                obj[key] = ProxyData(obj[key], cb);
            }
        }

    }

    return new Proxy(obj, {

        /**
         * @param {Object, Array} target 设置值的对象
         * @param {String} key 属性
         * @param {any} value 值
         * @param {Object} receiver this
         */
        set: function (target, key, value, receiver) {

            if (typeof value === 'object') {
                value = ProxyData(value, cb);
            }

            let cbType = target[key] == undefined ? 'create' : 'modify';

            //排除数组修改length回调
            if (!(Array.isArray(target) && key === 'length')) {
                cb(cbType, { target, key, value });
            }
            return Reflect.set(target, key, value, receiver);

        },
        deleteProperty(target, key) {
            cb('delete', { target, key });
            return Reflect.deleteProperty(target, key);
        }

    });

}

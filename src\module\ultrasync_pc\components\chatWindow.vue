<template>
    <div class="chat_window_page">
        <template>
            <div
                v-show="cid == 0"
                class="no_chat"
                :text="lang.envTitleMap[PROJECT_NOV]"
                v-waterMarker="{ text: lang.envTitleMap[PROJECT_NOV], textColor: 'rgba(180, 180, 180, 0.6)' }"
            ></div>
            <div v-if="cid != 0 &&!loadingPage" class="chat_window">
                <div class="chat_window_left">
                    <div class="unfinished_homework" v-if="!conferenceState&&unfinishedHomework" @click="openCloudExam">
                        <p><i class="iconfont icon1guangbo"></i>{{lang.cloud_exam}}:{{unfinishedHomework.assignmentInfo.paperInfo.title}}</p>
                        <i class="iconfont iconcuohao" @click.stop="clearUnfinishedHomework"></i>
                    </div>
                    <div class="unfinished_homework" v-if="!conferenceState&&correctedHomework" @click="openCloudExamCompleted">
                        <p><i class="iconfont icon1guangbo"></i>{{lang.cloud_exam}}<span>"{{correctedHomework}}"</span>{{lang.corrected}}</p>
                        <i class="iconfont iconcuohao" @click.stop="clearCorrectedHomework"></i>
                    </div>
                    <chat-component
                        v-show="!examPageType"
                        :chatType="CHAT_TYPE['CHAT_WINDOW']"
                        :cid="cid"
                        ref="chat"
                        :from="`index`"
                    ></chat-component>
                    <exam-mode-page v-show="examPageType" ref="examModePage"></exam-mode-page>
                    <div class="fold-button_right fold-button" v-if="!isFullScreenChatWindowLeft" @click="isFullScreenChatWindowLeft=!isFullScreenChatWindowLeft">
                        <i class="iconfont iconright2"></i>
                    </div>
                    <div class="fold-button_left fold-button" v-else @click="isFullScreenChatWindowLeft=!isFullScreenChatWindowLeft">
                        <i class="iconfont iconright1"></i>
                    </div>
                </div>
                <chat-window-right :cid="cid" v-show="!isFullScreenChatWindowLeft"></chat-window-right>
            </div>
        </template>
        <LiveRoomWeb @leaveChannelAux="HandleLeaveChannelAux" ref="chatWindowLiveRoomWeb" v-if="isPCBrowser&&showLiveRoom"></LiveRoomWeb>
        <LiveRoom @leaveChannelAux="HandleLeaveChannelAux" ref="chatWindowLiveRoom" v-else-if="showLiveRoom"></LiveRoom>
    </div>
</template>
<script>
import base from "../lib/base";
import chatWindowRight from "./chatWindowRight";
import chatComponent from "./chatComponent";
import examModePage from "./examModePage";

import LiveRoom from "./live/liveRoom"
import LiveRoomWeb from './live/liveRoomWeb'
import CEvent from "@/common/CEvent";
import Tool from "@/common/tool";
import {CHAT_TYPE} from '../lib/constants'

export default {
    mixins: [base],
    name: "chatWindow",
    components: {
        chatWindowRight,
        chatComponent,
        examModePage,
        LiveRoom,
        LiveRoomWeb,
    },
    data() {
        return {
            CHAT_TYPE,
            examPageType: false, //视图类型
            cidObj: {},
            showChatComponent: false,
            PROJECT_NOV: process.env.VUE_APP_PROJECT_NOV,
            loadingPage: false,
            isFullScreenChatWindowLeft:false,
            showLiveRoom:false
        };
    },
    computed: {
        conversation() {
            return this.conversationList[this.cid] || {};
        },
        controller() {
            return this.conversation.socket;
        },

        cid() {
            if (this.$route.query.cid && !Number.isNaN(Number(this.$route.query.cid))) {
                return this.$route.query.cid;
            } else if (this.$route.params.cid && !Number.isNaN(Number(this.$route.params.cid))) {
                return this.$route.params.cid;
            } else {
                return 0;
            }
        },
        isPCBrowser(){
            return this.systemConfig.client_type.Client == window.clientType;
        },
        conferenceState() {
            let conferenceState =
                this.$store.state.liveConference[this.cid] &&
                this.$store.state.liveConference[this.cid].conferenceState;
            return conferenceState;
        },
        unfinishedHomework(){
            return this.$store.state.homework.conversationUnfinish[this.cid];
        },
        correctedHomework(){
            const homework = this.$store.state.homework.conversationCorrected[this.cid];
            if (Array.isArray(homework) && homework.length > 0 && homework[0].assignmentInfo && homework[0].assignmentInfo.paperInfo) {
                return homework[0].assignmentInfo.paperInfo.title;
            } else if (homework && homework.assignmentInfo && homework.assignmentInfo.paperInfo) {
                return homework.assignmentInfo.paperInfo.title;
            }
            return '';
        },
    },
    watch: {
        cid: {
            handler(val, oldVal) {
                if(Number(val)){
                    this.initPage(val);
                }
            },
            immediate: true,
        }
    },
    created() {},
    mounted() {
        this.$nextTick(() => {
            var that = this;
            this.$router.replace(`/main/index/chat_window/0`);
            //设置视图类型
            this.$root.eventBus.$off("setPageType").$on("setPageType", ({ cid: cid, examPageType: examPageType }) => {
                if (typeof examPageType === "boolean") {
                    this.cidObj[cid] = examPageType;
                }
                if (cid != this.cid) {
                    return;
                }
                if (this.cidObj.hasOwnProperty(cid) && typeof this.cidObj[cid] === "boolean") {
                    this.examPageType = this.cidObj[cid];
                } else {
                    if (that.conversation.view_mode == 1) {
                        this.examPageType = true;
                        this.$refs.examModePage.init();
                    }
                }
            });
            this.$root.eventBus.$off("togglePageType").$on("togglePageType", this.togglePageType);
            this.$root.eventBus.$off("attachStream").$on("attachStream", (stream, domId) => {
                this.$nextTick(() => {
                    window.rtc.attachStream(stream, domId);
                });
            });
            this.$root.eventBus
                .$off("chatWindowStartJoinRoom")
                .$on("chatWindowStartJoinRoom", this.chatWindowStartJoinRoom);
            this.$root.eventBus
                .$off("refreshConversationSuccessToChatWindow")
                .$on("refreshConversationSuccessToChatWindow", (cid) => {
                    // this.initControllerEvent(cid)
                });
            this.$root.eventBus.$off("viewReject").$on("viewReject", this.viewReject);
        });
    },
    methods: {
        togglePageType() {
            this.examPageType = !this.examPageType;
            if (this.examPageType) {
                this.$refs.examModePage.init();
            }else{
                this.$refs.chat.shouldScrollBottom()
            }
            // this.shouldScrollBottom();
        },
        async initPage() {
            //无论从什么入口切换入会话触发
            //切换cid数据前的处理
            this.loadingPage = true;
            await Tool.handleAfterConversationCreated(this.cid, "chatWindow");
            this.getDeviceList(this.cid)
            this.loadingPage = false;
            this.examPageType = false;

            this.$nextTick(() => {
                if (this.cidObj.hasOwnProperty(this.cid) && typeof this.cidObj[this.cid] === "boolean") {
                    this.examPageType = this.cidObj[this.cid];
                    // delete this.cidObj[this.cid]
                } else {
                    if (this.conversation.view_mode == 1) {
                        this.examPageType = true;
                        this.$refs.examModePage.init();
                    }
                }
                if (!this.$root.conversation_event.hasOwnProperty(this.cid)) {
                    this.$set(this.$root.conversation_event, this.cid, {
                        time: new Date().getTime(),
                        event: new CEvent(),
                    });
                }
            });
        },
        getCurrentEnvTitle() {
            const titleMap = {
                CN: {
                    ultrasync: "瑞影云++",
                    ultrasync_pc: "瑞影云++",
                    audit: "audit",
                    activity: "activity",
                    whiteboard: "瑞影云++白板",
                },
                CE: {
                    ultrasync: "Mico+",
                    ultrasync_pc: "Mico+",
                    audit: "audit",
                    activity: "activity",
                    whiteboard: "whiteboard",
                },
            };
        },
        viewReject(){
            this.examPageType = !this.examPageType;
            this.$refs.examModePage.viewReject();
        },
        HandleLeaveChannelAux(){
            console.log('HandleLeaveChannelAux chatWindow')
            setTimeout(()=>{
                this.showLiveRoom = false
            },600)
        },
        chatWindowStartJoinRoom(options){
            this.showLiveRoom = true
            this.$nextTick(()=>{
                if(this.isPCBrowser){
                    this.$refs.chatWindowLiveRoomWeb.startJoinRoom({
                        ...options,
                        cid:this.cid
                    })
                }else{
                    this.$refs.chatWindowLiveRoom.startJoinRoom({
                        ...options,
                        cid:this.cid
                    })
                }
            })
        },
        getDeviceList(cid){
            window.main_screen.getDeviceList({cid:cid},(res)=>{
                if (res.error_code==0) {
                    this.$store.commit('conversationList/updateDeviceList',{
                        cid,
                        list:res.data.list
                    })
                }
            })
        },
        openCloudExam(){
            this.$router.push({
                path: `${this.$route.path}/cloud_exam`,
                query: this.$route.query,
            });
        },
        openCloudExamCompleted(){
            this.$store.commit('homework/setActiveTab', 'exam_completed');
            this.$router.push({
                path: `${this.$route.path}/cloud_exam`,
                query: {
                    ...this.$route.query
                },
            });
        },
        clearUnfinishedHomework(){
            let obj = {}
            obj[this.cid] = null
            this.$store.commit("homework/updateUnfinish",obj);
        },
        clearCorrectedHomework(){
            let obj = {}
            obj[this.cid] = null
            this.$store.commit("homework/updateCorrected",obj);
        },
    },
};
</script>
<style lang="scss">
.chat_window_page {
    flex: 1;
    background-color: #f5f8fa;
    font-size: 16px;
    position: relative;
    z-index: 2;
    width: 0;
    .chat_window {
        width: 100%;
        height: 100%;
        display: flex;
        position: absolute;
        .chat_window_left {
            min-width: 0;
            flex: 1;
            border-right: 1px solid #d9d9d9;
            position: relative;
            .unfinished_homework{
                background-color: rgb(255,247,236);
                position: absolute;
                top: 0;
                width: 100%;
                z-index: 2;
                padding: 6px 10px;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: space-between;
                .icon1guangbo{
                    margin-right: 10px;
                }
                .iconcuohao{
                    color: rgb(255,177,68);
                }
            }
        }
        .isFullScreenChatWindowLeft{

        }
        .fold-button {
            width: 24px;
            height: 48px;
            background-color: #a9bfbe;

            position: absolute;

            bottom: 25%;
            z-index: 10;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            i{
                font-size: 20px;
            }
            .iconright2{
                position: absolute;
                left: 0;
            }
        }
        .fold-button_right{
            right: -24px;
            border-radius: 0 24px 24px 0;
        }
        .fold-button_left{
            right: 0;
            border-radius: 24px 0 0 24px;
        }
    }
    .no_chat {
        width: 100%;
        height: 100%;
        background: #fff;
        position: absolute;
    }
    .watermark_1 {
        background-image: url("../../../../static/resource_pc/images/watermark_1.jpg");
    }
    .watermark_0 {
        background-image: url("../../../../static/resource_pc/images/watermark_0.jpg");
    }
}
.toolbar_item {
    border: none !important;
    background: rgba(0, 0, 0, 0.7) !important;
    color: #fff !important;
    min-width: 0 !important;
    &[x-placement^="top"] .popper__arrow {
        border-top-color: rgba(0, 0, 0, 0.7) !important;
        &::after {
            border-top-color: rgba(0, 0, 0, 0.7) !important;
        }
    }
    &[x-placement^="bottom"] .popper__arrow {
        border-bottom-color: rgba(0, 0, 0, 0.7) !important;
        &::after {
            border-bottom-color: rgba(0, 0, 0, 0.7) !important;
        }
    }
    #upload_picture {
        display: none;
    }
}
.send_setting {
    font-size: 16px !important;
    line-height: 1.8 !important;
    padding: 6px 12px !important;
    cursor: pointer;
    position: relative;
    p {
        padding-left: 22px;
    }
    i {
        position: absolute;
        top: 8px;
        &.type2 {
            top: 34px;
        }
    }
}
</style>

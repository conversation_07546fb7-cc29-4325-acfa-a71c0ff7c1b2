<template>
    <div class="localstorage_file_page third_level_page">
        <div v-show="!inited" class="full_loading_spinner van_loading_spinner">
            <van-loading color="#00c59d" />
        </div>
        <mrHeader>
            <template #title>
                {{lang.current_account_file_storage}}
            </template>
        </mrHeader>
        <div class="container">
            <div v-show="files.length>0" class="list">
                <div class="storage_item clearfix needsclick" v-for="(file,index) of files" :key="index">
                    <div :class='["file_type_icon", FileType(file)]'>{{file.show_file_type}}</div>
                    <div class="group_check_item clearfix needsclick">
                        <van-checkbox-group
                            v-model="chooseFiles" 
                        >
                            <van-checkbox :name="file.file_id" checked-color="#00c59d" @click.stop="chooseUser"></van-checkbox>
                        </van-checkbox-group>
                    </div>
                    <div class="item_right">
                        <p class="needsclick">{{lang.file_name}}:{{file.name}}</p>
                        <p class="subject needsclick">{{lang.file_size}}{{formatFileSize(file.size)}}</p>
                        <p class="subject needsclick">{{lang.time}}: {{formatDate(file.date)}}</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="toolbar">
            <span class="selectAll" v-show="!isSelectAll" @click="selectAll">{{lang.select_all}}</span>
            <span class="selectNone" v-show="isSelectAll" @click="selectNone">{{lang.cancel_select_all}}</span>
            <span class="delete" @click="deleteFile">{{lang.action_delete_text}}</span>
        </div>
    </div>
</template>
<script>
import base from '../lib/base'
import { Toast, Loading, Checkbox, CheckboxGroup } from 'vant';
import Tool from '@/common/tool.js';
export default {
    name:'localstorageFile',
    components: {
        VanLoading: Loading,
        // VanCheckbox: Checkbox, 
        // VanCheckboxGroup: CheckboxGroup
    },
    mixins:[base],
    data(){
        return {
            inited:false,
            files:[],
            folders:[],
            chooseFiles:[],
            filesObj:{},
            isSelectAll:false,
        }
    },
    mounted(){
        this.$nextTick(()=>{
            this.initData()
        })
    },
    methods:{
        async initData(){
            var sd_path_cell1 = '_downloads/'+this.systemConfig.server_type.host+'/'+this.$store.state.user.uid+'/files';
            var sd_path_cell2 = '';
            this.folders = []
            this.filesObj = {}
            // console.error('****************localstorageFile-initData',this.osName)
            if(true){
                try {
                    Tool.createCWorkstationCommunicationMng({
                        name: "convertLocalFileSystemURL",
                        emitName: 'NotifyConvertedLocalFileSystemURL',
                        params:{url: sd_path_cell1},
                        timeout:1500
                    }).then(async (res)=>{
                        sd_path_cell2 = res.url
                        // console.error('****************localstorageFile-res',res)
                        await this.getFiles(sd_path_cell2)
                        console.log(this.files)
                        this.inited = true
                    })
                }catch(error){
                    Toast(error);
                }
            }
        },
        async getFiles(path){
            let that = this;
            if(true){
                try {
                    console.log('start resolveLocalFileSystemURL:',path)
                    Tool.createCWorkstationCommunicationMng({
                        name:'resolveLocalFileSystemURL',
                        emitName:'NotifyResolvedLocalFileSystemURL',
                        params:{action: 'entryInfo', local_url: path},
                        timeout:null,
                    }).then(async(res)=>{
                        // console.error('****************localstorageFile-entryInfo-res',res)
                        console.log('end NotifyResolvedLocalFileSystemURL:',res)
                        if( res.entryInfo && res.entryInfo.length > 0 ){
                            let length = res.entryInfo.length
                            for(let entry of res.entryInfo){
                                console.log('the entries is --> ',entry)
                                if(entry.isFile && JSON.parse(entry.isFile)){
                                    let fileObj = {}
                                    console.log('是文件-->>',entry);
                                    fileObj.fullPath=entry.url;
                                    let directoryPath = entry.url.substr(0,entry.url.lastIndexOf('/'))
                                    let file_id = directoryPath.substring(directoryPath.lastIndexOf('/')+1)
                                    fileObj.file_id = file_id;
                                    fileObj.name=entry.name;
                                    fileObj.date=entry.lastModifiedDate;
                                    fileObj.size=entry.size;
                                    that.files.push(fileObj)
                                    that.filesObj[file_id] = fileObj
                                    fileObj = {}
                                }else{
                                    if(entry.url){
                                        that.folders.push(entry.url)
                                    }

                                }

                            }
                            console.error('that.folders',that.folders)
                            if(that.folders.length>0){
                                console.error('that.urls',that.folders)
                                let url = that.folders.shift()
                                // console.error('that.url',url)
                                await that.getFiles(url)
                            }
                        } else{
                            console.error('that.folders',that.folders)
                            if(that.folders.length>0){
                                console.error('that.urls',that.folders)
                                let url = that.folders.shift()
                                // console.error('that.url',url)
                                await that.getFiles(url)
                            }
                            // console.log('can not find the directory',res.error_message)
                        }
                    })
                } catch (error) {
                    Toast(error);
                }
            }
        },
        FileType(file) {
            let file_type=file.name.replace(/.+\./,"").toLowerCase()
            const re = new RegExp(file_type, 'ig')
            if(re.test('doc docx docm dot dotm dotx')) {
                file.show_file_type = 'W'
                return 'word'
            }else if(re.test('ppt pptx pptm')){
                file.show_file_type = 'PPT'
                return 'powerpoint'
            }else if(re.test('pdf')){
                file.show_file_type = 'PDF'
                return 'pdf'
            }else if(re.test('xlsx xlsm xlsb xls')){
                file.show_file_type = 'X'
                return 'excel'
            }else if(re.test('zip')){
                file.show_file_type = 'ZIP'
                return 'zip'
            }else if(re.test('ape au avi bat bin bmp cab chm css dll eml f4v fla flac flv gif hpl html ini jepg jpg mid midi mkv mov mp3 mp4 mpeg mpg png psd pst rtf swf temp tif tiff txt url wav wma wmv xls xlsx')){
                file.show_file_type = file_type.toUpperCase()
                return file_type
            }else {
                file.show_file_type = '?'
                return 'unkown'
            }
        },
        chooseUser(){
            console.log(this.files.length,this.chooseFiles.length)
            if (this.chooseFiles.length==this.files.length) {
                this.isSelectAll=true
            }else{
                this.isSelectAll=false;
            }
            console.log('chooseFiles',this.chooseFiles)
        },
        selectAll(){
            this.chooseFiles = []
            for(let file of this.files){
                this.chooseFiles.push(file.file_id)
            }
            this.isSelectAll = true;
        },
        selectNone(){
            this.chooseFiles = []
            this.isSelectAll = false
        },
        formatFileSize(size){
            if(size/1000>=100){
                return (size/1000/1000).toFixed(2)+'MB'
            }else if(size>=1000){
                return (size/1000 | 1)+'KB'
            }else{
                return size+'B'
            }
        },
        formatDate(date){
            return date
            // let year = date.getFullYear()
            // let month = date.getMonth()+1
            // let day = date.getDate()
            // let hour = ('00'+date.getHours()).substr(-2,2)
            // let minutes = ('00'+date.getMinutes()).substr(-2,2)
            // let second = ('00'+date.getSeconds()).substr(-2,2)
            // return `${year}-${month}-${day} ${hour}:${minutes}:${second}`
        },
        deleteFile(){
            if(this.chooseFiles && this.chooseFiles.length>0){
                Tool.openMobileDialog(
                    {
                        message:this.lang.confirm_delete_file,
                        showRejectButton:true,
                        confirm:async ()=>{
                            this.inited=false;
                            let files = this.files;
                            let that = this;
                            let chooseFiles = this.chooseFiles;
                            let chooseFilesUrls = [];
                            console.error('*****::::' , JSON.stringify( this.chooseFiles))
                            for(let i=0; i<chooseFiles.length; i++){
                                let fileObj = that.filesObj[chooseFiles[i]]
                                chooseFilesUrls.push(fileObj)

                            }
                            await this.deleteFiles(chooseFilesUrls)
                            this.inited=true;
                        }
                    }
                )
            }else{
                Toast(this.lang.transmit_less_tip)
            }
        },

        async deleteFiles(filesObj){
            let that = this
            let fileObj = filesObj.shift()
            let i = 0
            for(let file of that.files){
                if(file.file_id == fileObj.file_id) {
                    that.files.splice(i,1);
                    console.error('files:', JSON.stringify(that.files))
                    break
                }
                i = i + 1
            }
            that.chooseFiles.splice(that.chooseFiles.indexOf(fileObj.file_id),1);

            let filePath = fileObj.fullPath
            if(true){
                try {
                    Tool.createCWorkstationCommunicationMng({
                        name:'resolveLocalFileSystemURL',
                        emitName:'NotifyResolvedLocalFileSystemURL',
                        params:{action: 'remove', local_url: filePath},
                        timeout:2500,
                    }).then(async(res)=>{
                        console.error('****:RES', res)
                        if(res.error_code == '0'){
                            if(filesObj.length>0) {
                                await that.deleteFiles(filesObj)
                            }
                            // break;
                            console.log('localstorageFile--deleteFile:Delete success')
                        }else{
                            console.error('Error localstorageFile--deleteFile:Delete Fail.')
                        }
                    })
                }catch(err){
                    console.log(err)
                }
            }

        },

        removeAction(dir_entry){
            return new Promise((resolve,reject)=>{
                dir_entry.remove(function(){
                    resolve(true)
                },function(){
                    reject(false)
                })
            })
        }
    }
}
</script>
<style lang="scss">
.localstorage_file_page{
    background:#fff;
    .full_loading_spinner {
        height: calc(100% - 2.95rem);
        position: absolute;
        width: 100%;
        z-index: 9;
        bottom: 0;
    }

    .van_loading_spinner{
        display: flex;
        justify-content: center;
        align-items: center;

        .van-loading__spinner{
            width: 2.8rem;
            height: 2.8rem;
        }
    }

    .container{
        .list{
            padding-bottom:2rem;
            .storage_item{
                position: relative;
                padding:0.4rem 0.6rem;
                border-bottom: 1px solid #dadada;
                .file_type_icon{
                        float: left;
                        position: absolute;
                        top: 50%;
                        transform: translateY(-50%);
                        height: 2.4rem;
                        width: 1.9rem;
                        line-height: 2.4rem;
                        text-align: center;
                        font-size: .75rem;
                        border-radius: 5px;
                        margin-right: .75rem;
                        color: #fff;
                }
                .group_check_item{
                    float:right;
                    padding:0.5rem;
                    .van-checkbox-group{
                        margin-right:0.3rem;
                    }
                }
                .item_right{
                    margin-left:2.6rem;
                    font-size:0.8rem;
                    .needsclick{
                        word-break:break-all;
                    }
                    .subject{
                        color:#666;
                        font-size:.65rem;
                    }
                }
                // 主蓝色调
                .word, .au, .mov, .psd, .temp, .bmp, .html, .url, .mid, .midi, .mp4, .png, .ini, .txt{
                    background-color:rgb(86, 200, 255);
                }
                // 主红色调
                .powerpoint, .avi, .f4v, .fla, .flv, .pdf, .swf, .tif, .tiff, .wav, .jpeg, .jpg{
                    background-color:rgb(255, 103, 92);
                }
                // 主绿色调
                .excel, .ape, .bat, .flac, .gif, .mkv, .rtf{
                    background-color:rgb(4, 153, 114);
                }
                // 主橙色调
                .zip, .cab, .chm, .css, .eml, .hlp, .mp3, .pst, .wma, .wmv {
                    background-color:rgb(255, 177, 68);
                }
                // 主紫色调
                .bin, .mpeg, .mpg {
                    background-color:rgb(116, 128, 234);
                }
                // 未知文件主灰色
                .unkown {
                    background-color:rgb(170, 180, 185);
                    color: #fff;
                    font-size: 1.2rem;
                }
            }
        }
    }
    .toolbar{
        position: absolute;
        left: 0;
        bottom: 0;
        width: 100%;
        background:#f6f6f6;
        border-top:1px solid #dadada;
        .selectAll,.selectNone{
            color: #00c59d;
            float: left;
            font-size: 0.9rem;
            padding: 0.4rem;
            margin-left: .4rem;
        }
        .delete{
            float: right;
            color: #f00;
            font-size: 0.9rem;
            padding: 0.4rem;
            margin-right: .4rem;
        }
    }
}
</style>

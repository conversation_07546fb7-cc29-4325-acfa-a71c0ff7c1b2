<template>
    <div class="arranged_exam_list">
        <van-search
            :placeholder="lang.homework_search_key"
            v-model="searchKey"
            @input="debounceSearch"
            ></van-search>
        <van-list
          v-model="loadingPage"
          :finished="finished"
          :finished-text="lang.no_more_text"
          @load="onLoad"
        >
            <div class="cloud_exam_item" v-for="exam of examList" :key="exam._id">
                <div class="exam_infomation"  @click="enterCorrecting(exam)">
                    <div class="exam_type_icon">
                        <template v-if="exam.paperInfo.contentType">
                            <img class="title_icon" :src="`static/resource/images/homework_type${exam.paperInfo.contentType}.png`">
                            <p>{{lang['homework_type'+exam.paperInfo.contentType]}}</p>
                        </template>
                        <template v-else>
                            <img class="title_icon" src="static/resource/images/homework_type5.png">
                            <p>{{lang.homework_type5}}</p>
                        </template>
                    </div>
                    <div class="exam_detail">
                        <div class="exam_title">
                            <p>{{exam.paperInfo.title}}</p>
                        </div>
                        <div class="exam_description">
                            <p>{{lang.paper_total_score}}：{{exam.paperInfo.score}}{{lang.point_tip}}</p>
                            <p>{{lang.paper_question_count}}：{{exam.paperInfo.questionCount}}</p>
                        </div>
                        <div class="exam_description">
                            <p>{{lang.author}}：{{exam.paperInfo.author}}</p>
                            <p>{{lang.release_time}}：{{exam.createdAt | showData}}</p>
                        </div>
                    </div>
                </div>
            </div>
        </van-list>
    </div>
</template>
<script>
import base from '../../lib/base';
import service from '../../service/service.js'
import moment from 'moment';
import { List , Search } from 'vant';
import Tool from '@/common/tool.js'
export default {
    mixins: [base],
    name: 'cloudExamList',
    components: {
        vanList:List,
        vanSearch:Search
    },
    data(){
        return {
            cid:0,
            queryForm:{
                pageNo:1,
                pageSize:10,
            },
            examListTotal:0,
            examList:[],
            loadingPage:false,
            finished:false,
            searchKey:'',
            debounceSearch:null,
        }
    },
    filters:{
        showData(ts){
            return moment(ts).format("YYYY-MM-DD HH:mm")
        }
    },
    computed:{
    },
    created(){
        this.cid = parseInt(this.$route.params.cid) || 0;
        this.debounceSearch = Tool.debounce(this.refreshArrangedList,600);
    },
    mounted(){
        this.$root.eventBus.$off("refreshArrangedList").$on("refreshArrangedList",this.refreshArrangedList);
        this.fetchData();
    },
    watch:{
        
    },
    methods:{
        onLoad() {
            this.queryForm.pageNo = this.queryForm.pageNo += 1;
            this.fetchData()
        },
        refreshArrangedList(){
            this.examList = [];
            this.queryForm.pageNo = 1;
            this.fetchData()
        },
        fetchData(){
            this.loadingPage = true;
            service.getAssignmentList({
                gid:this.cid,
                page:this.queryForm.pageNo,
                pageSize:this.queryForm.pageSize,
                searchKey:this.searchKey,
            }).then(res=>{
                this.loadingPage = false;
                if (res.data.error_code === 0) {
                    this.examListTotal = res.data.data.total;
                    this.examList = this.examList.concat(res.data.data.data);
                    if (this.examList.length === this.examListTotal) {
                        this.finished = true;
                    }else{
                        this.finished = false;
                    }
                }
            })
        },
        enterCorrecting(exam){
            this.$store.commit('homework/setCurrentPaper',exam);
            this.$router.push({
                path: `${this.$route.path}/correcting_exam/1`,
                query: this.$route.query,
            });
        }
    },
    destroyed(){
    },

}

</script>
<style lang="scss">
.arranged_exam_list{
    
}
</style>

<template>
    <div class="mr-selecter">
        <div @click="showPopup" class="select_wrapper">
            <span class="show_value">{{ showText }}</span>
            <i class="icon iconfont icon-down"></i>
        </div>
        <van-popup v-model="isShowPopup" position="bottom" class="popup">
            <van-picker
                class="picker"
                show-toolbar
                :columns="columns"
                ref="picker"
                @confirm="onConfirm"
                @cancel="onCancel"
                :default-index="defaultIndex"
            ></van-picker>
        </van-popup>
    </div>
</template>
<script>
import { Popup, Picker, Toast } from "vant";
/*
    依靠van-popup和van-picker封装的选择器。
    传入参数options为[{id:1,value:"test"}]
    传入参数seletedId为选中Id
*/
export default {
    components: {
        VanPopup: Popup,
        VanPicker: Picker,
    },
    mixins: [],
    data() {
        return {
            isShowPopup: false,
            columns: [],
            defaultIndex: 0,
        };
    },
    props: {
        options: {
            type: Array,
            default: () => {
                return [];
            },
        },
        seletedId: [String, Number],
        callback: {
            type: Function,
            default: function () {
                console.log("mr-selecter callback!");
            },
        },
    },
    activated() {
        this.$nextTick(() => {});
    },
    computed: {
        lang() {
            return this.$store.state.language;
        },
        showText() {
            let text = "";
            for (let option of this.options) {
                if (option.id == this.seletedId) {
                    text = option.value;
                    break;
                }
            }
            return text;
        },
    },
    mounted() {
        this.$nextTick(() => {});
    },
    methods: {
        showPopup() {
            this.columns = [];
            for (let i = 0; i < this.options.length; i++) {
                let item = this.options[i];
                this.columns.push(item.value);
                if (item.id == this.seletedId) {
                    this.defaultIndex = i;
                }
            }
            this.isShowPopup = true;
        },
        onConfirm(value) {
            for (let item of this.options) {
                if (value == item.value) {
                    this.callback(item);
                    break;
                }
            }
            this.isShowPopup = false;
        },
        onCancel() {
            this.isShowPopup = false;
        },
    },
};
</script>
<style lang="scss">
.mr-selecter {
    padding-left: 0.2rem;
    .select_wrapper {
        display: flex;
    }
    .show_value {
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
    }
    .icon-down {
        font-size: 0.9rem;
        vertical-align: middle;
        margin-right: 0.3rem;
        margin-left: 0.1rem;
    }
    .picker {
        .van-picker__confirm {
            color: #00c59d;
            font-size: 0.8rem;
        }
        .van-picker__cancel {
            font-size: 0.8rem;
        }
    }
}
</style>

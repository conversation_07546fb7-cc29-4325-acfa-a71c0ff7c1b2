<template>
    <transition name="slide">
        <div class="edit_managers_page fourth_level_page">
            <mrHeader>
                <template #title>
                     {{title}}
                </template>
                <template #right>
                    <span class="add_attendee_btn" @tap="submit" :class="{enable:enable}">{{lang.confirm_txt}}({{groupUser.length}})</span>
                </template>
            </mrHeader>
            <div class="edit_managers_container">
                <div class="choose_list">
                    <ContactSelectList :optionList="checkOption" v-model="groupUser" v-if="activatedComponent"></ContactSelectList>
                </div>
            </div>
        </div>
    </transition>
</template>
<script>
import base from '../lib/base'
import { Toast } from 'vant';
import ContactSelectList from "../components/contactSelectList.vue";
export default {
    mixins: [base],
    name: 'editGroupManagePage',
    components: {
        ContactSelectList
    },
    data(){
        return {
            cid:this.$route.params.cid,
            type:this.$route.params.type,
            groupUser:[],
            activatedComponent:true
        }
    },
    beforeDestroy(){
    },
    mounted(){
    },
    activated(){
        this.cid=this.$route.params.cid
        this.type=this.$route.params.type
        this.groupUser=[]
        this.activatedComponent = true
    },
    deactivated(){
        this.activatedComponent = false
    },
    computed:{
        title(){
            if (this.type == 1) {
                return this.lang.add_group_manager;
            }else{
                return this.lang.delete_group_manager;
            }
        },
        conversation(){
            return this.conversationList[this.cid]||{galleryObj:{},iworksList:{}}
        },
        attendeeArray(){
            let list=this.parseObjToArr(this.conversation.attendeeList);
            let filterList = []; //后端把所有用户都返回回来，前端只显示未退群用户
            for(let i=0; i<list.length; i++){
                if(list[i].attendeeState != 0){
                    filterList.push(list[i]);
                }
            }
            return filterList;
        },
        remarkMap(){
            return this.$store.state.friendList.remarkMap;
        },
        checkOption(){
            const arr=[]
            let list = this.attendeeArray
            if (this.type == 1) {
                for(let item of list){
                    let option={}
                    option.label=this.remarkMap[item.userid]||item.nickname
                    option.value=item.userid
                    option.avatar=item.avatar
                    option.disabled=false
                    if (item.role > this.systemConfig.groupRole.normal ) {
                        option.disabled=true;
                    }
                    arr.push(option)
                }
            }else{
                for(let item of list){
                    if (item.role === this.systemConfig.groupRole.manager) {
                        let option={}
                        option.label=this.remarkMap[item.userid]||item.nickname
                        option.value=item.userid
                        option.avatar=item.avatar
                        option.disabled=false
                        arr.push(option)
                    }
                }
            }
            return arr;
        },
        enable(){
            return this.groupUser.length>0
        },
    },
    methods:{
        submit(){
            if (this.enable) {
                if (this.type == 1) {
                    window.main_screen.conversation_list[this.cid].addGroupManager({
                        userIdList:this.groupUser
                    },(result)=>{
                        if (result.error_code === 0) {
                            this.back();
                        }else{
                            Toast(result.error_msg);
                        }
                    })
                }else{
                    window.main_screen.conversation_list[this.cid].deleteGroupManager({
                        userIdList:this.groupUser
                    },(result)=>{
                        if (result.error_code === 0) {
                            this.back();
                        }else{
                            Toast(result.error_msg);
                        }
                    })
                }
            }
        }
    }
}
</script>
<style lang="scss">
.edit_managers_page{
    .edit_managers_container{
        flex: 1;
        overflow: hidden;
        .choose_list{
            height: 100%;
            .group_user_item{
                padding: 0.5rem;
            }
        }
        .search_wrap{
            padding: 0.2rem 0.5rem;
            .search_manager_txt{
                display:block;
                width:100%;
                box-sizing:border-box;
                height:1.6rem;
                border: 1px solid #ccc;
                padding: 0 .4rem;
                font-size: .8rem;
                border-radius:4px;
            }
        }

    }
    .add_attendee_btn{
        width: 4rem;
        color: #fff;
        opacity: 0.6;
        font-size: 0.7rem;
        text-align: right;
        &.enable {
            opacity: 1;
        }
    }
}
</style>

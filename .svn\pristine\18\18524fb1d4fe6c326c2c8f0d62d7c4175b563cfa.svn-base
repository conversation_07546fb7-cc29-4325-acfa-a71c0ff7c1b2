import Dexie from "dexie"
import {version }from '@/common/version'
import{deleteOldLogs,clearLogs} from '@/common/console'
export function newMainDB(dbame){
    let mainDB = new Dexie(String(dbame))
    mainDB.version(version['indexDB']).stores({
        chatList:"cid,is_single_chat,last_message_ts,fid",
        lastMessageList:"group_id",
        conversationMessageList:"gmsg_id,send_ts,group_id",
        conversationList:"id",
        unreadList:"group_id",
        friendList:"&id",
        groupList:"&id",
        dcmList:"last_update_ts,[exam_id+image_id]",
        userOptions:'id,value',
        logs: '++id,timestamp,message,clientType',
        ossUpload: 'id,value'
    })
    mainDB.open()
    window.mainDB = mainDB
    deleteOldLogs()
    return mainDB
}
// 将File对象保存到Dexie数据库
export async function saveFileToDB(info) {
    const file = info.file
    const fileName = info.fileName
    const checkPoint = info.cpt
    const clientOptions = {
        bucket:info.clientOptions.bucket,
        region:info.clientOptions.region,
    }

    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = function (event) {
            const fileData = event.target.result;
            const blob = new Blob([fileData], { type: file.type });
            window.mainDB.ossUpload.put({ id:checkPoint.uploadId,value:{name: fileName, blob: blob,checkPoint,clientOptions} })
                .then(() => {
                    resolve();
                })
                .catch((error) => {
                    reject(error);
                });
        };

        reader.readAsArrayBuffer(file);
    });
}
// 从Dexie数据库中获取文件
export async function getFileFromDB(uploadId) {
    return new Promise((resolve, reject) => {
        window.mainDB.ossUpload.get(uploadId)
            .then((res) => {
                const result = res.value;
                if (result) {

                    const blob = result.blob;
                    const file = new File([blob], result.checkPoint.name, { type: blob.type });
                    resolve({file,result});
                } else {
                    reject(new Error('File not found'));
                }
            })
            .catch((error) => {
                reject(error);
            });
    });
}

import Tool from "@/common/tool"
import io from 'socket.io-client';
import {Logger} from '@/common/console'
function CConversationGateway(option){
    this.uid=option.uid;
    this.url=option.url;
    this.cid=option.cid;

    this.socket=null;
    this.connected=false;
}

CConversationGateway.prototype.connectSocket=function(){
    var url = this.url + "/cid_" + this.cid;
    let transports = ['websocket', 'polling']
    if(Tool.checkAppClient('IOS')&&!Tool.checkAppClient('Browser')){
        transports = ['polling']
    }
    this.socket=io.connect(url,{
        transports,
        pingInterval: 3000, // 设置心跳检测间隔为 5 秒
        pingTimeout: 3000 // 设置心跳检测超时时间为 3 秒
    });
    if(this.socket){
        this.socket.on('connect',()=>{
            this.connected = true
            Logger.save({
                message:`conversation_gateway_connect_${this.cid}`,
                eventType:'socket'
            })
        });
        this.socket.on('error',()=>{
            this.connected = false
            Logger.save({
                message:`conversation_gateway_connect_${this.cid}`,
                eventType:'socket'
            })
        });
        this.socket.on('disconnect',()=>{
            this.connected = false
            Logger.save({
                message:`conversation_gateway_disconnect_${this.cid}`,
                eventType:'socket'
            })
        });
        this.socket.on('reconnecting',()=>{
            this.connected = false
        });
        this.socket.on('reconnect_failed',()=>{
            this.connected = false
            Logger.save({
                message:`conversation_gateway_reconnect_failed_${this.cid}`,
                eventType:'socket'
            })
        });
        this.socket.on('reconnect',()=>{
            this.connected = true
            Logger.save({
                message:`conversation_gateway_reconnect_${this.cid}`,
                eventType:'socket'
            })
        });
    }
    console.log("[event] CConversationGateway.connectSocket:" + url,Tool.triggerTime());
};

CConversationGateway.prototype.closeSocket=function(){
    this.socket && this.socket.disconnect();
    this.socket && this.socket.removeAllListeners();
    console.log('[socket event] CConversationGateway.closeSocket',Tool.triggerTime());
};

CConversationGateway.prototype.on=function(event,callback){
    this.socket && this.socket.on(event,function(arg1,...args){
        console.log("[socket event] CConversationGateway on",event,Tool.triggerTime());
        console.log(arg1,...args);

        callback(arg1,...args);
    });
};

CConversationGateway.prototype.emit=function (event,...args){
    console.log("[socket event] CConversationGateway emit",event,Tool.triggerTime());
    console.log(event,...args);
    if(!this.socket){
        return
    }
    this.socket.emit(event,...args);
};

export default CConversationGateway

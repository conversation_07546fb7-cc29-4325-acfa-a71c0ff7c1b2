<template>
    <div>
        <el-dialog
        class="transmit_dialog"
        :title="''"
        :visible="dialogVisible"
        :close-on-click-modal="false"
        :append-to-body="true"
        width="500px"
        :modal="false"
        :before-close="close">
        <div class="custom_header">
            <p class="title">{{title}}</p>
            <i class="iconfont iconcuohao" @click="close"></i>
        </div>
        <div class="custom_body">
            <div class="search_container">
                <p v-show="choosenChatList.length>0" class="share_risk_content">{{lang.share_risk_content}}</p>
                <el-input class="search_input" v-model="searchText" :placeholder="lang.search" @input="search"></el-input>
                <div class="list_container">
                    <div class="list" v-show="choosenChatList.length>0">
                        <p class="title">{{lang.recent_chat_text}} </p>
                        <div class="transmit_list">
                            <div v-for="chatItem of choosenChatList" @click="submit(chatItem,1)" class="transmit_item clearfix" :key="chatItem.cid">
                                <div class="avatar_box fl">
                                    <mr-avatar :url="getLocalAvatar(chatItem)" :key="chatItem.avatar" :radius="48"></mr-avatar>
                                </div>
                                <p class="fl transmit_subject">{{getSubject(chatItem)}}</p>
                            </div>
                        </div>
                    </div>
                    <div class="list" v-show="choosenFriendList.length>0">
                        <p class="title">{{lang.contact_text}}</p>
                        <div class="transmit_list">
                            <div v-for="chatItem of choosenFriendList" @click="submit(chatItem,2)" class="transmit_item clearfix" :key="chatItem.cid">
                                <div class="avatar_box fl">
                                    <mr-avatar :url="getLocalAvatar(chatItem)" :key="chatItem.avatar" :radius="48"></mr-avatar>
                                </div>
                                <p class="fl transmit_subject">{{chatItem.nickname}}</p>
                            </div>
                        </div>
                    </div>
                    <div class="list" v-show="choosenGroupList.length>0">
                        <p class="title">{{lang.group}}</p>
                        <div class="transmit_list">
                            <div v-for="chatItem of choosenGroupList" @click="submit(chatItem,3)" class="transmit_item clearfix" :key="chatItem.cid">
                                <div class="avatar_box fl">
                                    <mr-avatar :url="getLocalAvatar(chatItem)" :key="chatItem.avatar" :radius="48"></mr-avatar>
                                </div>
                                <p class="fl transmit_subject">{{chatItem.subject}}</p>
                            </div>
                        </div>
                    </div>
                    <p v-if="choosenChatList.length==0&&choosenFriendList.length==0&&choosenGroupList.length==0" class="no_search_data">
                        {{lang.no_search_data}}
                    </p>
                </div>
            </div>
                
        </div>
        </el-dialog>
    </div>
</template>
<script>
import base from '../lib/base'
import {getLocalAvatar} from '../lib/common_base'
export default {
    mixins: [base],
    name: 'TransmitComponent',
    components: {},
    data(){
        return {
            getLocalAvatar,
            dialogVisible:false,
            searchText:'',
            choosenChatList:[],
            choosenFriendList:[],
            choosenGroupList:[],
            callback:'',
            transmit_comfirm_msg:'',
            isStandaloneWorkstationShare:0,
            isServiceTypeNone:0,
            cid:null,
            title:'',
            disableChat:false,
            disableFriend:false,
            disableGroup:false,
        }
    },
    computed:{
        remarkMap(){
            return this.$store.state.friendList.remarkMap;
        },
        chatList(){
            return this.filterEnableList(this.$store.state.chatList.list)
        },
        friendList(){
            return this.filterEnableList(this.$store.state.friendList.list)
        },
        groupList(){
            return this.filterEnableList(this.$store.state.groupList)
        },
    },
    mounted(){
        this.$nextTick(()=>{
            this.choosenChatList=this.setChatListRemark(this.chatList);
            this.$root.eventBus.$off('openTransmit').$on('openTransmit',this.openTransmit);
        })
    },
    methods:{
        getSubject(item){
            if(item.service_type===0){
                return item.subject
            }else if(item.service_type===this.systemConfig.ServiceConfig.type.FileTransferAssistant){
                return this.lang.file_transfer_assistant
            }else{
                return item.subject
            }
        },
        openTransmit(data){
            this.searchText=''
            this.dialogVisible=true;
            this.callback=data.callback;
            this.transmit_comfirm_msg=data.comfirm_msg||this.lang.transmit_comfirm_msg
            this.isStandaloneWorkstationShare = data.isStandaloneWorkstationShare || 0
            this.isServiceTypeNone = data.isServiceTypeNone || 0
            this.cid = data.cid
            this.disableChat = data.disableChat || false
            this.disableFriend = data.disableFriend || false
            this.disableGroup = data.disableGroup || false
            this.title = data.title || this.lang.transmit_title;
            // this.choosenChatList=this.setChatListRemark(this.chatList);
            this.search();
        },
        chatListFilter(list){
            let new_list = []
            if(this.isServiceTypeNone){
                new_list=list.reduce((k,v)=>{
                    if(v.service_type===0){
                        k.push(v)
                    }
                    return k
                },[])
            }else{
                new_list=list.reduce((k,v)=>{
                    if(v.service_type!==this.systemConfig.ServiceConfig.type.AiAnalyze&&v.service_type!==this.systemConfig.ServiceConfig.type.DrAiAnalyze){ //文件传输助手不允许转发
                        k.push(v)
                    }
                    return k
                },[])
            }
            return new_list
        },
        search(){
            // 去除首尾空格
            let keyword = this.searchText.trim();
            // 初始化搜索结果数组
            this.choosenChatList = [];
            this.choosenFriendList = [];
            this.choosenGroupList = [];
            
            // 空关键词：显示所有数据
            if(keyword === ''){
                if(!this.disableChat) {
                    // 使用 setChatListRemark 来处理备注替换以及过滤
                    this.choosenChatList = this.setChatListRemark(this.chatList);
                }
                if(!this.disableFriend) {
                    // 对好友列表，先把 alias 替换到 nickname
                    const processedFriends = this.friendList.map(friend => {
                        return Object.assign({}, friend, { nickname: friend.alias || friend.nickname });
                    });
                    this.choosenFriendList = this.chatListFilter(processedFriends);
                }
                if(!this.disableGroup) {
                    this.choosenGroupList = this.chatListFilter(this.groupList);
                }
            } else {
                // 非空关键词：先转为小写用于不区分大小写的匹配
                let lowerKeyword = keyword.toLowerCase();
                if(!this.disableChat) {
                    // 处理会话列表（同时进行备注替换）
                    const processedChats = this.chatList.map(chat => {
                        let clone = Object.assign({}, chat);
                        let alias = this.remarkMap[clone.fid];
                        if(alias){
                            clone.subject = alias;
                        }
                        return clone;
                    });
                    const filteredChats = processedChats.filter(chat => {
                        return this.getSubject(chat).toLowerCase().indexOf(lowerKeyword) !== -1;
                    });
                    this.choosenChatList = this.chatListFilter(filteredChats);
                }
                if(!this.disableFriend) {
                    // 处理好友列表
                    const processedFriends = this.friendList.map(friend => {
                        return Object.assign({}, friend, { nickname: friend.alias || friend.nickname });
                    });
                    const filteredFriends = processedFriends.filter(friend => {
                        return friend.nickname.toLowerCase().indexOf(lowerKeyword) !== -1;
                    });
                    this.choosenFriendList = this.chatListFilter(filteredFriends);
                }
                if(!this.disableGroup) {
                    const filteredGroups = this.groupList.filter(group => {
                        return group.subject.toLowerCase().indexOf(lowerKeyword) !== -1;
                    });
                    this.choosenGroupList = this.chatListFilter(filteredGroups);
                }
            }
        },
        submit(item,type){
            console.log(item)
            let obj={}
            if (type==1) {
                obj.subject=item.subject
                obj.cid=item.cid
                if(item.is_single_chat){
                    obj.from = 'friend'
                    obj.uid = item.fid
                }else{
                    obj.from = 'group'
                }
            }else if(type==2){
                obj.subject=item.nickname
                obj.id=item.id
                obj.from = 'friend'
                obj.uid = item.id
            }else if(type==3){
                obj.subject=item.subject
                obj.cid=item.id
                obj.from = 'group'
            }

            let message=this.transmit_comfirm_msg+obj.subject;
            if(this.isStandaloneWorkstationShare){
                if(this.cid != obj.cid){
                    let tip=this.lang.will_open_conversation
                    message += tip.replace('${subject}',obj.subject);
                }
            }
            this.$confirm(message,this.lang.tip_title,{
                confirmButtonText:this.lang.confirm_button_text,
                cancelButtonText:this.lang.cancel_button_text,
                type:'warning'
            }).then(()=>{
                this.callback&&this.callback(obj)
                this.dialogVisible=false;
            })
        },
        close(){
            this.dialogVisible=false;
            if(this.isStandaloneWorkstationShare){
                window.CWorkstationCommunicationMng.ClearStandaloneWorkstationShareExamInfo();
                if(this.$route.name=='gallery'){
                    this.$root.eventBus.$emit('showRealTimeVideo')
                }
            }
        },
        setChatListRemark(chatList) {
            chatList = this.chatListFilter(chatList);
            let arr = [];
            for(let chat of chatList){
                let chatItem=Object.assign({},chat);
                let alias = this.remarkMap[chatItem.fid]
                if (alias) {
                    chatItem.subject = alias;
                }
                if(chatItem.service_type===this.systemConfig.ServiceConfig.type.AiAnalyze||chatItem.service_type===this.systemConfig.ServiceConfig.type.DrAiAnalyze){
                    //不可以转发给小麦同学
                    continue
                }
                arr.push(chatItem);
            }
            return arr;
        },
        filterEnableList(list){
            return list.filter(item=>(item.user_status!==this.systemConfig.userStatus.Destroy))
        }
    }
}
</script>
<style lang="scss">
.transmit_dialog{
    .el-dialog{
        border-radius: 6px;
        .el-dialog__body{
            display: flex;
            flex-direction: column;
            .search_container{
                border: 1px solid #aaa;
                border-radius: 4px;
                overflow: auto;
                flex: 1;
                display: flex;
                flex-direction: column;
                .share_risk_content{
                    font-size: 14px;
                    line-height: 1.8;
                    background: #fef1ef;
                    color: #ff675c;
                    padding: 4px 20px;
                }
            }
            .search_input{
                padding: 10px 20px;
            }
            .list_container{
                padding:0 20px;
                flex: 1;
                overflow: auto;
                display: flex;
                flex-direction: column;
                .title{
                    font-size: 14px;
                    line-height: 1.6;
                    background-color: #dfe3e6;
                    padding: 4px 10px;
                    color: #000;
                }
                .transmit_item{
                    cursor:pointer;
                    padding: 9px 0;
                    display: flex;
                    align-items: center;
                    .avatar_box{
                        margin-right: 15px;
                    }
                    .transmit_subject{
                        font-size: 18px;
                        line-height: 48px;
                    }
                    &:hover{
                        background-color: #f2fcfa;
                    }
                }
            }
        }
    }
    .el-dialog__header{
        display: none !important;
    }
    .el-dialog__body{
        height: 100% !important;
        padding: 0 !important;
        display: flex;
        flex-direction: column;
    }
    .custom_header{
        display: flex;
        align-items: center;
        border-bottom: 2px solid #eee;
        padding: 20px 20px 14px;
        .title{
            font-size: 18px;
            margin: 0 10px;
            font-weight: bold;
        }
        .iconcuohao{
            position: absolute;
            right: 20px;
            top: 16px;
            font-size: 24px;
            color: #888;
            cursor: pointer;
        }
    }
    .custom_body{
        flex:1;
        padding: 20px;
        display: flex;
        flex-direction: column;
        overflow: auto;
    }
}
</style>

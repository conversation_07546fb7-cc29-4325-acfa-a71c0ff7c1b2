<template>
    <div class="modify_photo_page third_level_page">
        <mrHeader>
            <template #title>
                {{ lang.modify_photo_text }}
            </template>
        </mrHeader>
        <div class="container needsclick">
            <div class="preview">
                <img :src="curAvatarUrl" :key="user.avatar" @click="preview" />
                <p>
                    {{ lang.avatar_preview_title }}
                </p>
                <p class="tips" v-show="isTipsShow">
                    {{ lang.avatar_preview_tips }}
                </p>
            </div>

            <van-overlay v-show="uploading" class="full_loading_spinner">
                <van-loading color="#00c59d" />
            </van-overlay>
            <div class="btn_group">
                <i class="icon-xuanzhuan1 iconfont rotate rotate_left" v-show="showCropper" @click="rotate('left')"></i>
                <van-button
                    type="primary"
                    class="primary_bg choose needsclick"
                    :text="lang.choose_file"
                    @click="choosePhoto"
                />
                <i class="icon-xuanzhuan iconfont rotate rotate_right" v-show="showCropper" @click="rotate('right')"></i>
            </div>
            <input type="file" accept="image/*" ref="fileRef" class="fileRef" @change="changeFile" />
            <div id="initCropper" ref="initCropper" v-show="!showCropper" @click="choosePhoto">
                <van-icon name="photo" id="initCropperIcon" />
            </div>
            <vue-cropper
                v-show="showCropper"
                ref="cropper"
                :img="uploadedImageURL"
                :autoCrop="true"
                :fixed="true"
                :autoCropWidth="200"
                :autoCropHeight="200"
                :fixedBox="false"
                :centerBox="true"
                @realTime="realTime"
            />
        </div>
        <div class="save_btn_container">
            <van-button
                type="primary"
                class="primary_bg save_btn"
                :disabled="newAvatarUrl === ''"
                :text="lang.avatar_preview.submit"
                @click="submit"
            />
        </div>
    </div>
</template>
<script>
// import service from '../service/service'
import base from "../lib/base";
import { Toast, Button, Overlay, Icon, ImagePreview, Loading } from "vant";
import { VueCropper } from "vue-cropper";
import { checkImgExist, parseImageListToLocal } from "../lib/common_base";
import { getLocalAvatar } from "../lib/common_base";
import Tool from "@/common/tool"
export default {
    mixins: [base],
    name: "modifyPhoto",
    components: { VueCropper, vanButton: Button, vanOverlay: Overlay, vanIcon: Icon, VanLoading: Loading },
    data() {
        return {
            getLocalAvatar,
            file_input: "",
            uploadedImageURL: "",
            showCropper: false,
            uploading: false,
            newAvatarUrl: "",
            saveCold: false,
            previewText: "",
            isTipsShow: false,
        };
    },
    computed: {
        curAvatarUrl() {
            if (this.newAvatarUrl) {
                return this.newAvatarUrl;
            } else {
                return this.getLocalAvatar(this.user);
            }
        },
    },
    mounted() {
        this.$nextTick(() => {
            this.previewText = this.lang.avatar_preview.general;
            this.file_input = this.$refs.fileRef;
        });
    },
    methods: {
        async choosePhoto() {
            await Tool.queryAppPermissions(['CAMERA'])
            this.$refs.fileRef.click();
        },
        changeFile() {
            var URL = window.URL || window.webkitURL;
            var files = this.file_input.files;
            if (files && files.length) {
                let file = files[0];
                if (/^image\/.+/.test(file.type)) {
                    let tempUrl = URL.createObjectURL(file);
                    checkImgExist(tempUrl)
                        .then((img) => {
                            this.showCropper = true;
                            this.uploadedImageURL = tempUrl;
                            // 图片完整，加载成功，不做任何操作
                        })
                        .catch((err) => {
                            // 图片损坏，加载失败，撤回链接释放内存
                            URL.revokeObjectURL(this.uploadedImageURL);
                            Toast(this.lang.image_corruption_text);
                        });
                } else {
                    Toast("Please choose an image file.");
                }
            }
        },
        preview() {
            ImagePreview({
                images: [this.curAvatarUrl],
                closeable: true,
                showIndex: false,
            });
        },
        submit() {
            if (this.newAvatarUrl == "") {
                throw "Invalid Callback";
            }
            var that = this;
            that.uploading = true;
            var uid = this.user.uid;
            var controller = window.main_screen.controller;
            controller.emit(
                "set_user_portrait_img",
                {
                    uid: uid,
                    imageType: "image/png",
                    fileName: uid + "_" + new Date().getTime() + ".png",
                    file: that.newAvatarUrl,
                },
                function (is_succ, path) {
                    if ("success" == is_succ) {
                        let user = {
                            avatar: path,
                        };
                        parseImageListToLocal([user], "avatar");
                        that.$store.commit("user/updateUser", {
                            avatar: user.avatar,
                            avatar_local: user.avatar_local,
                        });
                        that.$store.commit("conversationList/updateFriendToAttendeeList", {
                            avatar: user.avatar,
                            avatar_local: user.avatar_local,
                            id: uid,
                            state: that.user.state,
                            nickname: that.user.nickname,
                        });
                        Toast(that.lang.modify_photo_success);
                        that.back();
                    } else {
                        Toast("set_user_portrait_img error");
                    }
                }
            );
        },
        realTime(data) {
            if (this.showCropper && (data.w < 200 || data.h < 200)) {
                this.isTipsShow = true;
            } else {
                this.isTipsShow = false;
            }
            this.$refs.cropper.getCropData((data) => {
                this.newAvatarUrl = data;
                // console.log(data)
            });
        },
        rotate(direction) {
            switch (direction) {
            case "left": {
                this.$refs.cropper.rotateLeft();
                return;
            }
            case "right": {
                this.$refs.cropper.rotateRight();
                return;
            }
            default: {
                throw "Wrong params at rotate function!";
            }
            }
        },
        blobToDataURI(blob, callback) {
            var reader = new FileReader();
            reader.readAsDataURL(blob);
            reader.onload = (e) => {
                callback(e.target.result);
            };
        },
    },
};
</script>
<style lang="scss">
.modify_photo_page {
    .tips {
        width: 100%;
        position: absolute;
        left: 0;
        right: 0;
        font-size: 0.7rem;
        color: red;
    }
    .container {
        background: #fff;
        position: relative;
        .preview {
            width: 100%;
            margin: 1rem auto;
            text-align: center;
            img {
                width: 6rem;
                height: 6rem;
                border-radius: 5rem;
            }
        }
        .choose {
            display: inline-block;
            border: none;
            font-size: 0.8rem;
            line-height: 1.6rem;
            border-radius: 0.2rem;
            margin: 0.3rem 0.5rem;
        }
        .fileRef {
            display: none;
        }
        .vue-cropper {
            height: 20rem;
        }
    }
    .save_btn_container {
        // margin: 0 0.5rem;
        .save_btn {
            box-sizing: border-box;
            display: block;
            width: 100%;
            height: 100%;
            border: none;
            font-size: 1rem;
            line-height: 100%;
            // margin: 1rem 0 0.6rem;
            padding: 0.8rem;
            // border-radius: 0.2rem;
        }
    }
    .btn_group {
        text-align: center;
        margin-bottom: 1rem;
    }
    .van-loading {
        width: 100%;
        height: 100%;
        justify-content: center;
        align-items: center;
        display: flex;

        .van-loading__spinner{
            width: 2.8rem;
            height: 2.8rem;
        }
    }
    .rotate {
        position: relative;
        font-size: 2rem;
    }
    .rotate_left,
    .rotate_right {
        top: 0.7rem;
        color: #00c59d;
        cursor: pointer;
    }

    #initCropper {
        width: 100vw;
        height: 20rem;
        text-align: center;
        line-height: 20rem;
        background-color: #f7f8fa;

        #initCropperIcon {
            color: rgb(220, 222, 224);
            font-size: 2rem;
        }
    }
}
</style>

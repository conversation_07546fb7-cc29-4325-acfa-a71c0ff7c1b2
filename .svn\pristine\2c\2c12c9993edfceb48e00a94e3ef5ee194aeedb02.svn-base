import axios from 'axios'
import moment from 'moment'
import {MessageBox} from 'element-ui'
import Tool from '../lib/tool'
// const NETWORK_ERROR_MESSAGE='网络异常'
const SERVER_ERROR_MESSAGE='系统异常'
const UNEXPETED_STATUS=-1
// const EXPETED_STATUS=1
// const UNLOGIN_STATUS=100
// const SUCCESS_STATUS=0
// const COMMON_STATUS=1000
// 获取几天前的0点时间戳
const moreThanOneMonth = (time1, time2) => {
    const nextMonth = moment(time1).add(1, 'M');
    return nextMonth.isSameOrBefore(time2);
}
const api=axios.create({
    headers:{
        'Content-Type':'application/json'
    },
    timeout:1000*60*5,
    transformRequest:[(data)=>{
        if(!data){
            return ;
        }
        return JSON.stringify(data.data)
    }],
    transformResponse:[(data)=>{
        if(data){
            try{
                data=JSON.parse(data)
            }catch(e){
                console.log(SERVER_ERROR_MESSAGE)
            }
        }
        return data;
    }]
})
api.interceptors.request.use((config)=>{
    const token = window.localStorage.getItem('stat_token')
    config.headers.token = token
    const params = config.data.data.bizContent || {}
    if ('timeScope' in params) {
        params.startTime = params.timeScope[0].getTime()
        params.endTime = params.timeScope[1].getTime()
        delete params.timeScope
    }
    // 不应该在拦截器写死参数
    // if ('dataFrom' in params) {
    //     const query = JSON.parse(window.localStorage.getItem('stat_query'))
    //     if (query) {
    //         for (const key in query) {
    //             params[key] = query[key]
    //         }
    //     }
    // }
    return config
})
api.interceptors.response.use(
    (response)=>{
        const lang=window.vm.$store.state.language
        const status=typeof response.status!=='undefined' ? response.status:UNEXPETED_STATUS
        let error_code;
        let key='unknown_error';
        let result;
        if (status===200) {
            result=response;
            if (response.data.error_code) {
                error_code=response.data.error_code;
                key=response.data.key;
            }else{
                error_code=0;
            }
        }else{
            error_code=-1;
            key='server_abnormal_retry_again'
            result=Promise.reject({
                data:{
                    key:'server_abnormal_retry_again',
                    error_code:-1,
                }
            })
        }
        if (!isNaN(error_code)&&error_code!==0) {
            MessageBox.alert("error", {
                message: lang[response.data.key],
                center: true
            })
        }
        return result;

    },
    (err)=>{
        return Promise.reject({
            data:{
                key:'network_error_tip',
                error_code:-1,
                more_detail:err
            }
        })
    }
)
export default api

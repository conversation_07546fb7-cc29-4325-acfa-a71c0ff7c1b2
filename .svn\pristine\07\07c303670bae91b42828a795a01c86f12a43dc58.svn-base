<template>
    <div class="tv_wall_page">
        <div class="tv_wall_header">
            <div class="tv_wall_header-left">
                <div class="tv_wall_back_btn" @click="leaveTvWall">{{ lang.back_button }}</div>
                <div class="tv_wall_logo">
                    <img src="static/resource_pc/images/logo.png" alt="" srcset="" />
                    <p>{{ subjectTitle }}</p>
                </div>
            </div>
            <div class="tv_wall_header-mid tv_wall_title">{{ this.finalSubject }}</div>
            <div class="tv_wall_header-right">
                <div v-if="isShowFormMenu">
                    <el-form :inline="true" :model="searchForm" class="demo-form-inline">
                        <el-form-item label="">
                            <el-cascader
                                :placeholder="lang.search_invite_member"
                                :options="memberList"
                                :props="{ multiple: false }"
                                @visible-change="cascaderVisibleChange"
                                filterable
                                style="width: 100%"
                                v-model="searchForm.groupValue"
                                id="inviteCascader"
                            >
                            </el-cascader>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="onSubmitSearch({needAwait:true})">{{ lang.query_btn }}</el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </div>
        </div>

        <div class="tv_wall_grid" v-loading="isSearching">
            <no-Data v-if="wallList.length == 0" :text="lang.no_realtime_tip"></no-Data>
            <template v-else>
                <div class="row" v-for="(row, rowNum) in showDate" :key="rowNum">
                    <div class="column" v-for="column in row" :key="column.agora_uid">
                        <div class="tv_wall_item" v-if="!column.empty">
                            <div
                                class="monitor"
                                :ref="`monitor_${column.agora_uid}_${column.channelId}`"
                                :id="`monitor_${column.agora_uid}_${column.channelId}`"
                            >
                                <div
                                    class="video_container"
                                    :id="`video_${column.agora_uid}_${column.channelId}`"
                                ></div>
                                <img src="static/resource_pc/images/poster_video.png" />
                                <p class="longwrap" v-if="column.status == 0 && !column.isConversationLiving">
                                    {{ lang.not_connected }}
                                </p>
                                <p class="longwrap" v-if="column.status == 0 && column.isConversationLiving">
                                    {{ lang.live_session_in_progress }}
                                </p>
                                <p class="longwrap" v-if="column.status == 1">{{ checkShowVideo(column) }}</p>
                            </div>
                            <div class="footer">
                                <div class="nickname">
                                    <p class="longwrap">
                                        {{ column.nickname || lang.not_connected }}_{{
                                            column.device_name || column.deviceId
                                        }}
                                    </p>
                                </div>
                                <el-button
                                    @click="enterConversationToSubscribeDoppler(column)"
                                    type="primary"
                                    size="mini"
                                    class="fr"
                                    v-if="checkEnterConversationShow(column)"
                                    >{{ lang.enter_conversation }}</el-button
                                >
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        </div>

        <div class="tv_wall_footer">
            <div v-if="currentTvWallForm.id" class="tv_wall_footer_left">
                <i @click="openMonitorWallSetting" class="icon iconfont iconsetting tv_wall_setting"></i>
            </div>
            <div class="pagination tv_wall_footer_mid">
                <el-pagination
                    background
                    layout="prev,pager,next"
                    :current-page.sync="pageNum"
                    @current-change="currentChange"
                    :page-size="level * level"
                    :total="wallList.length"
                >
                </el-pagination>
            </div>
            <div class="tv_wall_footer_right">
                <img @mousedown.stop src="static/resource_pc/images/logob.png" />
            </div>
        </div>

        <div v-if="isMaxZoom" class="max_zoom">
            <div class="max_foot">
                {{ maxScanRoom.name }}{{ maxScanRoom.user_id ? "_" + maxScanRoom.user_nickname : "" }}
                <el-button
                    v-if="maxScanRoom.user_id"
                    @click="requestRemotePushDoppler(maxScanRoom)"
                    type="primary"
                    size="mini"
                    class="fr"
                    >{{ lang.enter_conversation }}</el-button
                >
            </div>
        </div>
        <div v-loading.fullscreen="isLoading" :element-loading-text="lang.starting_rt_video"></div>
        <LiveRoomWeb @leaveChannelAux="HandleLeaveChannelAux" ref="tvWallLiveRoomWeb" v-if="isPCBrowser"></LiveRoomWeb>
        <LiveRoom @leaveChannelAux="HandleLeaveChannelAux" ref="tvWallLiveRoom"></LiveRoom>
        <router-view class="tv_modal"></router-view>
    </div>
</template>
<script>
import base from "../lib/base";
import appOperateTool from "../lib/appOperateTool";
import service from "../service/service";
import { cloneDeep } from "lodash";
import noData from "../MRComponents/noData.vue";
import CMonitorWallPlayWeb from "@/common/CLiveConferenceWeb/CMonitorWallPlayWeb";
import Tool from "@/common/tool.js";
import LiveRoom from "../components/live/liveRoom";
import LiveRoomWeb from "../components/live/liveRoomWeb";
import CMonitorWallRoom from "@/common/socket/CMonitorWallRoom";
import {Logger} from '@/common/console.js'
class tvWallLogger {
    constructor() {
        this.log = function ({ message, data }) {
            Logger.save({
                message,
                eventType: `tvWall_log`,
                data
            });
        };
        this.error = function ({ message, data }) {
            Logger.saveError({
                message,
                eventType: `tvWall_error`,
                data
            });
        };
    }
}
const logger = new tvWallLogger();
export default {
    mixins: [base, appOperateTool],
    name: "TvWallAgoraWebPage",
    components: {
        noData,
        LiveRoom,
        LiveRoomWeb
    },
    data() {
        return {
            isSearching: false,
            pageNum: 1,
            isMaxZoom: 0,
            maxScanRoom: null,
            currentVideoWallData: null,
            waitingJoinRoom: false,
            waitingJoinRoomTimer: null,
            cid: 0,
            wallList: [],
            showDate: [],
            conversation: null,
            tvWallInfo: {
                appid: "",
                channel: [],
                channelMap: {},
            },
            publishedList: [],
            interval: null,
            searchForm: {
                groupValue: [],
            },
            memberList: [],
            currentTvWallForm: {
                id: "",
                type: "",
            },
            isLeaved: false,
            searchSubject: "",
            PROJECT_NOV: process.env.VUE_APP_PROJECT_NOV,
            showVideoList: [],
            cascaderVisible: false,
            isDestroy: false,
            monitorWallRoom: null,
            joiningRoom: false,
        };
    },
    computed: {
        level() {
            return "undefined" == typeof this.user.tv_wall_setting ||
                "undefined" == typeof this.user.tv_wall_setting.level
                ? 3
                : this.user.tv_wall_setting.level;
        },
        conferenceState() {
            return (
                this.$store.state.liveConference[this.cid] && this.$store.state.liveConference[this.cid].conferenceState
            );
        },
        tvWallCid() {
            return this.$route.params.cid;
        },
        tvWallConversation() {
            return this.conversationList[this.tvWallCid];
        },
        groupset_id() {
            return this.$route.params.groupset_id;
        },
        groupSubject() {
            return this.tvWallConversation && this.tvWallConversation.subject;
        },
        groupSetSubject() {
            return this.currentGroupset.subject;
        },
        currentGroupset() {
            return this.$store.state.groupset.currentGroupset;
        },
        isLoading() {
            return this.waitingJoinRoom;
        },
        isShowFormMenu() {
            return !this.groupset_id && String(this.tvWallCid) === "0";
        },
        finalSubject() {
            if (this.isShowFormMenu) {
                return this.searchSubject;
            } else {
                if (this.groupset_id) {
                    return this.groupSetSubject;
                } else {
                    return this.groupSubject;
                }
            }
        },
        subjectTitle() {
            return this.lang.envTitleMap[this.PROJECT_NOV];
        },
        LiveConferenceData() {
            return (
                (this.$store.state.liveConference[this.cid] &&
                    this.$store.state.liveConference[this.cid].LiveConferenceData) ||
                {}
            );
        }
    },
    beforeRouteLeave(to, from, next) {
        this.closeChatWindow(to, from, next);
    },
    created() {
        if(!window.hasOwnProperty('agoraClient')){
            window.agoraClient  = {}
        }
    },
    mounted() {
        let bodyContainer = document.querySelector(".flex_container");
        bodyContainer.style.minWidth = "unset";
        this.$nextTick(async () => {
            let that = this;
            this.$root.eventBus
                .$off("closeMonitorWallSetting")
                .$on("closeMonitorWallSetting", this.closeMonitorWallSetting);
            // window.main_screen.controller.emit("open_monitor_wall")
            this.$root.eventBus
                .$off("workStationPushDopplerResult")
                .$on("workStationPushDopplerResult", this.workStationPushDopplerResult);
            this.$root.eventBus.$on(`main_screen_gateway_connect`, async () => {
                if (this.isDestroy) {
                    return;
                }
                if (this.$route.name === "tv_wall_web") {
                    await this.initVideoWallSdk();
                    this.renderVideoWall();
                }
            });
            this.$root.eventBus.$on(`main_screen_gateway_disconnect`, async () => {
                if (this.isDestroy) {
                    return;
                }
                this.leaveChannel();
            });
            if (this.isShowFormMenu) {
                await this.getMemberList();
                if (localStorage.getItem("tvWallSearchForm")) {
                    let lastTvWallForm = JSON.parse(localStorage.getItem("tvWallSearchForm"));
                    if(lastTvWallForm.type === 'group'){
                        const index = this.memberList[0].children.findIndex(item=>String(item.groupId) === String(lastTvWallForm.id))
                        if(index>-1){
                            this.searchForm.groupValue = [lastTvWallForm.type, lastTvWallForm.id];
                            this.onSubmitSearch();
                        }
                    }else if(lastTvWallForm.type === 'groupset'){
                        const index = this.memberList[1].children.findIndex(item=>String(item.id) === String(lastTvWallForm.id))
                        if(index>-1){
                            this.searchForm.groupValue = [lastTvWallForm.type, lastTvWallForm.id];
                            this.onSubmitSearch();
                        }
                    }
                    if(this.searchForm.groupValue.length === 0){
                        this.searchTvWallByFirstOption()
                    }
                }else{
                    this.searchTvWallByFirstOption()
                }
            } else {
                this.isSearching = true;
                try{
                    this.initRequestData();
                    await this.initVideoWallSdk();
                    this.isSearching = false;
                    await this.renderVideoWall();
                }catch(error){
                    logger.error({ message: "initVideoWallSdk error", data: error.message });
                }finally{
                    this.isSearching = false;
                }
            }
        });
    },
    beforeDestroy() {
        this.leaveChannel();
        // window.agoraClient = {};
        window.main_screen.controller.emit("close_monitor_wall");
        this.$store.commit("dynamicGlobalParams/updateDynamicGlobalParams", {
            tv_wall_mode: false,
        });
        let bodyContainer = document.querySelector(".flex_container");
        bodyContainer.style.minWidth = "1200px";
        this.isDestroy = true;
    },
    methods: {
        closeChatWindow(to, from, next) {
            logger.log({ message: "closeChatWindow", data: { to, from } });
            if (window.livingStatus === 1) {
                next(false);
                return;
            }
            if (window.livingStatus===2) {
                if(this.isPCBrowser){
                    this.$refs.tvWallLiveRoomWeb.leaveChannel()
                    next(false)
                    return;
                }

            }
            next(true);
        },
        searchTvWallByFirstOption(){
            if(this.memberList[0].children.length>0){
                this.searchForm.groupValue = [this.memberList[0].value, this.memberList[0].children[0].value]
                this.onSubmitSearch();
            }else if(this.memberList[1].children.length>0){
                this.searchForm.groupValue = [this.memberList[1].value, this.memberList[1].children[0].value]
                this.onSubmitSearch();
            }
        },
        initRequestData() {
            if (this.groupset_id) {
                this.currentTvWallForm = {
                    type: "groupset",
                    id: this.groupset_id,
                };
            } else {
                this.currentTvWallForm = {
                    type: "group",
                    id: this.tvWallCid,
                };
            }
        },
        getMemberList() {
            return new Promise((resolve, reject) => {
                window.main_screen.getTvWallGroupListByExistDevice({}, (res) => {
                    logger.log({ message: "getMemberList", data: res });
                    if (res.error_code === 0 && res.data) {
                        this.memberList = this.handleConcatList(res.data.group, res.data.groupset);
                        resolve(res);
                    } else {
                        reject(res);
                    }
                });
            });
        },
        handleConcatList(oGroupList, oGroupsetList) {
            let result = [];
            let groupList = oGroupList;
            groupList.map((item) => {
                item.value = item.groupId;
                item.label = item.name;
            });
            result.push({
                value: "group",
                label: this.lang.group,
                children: groupList,
            });
            if (this.functionsStatus.groupset) {
                let groupSetList = oGroupsetList;
                groupSetList.map((item) => {
                    item.value = item.id;
                    item.label = item.name;
                });
                result.push({
                    value: "groupset",
                    label: this.lang.groupset_text,
                    children: groupSetList,
                });
            }

            return result;
        },
        async onSubmitSearch({needAwait=false}={}) {
            if (this.searchForm.groupValue.length === 0) {
                return;
            }
            this.isSearching = true;
            this.currentTvWallForm = {
                type: this.searchForm.groupValue[0],
                id: this.searchForm.groupValue[1],
            };
            localStorage.setItem("tvWallSearchForm", JSON.stringify(this.currentTvWallForm));
            try{
                await this.leaveChannel();
                await new Promise(resolve => {
                    setTimeout(async()=>{
                        await this.initVideoWallSdk();
                        this.isSearching = false;
                        await this.renderVideoWall();
                        this.searchSubject = this.getMemberListLabel();
                        resolve();
                    },needAwait?1000:0)
                });
            }catch(error){
                logger.error({ message: "onSubmitSearch error", data: error.message });
            }finally{
                this.isSearching = false;
            }
        },
        getMemberListLabel() {
            let label = "";
            this.memberList.forEach((typeItem) => {
                if (typeItem.value === this.currentTvWallForm.type) {
                    typeItem.children.forEach((item) => {
                        if (item.value === this.currentTvWallForm.id) {
                            label = item.label;
                        }
                    });
                }
            });
            return label;
        },
        currentChange(pageNum) {
            this.changeShowDate();
        },
        async initVideoWallSdk() {
            return new Promise(async (resolve, reject) => {
                try {
                    // window.agoraClient = {};
                    const { appId, socketAddress } = await this.requestOpenTvWall();
                    logger.log({ message: "initVideoWallSdk", data: { appId, socketAddress } });
                    await this.createCMonitorWallRoom({
                        socketAddress,
                        query: {
                            userId: this.user.uid,
                        },
                    });
                    resolve(appId);
                } catch (error) {
                    reject(error);
                }
            });
        },
        renderVideoWall() {
            return new Promise(async (resolve, reject) => {
                this.tvWallInfo = {
                    appid: "",
                    channel: [],
                    channelMap: {},
                }
                this.publishedList = []
                try {
                    const res = await this.getMonitorWallList();
                    resolve(res);
                    if (this.interval) {
                        clearInterval(this.interval);
                        this.interval = null;
                    }
                    this.interval = setInterval(() => {
                        if(this.waitingJoinRoom || this.isLeaved || this.isSearching || this.joiningRoom){
                            return
                        }
                        if (this.interval && this.$route.name === "tv_wall_web") {
                            this.getMonitorWallList();
                        }
                    }, 3000);
                } catch (error) {
                    reject(error);
                }

            })
        },
        async initAgoraChannel(channel, agora_uid) {
            //初始化房间
            if (channel) {
                if (window.agoraClient.hasOwnProperty(channel)) {
                    return;
                }
                window.agoraClient[channel] = new CMonitorWallPlayWeb({
                    channelId: channel,
                    token: this.tvWallInfo.channelMap[channel].token,
                    uid: this.tvWallInfo.channelMap[channel].uid,
                    appId: this.tvWallInfo.appid,
                    playDom: `video_`,
                    monitorWallRoom: this.monitorWallRoom,
                });
                window.agoraClient[channel].initAgoraRTC();
                window.agoraClient[channel].event.on("handleUserJoined", this.handleUserJoined);
                window.agoraClient[channel].event.on("handleUserLeft", this.handleUserLeft);
                window.agoraClient[channel].event.on("handleUserPublished", this.handleUserPublished);
                window.agoraClient[channel].event.on("handleUserUnPublished", this.handleUserUnPublished);
                await this.joinTvWallRoom(channel);
            }
        },
        async joinTvWallRoom(channel) {
            if (this.isLeaved) {
                return;
            }
            try {
                const isSuc = await window.agoraClient[channel].JoinChannelMonitor();
            } catch (error) {
                logger.error({ message: "joinTvWallRoom error", data: error.message });
            }
        },
        handleUserJoined(data) {
            logger.log({ message: "handleUserJoined", data });
            if (this.isLeaved) {
                return;
            }
            const channelId = data.channelId;
            const uid = data.uid;
            if (!this.tvWallInfo.channelMap[channelId].remoteUsers.includes(uid)) {
                this.tvWallInfo.channelMap[channelId].remoteUsers.push(uid);
            }

            this.publishedList.push(`${uid}_${channelId}`);
            // this.subscribeVideo(uid,channelId)
        },
        handleUserLeft(data) {
            if (this.isLeaved) {
                return;
            }
            logger.log({ message: "handleUserLeft", data });
            const channelId = data.channelId;
            const uid = data.uid;
            const index = this.tvWallInfo.channelMap[channelId].remoteUsers.indexOf(uid);
            if (index > -1) {
                this.tvWallInfo.channelMap[channelId].remoteUsers.splice(index, 1);
            }
            this.publishedList = this.publishedList.filter((item) => item !== `${uid}_${channelId}`);

            // this.stopSubscribeVideo(uid,channelId)
        },
        handleStreamTypeChanged(uid, streamType, channel) {
            logger.log({ message: "handleStreamTypeChanged", data: { uid, streamType, channel } });
        },
        async subscribeVideo(uid, channelId) {
            if (this.isLeaved) {
                return;
            }
            logger.log({ message: "subscribeVideo", data: { uid, channelId } });
            if (!uid || !channelId) {
                return;
            }
            const streamInfo = await window.agoraClient[channelId].SetRemoteVideoStreamType({
                uid,
                streamType: this.calcVideoStreamType(),
            });
            const res = await window.agoraClient[channelId].SubscribeRemoteStreamMonitor(uid);
        },
        async stopSubscribeVideo(uid, channelId) {
            if (this.isLeaved) {
                return;
            }
            if (!uid || !channelId) {
                return;
            }
            window.agoraClient[channelId] &&
                (await window.agoraClient[channelId].StopSubscribeRemoteStreamMonitor(uid));
        },
        handleUserPublished(data) {
            logger.log({ message: "handleUserPublished", data });
            this.subscribeVideo(data.uid, data.channelId);
        },
        handleUserUnPublished(data) {
            logger.log({ message: "handleUserUnPublished", data });
            this.stopSubscribeVideo(data.uid, data.channelId);
        },
        checkCanShowVideo() {
            return !this.isLoading && !this.cascaderVisible;
        },
        changeShowDate() {
            this.sortScanRoomList();
            this.$nextTick(() => {
                let length = this.level * this.level;
                let start = (this.pageNum - 1) * length;
                let end = start + length;
                let showDate = [];
                let uidList = [];
                let tempList = this.wallList.slice(start, end);
                let realIndex = 0;
                for (let item of this.wallList) {
                    item.isShow = false;
                }
                for (let row = 0; row < this.level; row++) {
                    let rowArr = [];
                    for (let column = 0; column < this.level; column++) {
                        let item = { empty: true };

                        if (tempList[realIndex]) {
                            tempList[realIndex].position = `${row},${column},${this.level},${this.pageNum}`;
                            tempList[realIndex].isShow = true;
                            rowArr.push(tempList[realIndex]);
                            uidList.push(tempList[realIndex]);
                        } else {
                            rowArr.push(item);
                        }
                        realIndex++;
                    }
                    showDate.push(rowArr);
                }
                let oldShowDate = cloneDeep(this.showDate);
                this.showDate = showDate;
                setTimeout(() => {
                    Object.keys(this.tvWallInfo.channelMap).forEach((channel) => {
                        //取消订阅不需要的视频
                        window.agoraClient[channel].stopUnVisibleSubscribe();
                        window.agoraClient[channel].checkVisibleSubscribe();
                    });
                }, 0);
            });
        },
        requestOpenTvWall() {
            return new Promise((resolve, reject) => {
                window.main_screen.requestOpenTvWall(
                    {
                        type: this.currentTvWallForm.type,
                        id: this.currentTvWallForm.id,
                        userId: this.user.uid,
                    },
                    (res) => {
                        if (res.error_code === 0 && res.data) {
                            resolve({ appId: res.data.appId, socketAddress: res.data.socketAddress });
                        } else {
                            logger.error({ message: "requestOpenTvWall error", data: res });
                            reject(res);
                        }
                    }
                );
            });
        },
        requestCloseTvWall() {
            return new Promise((resolve, reject) => {
                window.main_screen.requestCloseTvWall(
                    {
                        type: this.currentTvWallForm.type,
                        id: this.currentTvWallForm.id,
                    },
                    (res) => {
                        if (res.error_code === 0 && res.data) {
                            resolve(res);
                        } else {
                            reject(res);
                        }
                    }
                );
            });
        },
        requestTvWallList() {
            return new Promise((resolve, reject) => {
                window.main_screen.getTvWallList(
                    {
                        type: this.currentTvWallForm.type,
                        id: this.currentTvWallForm.id,
                    },
                    (res) => {
                        if (res.error_code === 0 && res.data) {
                            resolve(res);
                        } else {
                            reject(res);
                        }
                    }
                );
            });
        },
        async getMonitorWallList() {
            if (this.isLeaved) {
                return;
            }
            try {
                const res = await this.requestTvWallList();
                logger.log({ message: "getMonitorWallList", data: res });
                if(this.isLeaved || this.waitingJoinRoom || this.isSearching || this.joiningRoom){
                    return
                }
                let wallList = [];
                this.tvWallInfo.appid = res.data.appId;
                this.tvWallInfo.channel = res.data.channel;
                this.tvWallInfo.channel.forEach((item) => {
                    wallList.push(item.channelUser);
                    if (!this.tvWallInfo.channelMap.hasOwnProperty(item.channelId)) {
                        this.tvWallInfo.channelMap[item.channelId] = {
                            ...item,
                            remoteUsers: [],
                            subscribeList: [],
                        };
                        this.initAgoraChannel(item.channelId, item.channelUser.agora_uid);
                    }
                });
                let same = false;
                for (let i = this.wallList.length - 1; i >= 0; i--) {
                    same = false;
                    for (let j = wallList.length - 1; j >= 0; j--) {
                        if (wallList[j].id === this.wallList[i].id) {
                            same = true;
                        }
                    }
                    if (!same) {
                        this.stopSubscribeVideo(this.wallList[i].agora_uid, this.wallList[i].channelId);
                        this.wallList.splice(i, 1);
                    }
                }
                wallList.forEach((nItem) => {
                    const index = this.wallList.findIndex((oItem) => oItem.id === nItem.id);
                    if (index > -1) {
                        this.$set(this.wallList, index, nItem);
                    } else {
                        this.wallList.push(nItem);
                    }
                });
                this.changeShowDate();

                // this.wallList.push({
                //     agora_channel_id: 4,
                //     agora_uid: 1061,
                //     audio: 0,
                //     channelId: "50257871494",
                //     created_at: "2022-07-18T01:59:02.000Z",
                //     id: 21,
                //     nickname: "test",
                //     status: 0,
                //     stream_type: 0,
                //     updated_at: "2022-09-16T01:49:40.000Z",
                //     user_id: 70,
                //     video: 0

                // })
            } catch (error) {
                logger.error({ message: "getMonitorWallList error", data: error.message });
            }
        },
        sortScanRoomList() {
            if (
                this.user.tv_wall_setting &&
                this.user.tv_wall_setting.list &&
                this.user.tv_wall_setting.list.length > 0
            ) {
                let arr = [];
                for (let i in this.user.tv_wall_setting.list) {
                    let id = this.user.tv_wall_setting.list[i];

                    for (let j in this.wallList) {
                        let item = this.wallList[j];
                        if (id == item.id) {
                            let temp = this.wallList.splice(j, 1);
                            if (temp.length > 0) {
                                arr.unshift(temp[0]);
                            }
                            break;
                        }
                    }
                }

                for (let k in arr) {
                    let item = arr[k];
                    if (item) {
                        this.wallList.unshift(item);
                    }
                }
            }
            // this.publishedList.includes(`${column.agora_uid}_${column.channelId}`)
            this.wallList.sort((a, b) => {
                const aPublished = this.publishedList.includes(`${a.agora_uid}_${a.channelId}`);
                const bPublished = this.publishedList.includes(`${b.agora_uid}_${b.channelId}`);

                if (aPublished && !bPublished) {
                    return -1; // a 在 publishedList 中，b 不在，a 排在前面
                } else if (!aPublished && bPublished) {
                    return 1; // b 在 publishedList 中，a 不在，b 排在前面
                } else {
                    if (a.status === 1 && a.status !== b.status) {
                        return -1; // a 的 status 为 1，b 的 status 不为 1，a 排在前面
                    } else if (a.status !== 1 && b.status === 1) {
                        return 1; // b 的 status 为 1，a 的 status 不为 1，b 排在前面
                    } else {
                        return 0; // a 和 b 的 status 相同，保持原有顺序
                    }
                }
            });
        },
        openMonitorWallSetting() {
            const path =
                this.$route.fullPath +
                `/tv_wall_setting?id=${this.currentTvWallForm.id}&type=${this.currentTvWallForm.type}`;
            this.$router.push(path);
        },
        closeMonitorWallSetting(data) {
            logger.log({ message: "closeMonitorWallSetting", data });
            try {
                if (data.reset) {
                    this.pageNum = 1;
                }
                this.changeShowDate();
            } catch (error) {
                logger.error({ message: "closeMonitorWallSetting error", data: error.message });
            }
        },
        createCMonitorWallRoom({ socketAddress, query }) {
            return new Promise((resolve, reject) => {
                const options = {
                    socketAddress,
                    query,
                };
                if (this.monitorWallRoom) {
                    this.destroyCMonitorWallRoom();
                }
                this.monitorWallRoom = new CMonitorWallRoom(options);
                this.monitorWallRoom.gateway.on("conference_living_ping", (data) => {
                    if (this.isLeaved) {
                        return;
                    }
                    let channelIds = [];
                    Object.keys(window.agoraClient).forEach((channel) => {
                        if (window.agoraClient[channel].joined) {
                            channelIds.push(channel);
                        }
                    });
                    this.ServicePingChannel(channelIds);
                });
                this.monitorWallRoom.gateway.on("connect", () => {
                    this.monitorWallRoom.gateway.emit("checkout", {
                        userId: this.user.uid,
                    });
                    resolve(this.monitorWallRoom);
                });
            });
        },
        destroyCMonitorWallRoom() {
            if (this.monitorWallRoom) {
                this.monitorWallRoom.release();
                this.monitorWallRoom = null;
            }
        },
        async tvWallStartJoinRoom({ main = 0, aux = 0, isSender = 0, videoSource = "", from = "tv_wall_play" }, cid) {
            this.joiningRoom = true;
            try{
                this.$nextTick(async () => {
                    if(this.isPCBrowser){
                        await this.$refs.tvWallLiveRoomWeb.startJoinRoom({
                            main,
                            aux,
                            isSender,
                            videoSource,
                            from,
                            cid,
                        });
                        this.joiningRoom = false;
                    }else{
                        await this.$refs.tvWallLiveRoom.startJoinRoom({
                            main,
                            aux,
                            isSender,
                            videoSource,
                            from,
                            cid,
                        });
                        this.joiningRoom = false;
                    }
                });
            }catch(error){
                logger.error({ message: "tvWallStartJoinRoom error", data: error.message });
            }finally{
                this.joiningRoom = false;
            }
        },
        checkIsFriend(uid) {
            let friendList = this.$store.state.friendList.list;
            for (let friend of friendList) {
                if (friend.id == uid) {
                    return true;
                }
            }
            return false;
        },
        enterConversationToSubscribeDoppler(target) {
            if (!this.functionsStatus.live) {
                //todo 提示直播被禁用
                this.$alert(this.lang.live_disabled);
                return;
            }
            if (target.user_id === this.user.uid) {
                return;
            }
            if (!this.checkIsFriend(target.user_id)) {
                Tool.openCommonDialog({
                    buttons: [this.lang.supply_case_close_btn],
                    message: this.lang.not_friend_tips,
                });
                return;
            }
            this.waitingJoinRoom = true;
            this.leaveChannel({ isTmp: true });
            let params = {
                userId: target.user_id,
            };
            this.waitingJoinRoomTimer = setTimeout(async () => {
                if (this.waitingJoinRoom && window.vm.$route.name !== "tv_wall_conference") {
                    this.waitingJoinRoom = false;
                    Tool.openCommonDialog({
                        buttons: [this.lang.supply_case_close_btn],
                        message: this.lang.requestTimeout,
                    });
                    await this.initVideoWallSdk();
                    await this.renderVideoWall();
                }
            }, 30000);
            setTimeout(() => {
                window.main_screen.requestRemotePushDoppler(params, async (data) => {
                    logger.log({ message: "requestRemotePushDoppler", data });

                    if (!data.error_code) {
                        this.waitingJoinRoom = true;
                        setTimeout(() => {
                            window.main_screen.controller.emit("start_conversation_by_monitor_wall", data);
                        }, 0);
                    } else {
                        clearTimeout(this.waitingJoinRoomTimer);
                        this.waitingJoinRoomTimer = null;
                        this.waitingJoinRoom = false;
                        Tool.openCommonDialog({
                            buttons: [this.lang.supply_case_close_btn],
                            message: this.lang.ultrasync_box_disabled,
                        });
                        await this.initVideoWallSdk();
                        await this.renderVideoWall();
                    }
                });
            }, 0);
        },
        async workStationPushDopplerResult(data) {
            logger.log({ message: "workStationPushDopplerResult", data });
            if(!this.waitingJoinRoom){ //如果没有正在等待加入结果，就不要处理
                return
            }
            if (data.status === 1) {
                this.openConversation(data.groupId, 10, null, (is_suc, conversation) => {
                    setTimeout(async () => {
                        if (!is_suc) {
                            await this.initVideoWallSdk();
                            await this.renderVideoWall();
                            return;
                        }
                        this.cid = conversation.id;
                        this.conversation = this.conversationList[this.cid];
                        if (this.conferenceState) {
                            // this.leaveChannel({isTmp:true})
                            await this.tvWallStartJoinRoom({ main: 0, aux: 1, isSender: 0 }, this.cid);
                            clearTimeout(this.waitingJoinRoomTimer);
                            this.waitingJoinRoomTimer = null;
                            this.waitingJoinRoom = false;
                        } else {
                            setTimeout(async () => {
                                // this.leaveChannel({isTmp:true})
                                await this.tvWallStartJoinRoom({ main: 0, aux: 1, isSender: 0 }, this.cid);
                                clearTimeout(this.waitingJoinRoomTimer);
                                this.waitingJoinRoomTimer = null;
                                this.waitingJoinRoom = false;
                            }, 1000);
                        }
                    }, 800);
                });
            } else {
                clearTimeout(this.waitingJoinRoomTimer);
                this.waitingJoinRoomTimer = null;
                this.waitingJoinRoom = false;
                if (data.status === 0) {
                    Tool.openCommonDialog({
                        buttons: [this.lang.supply_case_close_btn],
                        message: this.lang.ultrasync_box_disabled,
                    });
                    await this.initVideoWallSdk();
                    await this.renderVideoWall();
                }
            }
        },
        async HandleLeaveChannelAux() {
            await this.initVideoWallSdk();
            await this.renderVideoWall();
        },
        calcVideoStreamType() {
            if (this.level * this.level <= 9) {
                //使用大流
                return "large";
            } else {
                //小流
                return "small";
            }
        },
        // 客户离开信道
        async leaveChannel({isTmp=false}={}){
            return new Promise((resolve,reject)=>{
                if(this.interval){
                    clearInterval(this.interval)
                    this.interval = null
                }
                // if(!isTmp){
                //     this.currentTvWallForm.id&&this.requestCloseTvWall()
                // }
                this.publishedList = []
                Object.keys(this.tvWallInfo.channelMap).forEach(key=>{
                    this.tvWallInfo.channelMap[key].remoteUsers = {}
                    delete this.tvWallInfo.channelMap[key]
                })
                if(window.agoraClient){
                    Object.keys(window.agoraClient).forEach(channel=>{
                        window.agoraClient[channel].event.off('handleUserJoined',this.handleUserJoined)
                        window.agoraClient[channel].event.off('handleUserLeft',this.handleUserLeft)
                        window.agoraClient[channel].event.off('handleUserPublished',this.handleUserPublished)
                        window.agoraClient[channel].event.off('handleUserUnPublished',this.handleUserUnPublished)
                        window.agoraClient[channel].LeaveChannelMonitor()
                        delete window.agoraClient[channel]
                        console.log(`离开房间成功----->${channel}`);
                    })
                }
                if(this.monitorWallRoom){
                    this.destroyCMonitorWallRoom()
                    resolve(true)

                }else{
                    resolve(true)
                }
            })

        },
        cascaderVisibleChange(val) {
            this.cascaderVisible = val;
        },
        leaveTvWall() {
            this.isLeaved = true;
            this.currentTvWallForm.id && this.requestCloseTvWall();
            let cid = this.$route.params.cid;
            this.$router.replace(`/main/index/chat_window/${cid}`)
        },
        checkShowVideo(column) {
            return this.lang.linking;
        },
        /**
         * @description  用于上报状态，让服务器依然知道用户在房间
         * @param {Object} params 参数说明
         */
        async ServicePingChannel(channelIds) {
            return new Promise((resolve, reject) => {
                this.monitorWallRoom.api.pingChannel(
                    {
                        channelIds,
                        clientSeq: new Date().getTime(),
                        client_type: this.systemConfig.clientType,
                    },
                    (res) => {
                        logger.log({ message: "pingChannel", data: res });
                        if (res.error_code === 0) {
                            resolve(res);
                        } else {
                            reject(res);
                        }
                    }
                );
            });
        },
        checkEnterConversationShow(column){
            if(Tool.checkAppClient('Browser')){
                return this.functionsStatus.webTvwallEnterConversation&&this.functionsStatus.live && this.publishedList.includes(`${column.agora_uid}_${column.channelId}`)
            }else{
                return this.functionsStatus.live && this.publishedList.includes(`${column.agora_uid}_${column.channelId}`)
            }
        }
    },
};
</script>
<style lang="scss">
.tv_wall_page {
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background: #e7eff2;
    z-index: 2000;
    display: flex;
    flex-direction: column;
    // .tv_modal{
    //     top:-80px;
    // }
    padding: 10px 10px 0;
    .tv_wall_header {
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 15px 0;
        position: relative;
        .tv_wall_header-left {
            display: flex;
            align-items: center;
            .tv_wall_back_btn {
                cursor: pointer;
                padding: 0 40px;
                text-align: center;
                text-shadow: 1px 1px 1px rgb(255 255 255 / 22%);
                font: bold 12px/40px Arial, sans-serif;
                border-radius: 30px;
                box-shadow: 1px 1px 1px rgb(0 0 0 / 29%), inset 1px 1px 1px rgb(255 255 255 / 44%);
            }
            .tv_wall_title {
                margin-left: 20px;
            }
            .tv_wall_logo {
                display: flex;
                align-items: center;
                margin-left: 20px;
                font-weight: 600;
                p {
                    margin-left: 4px;
                }
            }
        }
        .tv_wall_header-mid {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate3d(-50%, -50%, 0);
        }
        .tv_wall_header-right {
            .el-form-item {
                margin-bottom: 0;
            }
        }
    }
    .tv_wall_grid {
        flex: 1;
        display: flex;
        flex-direction: column;

        .row {
            flex: 1;
            display: flex;
            .column {
                flex: 1;
                padding: 4px 4px;
                .tv_wall_item {
                    height: 100%;
                    display: flex;
                    flex-direction: column;
                    position: relative;
                    .video_container {
                        width: 100%;
                        height: 100%;
                        position: relative;
                        video {
                            position: absolute;
                            top: 0;
                            bottom: 0;
                            left: 0;
                            right: 0;
                            z-index: 1;
                        }
                    }
                    .monitor {
                        background-color: #000;
                        background: -webkit-linear-gradient(to bottom right, #000, #363636);
                        background: linear-gradient(to bottom right, #000, #363636);
                        flex: 1;
                        position: relative;
                        img {
                            position: absolute;
                            top: 50%;
                            left: 50%;
                            transform: translate(-50%, -50%);
                            width: 40px;
                        }
                        p {
                            text-align: center;
                            position: absolute;
                            left: 50%;
                            top: calc(50% + 20px);
                            transform: translateX(-50%);
                            color: #b7b300;
                            font-size: 16px;
                            line-height: 20px;
                        }
                    }
                    .footer {
                        font-size: 18px;
                        color: #aaa;
                        background: #555;
                        height: 32px;
                        line-height: 32px;
                        padding: 0 6px;
                        display: flex;
                        .nickname {
                            flex: 1;
                            width: 0;
                            p {
                                width: 100%;
                            }
                        }
                        .el-button {
                            margin: 2px 6px;
                        }
                    }
                }
            }
        }
    }
    .no_data_tip {
        position: absolute;
        top: 0;
        left: 0;
    }
    .tv_wall_footer {
        font-size: 16px;
        color: #666;
        padding: 6px 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .pagination {
            // margin-top:4px;
        }
        .tv_wall_setting {
            font-size: 30px;
            margin-left: 10px;
            cursor: pointer;
        }
        .tv_wall_footer_right {
            width: 200px;
            img {
                width: 100%;
            }
        }
    }

    .max_zoom {
        position: absolute;
        width: 100%;
        height: 100%;
        background: #e7eff2;
        z-index: 2;
        top: 0;
        left: 0;
        .max_foot {
            position: absolute;
            left: 0;
            bottom: 0;
            width: 100%;
            font-size: 22px;
            line-height: 30px;
            color: #666;
            padding: 6px 10px;
        }
    }
}
</style>

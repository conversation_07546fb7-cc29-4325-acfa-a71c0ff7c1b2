<template>
    <div class="single-select-editor">
        <el-select
            v-model="localValue"
            :placeholder="placeholder"
            style="width: 100%"
            @change="handleChange"
        >
            <el-option
                v-for="option in options"
                :key="option.label"
                :label="option.label"
                :value="option.label"
            />
        </el-select>
    </div>
</template>

<script>
export default {
    name: "SingleSelectField",
    props: {
        value: {
            type: String,
            default: ""
        },
        placeholder: {
            type: String,
            default: ""
        },
        options: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            localValue: this.value
        };
    },
    watch: {
        value: {
            handler(newVal) {
                this.localValue = newVal;
            },
            immediate: true
        }
    },
    methods: {
        handleChange() {
            this.$emit("change", this.localValue);
        }
    }
}
</script>

<style lang="scss" scoped>
.single-select-editor {
    width: 100%;

    ::v-deep .el-select {
        width: 100% !important;

        .el-input__inner {
            font-size: 16px;
        }
    }
}
</style>

import cloneDeep from 'lodash/cloneDeep'
import Vue from 'vue'

const initState ={
    list:[],
    index:0,
    total_count:0
}

export default {
    state: cloneDeep(initState),
    mutations: {
        resetStore(state){
            if(window.vm.$store.state){
                Vue.set(window.vm.$store.state,'consultationImageList',cloneDeep(initState))
            }
        },
        initConsultationImages(state, valObj) {
            if (valObj != undefined && valObj.consultation_image_list != undefined){
                for(let image of valObj.consultation_image_list){
                    image.loaded=false
                    image.realUrl=''
                }
                state.list=valObj.consultation_image_list
                state.index=valObj.index
                state.total_count=valObj.total_count
            }
        },
        addFileToConsultationImages(state,data){
            data.loaded=false;
            data.realUrl='';
            state.list.unshift(data)
        },
        deleteFileToConsultationImages(state,data){
            let cid=data.cid;
            let resource_id=data.resource_id;
            if(resource_id && cid){
                let gallery_list=state.list;
                for(let i=gallery_list.length-1; i>=0; i--){
                    if(cid==gallery_list[i].group_id
                        && resource_id==gallery_list[i].resource_id){
                        gallery_list.splice(i,1);
                    }
                }
            }
        },
        pushMoreConsultationImages(state,data){
            let list=state.list
            for(let image of data.consultation_image_list){
                image.loaded=false
                image.realUrl=''
                list.push(image)
            }
            state.index=data.index
        },
        clearConsultationImages(state){
            state.list=[]
            state.index=0
            state.total_count=0
        },
        updateImageDes(state,data){
            let gallery_list=state.list;
            for(let i=0;i<gallery_list.length;i++){
                let item=gallery_list[i]
                if(data.resource_id == item.resource_id){
                    item.des = data.des;
                    //强制视图更新
                    gallery_list.splice(i,1,item)
                    break;
                }
            }
        },
        updateImageLocalUrl(state,data){
            let imgObj=data.imgObj;
            for(let index=0;index<state.list.length;index++){
                if (imgObj.resource_id==state.list[index].resource_id) {
                    let item=state.list[index]
                    item.url_local=data.url_local
                    state.list.splice(index,1,item)
                    break;
                }
            }

        },
        deleteConsultationImageListByGroupID(state,data){
            let cid=data.cid;
            let del_count = 0;
            if(cid){
                let gallery_list=state.list;
                for(let i=gallery_list.length-1; i>=0; i--){
                    if(cid==gallery_list[i].group_id){
                        gallery_list.splice(i,1);
                        del_count++;
                    }
                }
                state.total_count -= del_count;
                if(state.total_count < 0){
                    state.total_count = 0;
                }
            }
        },
        updateConsultationLiveRecordData(state,data){
            let msg=data;
            let galleryList=state.list
            for(let i=0;i<galleryList.length;i++){
                let image=galleryList[i]
                if (image.resource_id==msg.resource_id) {
                    for(let key in msg){
                        image[key]=msg[key]
                    }
                    galleryList.splice(i,1,image)
                    break;
                }
            }
        },
    },
    actions: {},
    getters: {}
}

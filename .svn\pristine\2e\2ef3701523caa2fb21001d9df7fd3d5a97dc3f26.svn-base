<template>
    <div>
        <van-loading v-if="!loaded" color="#00c59d" />
        <img :src="showUrl" class="file mui-zoom real_image" />
    </div>
</template>
<script>
import { dealObstetricReport } from "../lib/obstetricTool";
import base from "../lib/base";
import { getRealUrl, getResourceTempStatus, checkResourceType, getMessageAiReportFromLocal } from "../lib/common_base";
import { downloadImg } from "../lib/downloader";
import { Toast, Loading } from "vant";
import { flattenDeep } from "lodash";
import Tool from "@/common/tool";
export default {
    name: "real-image",
    mixins: [base],
    components: {
        VanLoading: Loading,
    },
    props: {
        fileItem: {
            type: Object,
            default: () => {
                return {};
            },
        },
    },
    computed: {},
    data() {
        return {
            getResourceTempStatus,
            checkResourceType,
            getMessageAiReportFromLocal,
            loaded: false,
            preloading: false,
            // tempRealUrl:'',
            showUrl: "",
        };
    },
    beforeMount() {
        this.$store.commit("resourceTempStatus/updateResourceTempStatus", {
            resource_id: this.fileItem.resource_id,
            data: {
                hasToastError: false,
                loadedErrorTime: 0,
            },
        });
        if (this.getResourceTempStatus(this.fileItem.resource_id, "error_image")) {
            this.showUrl = this.getResourceTempStatus(this.fileItem.resource_id, "error_image");
            this.loaded = true;
        } else if (this.getResourceTempStatus(this.fileItem.resource_id, "realUrl")) {
            this.showUrl = this.getResourceTempStatus(this.fileItem.resource_id, "realUrl");
            this.loaded = true;
        } else {
            this.showUrl = this.fileItem.url_local;
        }
        if (this.fileItem.img_encode_type) {
            let encode_type = this.fileItem.img_encode_type.toUpperCase();
            if (encode_type === "DCM") {
                const error_image = `static/resource/images/file_icon/dcm.png`;
                this.$store.commit("resourceTempStatus/updateResourceTempStatus", {
                    resource_id: this.fileItem.resource_id,
                    data: {
                        realUrl: this.showUrl,
                        error_image,
                    },
                });
                this.showUrl = error_image;
                this.loaded = true;
            }
        }
    },
    methods: {
        preload() {
            if (this.loaded) {
                return;
            }
            if (this.preloading) {
                //正在加载中，避免重复加载
                return;
            }
            let that = this;
            const imageObj = this.fileItem;
            this.preloading = true;
            //图片未加载完成5s内重复触发不执行，5s后如果仍未加载完成可再次触发
            setTimeout(() => {
                this.preloading = false;
            }, 5000);
            // if (imageObj.isExamFile) {
            //     //加载检查浏览文件
            //     if (imageObj.url&&!imageObj.loaded&&!imageObj.loading) {
            //         imageObj.loading=true;
            //         this.$root.eventBus.$emit('loadExamImage',index);
            //     }
            //     return ;
            // }
            let realUrl = getRealUrl(imageObj).localRealUrl;
            let tempRealUrl = getRealUrl(imageObj).serverRealUrl;
            if (this.checkResourceType(imageObj) === "image" || this.checkResourceType(imageObj) === "video") {
                let realImage = new Image();
                realImage.onerror = function (e) {
                    let loadedErrorTime = that.getResourceTempStatus(imageObj.resource_id, "loadedErrorTime");
                    if (loadedErrorTime > 10) {
                        return;
                    }
                    that.$store.commit("resourceTempStatus/updateResourceTempStatus", {
                        resource_id: imageObj.resource_id,
                        data: {
                            loadedErrorTime: loadedErrorTime + 1,
                        },
                    });
                    // that.tempRealUrl=tempRealUrl;
                    if (window.vm.$route.name.indexOf("gallery") === -1) {
                        return;
                    }
                    realImage.onload = function () {
                        console.log("load2", imageObj);
                        that.showUrl = that.drawCanvasToImage(imageObj, tempRealUrl, realImage);
                        if (that.showUrl == tempRealUrl) {
                            that.showUrl = that.drawCanvasToImageForStruct(imageObj, tempRealUrl, realImage);
                        }
                        that.loaded = true;
                        that.$store.commit("resourceTempStatus/updateResourceTempStatus", {
                            resource_id: imageObj.resource_id,
                            data: {
                                realUrl: that.showUrl,
                            },
                        });
                    };
                    let encode_type = "jpg";
                    if (imageObj.img_encode_type) {
                        encode_type = imageObj.img_encode_type.toUpperCase();
                    } else {
                        that.loaded = true;
                        that.preloading = false;
                        return;
                    }
                    if (encode_type == "DCM" || encode_type == "MP4" || encode_type == "PDF") {
                        that.loaded = true;
                        that.$store.commit("resourceTempStatus/updateResourceTempStatus", {
                            resource_id: imageObj.resource_id,
                            data: {
                                realUrl: that.showUrl,
                            },
                        });
                        let error_image = "";
                        if (encode_type == "DCM") {
                            error_image = `static/resource/images/file_icon/dcm.png`;
                        } else if (encode_type == "PDF") {
                            error_image = `static/resource/images/file_icon/pdf.png`;
                        } else if (encode_type == "MP4") {
                            error_image = `static/resource/images/file_icon/mp4.png`;
                        }
                        that.$store.commit("resourceTempStatus/updateResourceTempStatus", {
                            resource_id: imageObj.resource_id,
                            data: {
                                realUrl: that.showUrl,
                                error_image,
                            },
                        });
                        if (error_image) {
                            that.showUrl = error_image;
                        }
                    } else {
                        realImage.src = tempRealUrl;
                    }
                    // downloadImg({
                    //     img_src:tempRealUrl,
                    //     local_src:realUrl,
                    // })
                    if (!window.main_screen.gateway.check) {
                        const hasToastError = that.getResourceTempStatus(imageObj.resource_id, "hasToastError");
                        !hasToastError && Toast(that.lang.no_net_no_open_img);
                        that.$store.commit("resourceTempStatus/updateResourceTempStatus", {
                            resource_id: imageObj.resource_id,
                            data: {
                                hasToastError: true,
                            },
                        });
                    }
                };
                realImage.onload = function () {
                    console.log("load1", imageObj);
                    that.showUrl = that.drawCanvasToImage(imageObj, realUrl, realImage);
                    if (that.showUrl == realUrl) {
                        that.showUrl = that.drawCanvasToImageForStruct(imageObj, realUrl, realImage);
                    }
                    that.loaded = true;
                    that.preloading = false;
                    that.$store.commit("resourceTempStatus/updateResourceTempStatus", {
                        resource_id: imageObj.resource_id,
                        data: {
                            realUrl: that.showUrl,
                        },
                    });
                };
                if (this.systemConfig.serverInfo.network_environment === 1) {
                    realUrl = Tool.replaceInternalNetworkEnvImageHost(realUrl);
                }
                realImage.setAttribute("crossOrigin", "anonymous");
                realImage.src = `${realUrl}?temp=${Date.now()}`;
            } else {
            }
        },
        drawCanvasToImage(imageObj, realUrl, realImage) {
            let resource_id = imageObj.resource_id;
            let item = this.$store.state.gallery.commentObj[resource_id];
            let mark_list = item && item.ai_analyze_report && item.ai_analyze_report.mark_list;
            if (!mark_list || !mark_list[resource_id] || !this.$store.state.globalParams.functionsStatus.breastAI) {
                //存在描迹则返回描迹图片，否则返回原图片
                return realUrl;
            }
            mark_list = mark_list[resource_id];
            var canvas = document.getElementById("ai_canvas");
            var context = canvas.getContext("2d");
            canvas.width = realImage.width;
            canvas.height = realImage.height;
            context.drawImage(realImage, 0, 0);
            context.strokeStyle = "#f00";
            context.lineWidth = 2;
            try {
                for (let shape of mark_list) {
                    context.beginPath();
                    if (shape.type == 1) {
                    //直线
                        context.moveTo(shape.point[0].x, shape.point[0].y);
                        context.lineTo(shape.point[1].x, shape.point[1].y);
                        context.stroke();
                    } else if (shape.type == 2) {
                    //矩形
                        let width = shape.point[1].x - shape.point[0].x;
                        let height = shape.point[1].y - shape.point[0].y;
                        context.strokeRect(shape.point[0].x, shape.point[0].y, width, height);
                        context.stroke();
                    } else if (shape.type == 3) {
                    //椭圆
                        let radiusX = shape.point[1].x - shape.point[0].x;
                        let radiusY = shape.point[1].y - shape.point[0].y;
                        context.ellipse(
                            shape.point[0].x,
                            shape.point[0].y,
                            radiusX,
                            radiusY,
                            (Math.PI / 180) * shape.deg,
                            0,
                            Math.PI * 2,
                            true
                        );
                        context.stroke();
                    } else if (shape.type == 4) {
                    //描迹
                        context.moveTo(shape.point[0].x, shape.point[0].y);
                        for (let i = 1; i < shape.point.length; i++) {
                            context.lineTo(shape.point[i].x, shape.point[i].y);
                        }
                        context.lineTo(shape.point[0].x, shape.point[0].y);
                        context.stroke();
                    }
                    context.closePath();
                }
            } catch (error) {
                console.log("drawCanvasToImage Error", error);    
            }
            let base64 = canvas.toDataURL("image/jpeg");

            this.$store.commit("resourceTempStatus/updateResourceTempStatus", {
                resource_id: imageObj.resource_id,
                data: {
                    drawedCanvas: true,
                },
            });
            console.log("base64");
            return base64;
        },
        drawCanvasToImageForStruct(imageObj, realUrl, realImage) {
            let item = this.$store.state.gallery.commentObj[imageObj.resource_id];
            let ai_type = -1;
            let ai_analyze_report = this.getMessageAiReportFromLocal(imageObj);
            let clip = null;
            if (ai_analyze_report && ai_analyze_report.clips && ai_analyze_report.clips[imageObj.resource_id]) {
                clip = ai_analyze_report.clips[imageObj.resource_id][0];
            }
            let aiPresetData = null;
            if(ai_analyze_report&& ai_analyze_report.type== this.$store.state.aiPresetData.typeIndex.abdomen){
                aiPresetData =   this.$store.state.aiPresetData?.iworksAbdomenTest.views||{}
            }
            if(ai_analyze_report&& ai_analyze_report.type== this.$store.state.aiPresetData.typeIndex.cardiac){
                aiPresetData =   this.$store.state.aiPresetData?.cardiacViews.views||{}
            }
            
            if(ai_analyze_report&& ai_analyze_report.type== this.$store.state.aiPresetData.typeIndex.obstetrical){
                aiPresetData = dealObstetricReport(clip);
            }
            if (aiPresetData && ai_analyze_report && clip &&  this.$store.state.globalParams.functionsStatus.breastAI) {
                ai_type = ai_analyze_report.type || "";
                if (ai_type == this.$store.state.aiPresetData.typeIndex.abdomen||ai_type == this.$store.state.aiPresetData.typeIndex.cardiac) {
                
                    const child_id = clip.child_id && clip.child_id >= 0 && aiPresetData[clip.child_id]? clip.child_id : -1
                    let viewObj = aiPresetData[clip.id];
                    if(child_id>-1){
                        viewObj = aiPresetData[child_id];
                    }
                    let dispalyStructs = [];
                    if (viewObj) {
                        for (let i in viewObj.details) {
                            let struct = viewObj.details[i];
                            for (let j in clip.struct) {
                                let item = clip.struct[j];
                                if (item.type == struct.id) {
                                    dispalyStructs.push(item);
                                }
                            }
                        }
                        return this.setCanvasByPosition(imageObj, realImage, dispalyStructs);
                    } else {
                        return realUrl;
                    }
                }
                if (ai_type == this.$store.state.aiPresetData.typeIndex.obstetrical) {
                    let aiPresetData = dealObstetricReport(clip);
                    let all_items = flattenDeep(Object.values(aiPresetData.viewItemList));
                    let detail_ids = all_items.reduce((h, v) => {
                        h = [...h, ...v.detail_ids];
                        return h;
                    }, []);
                    
                
                    let all_structs = flattenDeep(Object.values(aiPresetData.viewstructList));
                    let dispalyStructs = all_structs.reduce((h, v) => {
                        if (detail_ids.indexOf(v.id) > -1) {
                            h.push(v);
                        }
                        return h;
                    }, []);

                    return this.setCanvasByPosition(imageObj, realImage, dispalyStructs);
                }
                return realUrl;
            } else {
                return realUrl;
            }
        },
        setCanvasByPosition(imageObj, realImage, dispalyStructs) {
            let colors = this.$store.state.aiPresetData.colors;
            var canvas = document.getElementById("ai_canvas");
            var context = canvas.getContext("2d");
            canvas.width = realImage.width;
            canvas.height = realImage.height;
            context.drawImage(realImage, 0, 0);
            context.strokeStyle = "#f00";
            context.lineWidth = 3;
            for (let i = dispalyStructs.length - 1; i >= 0; i--) {
                let position = dispalyStructs[i].position;
                context.beginPath();
                context.moveTo(position[0], position[1]); //把画笔移动到指定的坐标
                context.lineTo(position[2], position[1]); //绘制一条从当前位置到指定坐标(200, 50)的直线.
                context.lineTo(position[2], position[3]);
                context.lineTo(position[0], position[3]);
                context.strokeStyle = dispalyStructs[i].color ? dispalyStructs[i].color : colors[i];

                context.save();
                context.closePath();
                context.stroke(); //绘制路径。
            }
            let base64 = canvas.toDataURL("image/jpeg");
            this.$store.commit("resourceTempStatus/updateResourceTempStatus", {
                resource_id: imageObj.resource_id,
                data: {
                    drawedCanvas: true,
                    realUrl: base64,
                    loaded: true,
                },
            });
            return base64;
        },

        updateMarkListIfNeed(force = false) {
            const drawedCanvas = this.getResourceTempStatus(this.fileItem.resource_id, "drawedCanvas");
            if (!drawedCanvas || force) {
                this.loaded = false;
                this.preload();
            }
        },
    },
};
</script>
<style lang="scss"></style>

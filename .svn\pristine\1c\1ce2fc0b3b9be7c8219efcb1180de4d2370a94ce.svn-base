import {MessageBox,Message, Notification } from 'element-ui'
import Vue from 'vue'
export function checkDialogVisible() {
    const elDialogList = document.querySelectorAll('.el-dialog__wrapper');
    let visible  = false
    elDialogList.forEach(elDialog=>{
        if (elDialog && window.getComputedStyle(elDialog).display !== 'none') {

            visible =  true
        }
    })
    console.log('el-dialog 是否存在',visible);
    return visible

}
export function configureGlobalNotifications(context){
    //给Notify方法加入提示音
    const notifyObj={
        error:context.$notify.error,
        success:context.$notify.success,
        info:context.$notify.info,
        warning:context.$notify.warning,
    }
    const notifyFactory=(type)=>{
        return (options)=>{
            document.querySelector('#message_notify').play();
            notifyObj[type](options)
        }
    }
    context.$notify.error=notifyFactory('error')
    context.$notify.success=notifyFactory('success')
    context.$notify.info=notifyFactory('info')
    context.$notify.warning=notifyFactory('warning')
    const messageObj={
        error:context.$message.error,
        success:context.$message.success,
        info:context.$message.info,
        warning:context.$message.warning,
    }
    //给message方法加入关闭按钮，指定关闭时间,关闭播放器
    const messageFactory=(type)=>{
        return (options)=>{
            let params={}
            if (typeof(options)==='string') {
                params.message=options;
                params.showClose=true;
                params.duration=3000;
            }else{
                params=options
            }
            params.onClose=()=>{
                if(!checkDialogVisible()){
                    context.$root.eventBus.$emit('showRealTimeVideo')
                    context.$root.eventBus.$emit('showRealTimeVideoByMulticenterGallery')
                }
            }
            context.$root.eventBus.$emit('hideRealTimeVideo')
            context.$root.eventBus.$emit('hideRealTimeVideoByMulticenterGallery')
            messageObj[type](params)
        }
    }
    context.$message.error=messageFactory('error')
    context.$message.success=messageFactory('success')
    context.$message.info=messageFactory('info')
    context.$message.warning=messageFactory('warning')

    //提供平台toast方法，给公共文件调用
    context.$root.platformToast=(str,type=1)=>{
        if (type==1) {
            //error
            context.$message.error({
                message:str,
                showClose:true
            })
        }else if(type==2){
            context.$message.success({
                message:str,
                showClose:true
            })
        }else if(type==3){
            context.$notify.error({
                message:str,
                showClose:true
            })
        }else if(type==4){
            context.$notify.success({
                message:str,
                showClose:true
            })
        }else{
            context.$message.info({
                message:str,
                showClose:true
            })
        }
    }

    const alert=MessageBox.alert;
    MessageBox.alert=(msg,title,option)=>{
        const newTitle = title||window.vm.$store.state.language.tip_title
        option=option||{};
        option.confirmButtonText=option.confirmButtonText||window.vm.$store.state.language.confirm_txt;
        context.$root.eventBus.$emit('hideRealTimeVideo')
        option.onClose=()=>{
            if(!checkDialogVisible()){
                context.$root.eventBus.$emit('showRealTimeVideo')
            }
        }
        return alert(msg,newTitle,option);
    }
    const confirm=MessageBox.alert;
    MessageBox.confirm=(msg,title,option)=>{
        const newTitle = title||window.vm.$store.state.language.tip_title
        option=option||{};
        option.confirmButtonText=option.confirmButtonText||window.vm.$store.state.language.confirm_txt;
        option.cancelButtonText=option.cancelButtonText||window.vm.$store.state.language.cancel_btn;
        option.showCancelButton = true
        context.$root.eventBus.$emit('hideRealTimeVideo')
        option.onClose=()=>{
            if(!checkDialogVisible()){
                context.$root.eventBus.$emit('showRealTimeVideo')
            }
        }
        return confirm(msg,newTitle,option);
    }
    Vue.prototype.$MessageBox = MessageBox
    Vue.prototype.$Message = Message
    Vue.prototype.$baseNotify = (
        message,
        title,
        type = 'success',
        position = 'top-right',
        duration = 3000
    ) => {
        Notification({
            title,
            message,
            type,
            duration,
            position,
        })
    }
}

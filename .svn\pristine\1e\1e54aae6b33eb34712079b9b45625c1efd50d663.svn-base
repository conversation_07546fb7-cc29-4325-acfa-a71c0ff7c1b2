<template>
    <div class="data_display_mobile">
        <div class="data_header">
            <img class="logo" src="../../static/images/logo.png" />
            <p>
                {{pageTitle}}
            </p>
            <i class="el-icon-s-tools" @click="openSettingDialog"></i>
            <!-- <div class="data_operate">
                <p>{{ time }}</p>

                <el-select
                    class="quick_modification_time"
                    size="mini"
                    v-model="quickModificationTime"
                    placeholder="-"
                    @change="handleQuickModificationTimeChange()"
                >
                    <el-option
                        v-for="item in quickModificationTimeList"
                        :key="item.value"
                        :label="item.value"
                        :value="item.param"
                    >
                    </el-option>
                </el-select>
            </div> -->
        </div>
        <div class="rainbow">
            <div class="red"></div>
            <div class="green"></div>
            <div class="camel"></div>
            <div class="light_blue"></div>
            <div class="purple"></div>
        </div>
        <div class="data_container">
            <div class="left">
                <section class="section1_3 auto_height" v-if="statisticGroup === 'group'" v-loading="loadingLiveData">
                    <div class="count_panel">
                        <div class="count_item">
                            <p class="label">{{lang.statistic.liveDuration}}</p>
                            <p class="value">{{ parseInt(liveDataObj.liveDuration/60) }}</p>
                        </div>
                        <div class="count_item">
                            <p class="label">{{lang.statistic.liveCount}}</p>
                            <p class="value">{{ liveDataObj.liveCount }}</p>
                        </div>
                        <div class="count_item">
                            <p class="label">{{lang.statistic.iworksUseTimes}}</p>
                            <p class="value">{{ liveDataObj.iworksUseTimes }}</p>
                        </div>
                        <div class="count_item">
                            <p class="label">{{lang.statistic.iworksUserCount}}</p>
                            <p class="value">{{ liveDataObj.iworksUserCount }}</p>
                        </div>
                    </div>
                </section>
                <section class="section1_3" v-else-if="statisticGroup === 'device'" v-loading="loadingLiveData">
                    <div class="count_panel">
                        <div class="count_item">
                            <p class="label">{{lang.statistic.image_count}}</p>
                            <p class="value">{{ examSummaryObj.imageCount }}</p>
                        </div>
                        <div class="count_item">
                            <p class="label">{{lang.statistic.exam_count}}</p>
                            <p class="value">{{ examSummaryObj.examCount }}</p>
                        </div>
                    </div>
                </section>
                <section class="section3_4">
                    <BIMapChart @getData="getMapData" ref="map_chart" :statisticGroup=basicOptions.statisticGroup :mobileMode="true"/>
                </section>
            </div>
            <div class="center">
                <section class="section3_4" v-loading="loadingPieData">
                    <BIPieChart :data="stats.overallDistribution" @togglePieData="handdleTogglePieData" :totalExamCount="totalExamCount" :totalImageCount="totalImageCount" :statisticGroup="
                        statisticGroup"  :mobileMode="true"/>
                </section>
                <section
                    id="draggableBlock1"
                    :class="{ section1_3: true, draggable: true, fake_empty: !statsList.block1 }"
                    v-loading="loadingChart.block1"
                >
                    <div class="trend_chart_panel" v-if="statsList.block1">
                        <p class="title">{{ draggablePanelTitleGenerate(statsList.block1) }}</p>
                        <statisticCard :showTypes="showTypes" :block="statsList.block1" :statisticGroup="
                        statisticGroup"></statisticCard>
                    </div>
                </section>
                <!-- <section
                    id="draggableBlock3"
                    :class="{ section1_3: true, draggable: true, fake_empty: !statsList.block3 }"
                    draggable="true"
                    @dragstart="handleDragStart"
                    @dragover="handleDragOver"
                    @drop="handleDrop"
                    v-loading="loadingChart.block3"
                >
                    <div class="trend_chart_panel" v-if="statsList.block3">
                        <p class="title">{{ draggablePanelTitleGenerate(statsList.block3) }}</p>
                        <statisticCard :showTypes="showTypes" :block="statsList.block3" :statisticGroup="
                        statisticGroup"></statisticCard>
                    </div>
                </section> -->
            </div>
            <div class="right">
                <section
                    id="draggableBlock2"
                    :class="{ section1_3: true, draggable: true, fake_empty: !statsList.block2 }"
                    v-loading="loadingChart.block2"
                >
                    <div class="trend_chart_panel" v-if="statsList.block2">
                        <p class="title">{{ draggablePanelTitleGenerate(statsList.block2) }}</p>
                        <statisticCard :showTypes="showTypes" :block="statsList.block2" :statisticGroup="
                        statisticGroup"></statisticCard>
                    </div>
                </section>
                <section
                    id="draggableBlock3"
                    :class="{ section1_3: true, draggable: true, fake_empty: !statsList.block3 }"
                    v-loading="loadingChart.block3"
                >
                    <div class="trend_chart_panel" v-if="statsList.block3">
                        <p class="title">{{ draggablePanelTitleGenerate(statsList.block3) }}</p>
                        <statisticCard :showTypes="showTypes" :block="statsList.block3" :statisticGroup="
                        statisticGroup"></statisticCard>
                    </div>
                </section>
                <section
                    id="draggableBlock4"
                    :class="{ section1_3: true, draggable: true, fake_empty: !statsList.block4 }"
                    v-loading="loadingChart.block4"
                >
                    <div class="trend_chart_panel" v-if="statsList.block4">
                        <p class="title">{{ draggablePanelTitleGenerate(statsList.block4) }}</p>
                        <statisticCard :showTypes="showTypes" :block="statsList.block4" :statisticGroup="
                        statisticGroup"></statisticCard>
                    </div>
                </section>
            </div>
        </div>
        <van-popup
          v-model="showSettingDialog"
          class="setting"
          position="right"
          :style="{ width: '80%', height: '100%' }"
        >
            <div class="setting_container">
                <div class="count_date">
                    <p class="header_title">{{lang.statistic.statistic_time}}:</p>
                    <van-button v-for="item in quickModificationTimeList" :key="item.value" @click="adjustPeriod(item.param)" type="primary" size="small">{{ lang.statistic.time_map[item.param] }}</van-button>
                </div>
                <div @click="showDatePicker" class="date_range">
                    <p>{{basicOptions.period[0] | formatDate}} - {{basicOptions.period[1] | formatDate}}</p>
                </div>
                <div>
                    <p class="header_title">{{lang.statistic.show_type}}:</p>
                    <van-dropdown-menu>
                        <van-dropdown-item v-model="basicOptions.showTypes" :options="allShowTypes" />
                    </van-dropdown-menu>
                </div>
                <section>
                    <van-tabs v-model="basicOptions.statisticGroup" @change="handleClickTab" :ellipsis="false" type="card" color="#07c160">
                        <van-tab name="group" :title="lang.statistic.statistic_group"></van-tab>
                        <van-tab name="device" :title="lang.statistic.statistic_device"></van-tab>
                    </van-tabs>
                </section>
                <template v-if="basicOptions.statisticGroup === 'group'">
                    <section class="setting_content_box" v-if="settingGroupList.user_related">
                        <h2>{{lang.statistic.user_related}}</h2>
                        <van-checkbox v-for="item of settingGroupList.user_related" :key="item" v-model="detailOptions[item]" :disabled="isOptionsMaxExceed(detailOptions[item])">{{lang.statistic[item]}}</van-checkbox>
                    </section>
                    <section class="setting_content_box">
                        <h2>{{lang.statistic.content_related}}</h2>
                        <van-checkbox v-for="item of settingGroupList.content_related" :key="item" v-model="detailOptions[item]" :disabled="isOptionsMaxExceed(detailOptions[item])">{{lang.statistic[item]}}</van-checkbox>
                    </section>
                    <section class="setting_content_box">
                        <h2>{{lang.statistic.group_related}}</h2>
                        <van-checkbox v-for="item of settingGroupList.group_related" :key="item" v-model="detailOptions[item]" :disabled="isOptionsMaxExceed(detailOptions[item])">{{lang.statistic[item]}}</van-checkbox>
                    </section>
                    <section class="setting_content_box">
                        <h2>{{lang.statistic.iworks_protocol}}</h2>
                        <van-checkbox v-for="item of settingGroupList.iworks_protocol" :key="item" v-model="detailOptions[item]" :disabled="isOptionsMaxExceed(detailOptions[item])">{{lang.statistic[item]}}</van-checkbox>
                    </section>
                    <section class="setting_content_box" v-if="settingGroupList.device_related">
                        <h2>{{lang.statistic.device_related}}</h2>
                        <van-checkbox v-for="item of settingGroupList.device_related" :key="item" v-model="detailOptions[item]" :disabled="isOptionsMaxExceed(detailOptions[item])">{{lang.statistic[item]}}</van-checkbox>
                    </section>
                </template>
                <template v-else-if="basicOptions.statisticGroup === 'device'">
                    <section class="setting_content_box">
                        <h2>{{lang.statistic.device_types.doppler}}</h2>
                        <van-checkbox v-model="detailOptions.deviceInstallTime" :disabled="isOptionsMaxExceed(detailOptions.deviceInstallTime)">{{lang.statistic.deviceInstallTime}}</van-checkbox>
                        <van-checkbox v-model="detailOptions.deviceUtilizationRate" :disabled="isOptionsMaxExceed(detailOptions.deviceUtilizationRate)">{{lang.statistic.deviceUtilizationRate}}</van-checkbox>
                        <van-checkbox v-model="detailOptions.examCountOfOrganization" :disabled="isOptionsMaxExceed(detailOptions.examCountOfOrganization)">{{lang.statistic.examCountOfOrganization}}</van-checkbox>
                        <van-checkbox v-model="detailOptions.deviceFailureTip" :disabled="isOptionsMaxExceed(detailOptions.deviceFailureTip)">{{lang.statistic.deviceFailureTip}}</van-checkbox>
                        <van-checkbox v-model="detailOptions.examCountOfDevice" :disabled="isOptionsMaxExceed(detailOptions.examCountOfDevice)">{{lang.statistic.examCountOfDevice}}</van-checkbox>
                        <van-checkbox v-model="detailOptions.deviceStatus" :disabled="isOptionsMaxExceed(detailOptions.deviceStatus)">{{lang.statistic.deviceStatus}}</van-checkbox>
                        <van-checkbox v-model="detailOptions.probeUsageRate" :disabled="isOptionsMaxExceed(detailOptions.probeUsageRate)">{{lang.statistic.probeUsageRate}}</van-checkbox>
                    </section>
                </template>
            </div>
            <div class="setting_footer">
                <van-button type="default" @click="handleSettingCancel">{{lang.statistic.cancel_btn}}</van-button>
                <van-button type="primary" @click="handleSettingConfrim">{{lang.statistic.confirm_btn}}</van-button>
            </div>
        </van-popup>
        <van-popup

          v-model="showStartDatePicker"
          position="bottom"
          :style="{ width: '100%', height: '30%' }"
        >
            <van-datetime-picker
                type="year-month"
                :title="lang.statistic.start_date"
                v-model="pickerDate[0]"
                @cancel="showStartDatePicker = false"
                @confirm="showEndDatePicker = true"
            />
        </van-popup>
        <van-popup
          v-model="showEndDatePicker"
          position="bottom"
          :style="{ width: '100%', height: '30%' }"
        >
            <van-datetime-picker
                type="year-month"
                :title="lang.statistic.end_date"
                :min-date="pickerDate[0]"
                v-model="pickerDate[1]"
                @cancel="showEndDatePicker=false"
                @confirm="confirmDate"
            />
        </van-popup>
    </div>
</template>
<script>
import base from '../../lib/base'
import _ from "lodash";
import Tool from '../../lib/tool.js';
import request from '../../service/service'
import BIPieChart from "../../components/BIPieChart";
import BIMapChart from "../../components/BIMapChart";
import { Popup, Button,  DatetimePicker, DropdownMenu, DropdownItem, Checkbox, Tab, Tabs,Toast } from 'vant';
import moment from 'moment';
import statisticCard from "../../components/statisticCard";

export default {
    mixins: [base],
    name: "data_display",
    components: {
        BIPieChart,
        BIMapChart,
        vanPopup:Popup,
        vanButton:Button,
        vanDatetimePicker:DatetimePicker,
        vanDropdownMenu:DropdownMenu,
        vanDropdownItem:DropdownItem,
        vanCheckbox:Checkbox,
        vanTab:Tab,
        vanTabs:Tabs,
        statisticCard,
    },
    filters: {},
    data() {
        return {
            showStartDatePicker:false,
            showEndDatePicker:false,
            isAdmin:false,
            pickerDate:[],
            time: "",
            subject:'',
            onDraggingElement: null,
            showSettingDialog: false,
            pieType:'examTypeMap',
            query:{
                dataFrom:'',
                id:'',
            },
            tempQuery:{
                dataFrom:'', //超管在全局进入云端统计，可以按群筛选
                id:'',
                value:'',
            },
            loadingLiveData:false,
            loadingPieData:false,
            loadingChart:{
                block1:false,
                block2:false,
                block3:false,
                block4:false,
            },
            liveDataObj:{
                iworksUserCount:0,
                iworksUseTimes:0,
                liveCount:0,
                liveDuration:0,
            },
            examSummaryObj:{
                examCount:0,
                imageCount:0,
            },
            allShowTypes: [
                {
                    value: 0,
                    text: "",
                },
                {
                    value: 1,
                    text: "",
                },
            ],
            quickModificationTime: "",
            quickModificationTimeList: [
                {
                    param: "3M",
                },
                {
                    param: "6M",
                },
                {
                    param: "1Y",
                },
                {
                    param: "TY",
                },
            ],
            maxDetailOptions: 4,
            checkedOptions: [],
            statsList: {
                block1: null,
                block2: null,
                block3: null,
                block4: null,
            },
            period: [],
            showTypes: 1,
            statisticGroup: 'group', // 统计类别，group：数据统计 device：设备统计
            basicOptions: {
                period: [], // 统计时间 [起(Date), 终(Date)]
                showTypes: 1, // 0 -> 曲线图 | 1 -> 柱状图
                statisticGroup: 'group',
            },
            optionsSort: [
                'userIncreased',
                'userTotal',
                'userActive',
                'groupActive',
                'groupIncreased',
                'groupTotal',
                'liveCount',
                'liveUserTimes',
                'liveDuration',
                'iSyncIncreased',
                'iSyncTotal',
                'ulinkerIncreased',
                'ulinkerTotal',
                'dopplerIncreased',
                'dopplerTotal',
                'examIncreased',
                'imageIncreased',
                'videoIncreased',
                'breastAI',
                'library',
                'iworksUserCount',
                'iworksUseTimes',
                'deviceInstallTime',
                'deviceUtilizationRate',
                'examCountOfOrganization',
                'examCountOfDevice',
                'deviceFailureTip',
                'deviceStatus',
                'probeUsageRate'
            ], // detailOptions遍历是无序的，需要一个有序的数组遍历
            // 判断筛选条件用
            detailOptions: {
                // 用户相关
                userIncreased: false, // 新增用户
                userTotal: false, // 累计用户
                userActive: false, // 活跃用户
                // 群/群落
                groupActive: false, // 活跃群数
                groupIncreased: false, // 新增群数
                groupTotal: false, // 累计群数
                // 直播
                liveCount: false, // 直播场数
                liveUserTimes: false, // 直播参与人数
                liveDuration: false, // 直播时长
                // 设备
                iSyncIncreased: false, // 新增isync 数
                iSyncTotal: false, // 累计isync 数
                ulinkerIncreased: false, // 新增灵珂数
                ulinkerTotal: false, // 累计灵珂数
                dopplerIncreased: false, // 新增超声数
                dopplerTotal: false, // 累计超声数
                // 内容
                examIncreased: false, // 新增检查数
                imageIncreased: false, // 新增图像数
                videoIncreased: false, // 新增视频数
                breastAI: false, // 佳佳老师
                library: false, // 图书馆
                // iWorks协议（暂时定义）
                iworksUserCount: false, // 扫查规范使用人数
                iworksUseTimes: false, // 扫查规范使用次数
                // specCompliance: false, // 扫查规范遵从度
                deviceInstallTime: false, // 设备安装时间
                deviceUtilizationRate: false, // 设备利用率
                deviceStatus: false, //设备状态
                examCountOfOrganization: false, //创建检查数（机构）
                examCountOfDevice: false, //创建检查数（设备）
                deviceFailureTip:false, //设备故障提示
                probeUsageRate:false,//探头使用占比(%)
            },
            totalExamCount:0,
            totalImageCount:0,
            defaultDataItem: ['liveCount','liveUserTimes','examIncreased','imageIncreased'], //数据统计默认选中项
            defaultDeviceItem: ['deviceInstallTime','deviceUtilizationRate','examCountOfOrganization','deviceStatus'], // 设备统计默认选中项
            // 真实数据
            stats: {
                // 超声检查总体分布情况
                overallDistribution: [
                    { value: 0,key:4 },
                    { value: 0,key:2},
                    { value: 0,key:6},
                    { value: 0,key:3},
                    { value: 0,key:5},
                    { value: 0,key:1},
                    { value: 0,key:0},
                    { value: 0,key:11},
                    { value: 0,key:12},
                    { value: 0,key:-1},
                ],
                // 用户相关
                userIncreased: { title: "userIncreased", value: 0, data: undefined }, // 新增用户
                userTotal: { title: "userTotal", value: 0, data: undefined, hiddenCount:true }, // 累计用户
                userActive: { title: "userActive", value: 0, data: undefined }, // 活跃用户
                // 群/群落
                groupActive: { title: "groupActive", value: 0, data: undefined }, // 活跃群数
                groupIncreased: { title: "groupIncreased", value: 0, data: undefined }, // 新增群数
                groupTotal: { title: "groupTotal", value: 0, data: undefined, hiddenCount:true }, // 累计群数
                // 直播
                liveCount: { title: "liveCount", value: 0, data: undefined }, // 直播场数
                liveUserTimes: { title: "liveUserTimes", value: 0, data: undefined }, // 直播参与人数
                liveDuration: { title: "liveDuration", value: 0, data: undefined }, // 直播时长
                // 设备
                iSyncIncreased: { title: "iSyncIncreased", value: 0, data: undefined }, // 新增isync 数
                iSyncTotal: { title: "iSyncTotal", value: 0, data: undefined, hiddenCount:true }, // 累计isync 数
                ulinkerIncreased: { title: "ulinkerIncreased", value: 0, data: undefined }, // 新增灵珂数
                ulinkerTotal: { title: "ulinkerTotal", value: 0, data: undefined, hiddenCount:true }, // 累计灵珂数
                dopplerIncreased: { title: "dopplerIncreased", value: 0, data: undefined }, // 新增超声数
                dopplerTotal: { title: "dopplerTotal", value: 0, data: undefined, hiddenCount:true }, // 累计超声数
                // 内容
                examIncreased: { title: "examIncreased", value: 0, data: undefined }, // 新增检查数
                imageIncreased: { title: "imageIncreased", value: 0, data: undefined }, // 新增图像数
                videoIncreased: { title: "videoIncreased", value: 0, data: undefined }, // 新增视频数
                breastAI: { title: "breastAI", value: 0, data: undefined }, // 佳佳老师
                library: { title: "library", value: 0, data: undefined }, // 图书馆
                // iWorks协议（暂时定义）
                iworksUserCount: { title: "iworksUserCount", value: 0, data: undefined }, // 扫查规范使用人数
                iworksUseTimes: { title: "iworksUseTimes", value: 0, data: undefined }, // 扫查规范使用次数
                //specCompliance: { title: "扫查规范遵从度", value: 0, data: undefined }, // 扫查规范遵从度
                // 设备统计
                deviceInstallTime: { title: "deviceInstallTime", value: 0, data: undefined, hiddenCount:true },
                deviceUtilizationRate: { title: "deviceUtilizationRate", value: 0, data: undefined, hiddenCount:true },
                deviceStatus: { title: "deviceStatus", value: 0, data: undefined, hiddenCount:true },
                examCountOfOrganization: { title: "examCountOfOrganization", value: 0, data: undefined, hiddenCount:true },
                deviceFailureTip: {title: "deviceFailureTip", value: 0, data: undefined},
                examCountOfDevice: { title: "examCountOfDevice", value: 0, data: undefined, hiddenCount:true },
                probeUsageRate:{title:"probeUsageRate",value:0,data:undefined,hiddenCount:true}
            },
            settingGroupList:{
                user_related:['userActive','userIncreased','userTotal'],
                content_related:['examIncreased','imageIncreased','videoIncreased','breastAI','library'],
                group_related:['groupActive','groupIncreased','groupTotal','liveCount','liveUserTimes','liveDuration'],
                iworks_protocol:['iworksUserCount','iworksUseTimes'],
                device_related:['iSyncIncreased','iSyncTotal','ulinkerIncreased','ulinkerTotal','dopplerIncreased','dopplerTotal'],
            },
        };
    },
    filters: {
        formatDate: (value) => {
            return moment(value).format('YYYY-MM z');
        },
    },
    computed: {
        checkedOptionsLength() {
            let count = 0;
            for (const key in this.detailOptions) {
                if (this.detailOptions.hasOwnProperty(key) && this.detailOptions[key] === true) {
                    count++;
                }
            }
            return count;
        },
        checkedOptionsCountClassName() {
            return this.checkedOptionsLength >= this.maxDetailOptions ? "warning_count" : "count";
        },
        isOptionsMaxExceed() {
            return (self) => this.checkedOptionsLength >= this.maxDetailOptions && !self;
        },
        pageTitle(){
            const title = this.statisticGroup === 'device'?this.lang.statistic.BI_device_subtitle:this.lang.statistic.BI_subtitle
            return this.isAdmin?this.lang.statistic.BI_title:this.$store.state.globalParams.targetInfo.subject+' '+title;
        }
    },
    watch: {
        // detailOptions: {
        //     handler: function () {
        //         try {
        //             const trueKeys = [];
        //             for (const key in this.detailOptions) {
        //                 if (this.detailOptions.hasOwnProperty(key) && this.detailOptions[key] === true) {
        //                     trueKeys.push(key);
        //                 }
        //             }
        //             this.checkedOptions = trueKeys;
        //         } catch (e) {
        //             console.error("An error occured while watching detail options being modified. Detail: ", e);
        //             localStorage.removeItem("biDetailOptions");
        //             this.storageInitialize();
        //             this.$message.error("缓存中图表数据有错误数据，已将对应数据初始化，请至配置项重新设置");
        //         }
        //     },
        //     deep: true,
        // },
    },
    async created() {
        this.allShowTypes[0].text = this.lang.statistic.show_type_map[0];
        this.allShowTypes[1].text = this.lang.statistic.show_type_map[1];
        this.query = JSON.parse(window.localStorage.getItem('stat_query'))
        this.initPurview();
    },
    mounted() {
        //默认获取最近6月数据
        this.quickModificationTime = '6M';
        this.getTime();
    },
    methods: {
        async initPurview(){
            const userInfo = await request.getUserInfo();
            const isAdmin = userInfo.data.data.role === this.$store.state.systemConfig.role.Admin || userInfo.data.data.role === this.$store.state.systemConfig.role.SuperAdmin;
            // 初始化配置权限，非系统管理员隐藏部分配置
            if (isAdmin) {
                if (this.query.dataFrom === 'global') {
                    this.isAdmin = true;
                }
            }else{
                this.defaultDataItem = ['examIncreased','imageIncreased','videoIncreased','liveCount']
                // 非全局不可见 , 删除相关配置
                //用户相关
                delete this.settingGroupList.user_related
                this.stats.userActive = null;
                this.stats.userIncreased = null;
                this.stats.userTotal = null;
                // 图书馆
                this.settingGroupList.content_related.splice(4)
                this.stats.library = null;
                // 群落相关
                this.settingGroupList.group_related.splice(0,3)
                this.stats.groupTotal = null;
                this.stats.groupIncreased = null;
                this.stats.groupActive = null;
                //设备相关
                delete this.settingGroupList.device_related
                this.stats.iSyncIncreased = null;
                this.stats.iSyncTotal = null;
                this.stats.ulinkerIncreased = null;
                this.stats.ulinkerTotal = null;
                this.stats.dopplerIncreased = null;
                this.stats.dopplerTotal = null;
                this.stats.library = null;
                
            }
            //初始化本地存储
            this.storageInitialize();
            this.statsListInitialize();
            this.initMenu();
            this.handleQuickModificationTimeChange();
        },
        // 一般业务逻辑
        getTime() {
            const date = new Date();
            this.time = `${date.toLocaleString()} ${this.getWeek()}`;
            setInterval(() => {
                const date = new Date();
                this.time = `${date.toLocaleString()} ${this.getWeek()}`;
            }, 1000);
        },
        getWeek() {
            return this.lang.statistic.weeks[new Date().getDay()];
        },
        async initMenu() {
            if(this.query.dataFrom !== 'global') {
                const item = await request.getDataFromItem({dataFrom:this.query.dataFrom, id:this.query.id});
                this.$store.commit('globalParams/updateGlobalParams',{
                    targetInfo:item.data.data
                })
            }
        },
        deepMerge(target, source) {
            const merged = { ...target };

            for (const key in source) {
                if (source.hasOwnProperty(key)) {
                    if (source[key] instanceof Object && key in target) {
                        // 如果当前属性是对象，并且在目标对象中已存在，递归合并
                        merged[key] = this.deepMerge(target[key], source[key]);
                    } else {
                        // 否则直接赋值
                        merged[key] = source[key];
                    }
                }
            }

            return merged;
        },
        adjustPeriod(opt) {
            const end = new Date();
            end.setHours(0, 0, 0, 0);
            let start = new Date();
            const [param1, param2] = opt.split('');
            try {
                switch (opt) {
                case "TY": {
                    start.setUTCHours(0, 0, 0, 0);
                    start.setUTCMonth(0);
                    start.setUTCDate(1);
                    break;
                }
                case '3M': {
                    start = this.getDateOneOfMonth(3)
                    break;
                }
                case '6M': {
                    start = this.getDateOneOfMonth(6)
                    break;
                }
                case '1Y': {
                    start = this.getDateOneOfMonth(12)
                    break;
                }
                default: {
                    throw `Option error with an unknown parameter ${opt}`;
                }
                }
                start.setHours(0, 0, 0, 0);
                this.basicOptions.period = [start, end];
            } catch (e) {
                console.error("An error occured. Details:", e);
                this.$message.error("错误日期参数，设置失败");
                return;
            }
        },
        // 获取几个月前的1号0点时间
        getDateOneOfMonth(num) {
            const time = moment().set('date', 1).subtract(num-1, 'month');
            return new Date(new Date(time).setHours(0,0,0,0));
        },
        getRangeTime(){
            const startTime = new Date(moment(this.period[0]).subtract(0, 'days')).setHours(0, 0, 0, 0);
            const endTime = new Date(moment(this.period[1]).set('date', 1).add(1, 'month')).getTime() - 1;
            return {
                startTime,
                endTime
            }
        },
        adjustQuickModificationTime() {
            const period = _.cloneDeep(this.period);
            const today = new Date();
            const startDate = new Date(period[0]);
            const endDate = new Date(period[1]);
            function _isDateRangeThisYear() {
                const firstDayOfYear = new Date(today.getFullYear(), 0, 1);
                return (
                    startDate.getTime() === firstDayOfYear.getTime() && endDate.toDateString() === today.toDateString()
                );
            }
            function _isDateRangeMonthApart(month = 1) {
                const startDate = new Date(period[0]);
                const endDate = new Date(period[1]);
                const oneMonthAgo = new Date(endDate);
                oneMonthAgo.setMonth(endDate.getMonth() - month);
                return (
                    startDate.toDateString() === oneMonthAgo.toDateString() && oneMonthAgo < endDate && endDate.toDateString() === today.toDateString()
                );
            }
            function _isDateRangeOneYearApart(year = 1) {
                const startDate = new Date(period[0]);
                const endDate = new Date(period[1]);
                const oneYearAgo = new Date(endDate);
                oneYearAgo.setFullYear(endDate.getFullYear() - year);
                return (
                    startDate.toDateString() === oneYearAgo.toDateString() && oneYearAgo < endDate && endDate.toDateString() === today.toDateString()
                );
            }
            if (_isDateRangeThisYear()) {
                console.log("今年");
                this.quickModificationTime = "TY";
            } else if (_isDateRangeMonthApart(3)) {
                console.log("三个月");
                this.quickModificationTime = "3M";
            } else if (_isDateRangeMonthApart(6)) {
                console.log("六个月");
                this.quickModificationTime = "6M";
            } else if (_isDateRangeOneYearApart(1)) {
                console.log("一年");
                this.quickModificationTime = "1Y";
            } else {
                console.log("不符合");
                this.quickModificationTime = "";
            }
        },
        storageInitialize() {
            if (localStorage.getItem("biShowTypes")) {
                try {
                    const val = parseInt(localStorage.getItem("biShowTypes"), 10);
                    if (Number.isNaN(val)) {
                        throw "Wrong option type of show types, expect Number but get NaN.";
                    }
                    this.showTypes = val;
                } catch (e) {
                    console.error("An error occured while watching show type options being modified. Detail: ", e);
                    localStorage.removeItem("biShowTypes");
                    this.$message.error("缓存中图表类型有错误数据，已将对应数据初始化，请至配置项重新设置");
                    this.storageInitialize();
                }
            } else {
                localStorage.setItem("biShowTypes", this.showTypes);
            }
            if (localStorage.getItem("biStatisticGroup")) {
                try {
                    const val = localStorage.getItem("biStatisticGroup");
                    this.statisticGroup = val;
                } catch (e) {
                    console.error("An error occured while watching biStatisticGroup options being modified. Detail: ", e);
                    localStorage.removeItem("biStatisticGroup");
                    this.$message.error("缓存中图表类型有错误数据，已将对应数据初始化，请至配置项重新设置");
                    this.storageInitialize();
                }
            } else {
                localStorage.setItem("biStatisticGroup", this.statisticGroup);
            }
            if (JSON.parse(localStorage.getItem("biDetailOptions"))) {
                try {
                    this.detailOptions = JSON.parse(localStorage.getItem("biDetailOptions"));
                } catch (e) {
                    console.error("An error occured while watching detail options being modified. Detail: ", e);
                    localStorage.removeItem("biDetailOptions");
                    this.$message.error("缓存中图表数据有错误数据，已将对应数据初始化，请至配置项重新设置");
                    this.storageInitialize();
                }
            } else {
                this.setDefaultDetailOption();
                localStorage.setItem("biDetailOptions", JSON.stringify(this.detailOptions));
            }
        },
        statsListInitialize() {
            this.statsList = {
                block1: null,
                block2: null,
                block3: null,
                block4: null,
                block5: null
            };
            let blockSort = localStorage.getItem("biBlockSort");
            if (blockSort) {
                blockSort = JSON.parse(blockSort)
                //本地缓存了顺序
                const checkedOptions = [];
                for (const block in blockSort) {
                    const key =blockSort[block]
                    if (this.stats[key]) {
                        // 非超管会隐藏部分条目
                        checkedOptions.push(key);
                        this.statsList[block] = {
                            ...this.stats[key],
                            source:key
                        };
                    }else{
                        this.detailOptions[key] = false;
                    }
                }
                this.checkedOptions = checkedOptions;
            }else{
                const checkedOptions = [];
                for (const key of this.optionsSort) {
                    if (this.stats[key] && this.detailOptions.hasOwnProperty(key) && this.detailOptions[key] === true) {
                        checkedOptions.push(key);
                    }else{
                        this.detailOptions[key] = false;
                    }
                }
                this.checkedOptions = checkedOptions;
                for (let i = 0; i < this.checkedOptions.length; i++) {
                    this.statsList[`block${i + 1}`] = {
                        ...this.stats[this.checkedOptions[i]],
                        source:this.checkedOptions[i]
                    };
                }
                localStorage.setItem("biDetailOptions", JSON.stringify(this.detailOptions));
            }
        },
        draggablePanelTitleGenerate(item) {
            let title = void 0;
            let value = NaN;
            let data = null;
            if (item) {
                title = item.title ? this.lang.statistic[item.title] : "";
                value = item.value || item.value === 0 ? item.value : NaN;
                data = item.data ? item.data : null;
            }
            return title ? `${title} ${item.hiddenCount?'':`(${value})`}` : "";
        },

        // api请求

        // dialog操作
        openSettingDialog() {
            this.showSettingDialog = true;
            this.handleSettingOpen();
        },
        closeSettingDialog() {
            this.showSettingDialog = false;
        },

        // handlers
        handleSettingOpen() {
            try {
                this.basicOptions = {
                    period: this.period,
                    showTypes: parseInt(localStorage.getItem("biShowTypes"), 10),
                    statisticGroup: localStorage.getItem("biStatisticGroup"),
                };
                this.detailOptions = JSON.parse(localStorage.getItem("biDetailOptions"));
                this.tempQuery = this.query;
                this.subject = this.tempQuery.value || ''
                if (!this.isAdmin) {
                    this.detailOptions.groupTotal = false;
                    this.detailOptions.library = false;
                }
            } catch (e) {
                console.error("An error has occured while setting caches for settings. Details:", e);
                // localStorage.setItem("biPeriod", JSON.stringify(this.period));
                localStorage.setItem("biShowTypes", JSON.stringify(this.showTypes));
                localStorage.setItem("biStatisticGroup", JSON.stringify(this.statisticGroup));
                localStorage.setItem("biDetailOptions", JSON.stringify(this.detailOptions));
                this.$message.error("读取配置缓存失败，上次设置内容已丢失");
            }
        },
        handleSettingCancel() {
            this.closeSettingDialog();
        },
        handleSettingConfrim() {
            try {
                this.period = _.cloneDeep(this.basicOptions.period);
                this.showTypes = _.cloneDeep(this.basicOptions.showTypes);
                this.statisticGroup = _.cloneDeep(this.basicOptions.statisticGroup);
                this.adjustQuickModificationTime(this.period);
                this.query = this.tempQuery;
                localStorage.setItem("biShowTypes", this.basicOptions.showTypes);
                localStorage.setItem("biStatisticGroup", this.basicOptions.statisticGroup);
                localStorage.setItem("biDetailOptions", JSON.stringify(this.detailOptions));
                localStorage.setItem("biBlockSort", '');
            } catch (e) {
                console.error("An error has occured while setting cache for settings. Details:", e);
                this.$message.error("设置配置缓存失败，本次设置部分内容将不会在缓存中出现");
            } finally {
                this.statsListInitialize();
                this.closeSettingDialog();
                this.renderAll();
            }
        },
        handleQuickModificationTimeChange() {
            if (this.quickModificationTime !== "") {
                this.adjustPeriod(this.quickModificationTime);
                this.period = _.cloneDeep(this.basicOptions.period);
            }
            this.renderAll();
        },
        renderAll(){
            this.renderLiveData();
            this.renderChartData();
            this.renderPieData();
            this.renderMapData();
        },
        async renderLiveData(){
            this.loadingLiveData = true;
            if (this.statisticGroup === 'group') {
                const result = await request.getLiveAndIworksData({
                    startTime:this.getRangeTime().startTime,
                    endTime:this.getRangeTime().endTime,
                    dataFrom:this.query.dataFrom,
                    id:this.query.id
                })
                this.liveDataObj = result.data.data;
            } else if (this.statisticGroup === 'device') {
                const result = await request.getExamSummary({
                    startTime:this.getRangeTime().startTime,
                    endTime:this.getRangeTime().endTime,
                    dataFrom:this.query.dataFrom,
                    id:this.query.id
                })
                this.examSummaryObj = result.data.data;
            }
            this.loadingLiveData = false;
        },
        renderChartData(){
            for(let key in this.statsList){
                if (this.statsList[key]) {
                    this.renderChart(key);
                }
            }
        },
        async renderChart(blockName){
            this.loadingChart[blockName] = true;
            const block = this.statsList[blockName];
            let method = 'getChartData';
            if (this.statisticGroup === 'device') {
                switch(block.source){
                case 'deviceInstallTime': {
                    this.setDeviceInstallData(blockName);
                    return;
                }
                case 'examCountOfOrganization':
                {
                    const type = 2;
                    this.setExamCountData(blockName,type);
                    return;
                }
                case 'examCountOfDevice':
                {
                    this.setDeviceExamCountData(blockName);
                    return;
                }
                case 'deviceFailureTip':
                {
                    this.setDeviceFailureTip(blockName);
                    return;
                }
                case 'deviceStatus': {
                    this.setDeviceStatusData(blockName);
                    return;
                }
                case 'probeUsageRate': {
                    this.setProbeUsageRateData(blockName);
                    return;
                }
                default: {
                    this.setDeviceUtilizationRateData(blockName);
                    method = 'getUtillizationData'
                    return;
                }
                }
            }
            const result = await request[method]({
                startTime:this.getRangeTime().startTime,
                endTime:this.getRangeTime().endTime,
                dataFrom:this.query.dataFrom,
                id:this.query.id,
                source:block.source
            })
            this.loadingChart[blockName] = false;
            let data = {
                xAxis: {
                    data: [],
                },
                yAxis: {},
                series: [
                    {
                        data: [],
                    },
                ],
            };
            let value = 0;
            let times = 1;//倍数，liveDuration是分钟要除以60
            if (block.source === 'liveDuration') {
                times = 60;
            }
            const axis = Tool.buildMonthAxis(this.period[0],this.period[1], result.data.data);
            data.xAxis.data = axis.xAxis;
            data.series[0].data = axis.yAxis;
            for (let index = 0; index<data.series[0].data.length;index++) {
                const item = data.series[0].data[index];
                data.series[0].data[index] = parseInt(item/times)
                value += parseInt(item/times);
            }
            block.data = data;
            block.value = value;
        },
        async renderPieData(){
            this.loadingPieData = true;
            const result = await request.getPieData({
                startTime:this.getRangeTime().startTime,
                endTime:this.getRangeTime().endTime,
                dataFrom:this.query.dataFrom,
                id:this.query.id,
                type:this.statisticGroup,
            })
            this.loadingPieData = false;
            this.tempPieData = result;
            this.setPieData();

        },
        handdleTogglePieData(type){
            this.pieType = type;
            this.setPieData();
        },
        setPieData(){
            const result = this.tempPieData;
            const typeArr = [0,1,2,3,4,5,6,11,12];
            if (this.statisticGroup === 'device') {
                for(let item of this.stats.overallDistribution){
                    item.value = 0;
                }
                this.totalExamCount = 0;
                const map = result.data.data;
                for(let key in map){
                    if (typeArr.indexOf(parseInt(key))>-1) {
                        for(let item of this.stats.overallDistribution){
                            if(item.key == key){
                                item.value = map[key];
                            }
                        }
                    }else{
                        this.stats.overallDistribution[9].value +=map[key];
                    }
                    this.totalExamCount += map[key]
                }
            }else{
                for(let item of this.stats.overallDistribution){
                    item.value = 0;
                }
                const map = result.data.data[this.pieType]
                for(let key in map){
                    if (typeArr.indexOf(parseInt(key))>-1) {
                        for(let item of this.stats.overallDistribution){
                            if(item.key == key){
                                item.value = map[key];
                            }
                        }
                    }else{
                        this.stats.overallDistribution[8].value +=map[key];
                    }
                }
                this.totalExamCount = result.data.data.totalExamCount;
                this.totalImageCount = result.data.data.totalImageCount + result.data.data.totalVideoCount;
            }
        },
        async getMapData(data){
            if (data.regionCode === 'china') {
                data.regionCode = 86
            }
            const result = await request.getMapData({
                startTime:this.getRangeTime().startTime,
                endTime:this.getRangeTime().endTime,
                dataFrom:this.query.dataFrom,
                id:this.query.id,
                regionCode:data.regionCode,
                regionType:data.regionType,
                type:this.statisticGroup,
            })
            data.callback(result.data);
        },
        renderMapData(){
            this.$refs['map_chart'].renderMap();
        },
        async querySearchAsync(queryStr,cb){
            const arr=[]
            if (this.subject) {
                const result = await request.getSubjectData({
                    subject:this.subject
                })
                for(let item of result.data.data){
                    arr.push({
                        value:`${item.id}:${item.subject}(${item.adminInfo.nickname})`,
                        id:item.id,
                    })
                }
            }
            cb(arr)
        },
        handleSelect(item){
            this.tempQuery = {
                dataFrom: 'group',
                id:item.id,
                value:this.subject
            }
        },
        handleChange(value){
            if (!value) {
                this.tempQuery = {
                    dataFrom: 'global',
                    id:-1,
                    value:''
                }
            }
        },
        showDatePicker(){
            this.showStartDatePicker = true;
            this.pickerDate = _.cloneDeep(this.basicOptions.period);
        },
        confirmDate(){
            const startDate = new Date(this.pickerDate[0]);
            const endDate = new Date(this.pickerDate[1]);
            if (endDate.valueOf()-startDate.valueOf()>= 365*24*60*60*1000) {
                Toast(this.lang.statistic.time_range_tip)
            }else{
                this.basicOptions.period = this.pickerDate;
            }
            this.showStartDatePicker = false;
            this.showEndDatePicker = false;
        },
        handleClickTab(name){
            this.basicOptions.statisticGroup = name;
            this.setDefaultDetailOption();
        },
        setDefaultDetailOption(){
            let target = this.defaultDataItem;
            if (this.basicOptions.statisticGroup === 'device') {
                target = this.defaultDeviceItem;
            }
            for (const key in this.detailOptions) {
                this.detailOptions[key] = false;
            }
            for(let key of target){
                this.detailOptions[key] = true;
            }
        },
        async setDeviceInstallData(blockName){
            const block = this.statsList[blockName];
            const result = await request.getDopplerInstallData({
                startTime:this.getRangeTime().startTime,
                endTime:this.getRangeTime().endTime,
                dataFrom:this.query.dataFrom,
                id:this.query.id,
            })
            this.loadingChart[blockName] = false;
            let data = {
                xAxis: {
                    data: [],
                },
                yAxis: {},
                series: [
                    {
                        data: [],
                    },
                ],
            };
            const axis = Tool.buildYearRangeAxis(result.data.data);
            data.xAxis.data = axis.xAxis;
            data.series[0].data = axis.yAxis;
            block.data = data;
        },
        async setDeviceUtilizationRateData(blockName){
            const block = this.statsList[blockName];
            const result = await request.getDeviceUtilizationRateData({
                startTime:this.getRangeTime().startTime,
                endTime:this.getRangeTime().endTime,
                dataFrom:this.query.dataFrom,
                id:this.query.id,
            })
            this.loadingChart[blockName] = false;
            let data = {
                xAxis: {
                    data: [],
                },
                yAxis: {},
                series: [
                    {
                        data: [],
                    },
                ],
            };
            const axis = Tool.buildMonthAxis(this.period[0],this.period[1], result.data.data);
            data.xAxis.data = axis.xAxis;
            data.series[0].data = axis.yAxis;
            block.data = data;
        },
        async setExamCountData(blockName, type){
            const block = this.statsList[blockName];
            const result = await request.getCreateExamData({
                startTime:this.getRangeTime().startTime,
                endTime:this.getRangeTime().endTime,
                dataFrom:this.query.dataFrom,
                id:this.query.id,
                type
            })
            this.loadingChart[blockName] = false;
            let countArr = result.data.data;//[{id:1,name:'233',data:{inGroup:12,global:0}},{id:2,name:'wade',data:{inGroup:8,global:23}}];
            this.makeUpCountData(countArr);
            block.data = countArr
        },
        async setDeviceExamCountData(blockName){
            const block = this.statsList[blockName];
            const result = await request.getDeviceExamData({
                startTime:this.getRangeTime().startTime,
                endTime:this.getRangeTime().endTime,
                dataFrom:this.query.dataFrom,
                id:this.query.id,
            })
            this.loadingChart[blockName] = false;
            let countArr = result.data.data;//[{id:1,name:'233',data:{inGroup:12,global:0}},{id:2,name:'wade',data:{inGroup:8,global:23}}];
            this.makeUpCountData(countArr);
            block.data = countArr
        },
        async setDeviceFailureTip(blockName){
            const block = this.statsList[blockName];
            const groupIds = []
            if (this.query.dataFrom === 'groupset') {
                this.$store.state.globalParams.targetInfo.groupList.forEach((item)=>{
                    groupIds.push(parseInt(item.id));
                })
            }else if (this.query.dataFrom === 'group') {
                groupIds.push(parseInt(this.query.id));
            }
            const result = await request.getDeviceFailureTip({
                groupIds,
            })
            this.loadingChart[blockName] = false;
            let list =[];
            for(let item of result.data.data){
                list.push({
                    key:`${item.product_name}_${item.device_name}`,
                    value:`${item.alarm.alarm_detail.error_msg}_${item.alarm.alarm_detail.error_code}`
                })
            }
            block.data = list;
            block.value = list.length;
        },
        makeUpCountData(countArr){
            const currentLength = countArr.length;
            let makeUpLength = 0;
            if (currentLength  < 12) {
                makeUpLength = 12 - currentLength;
            }
            for(let index = 0 ; index < makeUpLength; index++){
                countArr.unshift({id:index,label:'',value:0})
            }
        },
        async setDeviceStatusData(blockName){
            const block = this.statsList[blockName];
            const result = await request.getDeviceStatusData({
                startTime:this.getRangeTime().startTime,
                endTime:this.getRangeTime().endTime,
                dataFrom:this.query.dataFrom,
                id:this.query.id,
            })
            this.loadingChart[blockName] = false;
            let tempArr =  []
            for(let key in result.data.data){
                tempArr.push({
                    key:key,
                    value:result.data.data[key],
                })
            }
            block.data = tempArr
        },
        async setProbeUsageRateData(blockName){
            const block = this.statsList[blockName];
            const result = await request.getProbeUsageRateData({
                startTime:this.getRangeTime().startTime,
                endTime:this.getRangeTime().endTime,
                dataFrom:this.query.dataFrom,
                id:this.query.id,
            })
            this.loadingChart[blockName] = false;
            let data = {
                xAxis: {
                    data: [],
                },
                yAxis: {},
                series: [
                    {
                        data: [],
                    },
                ],
            };
            const axis = Tool.buildProbeUsageAxis(result.data.data);
            data.xAxis.data = axis.xAxis;
            data.series[0].data = axis.yAxis;
            block.data = data;
        }
    },
};
</script>
<style lang="scss">
$boxGap: 1em !default; // 每个盒子的间距
$columnGap: 1.3em !default; // 列间距
$rainbowHeight: 2px !default; // 彩虹盒子的高度
$svhOffset: 40px !default; // 针对于 100svh 去除 header 的偏移量

$value1_3: 100% / 3 !default; // 列 1/3 的数量值
$value2_3: calc($value1_3 * 2 + $columnGap) !default; // 列 2/3 的数量值，不同单位的运算需用 calc 原生css函数计算
$value3_3: 100% !default; // 列 3/3 的数量值
$rowValue1_3: calc(
    100vh / 2 - $svhOffset - $rainbowHeight
) !default; // 行 1/3 的数量值，用的 100svh 作为固定数值进行计算，使用 100% 进行计算将会因为百分比参照不同等繁琐的原因产生误差
$rowValue2_3: calc($rowValue1_3 * 2 + $boxGap) !default; // 行 2/3 的数量值
$rowValue3_3: 100vh !default; // 行 3/3 的数量值
$rowValue3_4: 75vh !default; // 行 3/4 的数量值

$optionsCheckedColor: #ffc062 !default; // 筛选条件中选中后的字体色及已选择的计数数字色
$warningColor: red !default; // 警告色
$optionsGeneralButtonColor: #169bd5 !default; // 筛选条件中一般按钮背景色
$optionsGeneralButtonHoverColor: rgb(93, 186, 226) !default; // 筛选条件中一般按钮hover/focus背景色
$optionsHollowButtonColor: #fff !default; // 筛选条件中镂空按钮背景色
$optionsHollowButtonHoverColor: #ecf5ff !default; // 筛选条件中镂空按钮hover/focus背景色
$basicBoxColor: #202938 !default; // 基础盒背景色
$darkgray: #666 !default; // 深灰（边框）
$lightgray: #c2c2c2 !default; // 淡灰（边框）

@mixin under-border($color: black, $size: 1px, $style: solid) {
    border-bottom: $style $size $color;
}

@mixin flex-between($flexWrap: nowrap) {
    display: flex;
    justify-content: space-between;
    flex-wrap: $flexWrap;
}

@mixin box-basic($occupy: 200px, $borderTop: none, $borderBottom: none, $borderLeft: none, $borderRight: none) {
    height: $occupy;
    padding: .4rem;
    background-color: $basicBoxColor;
    border-radius: 1rem;
    border-top: $borderTop;
    border-bottom: $borderBottom;
    border-left: $borderLeft;
    border-right: $borderRight;
    overflow: auto;
    box-sizing: border-box;
    .el-empty {
        padding: 0;
        :deep(.el-empty__description p) {
            margin: 0;
            font-size: 0.8rem;
            color: #fff;
        }
    }
}

@mixin rainbow-basic($backgroundColor, $flexBasis: 0%, $flexGrow: 1, $flexShrink: 1) {
    height: 100%;
    flex-grow: $flexGrow;
    flex-shrink: $flexShrink;
    flex-basis: $flexBasis;
    background-color: $backgroundColor;
}

.rainbow {
    display: flex;
    height: $rainbowHeight;
    width: 100%;
    .red {
        @include rainbow-basic(#91696d);
    }
    .green {
        @include rainbow-basic(#59857a);
    }
    .camel {
        @include rainbow-basic(#b19f8e, 7.5%);
    }
    .light_blue {
        @include rainbow-basic(#819aab, 11%);
    }
    .purple {
        @include rainbow-basic(#727493, 11%);
    }
}

.draggable {
    cursor: move;
}

.data_display_mobile {
    background: #000;
    color: #fff;
    user-select: none;
    width: 100%;
    .data_header {
        display: flex;
        justify-content: space-between;
        flex-wrap: nowrap;
        height: 60px;
        width: 100%;
        align-items: center;
        position: relative;
        p {
            font-size: 1rem;
            flex:1;
            text-overflow: ellipsis;
            padding-right: .5rem;
            text-align: right;
        }
        .el-icon-s-tools{
            margin: 0.4rem;
        }
        .logo{
            width: 7rem;
        }
        .data_operate {
            @include flex-between;
            align-items: center;
            right: 1rem;
            position: absolute;
            p {
                display: inline-block;
                font-size: 0.8rem;
            }
            i {
                margin-left: 1rem;
                display: inline-block;
                font-size: 1.5rem;
                cursor: pointer;
            }
        }
        .quick_modification_time {
            width: 6rem;
            margin-left: 1rem;
        }
    }

    .data_container {
        padding: 0 .6rem 1rem;
        width: 100%;
        .left,
        .center,
        .right {
            display: flex;
            flex-direction: column;
            gap: $boxGap;
            height: $value3_3;
            flex:3;
            padding-top: 1rem;
        }
        .center{
            flex:4;
        }
        .section1_3 {
            // 1/3 区块
            @include box-basic($rowValue1_3);
            height: auto;
            .count_panel {
                .count_item {
                    @include under-border($darkgray);
                    display: flex;
                    padding: .6rem .6rem .6rem 0;
                    font-size: 1rem;
                    .label {
                        flex: 1;
                    }
                    .value {
                    }
                }
            }
            .trend_chart_panel {
                display: flex;
                flex-direction: column;
                height: 100%;
                .title {
                    @include under-border($darkgray);
                    font-size: 1rem;
                    font-weight: 600;
                    padding: 6px 0;
                    text-align: left;
                    color: #fff;
                }
                .chart_container {
                    flex: none;
                    height: 30vh;
                }
            }
        }
        .section2_3 {
            // 2/3 区块
            @include box-basic($rowValue2_3);
        }
        .section3_3 {
            // 3/3 区块
            @include box-basic($rowValue3_3);
        }
        .section3_4{
            @include box-basic($rowValue3_4);
        }
        .auto_height{
            height: auto;
        }
        .pie_chart_container,.map_chart_container{
            height: 45vh;
            flex:none;
        }
    }
    .van-picker{
        color: #000;
    }
}
.setting {
    color:#000;
    padding: .5rem;
    display: flex;
    flex-direction: column;
    .setting_container{
        flex: 1;
        overflow: auto;
    }
    .count_date{
        &>button{
            margin: .2rem;
        }
    }
    .header_title{
        margin-bottom: .2rem;
    }
    .date_range{
        margin: 0.3rem;
        border: 1px solid #ccc;
        padding: 0.3rem 0.5rem;
        border-radius: 0.3rem;
    }
    .setting_content_box{
        padding: 0.6rem 0 0;
        h2 {
            @include under-border($darkgray);
            text-align: left;
            padding-bottom: 0.4rem;
            margin-bottom: 0.8rem;
            font-size: 1rem;
        }
        .van-checkbox{
            margin: .4rem 0;
        }
    }
    .setting_footer{
        display: flex;
        justify-content: space-around;
    }
}
.fake_empty {
    background-color: #000 !important;
}

/* 小屏幕适配 */
@media (max-width: 768px) {
    .data_display_mobile {
        .data_container {
            .pie_chart_container,.map_chart_container{
                height: 35vh;
            }
            .section1_3 {
                .count_panel {
                    .count_item {
                        font-size: 0.9rem;
                        padding: .4rem .4rem .4rem 0;
                    }
                }
            }
        }
    }
}

@media (max-width: 480px) {
    .data_display_mobile {
        .data_container {
            .pie_chart_container,.map_chart_container{
                height: 30vh;
            }
        }
    }
}
</style>

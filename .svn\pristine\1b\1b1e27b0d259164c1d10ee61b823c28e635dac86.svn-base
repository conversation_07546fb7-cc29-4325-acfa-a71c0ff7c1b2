<template>
    <div class="modify_password_page third_level_page">
        <mrHeader>
            <template #title>
                {{lang.modify_password_text}}
            </template>
        </mrHeader>
        <div class="container">
            <div class="modify_wraper">
                <div class="modify_password_row" v-show="isShowOldPassword">
                    <div class="modify_password_label">{{lang.modify_old_password_label}}</div>
                    <input ref="my_pass_word" type="password" class="modify_password_input needsclick" maxlength="16" v-model="old_password" name="" >
                </div>
                <div class="modify_password_row">
                    <div class="modify_password_label">{{lang.modify_new_password_label}}</div>
                    <input ref="new_pass_word" type="password" maxlength="16" class="modify_password_input needsclick" v-model="new_password" name="" >
                </div>
                <div class="modify_password_row">
                    <div class="modify_password_label">{{lang.register_confirm_password}}</div>
                    <input type="password" maxlength="16" class="modify_password_input" v-model="new_confirm_password" name="" >
                </div>
            </div>

            <div class="save_btn_container">
                <button class="primary_bg  save_btn" @click="modify_current_password">{{lang.save_txt}}</button>
            </div>
        </div>

    </div>
</template>
<script>
import service from '../service/service'
import base from '../lib/base'
import { Toast } from 'vant';
import {passwordStrongRegExp} from '@/common/regExpMapping.js'
export default {
    mixins: [base],
    name: 'modifyPassword',
    components: {},
    data(){
        return {
            old_password:'',
            new_password:'',
            new_confirm_password:'',
            // force_enhance_password:false,
        }
    },
    mounted(){
        this.$nextTick(()=>{
            // var pos=window.location.href.lastIndexOf("?");
            // if (0<pos) {
            //     var name = window.location.href.substr(pos + 1);
            //     if ("force_enhance_password" == name) {
            //         this.force_enhance_password = true;
            //     }
            // }
            // setTimeout(()=>{
            //     if (this.isShowOldPassword) {
            //         this.$refs.my_pass_word.focus()
            //     }else{
            //         this.$refs.new_pass_word.focus()
            //     }

            // },500)
        })
    },
    computed:{
        // forceEnhancePassword () {
        //     return this.force_enhance_password;
        // },
        isShowOldPassword(){
            return this.user.is_password_privatized != 0;
        }
    },
    methods:{
        modify_current_password(){
            // var passwordRegexp=/^[a-zA-Z0-9~!@#$%^&*()_+`\[\]\\\|\;\':",.\/<>\?\*]{6,16}$/
            // var passwordRegexpEnhanced=/^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])[a-zA-Z0-9~!@#$%^&*()_+`\[\]\\\|\;\':",.\/<>\?\*]{8,16}$/
            if (this.isShowOldPassword && (this.old_password.length < 6 || this.old_password.length > 16)){
                Toast(this.lang.password_length_not_correct);
            }else if (!passwordStrongRegExp.test(this.new_password)){
                Toast(this.lang.enhanced_password_format_tip);
            }else if (this.new_password != this.new_confirm_password){
                Toast(this.lang.confirm_password_and_new_password_not_match);
            }else{
                var tip = '';
                var that = this;
                var input_data = {
                    old_pwd:this.old_password,
                    new_pwd:this.new_password,
                    token:this.user.new_token,
                };
                window.main_screen.commitPasswordModify(input_data, function(result){
                    if(result.is_succ){
                        Toast(that.lang.modify_password_success);
                        let ajaxServer=that.systemConfig.server_type.protocol+that.systemConfig.server_type.host+that.systemConfig.server_type.port
                        // service.encryptPassword(ajaxServer,{
                        //     src_str:that.new_password
                        // }).then((res)=>{
                        //     if (res.data.error==0) {

                        //         // window.localStorage.setItem('password',res.data.encrypt_str)
                        //     }
                        // })
                        that.$store.commit('user/updateUser',{
                            is_password_privatized:1,
                            is_enhance_password:true
                        });
                        that.$router.go(-1);
                    }else{
                        if (result.information == "account_err") {
                            tip = that.lang.modify_password_fail_account_incorrect;
                        } else if (result.information == "old_pwd_err") {
                            tip = that.lang.modify_password_fail_password_incorrect;
                        } else if (result.information == "pwd_same") {
                            tip = that.lang.modify_password_fail_password_same;
                        } else if (result.information == "database_err") {
                            tip = that.lang.modify_password_fail_database_err;
                        } else if (result.information == "password_format_error") {
                            tip = that.lang.password_format_tip;
                        } else if (result.information == "enhanced_password_format_error") {
                            tip = that.lang.enhanced_password_format_tip;
                        }else{
                            tip=that.lang.unknown_error
                        }

                        Toast(tip);
                    }
                })
            }
        },
    }
}

</script>
<style lang="scss">
	.modify_password_page{
		.container{
            background:#fff;
            margin-top:1rem;
            overflow: hidden;
            .modify_wraper{
                padding:0 .8rem;
                border-top: 1px solid #c8c7cc;
                border-bottom: 1px solid #c8c7cc;
            }
            .modify_password_row{
                border-bottom: 1px solid #c8c7cc;
                display:flex;
                padding: 0.1rem 0;
                justify-content: space-between;
                align-items: center;
                .modify_password_label{
                    line-height: 2rem;
                    font-size: .8rem;
                    min-width: 3.2rem;
                    text-align: right;
                    flex-shrink: 0;
                    margin-right: 0.8rem;
                    // width: 5rem;
                }
                .modify_password_input{
                    border: none;
                    font-size: .8rem;
                    padding: .5rem 0;
                    display: block;
                    color: #666;
                    flex: 1;
                    // width: 20px;
                }
                &:last-child{
                    border-bottom:none;
                }
            }

        }
        .save_btn_container{
            margin:0 .5rem;
                .save_btn{
                display: block;
                width: 100%;
                border: none;
                font-size: 1rem;
                line-height: 2rem;
                margin: 1rem 0 .6rem;
                border-radius: .2rem;
            }
        }

	}
</style>

<template>
    <div class="pie_chart_panel">
        <div class="exam_title">
            <p>{{lang.statistic.dr_distribution}}</p>
        </div>
        <div class="count_panel">
            <div class="count_item item1" @click="toggleData('adultData')">
                <p class="count_title">{{lang.statistic.adult}}</p>
                <p class="count_value">{{adultTotal}}</p>
            </div>
            <div class="count_item item2" @click="toggleData('childData')">
                <p class="count_title">{{lang.statistic.child}}</p>
                <p class="count_value">{{childTotal}}</p>
            </div>
        </div>
            
        <div class="pie_chart_container">
            <div
                class="pie_chart"
                ref="pie_chart_dom"
                :style="{
                    width: width + '%',
                    height: height + '%',
                }"
            ></div>
        </div>
    </div> 
</template>

<script>
import base from '../../lib/base'
import * as echarts from "echarts";
export default {
    mixins: [base],
    name: "bi_pie_chart",
    data: function () {
        return {
            pieCharts: {},
            type:'adultData',
        };
    },
    props: {
        data: {
            required: true,
            type: Array,
            default:()=>{
                return []
            }
        },
        adultTotal: {
            type: Number,
            default: 0,
        },
        childTotal: {
            type: Number,
            default: 0,
        },
        width: {
            type: Number,
            default: 100,
        },
        height: {
            type: Number,
            default: 100,
        },
        mobileMode: {
            type: Boolean,
            default: false,
        },
    },
    watch: {
        data: {
            handler: function () {
                this.updatePieChart();
            },
            deep: true,
        },
    },
    computed: {
        legendData() {
            return this.data.map((obj) => {
                // fixedName 写死的名称，不需要翻译
                obj.name =obj.fixedName || this.lang.statistic.dr_exam_types[obj.key]||this.lang.statistic.other_exam_type;
                return obj.name
            });
        },
        totalValue() {
            const sum = this.data.reduce(function (accumulator, currentObject) {
                return accumulator + currentObject.value;
            }, 0);
            return sum;
        },
    },
    mounted() {
        this.initPieChart();
        window.addEventListener("resize", () => {
            this.pieCharts.resize();
        });
    },
    beforeDestroy() {
        this.pieCharts.dispose();
    },
    methods: {
        getOptions() {
            let option = {
                legend: {
                    show: true,
                    orient: "horizontal",
                    x: "left",
                    bottom: 0,
                    left: 'center',
                    right: 'center',
                    padding:2,
                    data: this.legendData,
                    selectedMode: false,
                    formatter: (params) => {
                        const foundObject = this.data.find(function (obj) {
                            return obj.name === params;
                        });
                        const percentage = ((foundObject.value / this.totalValue) * 100) || 0;
                        return `{Legendstyle|${params} ${foundObject.value} | ${percentage
                            .toFixed(1)
                            .replace(/\.0+$/, "")}%}`;
                    },
                    textStyle: {
                        color:'#fff',
                        lineHeight: this.mobileMode?14:20,
                        rich: {
                            Legendstyle: {
                                width: this.mobileMode?126:154,
                                padding: [0, 0],
                                color: "#fff",
                                fontSize: this.mobileMode?12:16,
                            },
                        },
                    },
                    tooltip:{
                        lineStyle:{
                            width:100
                        }
                    }
                },
                color: {},
                tooltip: {},
                series: [
                    {
                        type: "pie",
                        radius: ["40%", "55%"],
                        center: ["50%", "35%"],
                        color: [
                            "#fbc84b",
                            "#01c59d",
                            "#a981d7",
                            "#5567d3",
                            "#383292",
                            "#ff675c",
                            "#df00df",
                            "#ffff00",
                            "#3aaad2",
                        ],
                        percentPrecision: 2,
                        universalTransition: true,
                        tooltip: {
                            show: true,
                            trigger: "item",
                            formatter: (params)=>{
                                const percentage = ((params.data.value / this.totalValue) * 100) || 0;
                                return `${params.name} ${params.data.value} | ${percentage
                                    .toFixed(1)
                                    .replace(/\.0+$/, "")}%`;
                            },
                            confine: true,
                        },
                        avoidLabelOverlap: true,
                        startAngle: 82,
                        label: {
                            show: false,
                            color: "inherit",
                            fontSize: 18,
                            fontWeight: 600
                            // overflow: 'break'
                        },
                        labelLine: {
                            show: false,
                        },
                        // emphasis: {
                        //     scaleSize: 12,
                        //     focus: "self",
                        //     itemStyle: {
                        //         shadowBlur: 10,
                        //         shadowOffsetX: 0,
                        //         shadowColor: 'rgba(0, 0, 0, 0.5)'
                        //     },
                        //     label: {
                        //         show: true,
                        //         fontSize: 25,
                        //         fontWeight: "bold",
                        //     },
                        //     labelLine: {
                        //         show: true,
                        //         lineStyle: {
                        //             color: "#fff",
                        //             width:1,
                        //             legend:1,
                        //         },
                        //     },
                        // },
                        data: this.data,
                    },
                    {
                        type: "pie",
                        color: "#256789",
                        radius: "30%",
                        center: ["50%", "35%"],
                        avoidLabelOverlap: false,
                        universalTransition: true,
                        tooltip: {
                            show: true,
                            trigger: "item",
                            formatter: "{b}: {c}",
                            confine: true,
                        },
                        label: {
                            show: true,
                            position: "center",
                            formatter: function (params) {
                                return `{name|${params.name}}\n{value|${params.value}}`;
                            },
                            rich: {
                                name: {
                                    fontSize: 16,
                                    padding: 8,
                                    color: "#fff",
                                    fontWeight: "normal",
                                },
                                value: {
                                    fontSize: 34,
                                    padding: 8,
                                    color: "#ffff00",
                                    fontWeight: "600",
                                },
                            },
                        },
                        emphasis: {
                            label: {
                                show: true,
                                formatter: function (params) {
                                    return ["{name|" + params.name + "}", "{value|" + params.value + "}"].join("\n");
                                },
                                rich: {
                                    name: {
                                        fontSize: 20,
                                    },
                                    value: {
                                        fontSize: 42,
                                    },
                                },
                            },
                        },
                        data: [
                            {
                                value: this.totalValue,
                                name: this.lang.statistic.exam_count,
                            },
                        ],
                    },
                ],
            };
            return option;
        },
        initPieChart() {
            let option = this.getOptions();
            this.pieCharts = echarts.init(this.$refs["pie_chart_dom"], null, { renderer: "svg" });
            this.pieCharts.setOption(option);
        },
        updatePieChart() {
            let option = this.getOptions();
            this.pieCharts.setOption(option);
        },
        toggleData(type){
            this.type = type;
            this.$emit('togglePieData',type)
        }
    },
};
</script>

<style lang="scss" scoped>
.pie_chart_panel{
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    flex-direction: column;
    .exam_title{
        display: flex;
        justify-content: center;
        position: relative;
        &::after{
            content: '';
            display: block;
            border-bottom: 1px solid #aaa;
            width: 100%;
            position: absolute;
            top: 50%;
            z-index: 1;
        }
        &>p{
            color: #fff;
            font-size: 18px;
            text-align: center;
            padding: 10px 40px;
            border-radius: 8px;
            border:2px solid #366;
            z-index: 2;
            background: #232937;
        } 
    }
    .count_panel{
        overflow: hidden;
        margin-top: 20px;
        display: flex;
        justify-content: center;
    }
    .count_item{
        border: 2px solid #366;
        border-radius: 10px;
        padding: 4px 20px;
        position: relative;
        cursor: pointer;
        text-align: center;
        &.item1{
            margin-right: 10px;
        }
        &.item2{
        }
        .count_item_bg{
            border: 3px solid #19568a;
            border-radius: 50%;
            position: absolute;
            left: 4px;
            top: 6px;
            width: 44px;
            height: 44px;
            padding: 4px;
            background: #0b2d53;
            .bg1{
                width: 26px;
                margin: 0 auto;
                display: block;
                margin-top: 4px;
            }
        }
        .count_title{
            color: #fff;
            line-height: 1.7;
        }
        .count_value{
            color: #ff0;
        }
    }
    .pie_chart_container{
        width: 100%;
        flex:1;
    }
}
</style>

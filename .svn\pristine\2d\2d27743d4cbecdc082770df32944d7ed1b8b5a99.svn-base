import axios from 'axios'
import { Toast } from "vant";
import Tool from '@/common/tool'
import {Logger} from '@/common/console'
// const NETWORK_ERROR_MESSAGE='网络异常'
const SERVER_ERROR_MESSAGE='系统异常'
const UNEXPETED_STATUS=-1
// const EXPETED_STATUS=1
// const UNLOGIN_STATUS=100
// const SUCCESS_STATUS=0
// const COMMON_STATUS=1000

const api=axios.create({
    headers:{
        'Content-Type':'application/json'
    },
    timeout:1000*60*5,
    transformRequest:[(data)=>{
        if(!data){
            return ;
        }
        return JSON.stringify(data.data)
    }],
    transformResponse:[(data)=>{
        if(data){
            try{
                data=JSON.parse(data)
            }catch(e){
                console.log(SERVER_ERROR_MESSAGE)
            }
        }
        return data;
    }]
})
api.interceptors.request.use((config)=>{
    let token = window.vm.$store.state.user.new_token
    config.headers.token = token
    return config
})
api.interceptors.response.use(
    (response)=>{
        // console.log('interceptors1',response)
        const status=typeof response.status!=='undefined' ? response.status:UNEXPETED_STATUS
        let error_code;
        let key='unknown_error';
        let result;
        if (status===200) {
            result=response;
            if (response.data.error_code) {
                error_code=response.data.error_code;
                key=response.data.key;
            }else{
                error_code=0;
            }
        }else{
            error_code=-1;
            key='server_abnormal_retry_again'
            result=Promise.reject({
                data:{
                    key:'server_abnormal_retry_again',
                    error_code:-1,
                }
            })
        }
        if (!isNaN(error_code)&&error_code!==0) {
            const messageError=[
                'userTokenError',
                'userChangePwdNeedLogin',
                'userOutOfTrail',
            ]
            const lang=window.vm.$store.state.language;
            let tip=lang[key]||lang.error[key]
            if (!tip) {
                let json=JSON.parse(response.config.data)
                tip=lang.unknown_error+json.method
                Logger.saveError({message:'API未知错误'+json.method,data:response})
            }
            if (messageError.indexOf(key)>-1) {
                Tool.openMobileDialog(
                    {
                        message:tip,
                    }
                )
                window.vm.$root.eventBus.$emit('clearAndDirectToLogin',tip)
                Logger.saveError({message:tip,data:response})
            }else{
                Toast(tip);
            }

        }
        return result;

    },
    (err)=>{
        // console.log('interceptors2',api);
        // const lang=window.vm.$store.state.language;
        // Toast(lang.network_error_tip);
        return Promise.reject({
            data:{
                key:'network_error_tip',
                error_code:-1,
                more_detail:err
            }
        })
    }
)
// api.interceptors.response.use(
//     (response)=>{
//         const status=typeof response.data.status!=='undefined' ? response.data.status:UNEXPETED_STATUS
//         if(typeof status !=='number' || status!==SUCCESS_STATUS){
//             if(status===UNLOGIN_STATUS){
//                 alert('登录失效')
//                 //return redirect
//             }
//             if(status > COMMON_STATUS){
//                 alert(response.data.msg||SERVER_ERROR_MESSAGE)
//             }
//             return Promise.reject(response.data)
//         }
//         return response.data||{}
//     },
//     (err)=>{
//         return Promise.reject({
//             msg:NETWORK_ERROR_MESSAGE,
//             status:UNEXPETED_STATUS
//         })
//     }
// )
export default api

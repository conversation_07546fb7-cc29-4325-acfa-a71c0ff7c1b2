<template>
    <div class="multicenter_gallery">
        <div class="top" ref="topSwiper" >

            <div class="mui-slider-item" :key="currentFile.resource_id+currentFile.file_id">
                <template v-if="currentFile.img_encode_type &&currentFile.img_encode_type.toUpperCase() == 'PDF'">
                    <pdf-reader ref="fileReader" :url="readingFile.url" v-if="isShowFileReader"></pdf-reader>
                </template>
                <template v-else-if="checkResourceType(currentFile) === 'image'">
                    <div class="">
                        <img v-if="currentFile.url" :src="currentFile.error_image||getRealUrl(currentFile)" class="preview" draggable="false" @error="setErrorImage(currentFile)">
                        <div v-else class="view_not_uploaded">
                            {{lang.view_not_uploaded}}
                        </div>
                    </div>
                </template>
                <template v-else-if="checkResourceType(currentFile) === 'video'">
                    <video v-if="!isCef&&mainVideoSrc" class="main_video" :poster="getRealUrl(currentFile)" :src="mainVideoSrc" controls @error="playVideoError()"></video>
                </template>
            </div>
            <i v-if="galleryList.length>1&&index!==0" @click="prevImage" class="iconfont iconright1"></i>
            <i v-if="galleryList.length>1&&index<galleryList.length-1" @click="nextImage" class="iconfont iconright2"></i>
        </div>
        <div class="thumb_wrap">
            <div class="thumb_loading" v-show="index===-1" v-loading="true"></div>
            <div ref="thumb_scroll_wrap" class="thumb_scroll_wrap">
                <vue-slide :key="'thumb_'+cid" class="thumb_slide" ref="thumb_slide" v-if="isShowGallery">
                    <div :style="{'width':galleryList.length*157+'px','min-width':'100%'}" @mousewheel.prevent.stop class="clearfix">
                        <div v-for="(file,f_index) in galleryList" class="thumb_item" :class="{'current_thumb':f_index==index}" @mousedown="mousedownThumb($event,f_index)" @mouseup="mouseupThumb($event,f_index)" :key="file.resource_id+file.file_id+f_index">
                            <template v-if="checkResourceType(file) === 'image'">
                                <img v-if="file.url" :src="file.error_image||file.url" class="preview" draggable="false" @error="setErrorImage(file)">
                                <template v-else>
                                    <img  src="static/resource_pc/images/default.png" class="preview" draggable="false" @error="setErrorImage(file)">
                                </template>
                                <i class="icon iconfont iconpicture"></i>
                            </template>
                            <template v-else-if="checkResourceType(file) === 'video'">
                                <img :src="file.url" class="preview" draggable="false">
                                <i class="icon iconfont iconvideo_fill_light"></i>
                            </template>
                            <template v-else>
                                <img src="static/resource_pc/images/poster2.jpg" class="preview" draggable="false">
                            </template>
                            <p v-if="file.protocol_view_name" class="view_name">{{file.protocol_view_name}}</p>
                        </div>
                    </div>
                </vue-slide>
            </div>
            <i @click="lastPage" class="icon iconfont iconsanjiaoxing last_page"></i>
            <i @click="nextPage" class="icon iconfont iconsanjiaoxing next_page"></i>
        </div>
    </div>
</template>
<script>
import base from '../lib/base'
import 'swiper/dist/css/swiper.css'
import Swiper from 'swiper'
import vueSlide from 'vuescroll/dist/vuescroll-slide'
import {checkResourceType,getResourceTempStatus,getRealUrl} from '../lib/common_base'
import {cloneDeep} from 'lodash'
import {initVideoPage,initDcm} from '../lib/common_realtimeVideo'
export default {
    mixins: [base],
    name: 'multicenter_gallery',
    components: {
        vueSlide,
        pdfReader: () => import(/* webpackPrefetch: true */ './pdfReader'),
    },
    data() {
        return {
            checkResourceType,
            getResourceTempStatus,
            getRealUrl,
            index:-1,
            mousedownThumpPoint:null,
            openFile:{},
            sliderTimer: null,
            isShowGallery: false,
            mainVideoSrc: '',
            isShowFileReader: false,
            readingFile: {},
        }
    },
    props: {
        galleryList: {
            type: Array,
            default: () => {
                return [];
            },
        },
    },
    computed:{
        currentFile(){
            let file = this.galleryList[this.index]||this.openFile
            file.mc_resource_map=file.mc_resource_map||false
            console.log('file:',file)
            return file
        },
    },
    mounted(){
        this.$root.eventBus.$off('multicenterGallerySlideById').$on('multicenterGallerySlideById',this.multicenterGallerySlideById);
        this.$root.eventBus.$off('showRealTimeVideoByMulticenterGallery').$on('showRealTimeVideoByMulticenterGallery',this.showRealTimeVideoByMulticenterGallery);
        this.$root.eventBus.$off('hideRealTimeVideoByMulticenterGallery').$on('hideRealTimeVideoByMulticenterGallery',this.hideRealTimeVideoByMulticenterGallery);
    },
    methods: {
        playVideoError(){
            this.showVideoErrorTips()
        },
        multicenterGallerySlideById({resource_id,url}){
            console.log(resource_id,url)
            let index = 0
            if(resource_id){
                index = this.galleryList.findIndex(item=>item.resource_id === resource_id)
            }else if(url) {
                index  = this.galleryList.findIndex(item=>item.url === url)
            }
            this.slideTop(index)
        },
        slideTop(index){
            try {
                this.index=index;
                // this.rightTab=0;
                this.slideThumb(index)
                this.changeHandler(index)
            } catch (error) {
                console.error(error)
            }
        },
        slideThumb(index){
            console.log(index,'slideThumb')
            let thumb_slide=this.$refs.thumb_slide;
            let thumb_scroll_wrap=this.$refs.thumb_scroll_wrap;
            let scroll_width=thumb_scroll_wrap.clientWidth
            let left=index*157-(scroll_width/2)+78
            this.$nextTick(()=>{
                thumb_slide&&thumb_slide.scrollTo({x:left},150)
            })

        },
        changeHandler(index){
            this.index=index;
            //快速划过不预加载图片
            clearTimeout(this.sliderTimer);
            this.sliderTimer=setTimeout(()=>{
                this.preLoad(index)
            },300)
            let file=this.galleryList[index]
            this.initVideoPageIfNeed(file);
            this.initFileReaderIfNeed(file);
            initDcm(file)
        },
        preLoad(index){
            var that=this;
            var imageObj=cloneDeep(this.galleryList[index]);
            if(this.checkResourceType(imageObj)==='video'||
            this.checkResourceType(imageObj) === 'review_video'
            ){
                let mainVideoSrc='';
                if (imageObj.msg_type==this.systemConfig.msg_type.Video||imageObj.img_type_ex==this.systemConfig.msg_type.Video) {
                    mainVideoSrc=imageObj.url.replace(imageObj.thumb,"")
                }else if(imageObj.msg_type==this.systemConfig.msg_type.Cine||imageObj.img_type_ex==this.systemConfig.msg_type.Cine){
                    if (this.getResourceTempStatus(imageObj.resource_id,'mainVideoSrc')) {
                        //存在mainVideoSrc直接播放该地址
                        mainVideoSrc=this.getResourceTempStatus(imageObj.resource_id,'mainVideoSrc')
                    }else if(imageObj.mainVideoSrc){
                        mainVideoSrc = imageObj.mainVideoSrc
                    }else{
                        mainVideoSrc=imageObj.url.replace('thumbnail.jpg','DeviceVideo.');
                        mainVideoSrc+=imageObj.img_encode_type
                    }
                }
                this.mainVideoSrc = mainVideoSrc
            }
        },
        async initVideoPageIfNeed(file){
            let container=this.$refs.topSwiper
            const {mainVideoSrc,gestrueVideoSrc,mainAudioSrc,isPlayReview} = await initVideoPage({file,container,cid:this.cid})
        },
        prevImage(){
            if (this.index!=0) {
                this.slideTop(this.index-1)
            }
        },
        nextImage(){
            if (this.index<this.galleryList.length) {
                this.slideTop(this.index+1)
            }
        },
        mousedownThumb(event,index){
            this.mousedownThumpPoint={
                x:event.x,
                y:event.y
            }
        },
        mouseupThumb(event,index){
            let offsetX=this.mousedownThumpPoint.x-event.x
            let offsetY=this.mousedownThumpPoint.y-event.y
            if (Math.abs(offsetX)<20&&Math.abs(offsetY)<20) {
                if (index==this.index) {
                    return
                }
                this.slideTop(index);
            }
        },
        lastPage(){
            let thumb_slide=this.$refs.thumb_slide;
            let left=thumb_slide.getPosition().scrollLeft;
            let thumb_scroll_wrap=this.$refs.thumb_scroll_wrap;
            let scroll_width=thumb_scroll_wrap.clientWidth
            left-=scroll_width
            if(left === 0){
                left = -1
            }
            this.$nextTick(()=>{
                thumb_slide&&thumb_slide.scrollTo({x:left},150)
            })
        },
        nextPage(){
            let thumb_slide=this.$refs.thumb_slide;
            let left=thumb_slide.getPosition().scrollLeft;
            let thumb_scroll_wrap=this.$refs.thumb_scroll_wrap;
            let scroll_width=thumb_scroll_wrap.clientWidth
            left+=scroll_width
            if(left === 0){
                left = -1
            }
            this.$nextTick(()=>{
                thumb_slide&&thumb_slide.scrollTo({x:left},150)
            })
        },
        initGalleryPage(){
            this.cid=this.$route.query.cid||this.$route.params.cid;
            this.index=0;
            this.openFile=this.galleryList[0];
            this.isShowFileReader = false;
            this.$nextTick(()=>{
                this.positionIndex()
            })
            this.isShowGallery=true
        },
        parsePdf(file) {
            let obj = {
                file_type: "pdf",
            };
            let arr = file.url.split("/");
            arr.pop();
            obj.file_name = arr.pop();
            obj.url = file.url.replace("thumbnail.jpg", "SingleFrame.pdf");
            return obj;
        },
        initFileReaderIfNeed(file) {
            this.isShowFileReader = false;
            this.$nextTick(()=>{
                if (file.msg_type == this.systemConfig.msg_type.File) {
                    this.isShowFileReader = true;
                    this.readingFile = file;
                    // this.$nextTick(() => {
                    //     this.$refs.fileReader.initPage();
                    // });
                } else if (
                    file.msg_type == this.systemConfig.msg_type.Frame&&file.img_encode_type &&
                file.img_encode_type.toUpperCase() == "PDF"
                ) {
                    this.isShowFileReader = true;
                    const pdfFile = this.parsePdf(file);
                    this.readingFile = pdfFile;
                    // this.$nextTick(() => {
                    //     this.$refs.fileReader.initPage();
                    // });
                }
            })

        },
        positionIndex(){
            if (!this.openFile.file_id&&!this.openFile.ai_analyze) {
                //未设置打开文件时不定位
                if(!this.openFile.protocol_view_guid&&!this.openFile.protocol_view_name){
                    return ;
                }
            }
            for(let i =0; i<this.galleryList.length;i++){
                let file_id=this.openFile.file_id||(this.openFile.ai_analyze&&this.openFile.ai_analyze.messages[0].file_id)||this.openFile.resource_id
                let resource_id=this.openFile.resource_id||(this.openFile.ai_analyze&&this.openFile.ai_analyze.messages[0].resource_id)||this.openFile.resource_id
                if (!resource_id) {
                    if (file_id==this.galleryList[i].file_id) {
                        this.index=i;
                        break;
                    }
                }else{
                    if (resource_id==this.galleryList[i].resource_id) {
                        this.index=i;
                        break;
                    }
                }
            }
            console.log('positionIndex',this.index);
            if (this.index>-1) {
                this.slideTop(this.index);
            }
        },
        showRealTimeVideoByMulticenterGallery(){
            if(this.isShowGallery){
                if (this.checkResourceType(this.currentFile) === 'video'||
                this.checkResourceType(this.currentFile) === 'review_video'
                ) {
                    window.CWorkstationCommunicationMng.showRealTimeVideo({
                        type:4
                    })
                }
            }
        },
        hideRealTimeVideoByMulticenterGallery(){
            //在画廊内调用时隐藏播放器显示
            if (this.isShowGallery) {
                if (this.checkResourceType(this.currentFile) === 'video'||
                this.checkResourceType(this.currentFile) === 'review_video'
                ) {
                    window.CWorkstationCommunicationMng.hideRealTimeVideo({})
                }
            }
        }
    },
}
</script>
<style lang="scss">
.multicenter_gallery{
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
    .top{
        flex: 1;
        position: relative;
        padding: 10px;
        min-height: 0;
        .mui-slider-item{
            font-size: 14px;
            position: relative;
            display: inline-block;
            width: calc(100% - 17px);
            height: 100%;
            vertical-align: top;
            white-space: normal;
            user-select:none;
        }
        .view_not_uploaded{
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 24px;
            color: #f9f90f;
        }
        .preview,.main_video{
            max-width:100%;
            max-height:100%;
            position: absolute;
            top: 0;
            bottom:0;
            left: 0;
            right:0;
            margin:auto;
        }
        .iconright1{
            font-size: 40px;
            left: 20px;
            top: 50%;
            position: absolute;
            color: #aaa;
            cursor: pointer;
            z-index: 3;
            display: none;
        }
        .iconright2{
            font-size: 40px;
            right: 20px;
            top: 50%;
            position: absolute;
            color: #aaa;
            cursor: pointer;
            z-index: 3;
            display: none;
        }
        &:hover .iconfont{
            display: block;
        }
    }
    .thumb_loading{
        position:absolute;
        top:0;
        width:calc(100% - 40px);
        height:100%;
        background-color:#212121;
        z-index:99;
    }
    .thumb_wrap{
        height: 120px;
        padding: 0px 20px 4px;
        position: relative;
        .thumb_scroll_wrap{
            width:100%;
            height:100%;
            user-select:none;
            .__rail-is-horizontal{
                height:0 !important;
            }
            .thumb_slide{
                position:relative;
                width:100%;
                height:100%;
                z-index: 1;
                .thumb_item{
                    float:left;
                    width: 156px;
                    height: 116px;
                    background: #000;
                    position: relative;
                    margin-right: 1px;
                    cursor:pointer;
                    &.current_thumb{
                        border:3px solid #599592;
                    }
                    .preview{
                        max-width:100%;
                        max-height:100%;
                        position: absolute;
                        top: 0;
                        bottom:0;
                        left: 0;
                        right:0;
                        margin:auto;
                    }
                    .iconvideo_fill_light,.iconpicture{
                        position: absolute;
                        bottom: 0;
                        color: #fff;
                        font-size: 24px;
                        line-height: 1;
                        z-index: 2;
                    }
                    .view_name{
                        color: #fff;
                        position: absolute;
                        top: 6px;
                        z-index: 9;
                        left: 2px;
                        transform: none;
                        font-size: 14px;
                        white-space: normal;
                        text-align: left;
                    }
                }
                .__bar-is-horizontal,.__bar-is-vertical{
                    display:none;
                }
            }
        }
        .last_page{
            transform: rotate(90deg) scaleX(1.5);
            position: absolute;
            left: 0px;
            top: 50px;
            color: #fff;
            font-size: 18px;
            line-height: 16px;
            cursor: pointer;
        }
        .next_page{
            transform: rotate(270deg) scaleX(1.5);
            position: absolute;
            right: 0px;
            top: 50px;
            color: #fff;
            font-size: 18px;
            line-height: 16px;
            cursor: pointer;
        }
    }
}
</style>

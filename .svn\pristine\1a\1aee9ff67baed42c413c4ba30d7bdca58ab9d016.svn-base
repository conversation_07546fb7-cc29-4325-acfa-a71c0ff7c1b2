<template>
    <mr-notify-bar
        v-if="announcementContent && !closedNotifyBar"
        :text="announcementContent"
        :closeable="true"
        icon="el-icon-message"
        @close="close"
    />
</template>
<script>
import base from "../lib/base";
import MrNotifyBar from "../MRComponents/mrNotifyBar.vue";
import service from '../service/service';

export default {
    name: "notifyBar",
    mixins: [base],
    components: {
        MrNotifyBar
    },
    computed: {
        announcementContent() {
            return this.globalParams.announcementContent;
        },
        closedNotifyBar() {
            return this.globalParams.closedNotifyBar;
        }
    },
    created() {
        this.getConfigAnnouncement();
    },
    methods: {
        async getConfigAnnouncement() {
            if (!this.announcementContent) {
                const { data } = await service.getConfigAnnouncement();
                if (data.error_code === 0) {
                    if (data.data.switch) {
                        this.$store.commit('globalParams/updateGlobalParams', {
                            announcementContent: data.data.content
                        });
                    }
                }
            }
        },
        close() {
            this.$store.commit('globalParams/updateGlobalParams', {
                closedNotifyBar: true
            });
        }
    }
};
</script>
<style lang="scss"></style>

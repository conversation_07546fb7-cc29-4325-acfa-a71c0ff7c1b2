<template>
    <div class="multi-select-editor">
        <el-select
            v-model="localValue"
            multiple
            :placeholder="placeholder"
            style="width: 100%"
            @change="handleChange"
        >
            <el-option
                v-for="option in options"
                :key="option.label"
                :label="option.label"
                :value="option.label"
            />
        </el-select>
    </div>
</template>

<script>
export default {
    name: "MultiSelectField",
    props: {
        value: {
            type: Array,
            default: () => []
        },
        placeholder: {
            type: String,
            default: ""
        },
        options: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            localValue: Array.isArray(this.value) ? this.value : []
        };
    },
    watch: {
        value: {
            handler(newVal) {
                this.localValue = Array.isArray(newVal) ? newVal : [];
            },
            immediate: true
        }
    },
    methods: {
        handleChange() {
            // 对于多选，确保答案数组是有序的
            const sortedValue = [...this.localValue].sort();
            this.$emit("change", sortedValue);
        }
    }
}
</script>

<style lang="scss" scoped>
.multi-select-editor {
    width: 100%;

    ::v-deep .el-select {
        width: 100% !important;

        .el-input__inner {
            font-size: 16px;
        }
    }
}
</style>

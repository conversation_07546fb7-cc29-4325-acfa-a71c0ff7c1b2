<template>
    <div v-show="isShowGallery" class="gallery_page second_level_page">
        <div class="weixin-tip" id="weixinTip">
            <div>
                <h3 class="cover_tip_text">{{lang.wechat_unsupported_tip}}</h3>
            </div>
        </div>
        <canvas style="display: none" id="ai_canvas"></canvas>
        <div class="gallery">
            <span class="close_gallery" @click="closeGallery">
                <svg class="svg_icon_back">
                    <use
                        xmlns:xlink="http://www.w3.org/1999/xlink"
                        xlink:href="#icon-back"
                    ></use>
                </svg>
            </span>
            <p class="index_tip">{{ index + 1 }}/{{ galleryList.length }}</p>
            <div class="mui-slider" id="my_slider" :key="sliderKey">
                <div class="mui-slider-group" ref="sliderGroup">
                    <div
                        v-for="(file, index) of galleryList"
                        class="mui-slider-item"
                        :class="{ 'mui-zoom-wrapper': checkResourceType(file) === 'image' }"
                        :key="file.resource_id + file.file_id"
                    >
                        <template v-if="checkResourceType(file) === 'image'">
                            <div class="mui-zoom-scroller" @doubletap="preventTap">
                                <real-image :fileItem="file" :ref="`realImage_${index}`"></real-image>
                                <template v-if="file.img_encode_type">
                                    <img
                                        v-if="file.img_encode_type.toUpperCase() == systemConfig.file_type.DCM"
                                        src="static/resource_activity/images/poster_video.png"
                                        @tap.stop="initDCMPlayer($event, index)"
                                        class="play_video_btn"
                                    />
                                    <img
                                        v-if="file.img_encode_type.toUpperCase() == systemConfig.file_type.PDF"
                                        src="static/resource_activity/images/poster_video.png"
                                        @tap.stop="showPdf(file)"
                                        class="play_video_btn"
                                    />
                                </template>
                            </div>
                        </template>
                        <template v-else-if="checkResourceType(file) === 'video'">
                            <real-image :fileItem="file" :ref="`realImage_${index}`"></real-image>
                            <img
                                src="static/resource_activity/images/poster_video.png"
                                @tap.stop="initVideoPage($event, index)"
                                class="play_video_btn"
                            />
                        </template>
                        <template v-else-if="file.msg_type == systemConfig.msg_type.File">
                            <img @click="showPdf(file)" :src="file.url_local" class="file" :ref="'image_' + index" />
                        </template>
                        <template v-else-if="file.msg_type == systemConfig.msg_type.RealTimeVideoReview">
                            <div class="file" @tap.stop="initVideoPage($event, index)" :ref="'image_' + index">
                                <p class="review_text">{{ lang.live_playback }}</p>
                                <p>{{ formatTime(file.start_ts) }}<br />{{ formatTime(file.stop_ts) }}</p>
                                <i class="icon iconfont icon-videofill"></i>
                            </div>
                        </template>
                        <template v-else-if="file.msg_type == systemConfig.msg_type.VIDEO_CLIP">
                            <div class="file" @tap.stop="initVideoPage($event, index)" :ref="'image_' + index">
                                <p class="review_text">{{ lang.video_clips }}</p>
                                <p>{{ formatTime(file.start_ts) }}</p>
                                <i class="icon iconfont icon-videofill"></i>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        </div>
        <div class="video_page" v-if="isShowVideo">
            <i class="icon iconfont icon-back close_video_page" @click.stop="closeVideo"></i>
            <video
                class="main_video"
                id="mainVideo"
                controls
                ref="mainVideo"
                :src="mainVideoSrc"
                @error="videoError"
                @timeupdate="timeupdate"

            >
                {{ lang.unsupport_video_text }}
            </video>
            <video
                class="gesture_video"
                id="gestureVideo"
                v-show="gestureVideoSrc != ''"
                controls
                ref="gestureVideo"
                :src="gestureVideoSrc"
            >
                {{ lang.unsupport_video_text }}
            </video>
            <audio :src="mainAudioSrc" ref="mainAudio"></audio>
        </div>
        <dcm-player v-if="isShowDCMPlayer" ref="dcm_player" :isShow.sync="isShowDCMPlayer"></dcm-player>
        <div v-if="isShowPdfPage" class="pdf_page fourth_level_page">
            <mrHeader @click-left="closePdf">
                <template #title>
                    {{ galleryList[index].file_name }}
                </template>
            </mrHeader>
            <div class="pdf_container" @click="toggleShowOperator">
                <template v-if="unsupportedPdf">
                    <p class="tip">{{ lang.unsurpport_pdf }}</p>
                </template>
                <template v-else>
                    <canvas
                        :style="{ width: scale + '%' }"
                        v-for="(pageNum, index) in pageSum"
                        :id="'the_canvas_' + pageNum"
                        :key="index"
                    ></canvas>
                    <p v-if="loadingPdf" class="tip">loading...</p>
                </template>
            </div>
            <div class="operator" v-show="isShowOperator">
                <i @click.stop="narrow" :class="{ disable: scale == 100 }" class="iconfont icon-minus"></i>
                <i @click.stop="enlarge" :class="{ disable: scale == 200 }" class="iconfont icon-plus1"></i>
            </div>
        </div>
    </div>
</template>
<script>
import base from "../lib/base";
import Tool from "@/common/tool.js";
import { Toast } from "vant";
import { getResourceTempStatus, checkResourceType } from "../lib/common_base";
import RealImage from "../components/realImage.vue";
import dcmPlayer from "./DCMPlayer.vue";

export default {
    mixins: [base],
    name: "GalleryComponent",
    components: { RealImage, dcmPlayer },
    data() {
        return {
            getResourceTempStatus,
            checkResourceType,
            isShowGallery: false,
            index: 0,
            isShowVideo: false,
            mainVideoSrc: "",
            gestureVideoSrc: "",
            preventTapTimeout: null,
            mainAudioSrc: "",
            isScreenHorizontal: false, //屏幕水平放置
            galleryLength: 0,
            sliderKey: 0,
            isShowPdfPage: false,
            pageSum: 0,
            isShowOperator: true,
            unsupportedPdf: false,
            loadingPdf: false,
            scale: 100,
            sliderTimer: 0,
            isInitGallery: false,
            isShowDCMPlayer: false,
            galleryList: [],
        };
    },
    computed: {
        globalParams() {
            return this.$store.state.globalParams;
        },
        isHorizontalMode() {
            //横屏
            return this.isScreenHorizontal;
        },
        resource_id() {
            return this.galleryList[this.index] && this.galleryList[this.index].resource_id;
        },
        loadMore() {
            return this.gallery.loadMore || false;
        },
    },
    watch: {},
    mounted() {
        this.mediaQuery = window.matchMedia("(orientation: portrait)"); // 横屏方向
        this.mediaQuery.addListener(this.handleOrientationChange);
        this.$nextTick(() => {
            this.$root.eventBus.$off("initGallery").$on("initGallery", this.initGallery);
            this.$root.eventBus.$off("destroyGallery").$on("destroyGallery", this.destroyGallery);
            this.$root.eventBus.$off("leaveGalleryRouter").$on("leaveGalleryRouter", this.leaveGalleryRouter);
            this.$root.eventBus.$off("enterGalleryRouter").$on("enterGalleryRouter", this.enterGalleryRouter);
        });
    },
    created() {
    },
    beforeDestroy() {
        this.mediaQuery.removeListener(this.handleOrientationChange);
    },
    methods: {
        initGallery() {
            const cacheData = window.vm.$store.state.gallery.cacheData[window.vm.$route.path];
            if (cacheData && cacheData.list.length > 0 && this.index === this.$store.state.gallery.index) {
                window.vm.$store.commit("gallery/setGallery", cacheData);
                this.galleryList = cacheData.list;
                this.index = cacheData.index;
            } else {
                this.galleryList = this.$store.state.gallery.list;
                this.index = this.$store.state.gallery.index;
            }
            this.isShowVideo = false;
            this.mainVideoSrc = "";
            this.gestureVideoSrc = "";
            this.preventTapTimeout = null;
            this.mainAudioSrc = "";
            this.isScreenHorizontal = false;
            this.galleryLength = 0;
            this.isShowGallery = true;
            this.isInitGallery = true;
            this.sliderKey = new Date().valueOf();
            var that = this;
            this.$nextTick(async () => {
                if (that.index == 0) {
                    //点击第一张图时触发change
                    that.changeHandler(0);
                }

                document.getElementById("my_slider").addEventListener("slide", that.slideHandler);
                var gallery = window.mui(".mui-slider");
                var sliderObj = gallery.slider();

                window.mui(".mui-zoom-wrapper").zoom();
                sliderObj.gotoItem(that.index, 0);
                this.shouldAutoPlay();
                // this.index=this.index > this.galleryLength? 0 : this.index
            });
        },
        closeGallery() {
            const that = this;
            new Promise((resolve) => {
                that.back();
                resolve(true);
            }).then(() => {
                that.destroyGallery();
            });
        },
        destroyGallery() {
            this.isShowGallery = false;
            this.isInitGallery = false;
            this.isScreenHorizontal = false;
            window.removeEventListener("deviceorientation", this.handleOrientation);
            document.getElementById("my_slider").removeEventListener("slide", this.slideHandler);
        },
        isWeChat() {
            const ua = navigator.userAgent.toLowerCase();
            return !!ua.match(/MicroMessenger/gi)
        },
        showWechatErrTip() {
            const winHeight = window.innerHeight;
            const tip = document.getElementById("weixinTip");
            console.log('视频播放出现错误')
            if (this.isWeChat()) {
                tip.style.height = winHeight;
                tip.style.display = 'block';
            }
        },
        slideHandler(event) {
            console.log("slideHandler");
            let index = event.detail.slideNumber;
            this.$store.commit("gallery/setGallery", {
                index: index,
            });
            this.changeHandler(index);
        },
        changeHandler(index) {
            this.index = index;
            console.log(this.index, "changeHandler");

            let cid = this.galleryList[this.index].group_id;
            // this.setResourceReviewQueue(cid,this.galleryList[this.index].resource_id)
            if (cid && !this.conversationList[cid]) {
                //展示当前图片是打开会话
                this.openConversation(cid, 4);
            } else {
                // updateResoureceReview(cid);
            }
            //快速划过不预加载图片
            clearTimeout(this.sliderTimer);
            this.sliderTimer = setTimeout(() => {
                this.preLoad(index);
                if (index - 1 >= 0) {
                    this.preLoad(index - 1);
                }
                if (index + 1 < this.galleryList.length) {
                    this.preLoad(index + 1);
                }
            }, 300);
        },
        preLoad(index) {
            let target = this.$refs[`realImage_${index}`];
            target && target[0] && target[0].preload();
        },
        shouldAutoPlay() {
            let imageObj = this.galleryList[this.index];
            if (!imageObj) {
                return;
            }
            if (imageObj.img_encode_type&&(imageObj.img_encode_type.toUpperCase() == this.systemConfig.file_type.DCM)) {
                this.initDCMPlayer({}, this.index);
            }else{
                if (
                    this.checkResourceType(imageObj) === "video" ||
                this.checkResourceType(imageObj) === "review_video"
                ) {
                    this.initVideoPage({}, this.index);
                }
            }
        },
        initVideoPage(e, index) {
            e.cancelBubble = true;
            var that = this;
            var imageObj = Object.assign({}, this.galleryList[index]);
            console.log(imageObj, "initVideoPage");
            if (
                imageObj.msg_type == this.systemConfig.msg_type.Video ||
                imageObj.img_type_ex == this.systemConfig.msg_type.Video
            ) {
                this.mainVideoSrc = imageObj.url.replace(imageObj.thumb, "");
            } else if (
                imageObj.msg_type == this.systemConfig.msg_type.Cine ||
                imageObj.img_type_ex == this.systemConfig.msg_type.Cine
            ) {
                let url = "";
                if (imageObj.isExamFile || imageObj.isTransferFile || imageObj.isLocalPatientFile) {
                    //检查浏览点开会诊视频
                    url = imageObj.url2;
                    if (!imageObj.loaded) {
                        return;
                        // Toast(this.lang.video_not_ready)
                    }
                } else {
                    if (imageObj.mainVideoSrc) {
                        //存在mainVideoSrc直接播放该地址
                        url = imageObj.mainVideoSrc;
                    } else {
                        url = imageObj.url.replace("thumbnail.jpg", "DeviceVideo.");
                        url += imageObj.img_encode_type;
                        imageObj.gesture_url =
                            imageObj.url.replace("thumbnail.jpg", "GestureVideo.") + imageObj.img_encode_type;
                    }
                }
                this.mainVideoSrc = url;
            } else if (this.checkResourceType(imageObj) === "review_video") {
                this.mainVideoSrc = imageObj.ultrasound_url;
                console.log("*** mainVideoSrc: ", imageObj.ultrasound_url);
                console.log("*** gestureVideoSrc: ", imageObj.gesture_url);
            }
            if (imageObj.img_has_gesture_video) {
                this.gestureVideoSrc = imageObj.gesture_url;
            } else {
                this.gestureVideoSrc = "";
            }
            this.mainAudioSrc = imageObj.voice_url || "";
            if (this.isApp) {
                var json = {
                    conversation_id: imageObj.cid || 0,
                    consultation_id: 0,
                    img_id: imageObj.img_id,
                    type: imageObj.msg_type,
                    subject: imageObj.des,
                    spriteImageList: imageObj.spriteImageList || [],
                    resourceId: imageObj.resource_id,
                };
                if (this.mainVideoSrc) {
                    json.ultrasound_video = Tool.percentURLEncode(this.mainVideoSrc);
                }
                if (this.gestureVideoSrc) {
                    json.gesture_video = Tool.percentURLEncode(this.gestureVideoSrc);
                }
                if (this.mainAudioSrc) {
                    json.voice_url = Tool.percentURLEncode(this.mainAudioSrc);
                }
                if (this.checkResourceType(imageObj) === "review_video") {
                    json.type = this.systemConfig.msg_type.Cine;
                }
                json.video_with_audio = imageObj.video_with_audio ? 1 : 0;
                if (
                    imageObj.msg_type == this.systemConfig.msg_type.Video ||
                    imageObj.img_type_ex == this.systemConfig.msg_type.Video
                ) {
                    if (this.mainVideoSrc) {
                        if ("http" == imageObj.url.substr(0, 4)) {
                            json.ultrasound_video = Tool.percentURLEncode(this.mainVideoSrc);
                        } else {
                            let socketServer =
                                this.systemConfig.server_type.protocol +
                                this.systemConfig.server_type.host +
                                this.systemConfig.server_type.port;
                            json.ultrasound_video = socketServer + "/" + Tool.percentURLEncode(this.mainVideoSrc);
                        }
                        json.video_with_audio = 1;
                    }
                }
                if (
                    imageObj.msg_type == this.systemConfig.msg_type.Cine ||
                    imageObj.img_type_ex == this.systemConfig.msg_type.Cine
                ) {
                    if (this.mainVideoSrc && imageObj.imported_from) {
                        json.video_with_audio = 1;
                        imageObj.from_repository = true;
                    }
                }

                json.from_repository = imageObj.from_repository;
            } else {
                this.isShowVideo = true;
                this.$nextTick(function () {
                    that.$refs.mainVideo.play();
                    that.$refs.gestureVideo.play();
                    that.$refs.mainAudio.play();
                });
                console.log("play2");
            }
        },
        closeVideo() {
            this.$refs.mainVideo.pause();
            this.$refs.gestureVideo.pause();
            this.$refs.mainAudio.pause();
            this.isShowVideo = false;
            // this.back();
        },
        timeupdate(e) {
            console.log("timeupdate", e);
        },
        preventTap() {
            clearTimeout(this.preventTapTimeout);
        },
        showPdf(file) {
            let pdfFile = {};
            if (file.msg_type == this.systemConfig.msg_type.File) {
                pdfFile = file;
            } else if (
                file.msg_type == this.systemConfig.msg_type.Frame &&
                file.img_encode_type.toUpperCase() === "PDF"
            ) {
                let arr = file.url.split("/");
                arr.pop();
                pdfFile.file_type = "pdf";
                pdfFile.file_name = arr.pop();
                pdfFile.url = file.url.replace("thumbnail.jpg", "SingleFrame.pdf");
            } else {
                return;
            }
            this.isShowPdfPage = true;
            this.initPDF(pdfFile);
        },
        initPDF(pdfFile) {
            var that = this;
            if (window.mui.os.ios && /^11/.test(window.mui.os.version)) {
                that.unsupportedPdf = true;
                return;
            } else {
                that.loadingPdf = true;
                let file = pdfFile;
                window.pdfjsLib.GlobalWorkerOptions.workerSrc = "static/resource_activity/pdf.worker.js";
                var loadingTask = window.pdfjsLib.getDocument(file.url);
                loadingTask.promise.then(
                    function (pdf) {
                        that.pageSum = pdf.numPages;
                        that.$nextTick(() => {
                            for (let i = 1; i <= that.pageSum; i++) {
                                that.loadingPdf = false;
                                pdf.getPage(i).then(function (page) {
                                    try {
                                        var scale = 1;
                                        var viewport = page.getViewport(scale);
                                        var canvas = document.getElementById("the_canvas_" + i);
                                        var context = canvas.getContext("2d");
                                        canvas.height = viewport.height;
                                        canvas.width = viewport.width;
                                        var renderContext = {
                                            canvasContext: context,
                                            viewport: viewport,
                                        };
                                        var renderTask = page.render(renderContext);
                                        renderTask.then(function () {
                                            console.log("Page rendered");
                                        });
                                    } catch (e) {
                                        console.log("render pdf but page change !");
                                    }
                                });
                            }
                        });
                    },
                    function (reason) {
                        Toast(that.lang.load_pdf_error + reason);
                        that.unsupportedPdf = true;
                        that.loadingPdf = false;
                    }
                );
            }
        },
        closePdf() {
            this.isShowPdfPage = false;
            this.pageSum = 0;
        },
        handleOrientationChange(event) {
            console.log("handleOrientation", event.matches);
            var that = this;
            if (!event.matches) {
                //横屏
                console.log("isScreenHorizontal", true);
                that.isScreenHorizontal = true;
            } else {
                //竖屏
                console.log("isScreenHorizontal", false);
                that.isScreenHorizontal = false;
            }

            //resize时重置容器宽度，兼容旋转屏幕时定位异常
            var gallery = window.mui(".mui-slider").slider();
            var paddingLeft = parseFloat(window.mui.getStyles(gallery.wrapper, "padding-left")) || 0;
            var paddingRight = parseFloat(window.mui.getStyles(gallery.wrapper, "padding-right")) || 0;
            var clientWidth = gallery.wrapper.clientWidth;
            gallery.scrollerWidth = gallery.scroller.offsetWidth;
            gallery.wrapperWidth = clientWidth - paddingLeft - paddingRight;
            gallery.currentPage.x = gallery.x = -(gallery.wrapperWidth * gallery.slideNumber);
            gallery.gotoItem(this.index, 0);
        },
        toggleShowOperator() {
            this.isShowOperator = !this.isShowOperator;
        },
        enlarge() {
            if (this.scale < 200) {
                this.scale += 20;
            }
        },
        narrow() {
            if (this.scale > 100) {
                this.scale -= 20;
            }
        },
        initDCMPlayer(e, index) {
            e.cancelBubble = true;
            let file = this.galleryList[index];
            if (!file.dcm_url) {
                //一键转发、检查浏览文件有dcm_url字段，会话内的文件需要拼接地址
                file.dcm_url = file.url.replace("thumbnail.jpg", `SingleFrame.${file.img_encode_type}`);
            }
            //初始化DCM文件播放器
            this.isShowDCMPlayer = true;
            this.$nextTick(() => {
                this.$refs.dcm_player.initDCMPlayer(file);
            });
        },
        errorDCM(img) {
            //dcm文件在一键转发打开大图时未生成大图，延时再加载一次
            var imageObj = Object.assign({}, img);
            if (
                imageObj.isTransferFile &&
                imageObj.msg_type == this.systemConfig.msg_type.Frame &&
                imageObj.img_encode_type.toUpperCase() == this.systemConfig.file_type.DCM
            ) {
                this.$store.commit("gallery/updateLocalDcm", {
                    imgObj: imageObj,
                    realUrl: imageObj.url_local,
                });
                setTimeout(() => {
                    this.$store.commit("gallery/updateLocalDcm", {
                        imgObj: imageObj,
                        realUrl: imageObj.tempRealUrl,
                    });
                }, 2000);
            }
        },
        videoError(err) {
            this.closeVideo();
            Toast(this.lang.video_cannot_played);
            this.showWechatErrTip()
        },
        enterGalleryRouter({ to, from, next }) {
            this.$store.commit("gallery/addPathCache", from.path);
            console.log({ to, from, next });
        },
        leaveGalleryRouter({ to, from, next }) {
            if (this.isScreenHorizontal) {
                window.CWorkstationCommunicationMng.changeOrientation();
                this.isScreenHorizontal = false
                next(false);
            } else {
                // console.log(to.path,window.vm.$store.state.gallery.cacheData,window.vm.$route.path)

                if (this.$store.state.gallery.pathCache.includes(to.path)) {
                    this.$store.commit("gallery/removePathCache", to.path);
                    this.$store.commit("gallery/clearGalleryData", window.vm.$route.path);
                } else if (to.path === "/index") {
                    this.$store.commit("gallery/removePathCache");
                    this.$store.commit("gallery/clearGalleryData");
                } else {
                    this.$store.commit("gallery/cacheGalleryData");
                }
                next(true);
            }
        },
    },
};
</script>
<style lang="scss">
.weixin-tip h3 {
    max-width: 100%;
    height: auto;
    text-align: center;
    color: #ffff4f;
}
.weixin-tip {
    display: none;
    position: fixed;
    left: 0;
    top: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    filter: alpha(opacity=80);
    height: 100%;
    width: 100%;
    z-index: 100;
}
.weixin-tip div {
    text-align: center;
    margin-top: 80%;
    padding: 0 5%;
}
.svg_icon_back {
    width: 1.5rem;
    height: 1.9rem;
    fill: #fff;
    color: #fff;
    position: absolute;
    top: 0.5rem;
    left: 0.8rem;
}

.gallery_page {
    color: #fff;
    background-color: #222;
    z-index: 900;
    bottom: 0;
    display: flex;
    flex-direction: column;
    .gallery {
        background-color: #222;
        position: relative;
        width: 100%;
        z-index: 1;
        flex: 3;
        min-height: 1px;
        .index_tip {
            position: absolute;
            right: 0.2rem;
            z-index: 2;
        }
        .close_gallery {
            position: absolute;
            top: 0rem;
            left: 0rem;
            font-size: 1.3rem;
            z-index: 2;
            width: 2rem;
            text-align: left;
            height: 3rem;

            .svg_icon_back {
                width: 1.5rem;
                height: 1.9rem;
                fill: #fff;
                color: #fff;
                position: absolute;
                top: 0.5rem;
                left: 0.8rem;
            }
        }
        .video_thumb {
            max-width: 100%;
        }
        .review_default_img {
            width: 100%;
        }
        @media only screen and (min-width: 480px) {
            .review_default_img {
                width: 60%;
            }
        }
        .play_video_btn {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
        .mui-slider {
            position: absolute;
            height: 100%;
            .mui-slider-group {
                height: 100%;
                .mui-zoom-scroller {
                    height: 100%;
                }
                .file {
                    position: absolute;
                    top: 0;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    margin: auto;
                    max-width: 100%;
                    max-height: 100%;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    font-size: 1.2rem;
                    .review_text {
                        color: yellow;
                    }
                    .icon-videofill {
                        font-size: 3rem;
                        color: #00c59d;
                    }
                }
                .van-loading{
                    position: absolute;
                    top: calc(50% - 1rem);
                    left: calc(50% - 1rem);
                    z-index: 99;
                }
            }
        }

        .btns {
            bottom: 0rem;
            right: 0rem;
            width: 100%;
            position: absolute;
            z-index: 4;
            overflow: auto;
            &.is_horizontal {
                .download_btn,
                .transfer_btn {
                    width: 1.2rem;
                    height: 1.2rem;
                    line-height: 1.2rem;
                    margin-bottom: 0.3rem;
                    margin-right: 0.3rem;
                    i {
                        font-size: 0.8rem;
                    }
                }
            }
            .download_btn,
            .transfer_btn {
                width: 1.6rem;
                height: 1.6rem;
                border-radius: 50%;
                background-color: rgba(0, 0, 0, 0.6);
                text-align: center;
                line-height: 1.6rem;
                margin-bottom: 0.6rem;
                margin-right: 0.4rem;
                float: right;
                i {
                    font-size: 1rem;
                }
                .active {
                    color: #00c49e;
                }
                .like {
                    color: red;
                }
            }
        }
    }

    .video_page {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        background-color: #333;
        z-index: 20;
        .main_video {
            width: 100%;
            height: auto;
            background-color: #000;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
        }
        .gesture_video {
            width: 8rem;
            height: auto;
            background-color: #000;
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
        }
        .close_video_page {
            position: absolute;
            top: 0.3rem;
            left: 0.3rem;
            font-size: 1.3rem;
            z-index: 1;
        }
    }

    .pdf_page {
        color: #000;
        .pdf_container {
            height: calc(100% - 2.2rem);
            .tip {
                text-align: center;
                margin-top: 3rem;
            }
            canvas {
                margin-bottom: 0.5rem;
            }
        }
        .operator {
            position: absolute;
            z-index: 2;
            top: 3.2rem;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.7);
            border-radius: 1rem;
            color: #fff;
            padding: 0 0.4rem;
            i {
                font-size: 1.4rem;
                margin: 0 0.3rem;
            }
            .reverse {
                transform: rotate(180deg);
                display: inline-block;
            }
            .disable {
                color: #aaa;
            }
        }
    }
    .lightness_modal {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        bottom: 0;
        z-index: 5;
        .lightness_process {
            position: absolute;
            width: 70%;
            left: 15%;
            top: 10%;
            z-index: 2;
            p {
                text-align: center;
                color: #fff;
            }
            .mt-range {
                height: 1rem;
                line-height: 1rem;
                .mt-range-content {
                    margin-right: 1rem;
                    .mt-range-runway {
                        right: -1rem;
                    }
                    .mt-range-thumb {
                        width: 1rem;
                        height: 1rem;
                    }
                }
            }
        }
    }
}
</style>

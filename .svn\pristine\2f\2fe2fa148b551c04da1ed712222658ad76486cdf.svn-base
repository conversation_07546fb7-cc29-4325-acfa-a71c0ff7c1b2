<template>
    <div class="assignment_page" >
      <vue-scroll v-loading="loading">
        <div class="search_bar">
          <el-form
              ref="form"
              :inline="true"
              label-width="70px"
              :model="queryForm"
              @submit.native.prevent
            >
              <el-form-item :label="lang.group_by" prop="conversations">
                  <el-select  class="group_by" v-model="queryForm.conversations" :placeholder="lang.group_by" multiple>
                      <el-option
                      v-for="(conversation,index) of conversationList"
                      :key="index"
                      :label="conversation.subject"
                      :value="conversation.id" />
                  </el-select>
              </el-form-item>
              <el-form-item :label="lang.submission_time">
                <el-date-picker
                  v-model="queryForm.uploadDateRange"
                  type="daterange"
                  unlink-panels
                  :range-separator="lang.date_to"
                  :start-placeholder="lang.start_date"
                  :end-placeholder="lang.end_date"
                  :picker-options="pickerOptions">
                </el-date-picker>
              </el-form-item>
              <el-form-item :label="lang.case_status" prop="case_status">
                  <el-select v-model="queryForm.caseStatus">
                      <el-option
                      v-for="caseItem of caseSelectList"
                      :label="caseItem.value"
                      :value="caseItem.id"
                      :key="caseItem.id"
                      />
                  </el-select>
              </el-form-item>
              <el-form-item>
                <el-button
                  icon="el-icon-search"
                  native-type="submit"
                  type="primary"
                  @click="handleQuery"
                >
                  {{lang.query_btn}}
                </el-button>
              </el-form-item>
              <el-form-item>
                <el-button
                  type="primary"
                  @click="exportExcel"
                >
                  {{lang.thyroid.export_excel}}
                </el-button>
              </el-form-item>
              <el-form-item>
                <el-button
                  type="primary"
                  @click="openExport"
                >
                  {{lang.export_case}}
                </el-button>
              </el-form-item>
            </el-form>
        </div>
        <!-- 病人信息组件 -->
        <patient-info :examList="examList" @toggleExam="toggleExam" @openGallery="openGallery"></patient-info>
        <!--导出表单组件-->
        <el-dialog
          class="export_confirm_dialog"
          :title="lang.tip_title"
          :visible="isShowConfirm"
          :close-on-click-modal="true"
          width="300px"
          height="300px"
          :append-to-body="true"
          :before-close="closeConfirmDialog">
          <div>
              {{lang.thyroid.export_excel_confirm}}
          </div>
          <div class="checkbox_wrap">
              <el-checkbox v-model="neverNotify">{{lang.never_notify}}</el-checkbox>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button @click="isShowConfirm = false">{{lang.cancel_btn}}</el-button>
            <el-button type="primary" @click="doExportExcel">{{lang.confirm_txt}}</el-button>
          </span>
        </el-dialog>
        <!-- 导出病例组件 -->
        <export-dialog
        :examList="examList"
        :queryForm="queryForm"
        :examListTotal="examListTotal"
        :handleCurrentChange="handleCurrentChange"
        :handleSizeChange="handleSizeChange"
        :loadingData="loading"
        ref="exportDialog"></export-dialog>
        <!-- 查看弹出框组件 -->
        <mr-gallery ref="MdGallery" class="mr_gallery">
          <div class="operation_btns" v-if="exam && exam.statusInfo.status == exam_status.submited">
              <el-button type="primary" @click="showReject">{{lang.reject_btn}}</el-button>
              <el-button type="primary" @click="passCase">{{lang.admin_pass}}</el-button>
          </div>
          <el-tabs v-model="activeName" :stretch="true" type="card" v-if="!isSubmited">
            <el-tab-pane v-for="tab of tabList" :key="tab.id" :label="tab.title" :name="tab.name"></el-tab-pane>
          </el-tabs>
          <div v-show="activeName=='case_view'" class="case_view_wrap">
            <!-- 查看病例表单 -->
            <case-info ref="caseInfo"></case-info>
          </div>
          <el-dialog
              :close-on-click-modal="false"
              :visible.sync="isShowReject"
              width="50%"
              :modal="false"
              class="reject_dialog"
              :before-close="closeReject">
              <div class="" style="width:100%">
                  <el-input type="textarea" width="100%" :rows="5" :placeholder="lang.reject_reason_title" v-model="rejectContent" :maxlength="200"></el-input>
                  <el-button class="reject_btn" type="primary" @click="rejectExam">{{lang.confirm_txt}}</el-button>
              </div>
          </el-dialog>
        </mr-gallery>
      </vue-scroll>
      <el-pagination
        background
        :current-page="queryForm.pageNo"
        :layout="layout"
        :page-size="queryForm.pageSize"
        :total="examListTotal"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        style="margin-top:20px"
      />
      <a ref="link" style="display:none;"></a>
    </div>
  </template>
<script>
import base from '../../../lib/base'
import service from '../../../service/multiCenterService.js'
import { transferPatientInfo,htmlEscape } from '../../../lib/common_base'
import MrGallery from '../common/MrGallery'
import CaseInfo from './caseInfo'
import PatientInfo from '../common/patientInfo'
import exportDialog from '../common/exportDialog'
export default {
    name: 'AssignmentPage',
    mixins:[base],
    components: {CaseInfo,MrGallery,PatientInfo,exportDialog},
    data() {
        return {
            queryForm:{
                conversations:[],
                uploadDateRange:[],
                caseStatus:2,
                pageNo:1,
                pageSize:10,
            },
            loading:true,
            exam:null,
            layout: 'total, sizes, prev, pager, next, jumper',
            caseSelectList:[
                {id:-1,value:''}, //全部
                {id:2,value:''}, //已提交
                {id:3,value:''}, //被驳回
                {id:6,value:''}, //已会诊
            ],
            conversationList:[],
            pickerOptions:{
                shortcuts: [{
                    text: window.vm.$store.state.language.recent_two_week,
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 13);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: window.vm.$store.state.language.recent_one_month,
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setMonth(start.getMonth()-1)
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: window.vm.$store.state.language.recent_two_month,
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setMonth(start.getMonth()-2)
                        picker.$emit('pick', [start, end]);
                    }
                }],
                disabledDate:(time)=>{
                    return time.getTime()>new Date().getTime()
                }
            },
            searchInput:'',
            isShowReject:false,
            rejectContent:'',
            caseData:null,
            activeName: 'case_view',
            tabList:[
                {
                    id:1,
                    title:window.vm.$store.state.language.case_view_label,
                    name:'case_view'
                }
            ],
            examObj: {},
            examList:[],
            examListTotal:0,
            isShowConfirm:false,
            neverNotify:false,
        }
    },
    computed: {
        currentMulticenter(){
            return this.$store.state.multicenter.currentMulticenter||{}
        },
        currentConfig(){
            return this.$store.state.multicenter.currentConfig;
        },
        exam_status(){
            return this.currentConfig.exam_status
        },
        isSubmited() {
            return this.exam && this.exam.statusInfo.status == this.exam_status.submited
        }
    },
    created() {
        this.init()
    },
    methods: {
        init(){
            this.caseSelectList.forEach(item=>{
                item.value=this.lang.exam_status[item.id]
            })
            this.fetchData()
            this.getConversations()
        },
        changeCase(){
            console.log(this.caseSelect)
        },
        toggleExam(index,caseData){
            this.caseData = caseData
        },
        handleSizeChange(val) {
            this.queryForm.pageSize = val
            this.fetchData()
        },
        handleCurrentChange(val) {
            this.queryForm.pageNo = val
            this.fetchData()
        },
        handleQuery() {
            console.log(this.queryForm.conversations)
            this.queryForm.pageNo = 1
            this.fetchData()
        },
        fetchData() {
            this.loading=true;
            let start = '',end = ''
            if(this.queryForm.uploadDateRange&&this.queryForm.uploadDateRange.length>1){
                start = this.queryForm.uploadDateRange[0]
                end = this.queryForm.uploadDateRange[1]
                start = this.formatDate(start)
                end = this.formatDate(end)
            }
            console.log(this.queryForm.caseStatus)
            service.getExamList({
                mcID:this.currentMulticenter.id,
                page:this.queryForm.pageNo,
                pageSize:this.queryForm.pageSize,
                condition:{
                    start_time:start,
                    end_time:end,
                    status:this.queryForm.caseStatus,
                    gidList:this.queryForm.conversations
                }
            }).then(async (res)=>{
                this.loading=false;
                if (res.data.error_code==0) {
                    let examList = res.data.data.data
                    for(let item of examList){
                        item.patientInfo = transferPatientInfo(item)
                        this.$set(item,'openState',false)
                        this.$set(item,'initImageList',false)
                    }
                    this.examList = examList;
                    this.examListTotal=res.data.data.total;
                }
            })
        },
        getConversations(){
            service.getConversations({
                mcID:this.currentMulticenter.id
            }).then((res)=>{
                if (res.data.error_code==0) {
                    res.data.data.forEach(item=>{
                        if (item.attendeeList) {
                            item.subject=item.attendeeList[0].userInfo.nickname;
                        }
                    })
                    this.conversationList=res.data.data;
                }

            })
        },
        openGallery(img,index,exam){
            this.exam=exam
            console.log('assignment -->> openGallery',img,index,exam,this.examObj)
            this.resetData()
            this.$refs.MdGallery.openGallery(img,index,exam)
            this.$nextTick(() => {
                this.$refs.caseInfo.initImageURL(this.exam.caseData)
                this.$refs.caseInfo.updateToView(this.exam.caseData, this.exam.topic)
            })
        },
        resetData() {
            if(this.exam.statusInfo.status == this.exam_status.submited){
                // 对于已提交的数据不做处理，可分配
            }else{
                this.activeName = 'case_view'
                this.tabList = [
                    {
                        id:1,
                        title:this.lang.case_view_label,
                        name:'case_view'
                    }
                ]
            }
        },
        modifyStatus(status){
            this.exam.statusInfo.status=status;
        },
        showReject(){
            this.rejectContent=''
            this.isShowReject=true;
            window.CWorkstationCommunicationMng.hideRealTimeVideo({})
        },
        closeReject(){
            this.isShowReject=false;
            this.$root.eventBus.$emit('regainVideo')
        },
        rejectExam(){
            if(this.rejectContent.trim().length==0){
                this.$message.error(this.lang.reject_reason_no_white);
                return;
            }
            if(!this.rejcetClick){
                this.rejectContent = htmlEscape(this.rejectContent)
                this.rejcetClick=true;
                service.refuteCase({
                    exam_id: this.exam.exam_id,
                    reason:this.rejectContent,
                    mcID:this.currentMulticenter.id,
                }).then(res => {
                    if(res.data.error_code==0){
                        this.$message({
                            message:this.lang.reject_success_tip,
                            type:'success'
                        })
                        this.fetchData()
                        this.modifyStatus(this.exam_status.reject)
                        this.closeReject();
                    }
                    this.rejcetClick=false;
                })
            }
        },
        passCase() {
            window.CWorkstationCommunicationMng.hideRealTimeVideo({})
            this.$confirm(this.lang.thyroid.confirm_passing_the_case,this.lang.tip_title,{
                confirmButtonText:this.lang.confirm_txt,
                cancelButtonText:this.lang.cancel_btn,
                type:'warning',
                closeOnClickModal:false,
                modal:false,
                customClass:'confirm-assign-exam'
            }).then(()=>{
                console.log(this.exam.exam_id,this.currentMulticenter.id);
                service.passExam({
                    exam_id: this.exam.exam_id || '',
                    mcID:this.currentMulticenter.id,
                }).then(res => {
                    console.log('[pass case]',res)
                    if(res.data.error_code === 0) {
                        this.$message.success(this.lang.operate_success)
                        this.fetchData()
                        this.modifyStatus(this.exam_status.assigned)
                    }
                    this.$root.eventBus.$emit('regainVideo')
                })
            }).catch(e=>{
                this.$root.eventBus.$emit('regainVideo')
            })
        },
        formatDate(date){
            let year = date.getFullYear()
            let month = '00' + (date.getMonth()+1)
            let day = '00' + date.getDate()
            month = month.substr(-2);
            day = day.substr(-2);
            return `${year}-${month}-${day}`
        },
        openExport(){
            this.$refs.exportDialog.init();
        },
        exportExcel() {
            let neverNotify = window.localStorage.getItem('neverNotifyExport');
            this.neverNotify = neverNotify?true:false;
            if (neverNotify || this.queryForm.caseStatus === 6 ) {
                this.doExportExcel();
            }else{
                this.isShowConfirm = true;
            }
        },
        doExportExcel(){
            this.isShowConfirm = false;
            window.localStorage.setItem('neverNotifyExport', this.neverNotify ? 1 : '');
            let start='',end='';
            if(this.queryForm.uploadDateRange&&this.queryForm.uploadDateRange.length>1){
                start = this.queryForm.uploadDateRange[0]
                end = this.queryForm.uploadDateRange[1]
                start = this.formatDate(start)
                end = this.formatDate(end)
            }
            service.exportExcel({
                mcID:this.currentMulticenter.id,
                condition:{
                    start_time:start,
                    end_time:end,
                    status:this.queryForm.caseStatus,
                    gidList:this.queryForm.conversations
                }
            }).then(async (res)=>{
                let blob = new Blob([res.data],{ type: "application/xlsx" });
                const url = window.URL.createObjectURL(blob); // 设置路径
                const link = this.$refs.link;
                link.href = url;
                link.download = `${this.lang.thyroid.thyroid_multicenter_form}.xlsx`; // 设置文件名
                link.click();
                URL.revokeObjectURL(url); // 释放内存
            }).catch(function (error) {
                console.log(error);
            });
        },
        closeConfirmDialog(){
            this.isShowConfirm = false;
        },
    },
}
</script>
<style lang="scss">
.assignment_page{
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  padding:20px;
  width:100%;
  height:100%;
  display:flex;
  flex-direction:column;
  background: #fff;
  .search_bar{
    .el-range-separator{
      width:9%;
    }
    .el-form-item__label{
      width:auto !important;
    }

  }
  .assign_persons_wrap{
    .checkbox_wrap{
      position:relative;
      width: 50%;
      display: inline-block;
      .alphaInex{
        position: absolute;
        width: 15px;
        height: 15px;
        top: 14px;
        left: 2px;
        color: #000;
        font-size: 14px;
        font-weight:bold;
        background-color: #fff;
        z-index: 1;
        text-align: center;
        line-height: 14px;
        cursor: pointer;
      }
      .el-checkbox{
        margin-right:15px;
        width:calc(50% - 15px);
        .el-checkbox__inner{
          background-color: #fff;
          width:18px;
          height:18px;
        }
      }
      .el-checkbox+.el-checkbox{
        margin-left:0;
      }
    }

  }
  .__bar-is-vertical{
    background-color: rgb(169,191,190) !important;
  }

  padding:15px 20px;
  .search_bar{
    .el-range-separator{
      width:9%;
    }
    .el-form-item__label{
      width:auto !important;
    }
  }
  .mr_gallery{
    color:#000;
    background-color:#a9bfbe;
    .el-tabs__header{
      margin-bottom:0;
    }
    .el-tabs__nav{
      width:100%;
      display:flex;
      background-color:#fff;
      color:#000;
      .el-tabs__item{
        flex:1;
        text-align:center;
        padding:0;
      }
      .el-tabs__item.is-active,.el-tabs__item:hover{
        color: #fff;
        background: #779a98;
      }
      .el-tabs__active-bar{
        background-color:#779a98;
        width:50%;
      }
    }
    .case_view_wrap{
      height:calc(100% - 44px);
    }
  }
}
.export_confirm_dialog.el-dialog__wrapper .el-dialog{
    height: auto;
    .el-dialog__body{
        height: auto;
    }
    .checkbox_wrap{
        margin: 10px 0 0;
    }
}
.reject_dialog .el-dialog{
  height: auto !important;
}
</style>

onmessage=(event)=>{
    MethodFactory[event.data.method](event.data)
}
var MethodFactory={
    compareChatHistory:(data)=>{
        var GroupMsgs=[];
        var GroupUsers=[];
        var GroupMsgTrack={};
        let gatewayMessages=data.gatewayMessages
        var len = gatewayMessages.length -1;
        let localObj={}
        for(let item of data.localMessages){
            localObj[item.gmsg_id]=1;
        }
        for(var i in gatewayMessages){
            let item=gatewayMessages[i]
            if (!localObj[item.gmsg_id]) {
                if(item.group_id){
                    item.tmp_gmsg_id=0;
                    GroupMsgs.push({
                        message:item
                    });
                    GroupUsers.push({
                        id:item.sender_id,
                        login_name:item.nickname,
                        name:item.nickname,
                        sex:1,
                        email:"",
                        nickname:item.nickname,
                        avatar:item.avatar,
                        avatar_local:item.avatar_local
                    });
                }
            }
        }
        if (GroupMsgs.length>0) {
            GroupMsgTrack.group_id =GroupMsgs[0].message.group_id
            GroupMsgTrack.group_msg_scope_end_id =GroupMsgs[0].message.gmsg_id
            GroupMsgTrack.group_msg_scope_start_id = GroupMsgs[GroupMsgs.length-1].message.gmsg_id;
            postMessage({
                GroupMsgs:GroupMsgs,
                GroupUsers:GroupUsers,
                GroupMsgTrack:GroupMsgTrack,
                info:'compareChatHistory'
            });
        }   
        self.close()
    },
    compareFriendList:(data)=>{
        let compareKeys= ['avatar','avatar_local','sex','nickname','email']
        let localObj={}
        let result=[];
        for(let localItem of data.localFriendList){
            localObj[localItem.id]=localItem
        }
        for(let gatewayItem of data.gatewayFriendList){
            let compareObj=localObj[gatewayItem.id]
            if (compareObj) {
                for(let key of compareKeys){
                    if (compareObj[key]!=gatewayItem[key]) {
                        gatewayItem['local_'+key]=compareObj[key]
                        gatewayItem['gateway_'+key]=gatewayItem[key]
                        result.push(gatewayItem);
                        break;
                    }
                }
            }else{
                result.push(gatewayItem);
            }
        }
        postMessage({
            friendList:result,
            info:'compareFriendList'
        });
        self.close()
    },
    compareChatList:(data)=>{
        let compareKeys= ['avatar','avatar_local','subject','is_single_chat','type']
        let localObj={}
        let result=[];
        for(let localItem of data.localChatList){
            localObj[localItem.cid]=localItem
        }
        for(let gatewayItem of data.gatewayChatList){
            let compareObj=localObj[gatewayItem.cid]
            if (compareObj) {
                for(let key of compareKeys){
                    if (compareObj[key]!=gatewayItem[key]) {
                        gatewayItem['local_'+key]=compareObj[key]
                        gatewayItem['gateway_'+key]=gatewayItem[key]
                        result.push(gatewayItem);
                        break;
                    }
                }
            }else{
                result.push(gatewayItem);
            }
        }
        postMessage({
            chatList:result,
            info:'compareChatList'
        });
        self.close()
    },
    compareLastMessages:(data)=>{
        let localObj={}
        let result=[];
        for(let localItem of data.localLastMessages){
            localObj[localItem.cid]=localItem
        }
        for(let gatewayItem of data.gatewayLastMessages){
            let compareObj=localObj[gatewayItem.cid]
            if (compareObj) {
                if (compareObj.message.gmsg_id!=gatewayItem.message.gmsg_id) {
                    gatewayItem['local_gmsg_id']=compareObj.message.gmsg_id
                    gatewayItem['gateway_gmsg_id']=gatewayItem.message.gmsg_id
                    result.push(gatewayItem);
                }
            }else{
                result.push(gatewayItem);
            }
        }
        postMessage({
            lastMessages:result,
            info:'compareLastMessages'
        });
        self.close()
    },
}
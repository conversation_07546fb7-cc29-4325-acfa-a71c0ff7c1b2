<template>
    <transition name="fade">
        <div class="search_page second_level_page">
            <mrHeader v-show="isFocusInpt">
                <template #title>
                    {{lang.search_by_group_member}}
                </template>
            </mrHeader>
            <div class="content" v-show="isFocusInpt">
                <div class="search_btn" @click="memberSearch" >
                    <i class="iconfont svg_icon_search icon-magnifier"></i>
                    <span>{{lang.search}}</span>
                </div>
                <template v-if="userList.length>0" >
                    <div class="user-list" >
                        <div class="user flex column_direction " v-for="(item) in userList" :key="item.userid" @click="openHistoryMessageList(item)">
                            <mr-avatar :url="getLocalAvatar(item)" :origin_url="item.avatar" :showOnlineState="false" :radius="2" :key="item.avatar"></mr-avatar>
                            <div class="name auto">
                                {{item.nickname}}
                            </div>
                        </div>

                    </div>
                </template>
            </div>

            <div class="container" v-show="!isFocusInpt">
                <div class="search_box">
                    <van-search
                        autofocus
                        v-model="keyword"
                        background="#00c59d"
                        show-action
                        clearable
                        :placeholder="lang.search_input_key"
                        ref='mtUserSearch'
                        @focus="switchFocusInpt"
                        @blur="switchFocusInpt"
                        @input="debounceSearch"
                        @cancel="cancelSearch"
                        >
                    </van-search>
                    <div class="van-search-list">
                        <template v-if="userList.length>0">
                            <div class="user-list" >
                                <div class="user flex column_direction " v-for="(item) in userList" :key="item.userid" @click="openHistoryMessageList(item)">
                                    <mr-avatar :url="getLocalAvatar(item)" :origin_url="item.avatar" :showOnlineState="false" :radius="2" :key="item.avatar"></mr-avatar>
                                    <div class="name auto">
                                        {{item.nickname}}
                                    </div>
                                </div>

                            </div>
                        </template>
                        <template v-else>
                            <div class="noDataBox">
                                {{lang.no_more_text}}
                            </div>
                        </template>
                    </div>
                    
                </div>
            </div>
        </div>
    </transition>
</template>
<script>
import Tool from '@/common/tool.js'
import base from '../lib/base'
import { Search } from 'vant';
import groupsetTool from '../lib/groupsetTool'
import {parseImageListToLocal,getLocalAvatar} from '../lib/common_base'

export default {
    mixins: [base,groupsetTool],
    name: 'chat_history_search_list',
    components:{
        VanSearch: Search
    },
    data(){
        return {
            getLocalAvatar,
            isFocusInpt:true,
            keyword:'',
            userList:[],
            currentUser:0,
        }
    },
    computed:{
        conversation(){
            return this.conversationList[this.cid]
        },
        cid(){
            return this.$route.params.cid
        },
        attendeeList(){
            let list = []
            let listObj = this.$store.state.conversationList[this.cid]? this.$store.state.conversationList[this.cid].attendeeList : {}
            for(let key in listObj){
                let item = listObj[key]
                list.push(item)
            }
            return list
        },
    },
    activated(){
        this.debounceSearch = Tool.debounce(this.searchFriends,300);
        this.userList = this.attendeeList
        this.searchFriends()
    },
    deactivated(){

    },
    methods:{
        cancelSearch(){
            this.memberSearch()
            this.keyword = ''
            this.searchFriends()
        },
        memberSearch(){
            this.isFocusInpt = !this.isFocusInpt
        },
        switchFocusInpt(){
        },
        searchFriends(){
            this.userList = this.attendeeList.reduce((h,v)=>{
                if(v && (v.nickname.toLowerCase()).indexOf(this.keyword.toLowerCase())>-1){
                    h.push(v)
                }
                return h
            },[])
        },
        openHistoryMessageList(user){
            if(user && user.userid){
                this.$router.push({
                    path: '/index/chat_window/'+this.cid+'/group_setting/chat_history_search_list/chat_history_window_condition/' + user.userid,
                    // params:{
                    //     userId:user.userid
                    // }
                })
            }
        }
    }
}

</script>
<style lang="scss">
.search_page{
    .search_btn {
        height:1.7rem;
        line-height:1.7rem;
        text-align: center;
        width: 100%;
        background-color:rgba(0, 197, 157, .16);
        color:rgb(179,179,179);
        font-size: 0.7rem;
        span,i{
            vertical-align:middle;
        }
        .svg_icon_search{
            width: 0.7rem;
            height: 0.7rem;
            top: 1rem;
            fill: rgb(0, 197, 157);
            color: rgb(0, 197, 157);
            margin-right: 0.1rem;
        }
    }
    .content{
        display:flex;
        flex-direction:column;
        flex:1;
        overflow: hidden;
        .user-list{
            flex: 1;
            overflow: auto;
        }
    }
    .user-list{

        .user{
            margin-top:0.8rem;
            padding-left:0.5rem;
            .name{
                font-size: 0.75rem;
                color:#666;
                display:inline;
                height:2rem;
                line-height:2rem;
                margin-left:0.8rem;
                border-bottom:0.01rem solid #f2efef;
            }
        }

    }

    .noDataBox{
        margin-top:0.5rem;
        text-align:center;
        color:#666;
        font-size: 0.8rem;
    }

    .flex{
        display:flex;
    }
    .column_direction{
        flex-direction:row;
    }
    .row_direction{
        flex-direction:column;
    }
    .auto{
        flex:2;
    }

}

</style>

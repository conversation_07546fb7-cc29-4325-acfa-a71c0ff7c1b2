<template>
    <div
        class="line_chart"
        ref="line_chart_dom"
        :style="{
            width: width + '%',
            height: height + '%',
        }"
    ></div>
</template>

<script>
import base from '../../lib/base'
import _ from "lodash";
import * as echarts from "echarts";
export default {
    mixins: [base],
    name: "bi_line_chart",
    data: function () {
        return {
            lineCharts: {},
        };
    },
    props: {
        data: {
            required: true,
            type: Object,
            default:()=>{
                return {
                    xAxis: {
                        data: [],
                    },
                    yAxis: {},
                    series: [
                        {
                            data: [],
                        },
                    ],
                }
            }
        },
        width: {
            type: Number,
            default: 100,
        },
        height: {
            type: Number,
            default: 100,
        },
    },
    watch: {
        data: {
            handler: function () {
                this.updateLineChart();
            },
            deep: true,
        },
    },
    computed: {},
    mounted() {
        this.initLineChart();
        window.addEventListener("resize", () => {
            this.lineCharts.resize();
        });
    },
    beforeD<PERSON>roy() {
        this.lineCharts.dispose();
    },
    methods: {
        getOptions() {
            const lang = this.lang;
            let option = {
                tooltip: {
                    trigger: "axis",
                    formatter: "{b}: {c}",
                    confine: true,
                    textStyle: {
                        fontFamily: "MyriadPro-Regular",
                    },
                },
                grid: {
                    containLabel: true,
                    left: 20,
                    right: 20,
                    top: 30,
                    bottom: 0,
                },
                xAxis: {
                    type: "category",
                    boundaryGap: true,
                    // alignTicks: true,
                    axisLine: {
                        show: true,
                        lineStyle: {
                            color: "#283548",
                            type:'dotted'
                        },
                    },
                    axisTick: {
                        show: false,
                        alignWithLabel: true,
                        interval: 0,
                    },
                    // splitLine: {
                    //     show: false,
                    //     lineStyle: {
                    //         color: "#354657",
                    //     },
                    // },
                    axisLabel: {
                        show: true,
                        color: "#8CBDFF",
                        interval: 0,
                        fontFamily: "MyriadPro-Regular",
                    },
                },
                yAxis: {
                    type: "value",
                    boundaryGap: false,
                    minInterval: 1,
                    axisTick: {
                        show: false,
                    },
                    axisLine: {
                        show: false,
                    },
                    axisLabel: {
                        color: "#8CBDFF",
                        fontFamily: "MyriadPro-Regular",
                    },
                    splitLine: {
                        lineStyle: {
                            color: "#283548",
                            type: "dotted",
                        },
                    },
                    nameTextStyle: {
                        color: "#c6c6c6",
                        align: "left",
                        fontSize: 16,
                        fontFamily: "MyriadPro-Regular",
                    },
                },
                series: [
                    {
                        type: "line",
                        smooth: true,
                        symbol: "circle",
                        lineStyle: {
                            color: "#4892f8",
                        },
                        itemStyle: {
                            color: "#ff0",
                        },
                        universalTransition: true,
                        symbol: "image://static/resource_statistic/images/linechart_point.png",
                        symbolSize: 30,
                        markPoint: {
                            data: [
                                {
                                    type: "max",
                                    name: lang.statistic.max_tip,
                                },
                            ],
                            symbol: "image://static/resource_statistic/images/linechart_point_max.png",
                            symbolSize: 30,
                            label: {
                                position: ["50%", "-50%"],
                                color: "#00ffff",
                                fontSize:20,
                                align: "center",
                                fontWeight: 'bold'
                            },
                        },
                        areaStyle: {
                            color: {
                                type: "linear",
                                x: 0,
                                y: 0,
                                x2: 0,
                                y2: 1,
                                colorStops: [
                                    {
                                        offset: 0,
                                        color: "rgba(42,89,153,0.6)",
                                    },
                                    {
                                        offset: 1,
                                        color: "rgba(42,89,153,0)",
                                    },
                                ],
                            },
                        },
                    },
                ],
            };
            option.xAxis = Object.assign(option.xAxis, this.data.xAxis);
            option.yAxis = Object.assign(option.yAxis, this.data.yAxis);
            option.series[0] = Object.assign(option.series[0], this.data.series[0]);
            return option;
        },
        initLineChart() {
            let option = this.getOptions();
            this.lineCharts = echarts.init(this.$refs["line_chart_dom"], null, { renderer: "svg" });
            this.lineCharts.setOption(option);
        },
        updateLineChart() {
            let option = this.getOptions();
            this.lineCharts.setOption(option);
        },
    },
};
</script>

<style scoped>
.line_chart {
    overflow: hidden;
}
</style>

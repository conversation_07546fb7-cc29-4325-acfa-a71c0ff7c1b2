//CLIENT
// Fallbacks for vendor-specific variables until the spec is finalized.
var PeerConnection = window.PeerConnection || window.webkitPeerConnection00 || window.webkitRTCPeerConnection || window.mozRTCPeerConnection;
var URL = window.URL || window.webkitURL || window.msURL || window.oURL || window.mozURL;
var getUserMedia = navigator.getUserMedia || navigator.webkitGetUserMedia || navigator.mozGetUserMedia || navigator.msGetUserMedia;


function DEBUG_TO_SERVER(msg, data) {
    if (window.main_screen) {
        window.main_screen.gateway.emit("debug", msg, data);
    }
}

(function() {

  var rtc;
  if('undefined' === typeof module) {
    rtc = this.rtc = {};
  } else {
    rtc = module.exports = {};
  }


  // Holds a connection to the server.
  rtc._socket = null;

  // Holds callbacks for certain events.
  rtc._events = {};

  // config audio param
  rtc.audioParam = {};

  rtc.on = function(eventName, callback) {
    rtc._events[eventName] = rtc._events[eventName] || [];
    rtc._events[eventName].push(callback);
  };

  rtc.fire = function(eventName, _) {
    var events = rtc._events[eventName];
    var args = Array.prototype.slice.call(arguments, 1);

    if(!events) {
      return;
    }

    for(var i = 0, len = events.length; i < len; i++) {
      events[i].apply(null, args);
    }
  };

  // Holds the STUN/ICE server to use for PeerConnections.
  rtc.SERVER = {
    iceServers: [
    {
      'url': 'turn:turn.mindray.com:30000',
      'credential': '123456',
      'username': 'wwh'
    },
    ]
  };

  // Referenc e to the lone PeerConnection instance.
  rtc.peerConnections = {};

  // Array of known peer socket ids
  rtc.connections = [];
  // Stream-related variables.
  rtc.streams = [];
  rtc.numStreams = 0;
  rtc.initializedStreams = 0;
  
  rtc.peerConnectionFlag = 0;

  rtc.objURL = {};

  rtc.succFun = function(){};
  rtc.errFun = function(){};

  rtc.socket_server_url = "";
  rtc.lockReconnect = false;
  rtc.socketCheckInterval = null;
  rtc.reconnSpcing = 8000; //5s

  rtc.socket_send = function(data){
    if (WebSocket.OPEN == rtc._socket.readyState) {
      rtc._socket.send(data);
    }else{
      console.log('[RT-Voice] rtc.socket send failed! socketState:' + rtc._socket.readyState);
    }
  }

  rtc.isIceConnMediumState = function(state){
      if(state == "connected" || state == "completed" || state == "failed" ){
          return false;
      }else{
          return true;
      }
  };

  rtc.iceRestart = function(pc, peer_socket_id){
      pc.lastRestartTime = new Date();
      pc.createOffer({iceRestart: true}) //ICE重启 //底层不断重连
          .then(function(session_description) {
              pc.setLocalDescription(session_description, function(){
                  console.log('[RT-Voice] -------------------------ice Restart --------------------------');
                  console.log('[RT-Voice] send_offer');
                  rtc.socket_send(JSON.stringify({
                      "eventName": "send_offer",
                      "data": {
                          "socketId": peer_socket_id,
                          "sdp": session_description
                      }
                  }));
              }, function(error){
                  console.log("Failed to setLocalDescription ", error);
              });
          },function(err){
              if(err){
                  console.log('Failed to createOffer ' + err);
              }
          });
  }

  rtc.socketCheck = function(){
    if(rtc._socket) {
        var socket_state = rtc._socket.readyState;
        if (WebSocket.CLOSED == socket_state) {
            console.log("[RT-Voice] check websocket state : close");
            for (var connection in rtc.peerConnections) {
                var pc = rtc.peerConnections[connection];
                if(pc){
                  pc.lastRestartTime = null;
                  pc.iceMediumConnStateBeginTime = null;
                }                
            }
            rtc.reconnect();
        } else if (WebSocket.OPEN == socket_state) {
            for (var connection in rtc.peerConnections) {
              var pc = rtc.peerConnections[connection];
              if(pc){
                var diff_restart = rtc.reconnSpcing;
                if (pc.lastRestartTime) {
                    diff_restart = new Date().getTime() - pc.lastRestartTime;
                }
                if (pc.iceConnectionState === "connected" || pc.iceConnectionState === "completed"){
                    pc.lastRestartTime = null;
                    pc.iceMediumConnStateBeginTime = null;
                }else if (pc.iceConnectionState === "failed"){
                    pc.iceMediumConnStateBeginTime = null;
                    if(diff_restart >= rtc.reconnSpcing){
                        console.log('[RT-Voice] check iceConnectionState: failed');
                        console.log('[RT-Voice] peer_socket_id: ' + connection);
                        rtc.iceRestart(pc, connection);
                    }
                }else{ //medium state
                    if(pc.iceMediumConnStateBeginTime){
                        var medium_state_duration = new Date().getTime() - pc.iceMediumConnStateBeginTime;
                        if(medium_state_duration > rtc.reconnSpcing && diff_restart >= rtc.reconnSpcing){
                            console.log('[RT-Voice] check medium_state_duration > ' + rtc.reconnSpcing);
                            console.log('[RT-Voice] peer_socket_id: ' + connection);
                            rtc.iceRestart(pc, connection);
                        }
                    }else{
                        pc.iceMediumConnStateBeginTime = new Date();
                    }
                }
              }                
            }
        }
    }
  }

  /**
   * Connects to the websocket server.
   */
  rtc.connect = function(server, room, rtmp_voice_server_url, server_time_ms, succFun, errFun, stopMediaStreamCallBack) {
    room = room || ""; // by default, join a room called the blank string
    var index = room.indexOf("uid");
    var uid = -1;
    if(-1 != index){                                //layim_chatVideo_1_0_1_uid1
      uid = parseInt(room.substring(index+3, room.length));  //1
      room = room.substring(0, index-1);           //layim_chatVideo_1_0_1
    }

    rtc.uid = uid;
    rtc.socket_server_url = server;
    rtc.room = room;
    rtc.rtmp_voice_server_url = rtmp_voice_server_url;
    rtc.server_time_ms = server_time_ms;
    rtc.succFun = succFun
    rtc.errFun = errFun;
    rtc.stopMediaStreamCallBack = stopMediaStreamCallBack;

    var strArr = room.split("_");//room: layim_chatVideo_629_1_1  layim_chatVideo_uid_meeting_starter
    rtc.is_start_audio_user = parseInt(strArr.pop());
    rtc.room = strArr.join("_");//rtc.room: layim_chatVideo_629_1
    rtc.is_meeting_mode = parseInt(strArr.pop());


    rtc.socket_identify = "";


    rtc._socket = new WebSocket(server);
    rtc.peerConnectionFlag = 0;
    rtc.socketOpenFlag = 0;

    rtc.socketBindEvent();
    rtc.onEventDeal();

    rtc.fire('connect');
  };

  rtc.onEventDeal = function(){
    rtc.on('notify_get_socket_identify', function(data) {
      console.log('[RT-Voice] recv msg: notify_get_socket_identify');
      console.log(data);
      console.log("rtc.socket_identify: " + rtc.socket_identify);

      if("" == rtc.socket_identify){
        rtc.socket_identify = data.socket_identify;

        if(1 == rtc.is_meeting_mode && 1 == rtc.is_start_audio_user){
          console.log('[RT-Voice] send msg: is_join_transfer');
          rtc.socket_send(JSON.stringify({
            "eventName": "is_join_transfer",
            "data": {
              "room": rtc.room,
              "is_meeting_mode": rtc.is_meeting_mode,
              "is_start_audio_user": rtc.is_start_audio_user,
              "uid": rtc.uid,
              "rtmp_voice_server_url": rtc.rtmp_voice_server_url,
              "server_time_ms": rtc.server_time_ms
            }
          }));
        }else{
          console.log('[RT-Voice] send msg: join_room');
          rtc.socket_send(JSON.stringify({
            "eventName": "join_room",
            "data": {
              "room": rtc.room,
              "is_meeting_mode": rtc.is_meeting_mode,
              "is_start_audio_user": rtc.is_start_audio_user,
              "uid": rtc.uid,
            }
          }));
        }
      }else if(rtc.socket_identify != data.socket_identify){ //server has not this socket
        rtc.socket.selfClose();
      }else{//reconnect
        var new_conns = data.peer_conn_id_set;
        if(new_conns){ //receive from server data
          var old_conns = rtc.connections;
          console.log("new_conns: ", new_conns);
          console.log("old_conns: ", old_conns);
          var need_add_conns = [];
          var need_del_conns = [];
          for(var i = 0; i < old_conns.length; i++) {
            var old_conn = old_conns[i];
            var find_old_in_new = false;
            for(var j = 0; j < new_conns.length; j++) {
              if(new_conns[j] == old_conn){
                find_old_in_new = true;
                break;
              }
            }
            if(!find_old_in_new){
              need_del_conns.push(old_conn);
            }
          }
          for(var i = 0; i < new_conns.length; i++) {
            var new_conn = new_conns[i];
            var find_new_in_old = false;
            for(var j = 0; j < old_conns.length; j++) {
              if(old_conns[j] == new_conn){
                find_new_in_old = true;
                break;
              }
            }
            if(!find_new_in_old){
              need_add_conns.push(new_conn);
            }
          }
          console.log("need_del_conns: ", need_del_conns);
          console.log("need_add_conns: ", need_add_conns);
          for(var k = 0; k < need_del_conns.length; k++) {
            var del_conn_id = need_del_conns[k];
            rtc.connections.splice(rtc.connections.indexOf(del_conn_id), 1);
            var peerConn = rtc.peerConnections[del_conn_id];
            if(peerConn) {
              var connectionState = peerConn.connectionState;
              if (connectionState != "closed") {
                peerConn.close();
              }
              peerConn = null;

              delete rtc.peerConnections[del_conn_id];
            }
          }
          for(var k = 0; k < need_add_conns.length; k++) {
            var add_conn_id = need_add_conns[k];
            rtc.connections.push(add_conn_id);
            var pc = rtc.createPeerConnection(add_conn_id);
            for(var i = 0; i < rtc.streams.length; i++) {
              var stream = rtc.streams[i];
              pc.addStream(stream);
            }
          }
        }else{
          console.log("[RT-Voice] reconnct socket has not recv peer_conn_id_set info from server");
          DEBUG_TO_SERVER("[RT-Voice-Client] reconnct socket has not recv peer_conn_id_set info from server");
        }
      }

    });

    rtc.on('normal_close', function(data) {
      console.log('[RT-Voice] normal_close ', data);
      rtc._socket.selfClose();
    });

    rtc.on('join_transfer_success', function(data) {
        console.log('[RT-Voice] join_transfer_success');
        console.log(data);

        rtc.socket_send(JSON.stringify({
          "eventName": "join_room",
          "data": {
            "room": rtc.room,
            "is_meeting_mode": rtc.is_meeting_mode,
            "is_start_audio_user": rtc.is_start_audio_user,
            "uid": data.uid,
          }
        }));
      });

      rtc.on('join_transfer_failed', function(data) {
        console.log('[RT-Voice] join_transfer_failed');
        console.log(data);

        data.info = "join transfer failed";
        data.type = "join_transfer_failed"
        rtc.errFun(data);
      });

      rtc.on('join_room_err', function(data) {
        console.log('[RT-Voice] join_room_err');
        console.log(data);

        rtc.errFun(data);
      });

      rtc.on('room_need_close', function(data) {
        console.log('[RT-Voice] room_need_close');
        console.log(data);

        rtc.errFun(data);
      });

      rtc.on('webrtc_transfer_close', function(data) {
        console.log('webrtc_transfer_close');
        console.log(data);

        rtc.errFun(data);
      });

      rtc.on('push_voice_rtmp_msg', function(data) {
        console.log('[RT-Voice] push_voice_rtmp_msg');
        console.log(data);
        rtc.fire('push voice rtmp msg');
      });

      rtc.on('get_peers', function(data) {
        console.log('[RT-Voice] get_peers');
        console.log("data.room_member:" + data.room_member);
        console.log("data.connections:" + data.connections);
        rtc.connections = data.connections;
        // fire connections event and pass peers
        rtc.fire('connections', rtc.connections);
        
        rtc.peerConnectionFlag = 1;

        rtc.succFun({});
      });

      rtc.on('receive_ice_candidate', function(data) {
        console.log('[RT-Voice] receive_ice_candidate');
        console.log(data);
        var candidate = new RTCIceCandidate(data);
        if(rtc.peerConnections[data.socketId]){
          rtc.peerConnections[data.socketId].addIceCandidate(candidate);
        }
        
        rtc.fire('receive ice candidate', candidate);
      });

      rtc.on('new_peer_connected', function(data) {
        console.log('[RT-Voice] new_peer_connected');
        rtc.connections.push(data.socketId);

        var pc = rtc.createPeerConnection(data.socketId);
        for(var i = 0; i < rtc.streams.length; i++) {
          var stream = rtc.streams[i];
          pc.addStream(stream);
        }
      });

      rtc.on('remove_peer_connected', function(data) {
        console.log('[RT-Voice] remove_peer_connected', data);
        rtc.fire('disconnect stream', data.socketId);

        var peerConn = rtc.peerConnections[data.socketId];
        if(peerConn) {
          var connectionState = peerConn.connectionState;
          if(connectionState != "closed"){
            peerConn.close();
          }
          peerConn = null;
        }
        delete rtc.peerConnections[data.socketId];
      });

      rtc.on('receive_offer', function(data) {
        console.log('[RT-Voice] receive_offer');
        rtc.receiveOffer(data.socketId, data.sdp);
        rtc.fire('receive offer', data);
      });

      rtc.on('receive_answer', function(data) {
        console.log('[RT-Voice] receive_answer');
        rtc.receiveAnswer(data.socketId, data.sdp);
        rtc.fire('receive answer', data);
      });
  }

  rtc.reconnect = function() {
    console.log("rtc.reconnect");
    DEBUG_TO_SERVER("[RT-Voice-Client] rtc.reconnect");
    if (rtc.lockReconnect) return;
    rtc.lockReconnect = true;
    //没连接上会一直重连，设置延迟避免请求过多
    rtc.socketOpenFlag = 0;
    delete rtc._socket;
    rtc._socket = new WebSocket(rtc.socket_server_url);
    rtc.socketBindEvent();

    rtc.lockReconnect = false;
  }

  rtc.sendOffers = function() {
    for(var i = 0, len = rtc.connections.length; i < len; i++) {
      var socketId = rtc.connections[i];
      rtc.sendOffer(socketId);
    }
  }

  rtc.onClose = function(data) {
    console.log('[RT-Voice]  rtc.onClose');
    rtc.on('close_stream', function() {
      rtc.fire('close_stream', data);
    });
  }

  rtc.createPeerConnections = function() {
    for(var i = 0; i < rtc.connections.length; i++) {
      rtc.createPeerConnection(rtc.connections[i]);
    }
  };

  rtc.createPeerConnection = function(id) {
    console.log('[RT-Voice] createPeerConnection');
    console.log("rtc.SERVER ", rtc.SERVER);
    var pc = rtc.peerConnections[id] = new PeerConnection(rtc.SERVER);
    pc.id = id;
    rtc.joinStatsQuality(pc);

    pc.onicecandidate = function(event) {
      console.log('[RT-Voice] onicecandidate' + id);
      //console.log(rtc._socket);
      if(event.candidate) {
        console.log(event.candidate);
        console.log('[RT-Voice] send_ice_candidate' + id);
        rtc.socket_send(JSON.stringify({
          "eventName": "send_ice_candidate",
          "data": {
            "label": event.candidate.label,
            "candidate": event.candidate.candidate,
            "socketId": id,
            "sdpMid": event.candidate.sdpMid,
            "sdpMLineIndex": event.candidate.sdpMLineIndex,
          }
        }));
      }
      rtc.fire('ice candidate', event.candidate);
    };

    pc.onopen = function() {
      // TODO: Finalize this API
      rtc.fire('peer connection opened');
    };

    pc.onaddstream = function(event) {
      console.log('[RT-Voice] onaddstream');
      // TODO: Finalize this API
      rtc.fire('add remote stream', event.stream, id);
    };

    pc.oniceconnectionstatechange = function(event) {
      console.log('[RT-Voice] ------ oniceconnectionstatechange: ' + pc.iceConnectionState + " ---- " + id);
      if(pc.iceConnectionState === "closed"){
        console.log("rtc.connections length: " + rtc.connections.length);
      }
    };
    return pc;
  };

  rtc.sendOffer = function(socketId) {
    var pc = rtc.peerConnections[socketId];
    if(pc){
      pc.createOffer(function(session_description) {
        pc.setLocalDescription(session_description, function(){
          console.log('[RT-Voice] send_offer');
          rtc.socket_send(JSON.stringify({
            "eventName": "send_offer",
            "data": {
              "socketId": socketId,
              "sdp": session_description
            }
          }));
        }, function(error){
          console.log("Failed to setLocalDescription", error);
        });
      },function(err){
        console.log('Failed to createOffer', err);
      });
    }
  };


  rtc.receiveOffer = function(socketId, sdp) {
    console.log('[RT-Voice] receiveOffer');
    var pc = rtc.peerConnections[socketId];
    if(pc){
      //temp test
      if(rtc.audioParam.maxplaybackrate){
        var newSdp = rtc.updateSamplingRate(sdp.sdp, rtc.audioParam.maxplaybackrate);
        sdp.sdp = newSdp;
        console.log("update sdp ", sdp.sdp);
      }

      pc.setRemoteDescription(new RTCSessionDescription(sdp),function(){
        rtc.sendAnswer(socketId);
      },function(err){
        console.log('Failed to setRemoteDescription', err);
      });
    }
    
  };


  rtc.sendAnswer = function(socketId) {
    console.log('[RT-Voice] sendAnswer');
    var pc = rtc.peerConnections[socketId];
    if(pc){
      pc.createAnswer(function(session_description) {
        pc.setLocalDescription(session_description, function(){
          console.log('[RT-Voice] send_answer');
          rtc.socket_send(JSON.stringify({
            "eventName": "send_answer",
            "data": {
              "socketId": socketId,
              "sdp": session_description
            }
          }));
        }, function(error){
          console.log("Failed to setLocalDescription", error);
        });
      },function(err){
        console.log('Failed to createAnswer error', err);
      });
    }    
  };


  rtc.receiveAnswer = function(socketId, sdp) {
    console.log('[RT-Voice] receiveAnswer');
    if(socketId) {
      var pc = rtc.peerConnections[socketId];
      if(pc){
        //temp test
        if(rtc.audioParam.maxplaybackrate){
          var newSdp = rtc.updateSamplingRate(sdp.sdp, rtc.audioParam.maxplaybackrate);
          sdp.sdp = newSdp;
          console.log("update sdp ", sdp.sdp);
        }

        pc.setRemoteDescription(new RTCSessionDescription(sdp), function(){
        //do nothing
        }, function(error){
          console.log('Failed to setRemoteDescription', err);
        });
      }      
    }
  };


  rtc.createStream = function(opt, onSuccess, onFail) {
    console.log('[RT-Voice] createStream');
    var options;
    onSuccess = onSuccess ||
    function() {};
    onFail = onFail ||
    function() {};

    if(opt.audio && opt.video) {
      options = {
        video: true,
        audio: true
      };
    } else if(opt.video) {
      options = {
        video: true,
        audio: false
      };
    } else if(opt.audio) {
      options = {
        video: false,
        audio: true
      };
    } else {
      options = {
        video: false,
        audio: false
      };
    }

    if(getUserMedia) {
      getUserMedia.call(navigator, options, function(stream) {
          //cef判断是否插入耳机
          if (false == stream.active) {
            //$('<p>' + unconnected_voice_input_device + '</p>').notify({position: 'top-right',type: 'error',stay: 1000});
            DEBUG_TO_SERVER("[RT-Voice-Client] failed type: unconnected_voice_input_device 1");
            onFail();
            return;
          }
          onSuccess(stream);
      }, function(err) {
        //alert("未连接语音输入设备！");
        //$('<p>' + unconnected_voice_input_device + '</p>').notify({position: 'top-right',type: 'error',stay: 1000});
        DEBUG_TO_SERVER("[RT-Voice-Client] failed type: unconnected_voice_input_device 2");

        onFail();
      });
    } else {
      //$('<p>webRTC is not yet supported in this browser.！</p>').notify({position: 'top-right',type: 'error'});
      DEBUG_TO_SERVER("[RT-Voice-Client] failed type: webRTC is not yet supported in this browser");
      onFail();
    }
  }


  rtc.addStreams = function() {
    console.log('[RT-Voice] addStreams');
    for(var i = 0; i < rtc.streams.length; i++) {
      var stream = rtc.streams[i];
      for(var connection in rtc.peerConnections) {
        if(rtc.peerConnections[connection]){
          rtc.peerConnections[connection].addStream(stream);
        }        
      }
    }
  };

  rtc.attachStream = function(stream, domId) {
    console.log('[RT-Voice] attachStream ' + domId);
    try{
      if(rtc.objURL[domId]){
        URL.revokeObjectURL(rtc.objURL[domId]);
      }
      rtc.objURL[domId] = URL.createObjectURL(stream);
      console.log("document.getElementById(domId) " + document.getElementById(domId));
      document.getElementById(domId).src = rtc.objURL[domId];
    }catch(error){
      console.log("-----------------attachStream use new srcObject interface---------------------");
      console.log("document.getElementById(domId) " + document.getElementById(domId));
      document.getElementById(domId).srcObject = stream;
    }
  };

  rtc.on('ready', function() {
    rtc.waitForConnect(function(){
        console.log('webRTC ready');
        rtc.createPeerConnections();
        rtc.addStreams();
        rtc.sendOffers();
      }, 200);
  });
  
  rtc.waitForConnect = function (callback, interval) {
    if(rtc._socket.readyState === 1 && rtc.peerConnectionFlag === 1){
      callback();
    }else{
      setTimeout(function(){
        rtc.waitForConnect(callback, interval);
      }, interval);
    }
  }

  rtc.waitForCreateStream = function (callback, interval) {
    if(rtc._socket.readyState === 1 && rtc.peerConnectionFlag === 1 && rtc.streams.length > 0){
      callback();
    }else{
      setTimeout(function(){
        rtc.waitForCreateStream(callback, interval);
      }, interval);
    }
  }

  rtc.socketBindEvent = function(){
    rtc._socket.onerror = function(err) {
      DEBUG_TO_SERVER("[RT-Voice-Client] rtc._socket.onerror");
      if(0 ==  rtc.socketOpenFlag){//not open
        DEBUG_TO_SERVER("[RT-Voice-Client] socket connect signal server failed");
        /*rtc.errFun({
          "info": "webrtc signalling server connect failed",
          "type": "webrtc signalling server connect failed"
        });*/

      setTimeout(function(){
        rtc.reconnect();
      }, 200);
        
      }

      console.log('[RT-Voice] rtc._socket.onerror');
      console.log(err);
    };

    rtc._socket.onopen = function() {
      console.log("[RT-Voice] rtc._socket.onopen");
      clearInterval(rtc.socketCheckInterval);
      rtc.socketCheckInterval = setInterval(rtc.socketCheck, 3000);
      rtc.socketOpenFlag = 1;

      console.log('[RT-Voice] send msg: get_socket_identify');
      rtc.socket_send(JSON.stringify({
        "eventName": "get_socket_identify",
        "data": {
          socket_identify: rtc.socket_identify,
          room: rtc.room,
          uid: rtc.uid,
          is_meeting_mode: rtc.is_meeting_mode,
          is_start_audio_user: rtc.is_start_audio_user,
        }
      }));
    }; 

    rtc._socket.onmessage = function(msg) {
        var json = JSON.parse(msg.data);
        rtc.fire(json.eventName, json.data);
      };

    //客户端主动关闭socket
    rtc._socket.selfClose = function(data) {
      console.log('[RT-Voice] rtc._socket.selfClose ');
      if(rtc.socketCheckInterval){
        clearInterval(rtc.socketCheckInterval);
        rtc.socketCheckInterval = null;
      }
      rtc.socket_send(JSON.stringify({
        "eventName": "normal_close",
      }));
      for(var connection in rtc.peerConnections) {
        var peerConn = rtc.peerConnections[connection];
        if(peerConn) {
            var connectionState = peerConn.connectionState;
            if (connectionState != "closed") {
              peerConn.close();
            }
            peerConn = null;

            delete rtc.peerConnections[connection];
        }
      }

      delete rtc._events['add remote stream'];
      delete rtc._events['get_peers'];
      delete rtc._events['receive_ice_candidate'];
      delete rtc._events['new_peer_connected'];
      delete rtc._events['receive_offer'];
      delete rtc._events['receive_answer'];
      delete rtc._events['connections'];
      delete rtc._events['connection'];
      delete rtc._events['join_room_err'];
      delete rtc._events['room_need_close'];
      delete rtc._events['notify_get_socket_identify'];
      delete rtc._events['join_transfer_success'];
      delete rtc._events['normal_close'];
      delete rtc._events['disconnect stream'];
      delete rtc._events['remove_peer_connected'];
      delete rtc._events['webrtc_transfer_close'];
      delete rtc._events['push_voice_rtmp_msg'];
      rtc._socket.onopen = function(){};
      rtc._socket.onmessage = function(){};
      rtc._socket.onclose = function(){};
      rtc._socket.send = function(){};
      rtc.succFun = function(){};
      rtc.errFun = function(){};
      rtc.streams = null;
      rtc.streams = [];
      rtc.peerConnectionFlag = 0;
      rtc.socketOpenFlag = 0;
      rtc.socket_identify = "";
      //stopMediaStream();
      rtc.stopMediaStreamCallBack();
      
      rtc._socket.close();
      //errFun({info:"rtc socket close", type:"onclose"});
    };

    rtc._socket.onclose = function(data) {
      console.log('[RT-Voice] rtc._socket.onclose ');
      DEBUG_TO_SERVER("[RT-Voice-Client] rtc._socket.onclose");
    };
  }

  // Find the line in sdpLines that starts with |prefix|, and, if specified,
  // contains |substr| (case-insensitive search).
  rtc.findLine = function(sdpLines, prefix, substr) {
    return rtc.findLineInRange(sdpLines, 0, -1, prefix, substr);
  };

  // Find the line in sdpLines[startLine...endLine - 1] that starts with |prefix|
  // and, if specified, contains |substr| (case-insensitive search).
  rtc.findLineInRange = function(sdpLines, startLine, endLine, prefix, substr) {
    var realEndLine = endLine !== -1 ? endLine : sdpLines.length;
    for (var i = startLine; i < realEndLine; ++i) {
      if (sdpLines[i].indexOf(prefix) === 0) {
        if (!substr ||
            sdpLines[i].toLowerCase().indexOf(substr.toLowerCase()) !== -1) {
          return i;
        }
      }
    }
    return null;
  };

  rtc.updateSamplingRate = function(sdp, samplingRate){
    var sdpLines = sdp.split('\r\n');
    // Search for m line.
    var mLineIndex = rtc.findLine(sdpLines, 'm=', "audio");
    if (mLineIndex === null) {
      return sdp;
    }

    // a=fmtp:111 minptime=10;useinbandfec=1
    var lineIndex = rtc.findLine(sdpLines, 'a=fmtp', "111");
    console.log('lineIndex', lineIndex);
    if (lineIndex) {
      var lineInfo = sdpLines[lineIndex];
      var pos = lineInfo.indexOf("maxplaybackrate");
      if(-1 === pos){
        sdpLines[lineIndex] += ";maxplaybackrate=" + samplingRate + ";";
        sdp = sdpLines.join('\r\n');
        console.log("updateSamplingRate succ");
        return sdp;
      }
    }
    return sdp;
  };

  rtc.initStatsWebrtcQuality = function(params, callback){
    rtc.statsWebrtcQualityCallback = callback;
    rtc.statsWebrtcQualityParams = params;
    rtc.statsResultTemp = {audioOutputLevelTotal:{}};
  };

  rtc.joinStatsQuality = function(peerConn){
    if(peerConn && rtc.statsWebrtcQualityCallback && rtc.statsWebrtcQualityParams) {
      var callback = rtc.statsWebrtcQualityCallback;
      var params = rtc.statsWebrtcQualityParams;

      var repeatInterval = params.repeatInterval;
      var samplePoints = params.samplePoints;

      window.getStats(peerConn, samplePoints, function(result) {
        if("closed" === peerConn.iceConnectionState){
          result.nomore();//结束此对端连接的统计上报
          if(rtc.statsResultTemp.audioOutputLevelTotal.hasOwnProperty(peerConn.id)){
            delete rtc.statsResultTemp.audioOutputLevelTotal[peerConn.id];
          }

          var all_peer_conn_close = true;
          for (var connection in rtc.peerConnections) {
            var pc = rtc.peerConnections[connection];
            if("closed" != pc.iceConnectionState){
              all_peer_conn_close = false;
            }
          }
          if(all_peer_conn_close){
            rtc.statsResultTemp = {audioOutputLevelTotal:{}};
            callback(null);
          }
          return;
        }
        /*console.log("stats peer id: ", peerConn.id);
        console.log('packetsLost: ', result.audio.packetsLost);
        console.log('send packetsLostRate: ', result.audio.send.packetsLostRate);
        console.log('** send packetsLostRate: ', result.audio.send.packetsLostRateSet);
        console.log('recv packetsLostRate: ', result.audio.recv.packetsLostRate);
        console.log('latency: ', result.audio.latency);
        console.log('bandwidth download speed: ', result.bandwidth.speed );// bandwidth download speed (bytes per second)
        console.log('availableBandwidth recv', result.audio.recv.availableBandwidth);
        console.log('availableBandwidth send', result.audio.send.availableBandwidth);
        console.log('audioInputLevel', result.audio.audioInputLevel);
        console.log('** audioInputLevelSet', result.audio.audioInputLevelSet);
        console.log('audioOutputLevel', result.audio.audioOutputLevel);
        console.log('googRtt', result.audio.googRtt);
        console.log('** googRttSet', result.audio.googRttSet);
        console.log('googResidualEchoLikelihood', result.audio.googResidualEchoLikelihood);
        console.log('** googResidualEchoLikelihoodSet', result.audio.googResidualEchoLikelihoodSet);

        console.log('audioInputLevel', result.audio.audioInputLevel);
        console.log('** audioInputLevelSet', result.audio.audioInputLevelSet);*/

        //二次统计
        //各个对端输出音量之和
        rtc.statsResultTemp.audioOutputLevelTotal[peerConn.id] = result.audio.audioOutputLevel;

        var audioOutputLevelTotal = 0;
        for(var key in rtc.statsResultTemp.audioOutputLevelTotal){
          audioOutputLevelTotal += parseInt(rtc.statsResultTemp.audioOutputLevelTotal[key]);
        }
        result.audio.audioOutputLevelTotal = audioOutputLevelTotal;

        //结果回传
        callback(result);
      }, repeatInterval);


    }
  }

}).call(this);
<template>
    <div class="app-container">
        <div class="title">{{ pageTitle }}</div>
        <div class="filter-container">
            <el-date-picker v-model="listQuery.timeScope" type="monthrange" align="right" unlink-panels
                :clearable="false" style="width: auto;margin-right: 10px;">
            </el-date-picker>
            <el-button type="success" @click="presetTimeScope('oneMonth')">
                {{ lang.statistic.time_map["1M"] }}
            </el-button>
            <el-button type="success" @click="presetTimeScope('threeMonth')">
                {{ lang.statistic.time_map["3M"] }}
            </el-button>
            <el-button type="success" @click="presetTimeScope('sixMonth')">
                {{ lang.statistic.time_map["6M"] }}
            </el-button>
            <el-button type="success" @click="presetTimeScope('oneYear')">
                {{ lang.statistic.time_map["1Y"] }}
            </el-button>
            <el-button type="primary" icon="el-icon-search" @click="handleFilter" v-loading.fullscreen.lock="fullscreenLoading">
                {{ lang.statistic.search_btn }}
            </el-button>
        </div>
        <div ref="chart" class="barChart" />
        <div class="table-top">
            {{ lang.statistic.data_list }}
            <el-button type="primary" size="mini" @click="exportExcel" :disabled="buttonLoading"
                v-loading="buttonLoading">{{ lang.statistic.data_export }}</el-button>
        </div>
        <el-table :data="list" element-loading-text="Loading" border fit highlight-current-row>
            <el-table-column :label="lang.statistic.table_index" type="index" width="70" align="center">
            </el-table-column>
            <el-table-column align="center" :label="lang.statistic.table_group_id">
                <template slot-scope="{row}">
                    {{ row.id }}
                </template>
            </el-table-column>
            <el-table-column align="center" :label="lang.statistic.table_group_subject">
                <template slot-scope="{row}">
                    {{ row.subject }}
                </template>
            </el-table-column>
            <el-table-column align="center" :label="lang.statistic.table_group_creator">
                <template slot-scope="{row}">
                    {{ row.creator_name }}
                </template>
            </el-table-column>
            <el-table-column align="center" :label="lang.statistic.table_group_admin">
                <template slot-scope="{row}">
                    {{ row.admin_name }}
                </template>
            </el-table-column>
            <el-table-column align="center" :label="lang.statistic.table_group_create_ts">
                <template slot-scope="{row}">
                    {{ row.creation_ts | formatTime }}
                </template>
            </el-table-column>
            <el-table-column align="center" :label="lang.statistic.table_group_type">
                <template slot-scope="{row}">
                    {{ row.is_public | publicFilter }}
                </template>
            </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.pagesize"
            @pagination="handleFilter(false)" />
    </div>
</template>
<script>
import base from '../lib/base'
import Tool from '../lib/tool.js';
import request from '../service/service'
import download from '../service/export'
import moment from 'moment';
import Pagination from '../components/Pagination'

export default {
    mixins: [base],
    name: 'statistic_index',
    components: { Pagination },
    filters: {
        publicFilter(is_public) {
            const map = window.vm.$store.state.language.statistic.group_types_map;
            return map[is_public]
        }
    },
    data() {
        return {
            count: 0,
            duration: 0,
            attendeeTimes: 0,
            buttonLoading: false,
            fullscreenLoading: false,
            list: [],
            total: 0,
            listQuery: {
                page: 1,
                pagesize: 10,
                timeScope: [Tool.getZeroTimeDateOfMonth(5), new Date(Tool.getZeroTimeDateOfMonth(-1)-1)],
                dataFrom: 'global',
                id: -1
            },
            myChart: {},
            xData: [],
            yData: [],
            totalData: []
        }
    },
    computed: {
        pageTitle() {
            return this.$store.state.globalParams.targetInfo.subject || this.lang.statistic.increased_group_report;
        }
    },
    async created() {
        const query = JSON.parse(window.localStorage.getItem('stat_query'))
        if (query) {
            for (const item in query) {
                this.listQuery[item] = query[item]
            }
        }
        this.handleFilter()
    },
    watch: {
        'listQuery.timeScope'(newValue) {
            if (newValue[1] - newValue[0] >= 60 * 60 * 24 * 365 * 1000) {
                this.$message.error(this.lang.statistic.time_range_tip);
                this.presetTimeScope('oneYear')
            } else {
                newValue[1] = new Date(moment(newValue[1]).add(1, 'month').valueOf() - 1)
            }
            this.handleFilter()
        }
    },
    methods: {
        handleSizeChange(val) {
            this.listQuery.pagesize = val
            this.handleFilter()
        },
        async handleFilter(getChart = true) {
            this.fullscreenLoading = true
            const data = await request.getIncreasedGroupList(this.listQuery);
            this.list = data.data.data.list;
            this.total = data.data.data.total;
            if (getChart) {
                this.$nextTick(() => {
                    this.myChart = this.$echarts.init(this.$refs.chart)
                    this.myChart.clear()
                    this.drawLine()
                })
            }
            this.fullscreenLoading = false
        },
        async drawLine() {
            const list = await request.getIncreasedGroupChartData(this.listQuery)
            const totalGroup = await request.getTotalGroupCount(this.listQuery)
            this.yData = list.data.data
            this.yData = Tool.buildHorizontalAxis(this.listQuery.timeScope)
            this.totalData = totalGroup.data.data
            const statistic = this.lang.statistic
            this.myChart.setOption(
                Tool.buildBarChartData({
                    timeScope: this.listQuery.timeScope, 
                    yData: list.data.data,
                    totalData: totalGroup.data.data,
                    needAvg: true,
                    title: statistic.groupIncreased,
                    legend: [statistic.groupIncreased, statistic.increased_group_avg, statistic.increased_group_total],
                    selected: {[statistic.increased_group_total]:false}
                })
            )
        },
        presetTimeScope(type) {
            this.listQuery.timeScope = Tool.presetTimeScope(type)
        },
        async exportExcel() {
            this.buttonLoading = true
            await this.doExportExcel();
            this.buttonLoading = false
        },
        async doExportExcel() {
            this.isShowConfirm = false;
            const statistic = this.lang.statistic;
            window.localStorage.setItem('neverNotifyExport', this.neverNotify ? 1 : '');

            const res = await download.exportExcel(Object.assign({ chartData: [[statistic.time_txt, ...this.xData], [statistic.groupIncreased, ...this.yData], [statistic.increased_group_total, ...this.totalData]] }, this.listQuery, { type: 'groupIncreased' }))
            const blob = new Blob([res.data]);
            const link = document.createElement("a");
            link.style.display = "none";
            link.download = `${statistic.increased_group_report}-${moment().format("YYYY-MM-DD z")}.xlsx`; // 设置文件名
            link.href = window.URL.createObjectURL(blob)
            document.body.appendChild(link)
            link.click();
            link.remove(); // 释放内存
        },
    },
}
</script>
<style lang="scss">
.title {
    font-size: 18px;
    text-align: center;
    margin-bottom: 10px;
}

.filter-container {
    padding-bottom: 10px;
}

.table-top {
    margin: 10px 0;
}

.barChart {
    width: 100%;
    height: 500px;
}
</style>

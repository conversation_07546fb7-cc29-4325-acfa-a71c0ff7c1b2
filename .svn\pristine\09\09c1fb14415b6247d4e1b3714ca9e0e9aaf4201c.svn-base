/////////////////////////////////////////// 电视墙播放端(主任) end ////////////////////////////////////////
import requestManager from '@/common/CommunicationMng/requestManager';
export default function CMonitorWallPlayBridge(CWorkstationCommunicationMng){
    //需要告知PC原生打开电视墙，等待初始化声网引擎 js--->app  params {channelId:'', uid:'', token:''}
    CWorkstationCommunicationMng.OpenVideoWall = function (params) {
        console.info('CWorkstationCommunicationMng.OpenVideoWall', params)
        CWorkstationCommunicationMng.query('OpenVideoWall:' + JSON.stringify(params))
    }
    //通知初始化引擎结果 app-->js json {error_code:0,data:{uid:xxx,channelId:xxx},error_message:'success'}
    CWorkstationCommunicationMng.NotifyOpenVideoWall = function(json_str){
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        console.info('CWorkstationCommunicationMng.NotifyOpenVideoWall', json)
        requestManager.handleResponse('NotifyOpenVideoWall', json)
    }
    //加入房间 js--->app  params {channelId:'', uid:'', token:''}
    CWorkstationCommunicationMng.JoinChannelMonitor = function (params) {
        console.info('CWorkstationCommunicationMng.JoinChannelMonitor', params)
        CWorkstationCommunicationMng.query('JoinChannelMonitor:' + JSON.stringify(params))
    }
    //通知加入房间结果 app-->js json {error_code:0,data:{uid:xxx,channelId:xxx},error_message:'success'}
    CWorkstationCommunicationMng.NotifyJoinChannelMonitor = function(json_str){
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        console.info('CWorkstationCommunicationMng.NotifyJoinChannelMonitor', json)
        // window.main_screen.CMonitorWallPush.controller.emit('NotifyJoinChannelMonitor',json)
        console.log("NotifyJoinChannelMonitor", json);
        requestManager.handleResponse('NotifyJoinChannelMonitor', json)
    }
    //申请离开房间 js-->app params {channelId:xxx}
    CWorkstationCommunicationMng.LeaveChannelMonitor = function(params){
        console.info('CWorkstationCommunicationMng.LeaveChannelMonitor', params)
        CWorkstationCommunicationMng.query('LeaveChannelMonitor:' + JSON.stringify(params))
    }
    //通知离开房间结果 app-->js json {error_code:0,data:{channelId:xxx},error_message:'success'}
    CWorkstationCommunicationMng.NotifyLeaveChannelMonitor = function(json_str){
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        console.info('CWorkstationCommunicationMng.NotifyLeaveChannelMonitor', json)
        const channel = json.data.qsChannel
        if(channel&&(window.agoraClient&&window.agoraClient[channel])){
            window.agoraClient[channel].event.emit('NotifyLeaveChannelMonitor',json)
        }
    }
    //申请订阅远端视频 js-->app params {uid:xxx,channelId:xxx}
    CWorkstationCommunicationMng.SubscribeRemoteStreamMonitor = function(params){
        console.info('CWorkstationCommunicationMng.SubscribeRemoteStreamMonitor', params)
        CWorkstationCommunicationMng.query('SubscribeRemoteStreamMonitor:' + JSON.stringify(params))
    }
    //通知订阅远端视频结果 app-->js json {error_code:0,data:{uid:xxx,channelId},error_message:'success'}
    CWorkstationCommunicationMng.NotifySubscribeRemoteStreamMonitor = function(json_str){
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        console.info('CWorkstationCommunicationMng.NotifySubscribeRemoteStreamMonitor', json)
        requestManager.handleResponse('NotifySubscribeRemoteStreamMonitor',json)
    }
    //取消订阅远端视频 js-->app params {uid:xxx,channelId:xxx}
    CWorkstationCommunicationMng.StopSubscribeRemoteStreamMonitor = function(params){
        console.info('CWorkstationCommunicationMng.StopSubscribeRemoteStreamMonitor', params)
        CWorkstationCommunicationMng.query('StopSubscribeRemoteStreamMonitor:' + JSON.stringify(params))
    }
    //通知取消订阅远端视频结果 app-->js json {error_code:0,data:{uid:xxx,channelId},error_message:'success'}
    CWorkstationCommunicationMng.NotifyStopSubscribeRemoteStreamMonitor = function(json_str){
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        console.info('CWorkstationCommunicationMng.NotifyStopSubscribeRemoteStreamMonitor', json)
        requestManager.handleResponse('NotifyStopSubscribeRemoteStreamMonitor',json)
    }
    //通知实时远端用户进入 app-->js json {error_code:0,data:{uid,channelId},error_message:'success'}
    CWorkstationCommunicationMng.NotifyOnUserJoined = function(json_str){
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        console.info('CWorkstationCommunicationMng.NotifyOnUserJoined', json)
        if(!json.error_code){
            const channel = json.data.qsChannel
            if(channel&&(window.agoraClient&&window.agoraClient[channel])){
                window.agoraClient[channel].event.emit('NotifyOnUserJoined',json.data)
            }
        }
    }
    //通知实时远端用户离开 app-->js json {error_code:0,data:{uid,channelId},error_message:'success'}
    CWorkstationCommunicationMng.NotifyOnUserLeaved = function(json_str){
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        console.info('CWorkstationCommunicationMng.NotifyOnUserLeaved', json)
        if(!json.error_code){
            const channel = json.data.qsChannel
            if(channel&&(window.agoraClient&&window.agoraClient[channel])){
                window.agoraClient[channel].event.emit('NotifyOnUserLeaved',json.data)
            }
        }
    }
    //通知实时订阅的用户状态变化 app-->js json {error_code:0,data:{state},error_message:'success'}
    CWorkstationCommunicationMng.NotifyOnUserPublished = function(json_str){
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        console.info('CWorkstationCommunicationMng.NotifyOnUserPublished', json)
        if(!json.error_code){
            const channel = json.data.qsChannel
            if(channel&&(window.agoraClient&&window.agoraClient[channel])){
                window.agoraClient[channel].event.emit('NotifyOnUserPublished',json.data)
            }
        }
    }
    //设置订阅视频流的大小 js-->app params {channelId:'', uid:'', streamType:'small/large'}
    CWorkstationCommunicationMng.SetRemoteVideoStreamType = function(params){
        console.info('CWorkstationCommunicationMng.SetRemoteVideoStreamType', params)
        CWorkstationCommunicationMng.query('SetRemoteVideoStreamType:' + JSON.stringify(params))
    }
    //通知设置订阅视频流的大小结果 app-->js json {error_code:0,data:{streamType:'small/large',channelId},error_message:'success'}
    CWorkstationCommunicationMng.NotifySetRemoteVideoStreamType = function(json_str){
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        console.info('CWorkstationCommunicationMng.NotifySetRemoteVideoStreamType', json)
        requestManager.handleResponse('NotifySetRemoteVideoStreamType',json)
    }
    //展示电视墙播放器元素 js-->app params {dpr,width,height,left,top,channelId,uid}
    CWorkstationCommunicationMng.ShowVideoWall = function (params) {
        console.info('CWorkstationCommunicationMng.ShowVideoWall', params)
        CWorkstationCommunicationMng.query('ShowTVPlayer:' + JSON.stringify(params))
    }
    //隐藏电视墙播放器元素 js-->app params {channelId:'', uid:'', streamType:'small/large'}
    CWorkstationCommunicationMng.HideVideoWall = function (params) {
        console.info('CWorkstationCommunicationMng.HideVideoWall', params)
        CWorkstationCommunicationMng.query('HideTVPlayer:' + JSON.stringify(params))
    }
    //离开电视墙，通知PC原生结束声网引擎 js-->app params {}
    CWorkstationCommunicationMng.CloseVideoWall = function (params) {
        CWorkstationCommunicationMng.query('CloseVideoWall')
    }
    //通知声网Token已过期 aux app-->js json {error_code:0,data:{uid:xxx,channelId:xxx},error_message:'success'}
    CWorkstationCommunicationMng.NotifyTokenExpiredMonitor= function(json_str){
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        console.info('CWorkstationCommunicationMng.NotifyTokenExpiredMonitor', json)
        const channel = json.data.qsChannel
        if(channel&&(window.agoraClient&&window.agoraClient[channel])){
            window.agoraClient[channel].event.emit('NotifyTokenExpiredMonitor',json)
        }
    }
    //通知声网Token即将过期 main app-->js json {error_code:0,data:{uid:xxx,channelId:xxx},error_message:'success'}
    CWorkstationCommunicationMng.NotifyTokenPrivilegeWillExpireMonitor= function(json_str){
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        console.info('CWorkstationCommunicationMng.NotifyTokenPrivilegeWillExpireMonitor', json)
        const channel = json.data.qsChannel
        if(channel&&(window.agoraClient&&window.agoraClient[channel])){
            window.agoraClient[channel].event.emit('NotifyTokenPrivilegeWillExpireMonitor',json)
        }
    }
    //js通知PC 更新token js-->pc params {newToken:xxx,channelId:xx,uid:xx}
    CWorkstationCommunicationMng.RenewTokenMonitor = function (params) {
        console.info('CWorkstationCommunicationMng.RenewTokenMonitor',params)
        this.query('RenewTokenMonitor:' + JSON.stringify(params))
    }
    //通知更新token结果 main app-->js json {error_code:0,data:{uid:xxx,channelId:xxx},error_message:'success'}
    CWorkstationCommunicationMng.NotifyRenewTokenMonitor= function(json_str){
        let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str
        console.info('CWorkstationCommunicationMng.NotifyRenewTokenMonitor', json)
        requestManager.handleResponse('NotifyRenewTokenMonitor', json)
    }
}

<template>
    <transition name="slide">
        <div class="club_entry second_level_page">
            <mrHeader @click-left="back">
                <template #title>
                    Mindray Club
                </template>
            </mrHeader>
            <div class="container clearfix" v-loading="loading">
                <div class="club_item" v-for="item in clubList" :key="item.club_name" @click="openClub(item.club_name)">
                    <img :src="`static/resource/images/club_bg_${item.club_name}.jpg`">
                    <p>{{clubNames[item.club_name]}}</p>
                </div>
            </div>
            <router-view></router-view>
        </div>
    </transition>
</template>
<script>
import base from '../../lib/base';
import { Toast } from "vant";
import service from '../../service/service';
import Tool from '@/common/tool.js'
import {clubNames} from '@/common/tools/mappings.js'
export default {
    name:'clubEntry',
    mixins:[base],
    
    components:{
    },
    data(){
        return {
            clubNames,
            clubList:[],
            loading:false
        }
    },
    computed:{
        
    },
    watch:{
        
    },
    mounted(){
        this.loadClubs()
    },
    beforeDestroy(){
        
    },
    methods:{
        loadClubs(){
            service.getClubList().then(res=>{
                if (res.data.error_code === 0) {
                    this.clubList = res.data.data
                }
            })
        },
        openClub(club_name){
            this.loading = true;
            service.getClubInfo({
                club_name:club_name
            }).then(res=>{
                this.loading = false;
                if (res.data.error_code === 0) {
                    const status = res.data.data.status
                    if (status===2) {
                        // 审批通过，进入Club
                        Tool.loadModuleRouter(`/index/club_entry/club_index/${club_name}`);
                    }else if (status===1) {
                        Toast('The application has been submitted and is awaiting approval from the administrator')
                    }else{
                        //0未申请，3被驳回，4被解除
                        if (status === 3) {
                            Toast('The application has been rejected, please resubmit the information')
                        }else if (status === 4) {
                            Toast('Your permission has been revoked, please resubmit the application')
                        }
                        Tool.loadModuleRouter(`/index/club_entry/club_apply/${club_name}/${status}`);
                    }
                }
            }).catch(()=>{
                this.loading = false;
            })
        }
    }
}
</script>
<style lang="scss">
.club_entry{
    .container{
        overflow: auto;
    }
    .club_item{
        background: #f2f2f2;
        margin: .5rem .8rem;
        padding: .4rem 2rem;
        text-align: center;
        border-radius:.4rem;
        img{
            max-width: 100%;
        }
    }
    
}
</style>

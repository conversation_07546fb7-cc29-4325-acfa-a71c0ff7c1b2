<template>
    <div>
        <CommonDialog
            :title="lang.live_setting"
            :show.sync="visible"
            width="700px"
            height="auto"
            @closed="handleClose"
            @submit="submit"
            :isSubmitting="isSubmitting"
            :submitText="lang.confirm_txt"
            append-to-body
        >
            <el-form label-position="left" label-width="150px" :model="form" class="setting_form">
                <!-- <el-form-item :label="lang.reserved_conference_subject">
                    <el-input v-model="form.subject" maxlength="24" show-word-limit></el-input>
                </el-form-item>
                <el-form-item prop="resource" class="review_form_item_type" :label="lang.label_txt">
                    <el-radio-group v-model="form.type" @change="changeReviewFormType">
                        <el-radio :label="1">{{lang.universal_live}}</el-radio>
                        <el-radio :label="2">{{lang.consultation_live}}</el-radio>
                        <el-radio :label="3">{{lang.teaching_live}}</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item :label="lang.moderator">
                    <el-input v-model="form.speaker" maxlength="24" show-word-limit></el-input>
                </el-form-item>
                <el-form-item :label="lang.describe">
                    <el-input
                        v-model="form.description"
                        type="textarea"
                        :autosize="{ minRows: 2, maxRows: 4 }"
                        maxlength="70"
                        show-word-limit
                        clearable
                    ></el-input>
                </el-form-item> -->
                <el-form-item :label="lang.group_setting_whether_live_record">
                    <el-radio-group v-model="form.isRecord">
                        <el-radio-button :label="0">{{ lang.cancel_button_text }}</el-radio-button>
                        <el-radio-button :label="1">{{ lang.confirm_button_text }}</el-radio-button>
                    </el-radio-group>
                </el-form-item>
            </el-form>
        </CommonDialog>
    </div>
</template>
<script>
import base from "../../lib/base";
import CommonDialog from "../../MRComponents/commonDialog.vue";
import moment from "moment";
export default {
    mixins: [base],
    name: "ReviewEditDialog",
    model: {
        prop: "value",
        event: "change",
    },
    props: {
        message: {
            type: Object,
            default: () => {
                return {};
            },
        },
        value: {
            type: Boolean,
            default: false,
        },
    },
    watch: {
        value: {
            handler(val) {
                this.visible = val;
            },
            immediate: true,
        },
        visible: {
            handler(val) {
                this.$emit("change", val);
            },
        },
    },
    computed:{
        cid(){
            return this.$route.query.cid || this.$route.params.cid
        }
    },
    components: {
        CommonDialog,
    },
    data() {
        return {
            that: this,
            visible: false,
            form: {
                isRecord: 1,
                subject: "",
                description: "",
                speaker: "",
                type:1,
            },
            isSubmitting: false,
        };
    },
    methods: {
        handleClose() {
            this.visible = false
            this.form.isRecord = 1
        },
        async submit() {
            this.isSubmitting = true
            try {
                await this.preHandleRecordMode()
                this.isSubmitting = false
                this.$emit('liveConferenceSettingSubmit')
                this.visible = false
            } catch (error) {
                console.error(error)
                this.isSubmitting = false
                this.visible = false
            }
            this.form.isRecord = 1


        },
        preHandleRecordMode(){
            return new Promise((resolve,reject)=>{
                let data={
                    gid:this.cid,
                    record_mode:this.form.isRecord
                }
                let timer = setTimeout(()=>{
                    reject('preHandleRecordMode time out')
                },10000)
                this.conversationList[this.cid].socket.emit('edit_record_mode',data,(is_succ,data)=>{
                    if(is_succ){
                        //修改成功
                        this.$store.commit('conversationList/updateIsLiveRecord',{
                            cid:this.cid,
                            record_mode:this.form.isRecord
                        });
                        resolve(true)
                    }else{//修改失败
                        reject(false)
                    }
                    clearTimeout(timer)
                    timer = null
                })

            })
        },
        changeReviewFormType(type) {
            this.form.type = type;
        },
    },
};
</script>
<style lang="scss">
.setting_form {

}
</style>

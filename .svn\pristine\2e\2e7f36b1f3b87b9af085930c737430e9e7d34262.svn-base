<template>
    <div class="mr-group">
        <div @click="showPopup" class="select_wrapper">
            <span class="show_value">{{showText}}
            </span>
            <i class="icon iconfont icon-down"></i>
        </div>
        <div >
            <van-popup position="bottom" v-model="isShowPopup" class="popup">
                <!-- <div class="clearfix header" @tap.stop>
                    <span class="primary_bg submit fr" @click="submit">{{lang.confirm_txt}}</span>
                </div> -->
                <div class="group_type_list">
                    <div class="group_type_item" @tap="clickPicker(0)">
                        <p class="group_title">{{options[0].value}}</p>
                        <p class="group_tip">{{options[0].tip}}</p>
                    </div>
                    <div class="group_type_item" @tap="clickPicker(1)">
                        <p class="group_title">{{options[1].value}}</p>
                        <p class="group_tip">{{options[1].tip}}</p>
                    </div>
                </div>
            </van-popup>
        </div>

    </div>
</template>
<script>
import { Popup } from 'vant'

/*
    依靠van-popup和mt-picker封装的选择器。
    传入参数options为[{id:1,value:"test"}]
    传入参数seletedId为选中Id
*/
export default {
    components:{
        vanPopup:Popup
    },
    mixins:[],
    data(){
        return {
            isShowPopup:false,
            // slots:[{
            //     flex:1,
            //     values:['123','456'],
            //     textAlign: 'center'
            // }],
            options:[
                {
                    id:0,
                    value:'',
                    tip:'',
                },
                {
                    id:1,
                    value:'',
                    tip:'',
                }
            ]
        }
    },
    props:{
        // options:{
        //     type:Array,
        //     default:()=>{
        //         return []
        //     }
        // },
        seletedId:[String,Number],
        callback:{
            type:Function,
            default:function(){
                console.log('mr-selecter callback!')
            }
        },

    },
    activated(){
        this.$nextTick(()=>{

        })
    },
    computed:{
        lang(){
            return this.$store.state.language
        },
        showText(){
            let text='';
            for(let option of this.options){
                if (option.id==this.seletedId) {
                    text=option.value;
                    break;
                }
            }
            return text;
        }
    },
    mounted(){
        this.$nextTick(()=>{
            this.options[0].value=this.lang.private_group_text;
            this.options[1].value=this.lang.public_group_text;
            this.options[0].tip=this.lang.private_group_tip;
            this.options[1].tip=this.lang.public_group_tip;
        })

    },
    methods:{
        showPopup(){
            // let arr=[];
            // let selectValue=''
            // for(let i=0;i<this.options.length;i++){
            //     let item=this.options[i]
            //     arr.push(item.value);
            //     if (item.id==this.seletedId) {
            //         selectValue=item.value
            //     }
            // }
            // this.slots[0].values=arr;
            // this.$refs.picker.setSlotValue(0,selectValue);
            this.isShowPopup=true;
        },
        clickPicker(value){
            this.callback({id:value});
            console.log('clickPicker')
            this.isShowPopup=false;
        }
    }
}
</script>
<style lang="scss">
    .mr-group{
        padding-left: 0.2rem;
        .select_wrapper{
            display: flex;
        }
        .show_value{
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
        }
        .icon-down{
            font-size:0.9rem;
            vertical-align: middle;
            margin-right:0.3rem;
            margin-left:0.1rem;
        }
        .popup{
            width:100%;
            .group_type_list{
                padding: 0 .5rem;
                .group_type_item{
                    border-bottom: 1px solid #eee;
                    padding: .5rem;
                    &:last-child{
                        border-bottom: none;
                    }
                    .group_title{
                        font-size: 1rem;
                    }
                    .group_tip{
                        color: #999;
                        font-size: .7rem;
                    }
                }
            }
        }
    }
</style>

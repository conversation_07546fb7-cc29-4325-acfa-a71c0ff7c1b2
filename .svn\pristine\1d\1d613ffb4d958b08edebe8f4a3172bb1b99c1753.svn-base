<template>
    <div class="ultrasoundReportQC-search-form">
        <el-form :inline="true" :model="formData" class="form-inline">
            <el-form-item label="检查医生">
                <el-select v-model="formData.doctor" placeholder="请选择" size="small">
                    <el-option label="请选择" value=""></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="报告医生">
                <el-select v-model="formData.reporter" placeholder="请选择" size="small">
                    <el-option label="请选择" value=""></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="质控结果">
                <el-select v-model="formData.result" placeholder="请选择" size="small">
                    <el-option label="请选择" value=""></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="报告上传时间">
                <el-date-picker
                    v-model="formData.dateRange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    size="small"
                >
                </el-date-picker>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="onSearch" size="small">查询</el-button>
                <el-button @click="onReset" size="small">重置</el-button>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
export default {
    name: "ultrasoundReportQCSearchForm",
    data() {
        return {
            formData: {
                doctor: "",
                reporter: "",
                result: "",
                dateRange: [],
            },
        };
    },
    methods: {
        onSearch() {
            this.$emit("search", this.formData);
        },
        onReset() {
            this.formData = {
                doctor: "",
                reporter: "",
                result: "",
                dateRange: [],
            };
        },
    },
};
</script>

<style lang="scss" scoped>
@import "@/module/ultrasync_pc/style/aiChat.scss";
.ultrasoundReportQC-search-form {
    .form-inline {
        display: flex;
        align-items: center;
        gap: 20px;
        flex-wrap: wrap;
        .el-form-item {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 0;
        }
        .el-button {
            width: 120px;
        }
    }
    :deep(.el-button--primary) {
        @extend .ai-theme-background;
        border-radius: 5px;
    }

    :deep(.el-input__inner),
    :deep(.el-select .el-input__inner),
    :deep(.el-date-editor.el-input__inner) {
        border: 1px solid #a8b0c7;
        border-radius: 4px;
        color: rgba(32, 34, 38, 0.4);
        font-weight: 400;
    }

    :deep(.el-form-item__label) {
        color: #202226;
        text-align: right;
        font-weight: 500;
        font-size: 16px;
    }
}
</style>

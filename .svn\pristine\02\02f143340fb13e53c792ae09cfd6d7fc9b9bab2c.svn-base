import cloneDeep from 'lodash/cloneDeep'
import Vue from 'vue'

const initState = {
    list:[],
    index:0,
    commentObj:{

    },
    iworks_protocol_list:{},
    tagTopInfo:{
        systemTagTop: [],
        userTagTop: [],
        allTagTop:[],
    },
    cid:0,
    fileId:'',
    cacheData:{

    },
}
export default {
    state: cloneDeep(initState),
    mutations: {
        resetStore(state){
            if(window.vm.$store.state){
                Vue.set(window.vm.$store.state,'gallery',cloneDeep(initState))
            }
        },
        setGallery(state, valObj) {
            let galleryData = cloneDeep(valObj)
            for (let key in galleryData) {
                Vue.set(state,key,galleryData[key])
            }
        },
        updateGalleryItemLocal(state,data){
            let imgObj=data.imgObj;
            for(let index=0;index<state.list.length;index++){
                if (imgObj.resource_id==state.list[index].resource_id) {
                    let item=state.list[index]
                    item.realUrl=data.realUrl
                    item.loaded=true;
                    item.preloading=false;
                    if (data.drawedCanvas) {
                        //绘制了描迹
                        item.drawedCanvas=data.drawedCanvas
                    }
                    state.list.splice(index,1,item)
                    break;
                }
            }
        },
        updateLocalDcm(state,data){
            let imgObj=data.imgObj;
            for(let index=0;index<state.list.length;index++){
                if (imgObj.img_id==state.list[index].img_id) {
                    let item=state.list[index]
                    item.realUrl=data.realUrl
                    state.list.splice(index,1,item)
                    break;
                }
            }
        },
        deleteTagList(state,data){
            let item=state.commentObj[data.resource_id];
            let delIndex=0;
            let tagList=item.tags_list
            for(let i=0;i<tagList.length;i++){
                if (tagList[i].group_id==data.group_id&&tagList[i].img_id==data.img_id&&tagList[i].tags==data.tags&&tagList[i].sender_id==data.sender_id) {
                    delIndex=i;
                    break;
                }
                //删除标签应该使用唯一id
                // if (tagList[i].ft_id==data.ft_id) {
                //     delIndex=i;
                //     break;
                // }
            }
            tagList.splice(delIndex,1)
            Vue.set(state.commentObj,data.resource_id,item)
        },
        addTagList(state,data){
            let item=state.commentObj[data.resource_id];
            if (item) {
                item.tags_list.push(data)
                Vue.set(state.commentObj,data.resource_id,item)
            }else{
                let arr = [data];
                let json = {tags_list:arr, comment_list:[]};
                Vue.set(state.commentObj,data.resource_id,json);
            }
        },
        setTagList(state,{resource_id,tagList}){
            let item=state.commentObj[resource_id];
            if (item) {
                item.tags_list = tagList
                Vue.set(state.commentObj,resource_id,item)
            }else{
                let json = {tags_list:tagList, comment_list:[]};
                Vue.set(state.commentObj,resource_id,json);
            }
        },
        setCommentToGallery(state,data){
            for(let key in data.list){
                Vue.set(state.commentObj,key,data.list[key])
            }
            for(let key in data.iworks_protocol_list){
                Vue.set(state.iworks_protocol_list,key,data.iworks_protocol_list[key])
            }
        },
        updateCommentToGallery(state,data){
            let item=state.commentObj[data.resource_id];
            if (item) {
                //更新comment对象时若已存在，且tags_list比say时传过来的长，使用原有的
                //tags_list，否则直接使用data.obj
                if (item.tags_list.length>data.obj.tags_list.length) {
                    data.obj.tags_list=item.tags_list
                }
            }else{
                item=data.obj;
            }
            Vue.set(state.commentObj,data.resource_id,item)
        },
        updateAiReportToGallery(state,data){
            let item=state.commentObj[data.resource_id];
            item.ai_analyze_report=data.ai_analyze_report;
            Vue.set(state.commentObj,data.resource_id,item)
        },
        addCommentToList(state,data){
            let resource_id=data.resource_id
            if(!state.commentObj.hasOwnProperty(resource_id)){
                Vue.set(state.commentObj,resource_id,{comment_list:[],tags_list:[]})
            }
            let item=state.commentObj[data.resource_id];
            item.comment_list.push(data)
        },
        addTagTopInfo(state, data){
            state.tagTopInfo.allTagTop=data;
        },
        addCustomTag(state, data){
            state.tagTopInfo.allTagTop.push({
                id:data.tag_id,
                caption:data.tags,
                count:1
            });
        },
        deleteGalleryListByGroupID(state,data){
            let cid=data.cid;
            if(cid){
                let gallery_list=state.list;
                for(let i=gallery_list.length-1; i>=0; i--){
                    if(cid==gallery_list[i].group_id){
                        gallery_list.splice(i,1);
                    }
                }
            }
        },
        deleteCommentByCommentId(state,{resource_id,comment_id}){
            let item=state.commentObj[resource_id];
            for(var i in item.comment_list){
                let comment = item.comment_list[i];
                if (comment.comment_id==comment_id) {
                    item.comment_list.splice(i,1);
                    break
                }
            }
        },
        addTagCount(state,data){
            for(let tag of state.tagTopInfo.allTagTop){
                if (tag.id==data.tag_id) {
                    tag.count++
                    break;
                }
            }

        },
        reduceTagCount(state,data){
            for(let tag of state.tagTopInfo.allTagTop){
                if (tag.id==data.tag_id) {
                    tag.count--
                    break;
                }
            }
        },
        sortTags(state){
            state.tagTopInfo.allTagTop.sort((a,b)=>{
                if (a.id>b.id) {
                    return -1;
                }else{
                    return 1;
                }
            })
            state.tagTopInfo.allTagTop.sort((a,b)=>{
                if (a.count>b.count) {
                    return -1;
                }else{
                    return 1;
                }
            })
        },
        cacheGalleryData(state){
            if(state.list&&state.list.length>0){
                state.cacheData[window.vm.$route.path] = cloneDeep({
                    list:state.list,
                    index: state.index,
                    cid:state.cid,
                    fileId:state.fileId,
                    loadMore:state.loadMore,
                    loadMoreCallback:state.loadMoreCallback
                })
            }

        },
        clearGalleryCacheData(state,path){
            if(!path){
                state.cacheData = {}
            }else{
                delete state.cacheData[path]
            }
        },
        clearGalleryData(state){
            let galleryData = {
                list:[],
                index: 0,
                cid:0,
                fileId:'',
                loadMore:false,
                loadMoreCallback:null
            }
            for (let key in galleryData) {
                Vue.set(state,key,galleryData[key])
            }
        },
        updateCommentObjByKey(state,data){
            //更新聊天消息录音文件本地地址
            let item=state.commentObj[data.resource_id];
            let keys=data.keys;
            if(item){
                for(let key in keys){
                    Vue.set(state.commentObj[data.resource_id],key,keys[key])
                }
            }else{
                Vue.set(state.commentObj,data.resource_id,data.keys);
            }
        },
    },
    actions: {},
    getters: {}
}

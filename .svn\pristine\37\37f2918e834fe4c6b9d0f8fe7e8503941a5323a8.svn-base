<template>
    <div class="app-container">
        <div class="title">{{pageTitle}}</div>
        <div class="filter-container">
            <el-date-picker
                v-model="listQuery.timeScope"
                type="monthrange"
                align="right"
                unlink-panels
                :clearable="false"
                style="width: auto;margin-right: 10px;"
            >
            </el-date-picker>
            <el-button type="success" @click="presetTimeScope('oneMonth')">
                {{lang.statistic.time_map["1M"]}}
            </el-button>
            <el-button type="success" @click="presetTimeScope('threeMonth')">
                {{lang.statistic.time_map["3M"]}}
            </el-button>
            <el-button type="success" @click="presetTimeScope('sixMonth')">
                {{lang.statistic.time_map["6M"]}}
            </el-button>
            <el-button type="success" @click="presetTimeScope('oneYear')">
                {{lang.statistic.time_map["1Y"]}}
            </el-button>
            <!-- <el-button type="primary" icon="el-icon-search" @click="handleFilter">
                {{lang.statistic.search_btn}}
            </el-button> -->
        </div>
        <div ref="chart" class="barChart" />
        <div class="table-top">
            {{lang.statistic.data_list}}
            <el-button type="primary" size="mini" @click="exportExcel" :disabled="buttonLoading" v-loading="buttonLoading" v-loading.fullscreen.lock="fullscreenLoading">{{lang.statistic.data_export}}</el-button>
        </div>
        <el-table :data="list" element-loading-text="Loading" border fit highlight-current-row>
            <el-table-column
                :label="lang.statistic.table_index"
                type="index"
                width="70"
                align="center">
            </el-table-column>
            <el-table-column align="center" :label="lang.statistic.table_account">
                <template slot-scope="{row}">
                    {{ row.login_name}}
                </template>
            </el-table-column>
            <el-table-column align="center" :label="lang.statistic.table_nickname">
                <template slot-scope="{row}">
                    {{ row.nickname }}
                </template>
            </el-table-column>
            <el-table-column align="center" :label="lang.statistic.table_register_time">
                <template slot-scope="{row}">
                    {{ row.creation_ts|formatTime }}
                </template>
            </el-table-column>
            <el-table-column align="center" :label="lang.statistic.table_user_type">
                <template slot-scope="{row}">
                    {{ row.status|statusFilter }}
                </template>
            </el-table-column>
            <el-table-column align="center" :label="lang.statistic.table_email">
                <template slot-scope="{row}">
                    {{ row.email }}
                </template>
            </el-table-column>
            <el-table-column align="center" :label="lang.statistic.table_organization">
                <template slot-scope="{row}">
                    {{ row.orgName }}
                </template>
            </el-table-column>
            <el-table-column align="center" :label="lang.statistic.table_province">
                <template slot-scope="{row}">
                    {{ row.province }}
                </template>
            </el-table-column>
            <el-table-column align="center" :label="lang.statistic.table_city">
                <template slot-scope="{row}">
                    {{ row.city }}
                </template>
            </el-table-column>
            <el-table-column v-if="isGlobal" align="center" :label="lang.statistic.branch_txt">
                <template slot-scope="{row}">
                    {{ row.branch }}
                </template>
            </el-table-column>
            <el-table-column v-if="isGlobal" align="center" :label="lang.statistic.region_txt">
                <template slot-scope="{row}">
                    {{ row.region }}
                </template>
            </el-table-column>
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.pagesize" @pagination="handleFilter(false)" />
    </div>
</template>
<script>
import base from '../lib/base'
import Tool from '../lib/tool.js';
import request from '../service/service'
import download from '../service/export'
import moment from 'moment';
import Pagination from '../components/Pagination'

export default {
    mixins: [base],
    name: 'statistic_index',
    components: { Pagination },
    filters: {
        statusFilter(status) {
            const map = window.vm.$store.state.language.statistic.user_types;
            return map[status]
        }
    },
    data() {
        return {
            count: 0,
            duration: 0,
            attendeeTimes: 0,
            buttonLoading: false,
            fullscreenLoading: false,
            list: [],
            total: 0,
            listQuery: {
                page: 1,
                pagesize: 10,
                timeScope: [Tool.getZeroTimeDateOfMonth(5), new Date(Tool.getZeroTimeDateOfMonth(-1)-1)],
                dataFrom: 'global',
                id: -1
            },
            myChart: {},
            xData: [],
            yData: [],
            totalData: []
        }
    },
    computed: {
        pageTitle(){
            return this.$store.state.globalParams.targetInfo.subject || this.lang.statistic.user_increased_report;
        },
        isGlobal(){
            return this.listQuery.dataFrom === 'global'
        }
    },
    async created() {
        const query = JSON.parse(window.localStorage.getItem('stat_query'))
        if(query) {
            for (const item in query) {
                this.listQuery[item] = query[item]
            }
        }
        this.handleFilter()
    },
    watch: {
        'listQuery.timeScope'(newValue){
            if (newValue[1]-newValue[0] >= 60*60*24*365*1000) {
                this.$message.error(this.lang.statistic.time_range_tip);
                this.presetTimeScope('oneYear')
            } else {
                newValue[1] = new Date(moment(newValue[1]).add(1, 'month').valueOf() - 1)
            }
            this.handleFilter()
        }
    },
    methods: {
        handleSizeChange(val) {
            this.listQuery.pagesize = val
            this.handleFilter()
        },
        async handleFilter(getChart = true) {
            this.fullscreenLoading = true
            const data = await request.getIncreasedUserList(this.listQuery);
            this.list = data.data.data.list;
            this.total = data.data.data.total;
            if (getChart) {
                this.$nextTick(() => {
                    this.myChart = this.$echarts.init(this.$refs.chart)
                    this.myChart.clear()
                    this.drawLine()
                })
            }
            this.fullscreenLoading = false
        },
        async drawLine() {
            const list = await request.getIncreasedUserChartData(this.listQuery)
            const totalUser = await request.getTotalUserCount(this.listQuery)
            this.xData = Tool.buildHorizontalAxis(this.listQuery.timeScope)
            this.yData = list.data.data;
            this.totalData = totalUser.data.data
            const statistic = this.lang.statistic;
            this.myChart.setOption(
                Tool.buildBarChartData({
                    timeScope: this.listQuery.timeScope, 
                    yData: list.data.data,
                    totalData: totalUser.data.data,
                    needAvg: true,
                    title: statistic.userIncreased,
                    legend: [statistic.userIncreased, statistic.user_increased_avg, statistic.total_user],
                    selected: JSON.parse(`{"${statistic.total_user}":false}`)
                })
            )
        },
        presetTimeScope(type) {
            this.listQuery.timeScope = Tool.presetTimeScope(type)
        },
        async exportExcel() {
            this.buttonLoading = true
            await this.doExportExcel();
            this.buttonLoading = false
        },
        async doExportExcel(){
            this.isShowConfirm = false;
            const statistic = this.lang.statistic;
            window.localStorage.setItem('neverNotifyExport', this.neverNotify ? 1 : '');

            const res = await download.exportExcel(Object.assign({chartData: [[statistic.time_txt, ...this.xData], [statistic.userIncreased,...this.yData], [statistic.total_user,...this.totalData]]}, this.listQuery, {type: 'userIncreased'}))
            const blob = new Blob([res.data]);
            const link = document.createElement("a");
            link.style.display = "none";
            link.download = `${statistic.user_increased_report}-${moment().format("YYYY-MM-DD z")}.xlsx`; // 设置文件名
            link.href = window.URL.createObjectURL(blob)
            document.body.appendChild(link)
            link.click();
            link.remove(); // 释放内存
        },
    },
}
</script>
<style lang="scss">
    .title {
        font-size: 18px;
        text-align: center;
        margin-bottom: 10px;
    }

    .filter-container {
        padding-bottom: 10px;
    }
    .table-top {
        margin: 10px 0;
    }
    .barChart {
        width: 100%;
        height: 500px;
    }
</style>

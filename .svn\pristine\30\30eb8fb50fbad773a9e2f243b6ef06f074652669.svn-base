<template>
    <div class="header-bar">
        <div class="header-bar-left" :style="{ '--header-left-bg': `url(${headerLeftBg})` }" @click="handleLogoClick" :title="user.role | FilterRole">
            <img class="header-bar-logo" :src="mindrayLogo" alt="">
        </div>
        <div class="header-bar-right" :style="{ '--header-right-bg': `url(${headerRightBg})` }">
            <div class="hospital"><img class="hospital-logo" src="" alt=""><span>迈瑞云PACS测试医院</span></div>
            <div class="header-bar-right-divider"></div>

            <!-- 用户下拉菜单 -->
            <div class="user-dropdown">
                <el-dropdown trigger="click" placement="bottom-end" @command="handleCommand">
                    <span class="user-info">
                        <span class="username">{{ user.nickname || '用户' }}</span>
                        <i class="el-icon-arrow-down el-icon--right"></i>
                    </span>
                    <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item command="accountSecurity">
                            <i class="el-icon-user"></i>
                            账号与安全
                        </el-dropdown-item>
                        <el-dropdown-item command="language">
                            <i class="el-icon-s-tools"></i>
                            多语言-中文
                        </el-dropdown-item>
                        <el-dropdown-item divided command="logout">
                            <i class="el-icon-switch-button"></i>
                            退出登录
                        </el-dropdown-item>
                    </el-dropdown-menu>
                </el-dropdown>
            </div>

            <!-- 窗口控制按钮 -->
            <div  class="window-controls" @mousedown.stop v-if="isCef">
                <i class="control-btn minimize-btn el-icon-minus" @click="minApp" title="最小化"></i>
                <i class="control-btn maximize-btn el-icon-full-screen" @click="maximizeApp" title="最大化"></i>
                <i class="control-btn close-btn el-icon-close" @click="closeApp" title="关闭"></i>
            </div>
        </div>
    </div>
</template>
<script>
import { headerLeftBg, headerRightBg, mindrayLogo } from "@/icons/base64Image.js";
import base from "../lib/base";
import appOperateTool from "../lib/appOperateTool";
import service from "../service/service";

export default {
    name: 'headerBar',
    mixins: [base, appOperateTool],
    filters: {
        FilterRole(val) {
            let arrayData = {
                0: "临时用户",
                1: "普通用户",
                2: "管理员",
                3: "超级管理员",
                4: "主任",
            };
            return arrayData[val];
        },
    },
    data() {
        return {
            headerLeftBg,
            headerRightBg,
            mindrayLogo
        }
    },
    methods: {
        handleLogoClick(){
            console.log(window.vm.$store.state)
        },
        handleCommand(command) {
            switch (command) {
            case 'accountSecurity':
                this.handleAccountSecurity();
                break;
            case 'language':
                this.handleLanguage();
                break;
            case 'hiddenInfo':
                this.handleHiddenInfo();
                break;
            case 'about':
                this.handleAbout();
                break;
            case 'logout':
                this.handleLogout();
                break;
            default:
                break;
            }
        },

        // 预留的功能函数
        handleAccountSecurity() {
            // TODO: 实现账号与安全功能
            console.log('账号与安全');
        },

        handleLanguage() {
            // TODO: 实现多语言切换功能
            console.log('多语言切换');
        },

        handleHiddenInfo() {
            // TODO: 实现隐藏病人信息功能
            console.log('隐藏病人信息');
        },

        handleAbout() {
            // TODO: 实现关于页面功能
            console.log('关于瑞影-云PACS');
        },

        handleLogout() {
            this.logout();
        },

        logout() {
            this.$root.eventBus.$emit("unBindControllerEvent");
            setTimeout(() => {
                if (window.main_screen && window.main_screen.gateway) {
                    window.main_screen.CloseSocket();
                }
                service
                    .logout({
                        user_id: this.user.uid,
                        client_uuid: this.user.client_uuid,
                    })
                    .then((res) => { })
                    .catch((res) => { });
                this.resetApp();
            }, 0);
        },

        resetApp() {
            window.CWorkstationCommunicationMng.CloseNewWindow();
            window.CWorkstationCommunicationMng.resetApp();
            window.CWorkstationCommunicationMng.notifyDisconnectFromDoppler();
            window.localStorage.setItem("account", "");
            window.localStorage.setItem("password", "");
            window.localStorage.setItem("loginToken", "");
            window.localStorage.removeItem("local_store_device_token");
            this.$store.commit("user/updateUser", {
                new_token: "",
            });
            window.CWorkstationCommunicationMng.ClearStartupOption();
            if (window.main_screen.CMonitorWallPush && window.main_screen.CMonitorWallPush.joined) {
                window.main_screen.CMonitorWallPush.LeaveChannelSilence();
            }

            // setTimeout(()=>{
            //     window.location.reload();
            // },300)
            this.$root.eventBus.$emit("reloadRouter");
        }
    }
}
</script>
<style scoped lang="scss">
.header-bar{
    height: 64px;
    user-select: none;
    background-image: linear-gradient(179deg, #fcfeff 0%, #dde3f0 100%);
    border-bottom: none;
    padding: 0;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    color: rgba(0, 0, 0, 0.88);
    line-height: 55px;
    flex: 0 0 auto;
    .header-bar-left{
        width: 235px;
        min-width: 235px;
        background-color: linear-gradient(179deg, #fcfeff 0%, #dde3f0 100%);
        background-image: var(--header-left-bg);
        background-repeat: no-repeat;
        background-position-x: -20px;
        background-position-y: -8px;
        height: 100%;
        padding-left: 16px;
        margin-right: 195px;
        display: flex;
        align-items: center;
        font-size: 18px;
        font-weight: bold;
        .header-bar-logo{
            width: 80px;
            height: 20px;
            margin-right: 16px;
        }
    }
    .header-bar-right{
        height: 100%;
        margin-left: auto;
        background-image: linear-gradient(180deg, #c4e2ff 0%, #dde3f0 100%);
        padding-right: 16px;
        position: relative;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        &:before{
            content: "";
            display: block;
            position: absolute;
            width: 80px;
            height: 100%;
            top: 0;
            left: -80px;
            background: var(--header-right-bg) no-repeat top right;
        }
        .hospital{
            display: flex;
            justify-content: flex-start;
            align-items: center;
            margin-right: 30px;
            font-weight: bold;
            font-size: 16px;
            .hospital-logo{
                height: 30px;
                width: 30px;
                margin-right: 10px;
            }
        }
        .header-bar-right-divider{
            margin: 0 20px;
            height: 1.3em;
            position: relative;
            display: inline-block;
            vertical-align: middle;
            border-top: 0;
            border-inline-start: 1px solid rgba(5, 5, 5, 0.06);
            line-height: 1.5714285714285714;
            list-style: none;
            padding: 0;
            color: rgba(0, 0, 0, 0.88);
        }

        .user-dropdown {
            display: flex;
            align-items: center;

            .user-info {
                display: flex;
                align-items: center;
                cursor: pointer;
                padding: 4px 12px;
                border-radius: 4px;
                transition: background-color 0.3s;
                color: rgba(0, 0, 0, 0.88);
                font-size: 16px;
                box-sizing: border-box;
                height: 100%;
                overflow: hidden;
                &:hover {
                    background-color: rgba(0, 0, 0, 0.04);
                }

                .username {
                    margin-right: 4px;
                    font-weight: 500;
                }

                .el-icon-arrow-down {
                    font-size: 12px;
                    transition: transform 0.3s;
                }
            }
        }

        .window-controls {
            display: flex;
            align-items: center;
            margin-left: 12px;
            height: 100%;

            .control-btn {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 40px;
                height: 30px;
                font-size: 16px;
                color: #909399;
                cursor: pointer;
                transition: all 0.15s ease;
                user-select: none;
                border-radius: 2px;
                margin-left: 1px;

                &:hover {
                    background-color: rgba(0, 0, 0, 0.06);
                    color: #606266;
                }

                &.minimize-btn {
                    &:hover {
                        background-color: rgba(0, 0, 0, 0.08);
                        color: #303133;
                    }
                }

                &.maximize-btn {
                    &:hover {
                        background-color: rgba(0, 0, 0, 0.08);
                        color: #303133;
                    }
                }

                &.close-btn {
                    &:hover {
                        background-color: #f56c6c;
                        color: #fff;
                    }

                    &:active {
                        background-color: #f04747;
                    }
                }

                &:active {
                    background-color: rgba(0, 0, 0, 0.12);
                }
            }
        }
    }
}
</style>

<style lang="scss">
// 全局样式，用于下拉菜单
.el-dropdown-menu {
    min-width: 160px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    background-color: #fff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    .el-dropdown-menu__item {
        display: flex !important;
        align-items: center !important;
        padding: 10px 16px !important;
        font-size: 14px !important;
        line-height: 1.5 !important;
        color: #606266 !important;
        cursor: pointer !important;
        outline: none !important;
        white-space: nowrap !important;

        i {
            margin-right: 8px !important;
            font-size: 14px !important;
            width: 16px !important;
            text-align: center !important;
            color: #909399 !important;
        }

        &:hover {
            background-color: #f5f7fa !important;
            color: #409eff !important;

            i {
                color: #409eff !important;
            }
        }

        &.is-divided {
            border-top: 1px solid #ebeef5 !important;
            margin-top: 6px !important;
        }

        &:focus {
            background-color: #f5f7fa !important;
            color: #409eff !important;
        }
    }
    .el-dropdown-menu__item--divided:before {
        margin: 0 !important;
    }
}
</style>

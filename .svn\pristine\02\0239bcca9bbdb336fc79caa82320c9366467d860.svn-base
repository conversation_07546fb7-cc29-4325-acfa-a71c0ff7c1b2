/**
 * 会话管理器
 * 负责管理会话控制器事件、会话数据处理、消息处理等
 */
import CEvent from "@/common/CEvent"
import { cloneDeep } from 'lodash'
import Tool from '@/common/tool'
import {
    setExpirationResource,
    setIworksInfoToMsg,
    parseImageListToLocal,
    patientDesensitization,
    transferPatientInfo,
    checkIsCreator,
    checkIsManager,
    getLiveRoomObj,
    pushImageToList
} from './common_base'

class ConversationManager {
    constructor(vueInstance) {
        this.vm = vueInstance
        this.notifying = false
        this.applyingPermission = false
        this.task_index = 0
    }

    /**
     * 初始化会话控制器事件
     * @param {Object} controller 会话控制器
     * @param {boolean} exist 是否已存在
     * @param {Function} callback 回调函数
     */
    initConversationControllerEvent(controller, exist, callback) {
        const that = this.vm
        // 网关连接事件
        controller.on("gateway_connect", function(e) {
            console.info('Conversation_gateway_connect', e)
            callback && callback()
        })

        // 画廊消息详情事件
        controller.on("gallery_messages_detail_info", function(is_succ, data) {
            that.eventListenerManager.setGalleryMessageDetail(is_succ, {
                cid: controller.cid,
                list: data.list,
                iworks_protocol_list: data.iworks_protocol_list
            })
        })

        // 历史聊天消息事件
        controller.on("history_chat_messages", async(is_succ, data, scroll) => {
            this.setHistoryChatMessage(is_succ, data, scroll, controller.cid)
        })

        // 其他人发言事件
        controller.on("other_say", (messageList) => {
            if (messageList && messageList.length > 0) {
                this.setSayChatMessage(messageList, controller.cid)
                that.eventListenerManager.debounceSortChatList()
            }
        })

        // 更新消息事件
        controller.on("update_messages", function(messageList) {
            that.updateChatMessage(messageList, controller.cid)
        })

        // 文件传输进度更新事件
        controller.on("update_file_transmit_progress", function(data) {
            if (data.type == 1) {
                // pc客户端，下载进度不更新
                return
            } else {
                data.cid = controller.cid
                that.updateUploadProgress(data)
            }
        })

        // 更新会话参与者状态事件
        controller.on("update_conversation_attendee_state", function(data) {
            that.$store.commit('conversationList/updateFriendToAttendeeList', data)
        })

        // 添加参与者通知事件
        controller.on("notify_add_attendee", function(data) {
            console.log("------------------------notify_add_attendee------------------")
            that.$store.commit('conversationList/addAttendee', data)
            that.eventListenerManager.findMulticenter(controller.cid)
        })

        // 删除参与者通知事件
        controller.on("notify_delete_attendee", function(data) {
            that.kickoutAttendeeHandle(data, controller)
            that.eventListenerManager.findMulticenter(controller.cid)
        })

        // 编辑主题通知事件
        controller.on("notify_edit_subject", function(is_succ, data) {
            that.setNewSubject(is_succ, data, controller.cid)
        })

        // 添加标签通知事件
        controller.on("notify_add_tag", function(data) {
            that.$store.commit('gallery/addTagList', data)
        })

        // 删除标签通知事件
        controller.on("notify_del_tag", function(data) {
            that.$store.commit('gallery/deleteTagList', data)
        })

        // 添加评论通知事件
        controller.on("notify_add_comment", function(data) {
            that.$store.commit('gallery/addCommentToList', data)
        })

        // 编辑公开状态通知事件
        controller.on("notify_edit_public", function(is_succ, data) {
            if (is_succ) {
                data.cid = controller.cid
                that.$store.commit('conversationList/updateIsPublic', data)
            }
        })

        // 编辑查看模式通知事件
        controller.on("notify_edit_view_mode", function(is_succ, data) {
            if (is_succ) {
                data.cid = controller.cid
                data.value = data.view_mode
                that.$store.commit('conversationList/updateViewMode', data)
            }
        })

        // 编辑录制模式通知事件
        controller.on("notify_edit_record_mode", function(is_succ, data) {
            if (is_succ) {
                if (Number(window.vm.$root.currentLiveCid) !== Number(controller.cid)) {
                    // 直播中不接受变更的录制内容
                    data.cid = controller.cid
                    data.record_mode = data.record_mode
                    that.$store.commit('conversationList/updateIsLiveRecord', data)
                }
            }
        })

        // 更新AI结果事件
        controller.on('update_ai_report', function(data) {
            console.log('update_ai_report：', data)
            that.$store.commit('examList/updateExamListMCAiReport', data)
            that.$store.commit('conversationList/updateMessageMCAiReport', data)
            that.$store.commit('gallery/updateGalleryMCAiReport', data)
            that.$root.eventBus.$emit('updateAiReportInMulitcenter', data)
        })

        // 设置管理员通知事件
        controller.on("notify_set_admin", function(is_succ, data) {
            if (is_succ) {
                data.cid = controller.cid
                that.$store.commit('conversationList/updateGroupOwnerId', data)
            }
        })

        // 编辑公告通知事件
        controller.on("notify_edit_announcement", function(is_succ, data) {
            console.log("[event] notify_edit_announcement", is_succ, data)
            data.cid = controller.cid
            that.$store.commit('conversationList/updateAnnounce', data)
        })

        // 删除聊天消息通知事件
        this.bindDeleteChatMessagesEvent(controller, that)

        // 撤回聊天消息通知事件
        this.bindWithdrawChatMessageEvent(controller, that)

        // 会话直播分析结果事件
        controller.on('update_ai_analyze_report', (is_succ, data) => {
            data.cid = controller.cid
            this.updateAiAnalyzeReport(is_succ, data)
        })

        // 切换实时流通知事件
        controller.on('notify_switch_realtime_flow', (data) => {
            that.$root.eventBus.$emit('notifySwitchRealtimeFlow', data)
        })

        // 编辑报告信息通知事件
        controller.on('notify_edit_report_info', (data) => {
            that.$root.eventBus.$emit('lockReport', data)
        })

        // 添加会议计划通知事件
        controller.on('notify_add_conference_plan', (data) => {
            that.$store.commit('conversationList/addConferencePlan', data)
        })

        // 删除会议计划通知事件
        controller.on('notify_del_conference_plan', (data) => {
            that.$store.commit('conversationList/delConferencePlan', data)
        })

        // 设置参与者偏好通知事件
        controller.on('notify_set_attendee_preferences', (data) => {
            data.cid = controller.cid
            that.$store.commit('conversationList/updateMuteToConversation', data)
            that.$store.commit('chatList/updateMuteToChatList', data)
        })

        // 删除考试通知事件
        controller.on('notify_delete_exams', function(is_succ, data) {
            // 暂时为空
        })

        // 异常通知事件
        controller.on("notify_exception", function(data) {
            that.notifyException(data)
        })

        // 更新群头像通知事件
        this.bindUpdateGroupPortraitEvent(controller, that)

        // 更新资源描述通知事件
        controller.on('notify_update_resource_des', (data) => {
            console.log("updateResourceDes ", data)
            that.$store.commit('conversationList/updateResourceDes', data)
            that.$store.commit('consultationImageList/updateImageDes', data)
            that.$store.commit('userFavorites/updateFavorites', data)
        })

        // 接收群消息事件
        this.bindReceiveGroupMessageEvent(controller, that)

        // 参与者更新事件
        controller.on('attendeesUpdate', (data) => {
            that.$store.commit('conversationList/updateAttendeeRole', {
                cid: controller.cid,
                attendeeList: data,
            })
        })

        // 用户申请加群通知事件
        controller.on('notify_user_apply_join_group', (data) => {
            const isCreator = checkIsCreator(controller.cid)
            const isManager = checkIsManager(controller.cid)
            if (isCreator || isManager) {
                let applyCount = that.conversationList[controller.cid].applyCount
                that.$store.commit('conversationList/updateConversation', {
                    cid: controller.cid,
                    key: 'applyCount',
                    value: applyCount + 1,
                })
            }
        })

        // 参与者更新别名事件
        controller.on('attendeesUpdateAliasName', (data) => {
            that.$store.commit('conversationList/updateAttendeeAliasName', {
                uid: data.uid,
                cid: controller.cid,
                aliasName: data.alias_name
            })
        })

        // 资源设置名称事件
        controller.on('resourceSetName', (data) => {
            that.$store.commit("resourceTempStatus/updateResourceTempStatus", {
                resource_id: data.resource_id,
                data: {
                    custom_file_name: data.custom_file_name,
                }
            })
        })

        // 初始化会话事件对象
        if (!that.$root.conversation_event.hasOwnProperty(controller.cid)) {
            that.$set(that.$root.conversation_event, controller.cid, {
                time: new Date().getTime(),
                event: new CEvent()
            })
        }
    }

    /**
     * 绑定删除聊天消息事件
     * @param {Object} controller 会话控制器
     * @param {Object} that Vue实例
     */
    bindDeleteChatMessagesEvent(controller, that) {
        controller.on("notify_delete_chat_messages", function(is_succ, list) {
            if (is_succ) {
                for (let i in list) {
                    let item = list[i]
                    that.$store.commit("conversationList/deleteChatMessagesByGmsgIdList", {
                        cid: controller.cid,
                        gmsg_id_list: item.gmsg_id_list
                    })
                    that.$root.eventBus.$emit('notifyDeleteChatMessages', {
                        cid: controller.cid,
                        gmsg_id_list: item.gmsg_id_list
                    })
                    let resource_id_list = item.resource_id_list
                    if (resource_id_list) {
                        for (let i in resource_id_list) {
                            let resource_id = resource_id_list[i]

                            that.$store.commit("conversationList/deleteFileToConversation", {
                                cid: controller.cid,
                                resource_id: resource_id
                            })

                            that.$root.eventBus.$emit('deleteFileToExamList', {
                                cid: controller.cid,
                                resource_id: resource_id
                            })
                            that.$store.commit("resourceTempStatus/updateResourceTempStatus", {
                                resource_id,
                                data: {
                                    state: 0 // 被删除
                                }
                            })
                        }
                    }
                }
            }
        })
    }

    /**
     * 绑定撤回聊天消息事件
     * @param {Object} controller 会话控制器
     * @param {Object} that Vue实例
     */
    bindWithdrawChatMessageEvent(controller, that) {
        controller.on("notify_withdraw_chat_message", function(is_succ, data) {
            if (is_succ) {
                that.$store.commit("conversationList/withDrawChatMessagesByGmsgIdList", {
                    cid: controller.cid,
                    gmsg_id_list: data.gmsg_id_list
                })
                that.$root.eventBus.$emit('notifyWithdrawChatMessage', {
                    cid: controller.cid,
                    gmsg_id_list: data.gmsg_id_list
                })
                let resource_id_list = data.resource_id_list
                if (resource_id_list) {
                    for (let i in resource_id_list) {
                        let resource_id = resource_id_list[i]
                        that.$store.commit("conversationList/deleteFileToConversation", {
                            cid: controller.cid,
                            resource_id: resource_id
                        })
                        that.$root.eventBus.$emit('deleteFileToExamList', {
                            cid: controller.cid,
                            resource_id: resource_id
                        })
                        that.$store.commit("resourceTempStatus/updateResourceTempStatus", {
                            resource_id,
                            data: {
                                state: 2 // 被撤回
                            }
                        })
                    }
                }
            }
        })
    }

    /**
     * 绑定更新群头像事件
     * @param {Object} controller 会话控制器
     * @param {Object} that Vue实例
     */
    bindUpdateGroupPortraitEvent(controller, that) {
        controller.on("notify_update_group_portrait", function(data) {
            console.log('notify_update_group_portrait', data)
            that.$store.commit('chatList/updateChatAvatarLocalUrl', {
                imgObj: {
                    cid: controller.cid,
                },
                avatar: data.avatar
            })
            that.$store.commit('groupList/updateAvatarToGroupList', {
                cid: controller.cid,
                avatar: data.avatar
            })
            that.$store.commit('conversationList/updateConversationAvatar', {
                cid: controller.cid,
                avatar: data.avatar,
            })
            that.$store.commit('groupset/updateAvatarToGroupsetlist', {
                cid: controller.cid,
                avatar: data.avatar
            })
        })
    }

    /**
     * 绑定接收群消息事件
     * @param {Object} controller 会话控制器
     * @param {Object} that Vue实例
     */
    bindReceiveGroupMessageEvent(controller, that) {
        controller.on('receive_group_message', (data) => {
            console.log('receive_group_message', data, 1)
            that.eventListenerManager.setSayChatMessageReceiveGroupMessage(data, true)
            let condition1 = data.msg_type === that.systemConfig.msg_type.SYS_STOP_REALTIME_CONSULTATION
            let condition2 = data.msg_type === that.systemConfig.msg_type.SYS_START_REALTIME_CONSULTATION
            let condition3 = data.groupInfo.service_type === 104
            if ((condition1 || condition2) && condition3) {
                // 收到的直播开始或结束通知，并且是预约直播的群
                that.debounceUpdateLiveCount()
            }
            that.eventListenerManager.debounceSortChatList()
        })
    }

    /**
     * 设置历史聊天消息
     * @param {boolean} is_succ 是否成功
     * @param {Array} data 消息数据
     * @param {Object} scroll 滚动信息
     * @param {string} cid 会话ID
     */
    async setHistoryChatMessage(is_succ, data, scroll, cid) {
        if (is_succ) {
            patientDesensitization(data)
            parseImageListToLocal(data, 'url')
            setExpirationResource(data, cid)
            let last_send_ts = data[0] && data[0].send_ts
            for (let message of data) {
                message.original_msg_body = message.msg_body
                message.msg_body = this.vm.parseMessageBody(message.msg_body)
                message.patientInfo = transferPatientInfo(message)
                message.sending = false
                message.downloading = false
                message.sendFail = message.sendFail || false
                if (message.msg_type == this.vm.systemConfig.msg_type.AI_ANALYZE) {
                    parseImageListToLocal(message.ai_analyze && message.ai_analyze.messages, 'url')
                }
                if (message.protocol_guid) {
                    // 消息存在iworks信息
                    setIworksInfoToMsg(message)
                }
            }
            var type = 'prepend'
            if (scroll && scroll.type == "bottom") {
                data.unshift({
                    msg_type: this.vm.systemConfig.msg_type.HISTORY_TIP,
                    sender_id: this.vm.user.uid,
                    send_ts: last_send_ts,
                })
                type = 'splice'
            }
            let obj = {
                list: data,
                cid: cid,
                type: type,
            }

            // 聊天界面获取历史消息也走这个方法
            this.vm.$root.eventBus.$emit('historyLoaded', obj)
            this.vm.$store.commit('conversationList/updateMessageListIsLoaded', { cid, is_loaded_history_list: true })
            if (0 < data.length && scroll && "bottom" == scroll.type
                && this.vm.$store.state.conversationList
                && this.vm.$store.state.conversationList[cid]
                && this.vm.$store.state.conversationList[cid].start_type
                && this.vm.systemConfig.start_type.NewChatMessage == this.vm.$store.state.conversationList[cid].start_type.type) {
                this.vm.$store.commit('chatList/addMessageNoSort', data[1])
            }

            this.vm.$store.commit('conversationList/setChatMessage', obj)
        } else {
            this.vm.$message.error('setHistoryChatMessage error')
        }
    }

    /**
     * 获取会话画廊数据
     * @param {Object} controller 会话控制器
     * @param {boolean} exist 是否已存在
     * @returns {Promise} 返回Promise对象
     */
    getConverSationGalleryData(controller, exist) {
        return new Promise((resolve, reject) => {
            let conversation = this.vm.$store.state.conversationList[controller.cid]
            let conversationGalleryData = conversation.galleryObj
            if (conversationGalleryData.hasOwnProperty('cid')) {
                resolve(conversationGalleryData.gallery_list)
            } else {
                window.main_screen.conversation_list[controller.cid].getResourceList({
                    limit: this.vm.systemConfig.consultationImageShowNum,
                    type: 'all'
                }, (res) => {
                    if (res.error_code === 0) {
                        const data = {
                            cid: controller.cid,
                            gallery_list: res.data
                        }
                        this.setGalleryList(data, exist)
                        resolve(res.data)
                    } else {
                        this.vm.$message.error('get_gallery_messages error')
                        reject(false)
                    }
                })
            }
        })
    }

    setNewSubject(is_succ,subject,cid){
        let chat={subject:subject}
        this.vm.$store.commit('conversationList/updateSubjectToConversation',{
            subject:subject,
            cid:cid
        })
        this.vm.$store.commit('chatList/updateSubjectToChatList',{
            subject:subject,
            clamp_subject:chat.clamp_subject,
            clamped:chat.clamped,
            cid:cid
        })
        this.vm.$store.commit('groupList/updateSubjectToGroupList',{
            subject:subject,
            cid:cid
        })
    }
    async updateAiAnalyzeReport(is_succ,data){
        if (is_succ) {
            console.log('updateAiAnalyzeReport',data)
            if(data&&data.type == this.vm.$store.state.aiPresetData.typeIndex.breastSearch){
                if(data.report&&data.report.list&&data.report.list.length>0){
                    this.vm.$store.commit('gallery/updateCommentObjByKey',{ keys:{showAISearchSuggest:true},resource_id:data.resource_id});
                }else{
                    this.vm.$store.commit('gallery/updateCommentObjByKey',{ keys:{showAISearchSuggest:false},resource_id:data.resource_id});
                }
            }else{
                await this.updateReportToGallery(data)
                this.vm.$store.commit('conversationList/updateAiAnalyzeReport',data);
                if(data.type == this.vm.$store.state.aiPresetData.typeIndex.drChest){
                    //通知dr
                    window.CWorkstationCommunicationMng.UpdateAiAnalyzeReport(data);
                }
            }
        }
    }
    async updateReportToGallery(data){
        let mark_list=data.report&&data.report.mark_list
        let report={
            ai_analyze_report:{
                detected_tumor:data.report.detected_tumor,
                error:data.report.error,
                summary:data.report.summary,
                mark_list:{},
                clips:{},
                ai_analyze_id:data.ai_analyze_id,
                type:data.type,
                status:1
            },
            type:data.type,
            group_id:data.group_id||data.cid,
            exam_id:data.exam_id,
            ai_analyze_id:data.ai_analyze_id,
        }
        if(mark_list){
            for(let key in mark_list){
                let new_report = cloneDeep(report)
                new_report.resource_id = key
                new_report.ai_analyze_report.mark_list[key]=data.report.mark_list[key]
                let clips=data.report&&data.report['clips']
                if(clips){
                    new_report.ai_analyze_report['clips'][key]=clips[key]
                }
                this.vm.$store.commit('gallery/updateAiReportToGallery',new_report);
                this.vm.$store.commit('examList/updateAiReportToExamList',new_report);
            }
        }else{
            let clips=data.report&&data.report['clips']
            if(clips){
                for(let key in clips){
                    let new_report = cloneDeep(report)
                    new_report.resource_id = key
                    new_report.ai_analyze_report['clips'][key]=clips[key]
                    // console.error('updateReportToGallery',new_report)
                    this.vm.$store.commit('gallery/updateAiReportToGallery',new_report);
                    this.vm.$store.commit('examList/updateAiReportToExamList',new_report);
                }
                if(data.state=='SUCCESS'){
                    let examObj = this.vm.$store.state.examList[data.group_id]||{};
                    let examList = examObj.list||[];
                    for (let index = 0; index < examList.length; index++) {
                        let exam = examList[index];
                        if (data.exam_id == exam.exam_id) {
                            for (let k = 0; k < exam.imageList.length; k++) {
                                let image = exam.imageList[k]
                                if(image.resource_id == data.resource_id){
                                    this.vm.$root.eventBus.$emit('updateExamImageListIfNeed',image)
                                }
                            }
                        }
                    }
                }
            }else{
                this.vm.$store.commit('examList/updateAiReportToExamList',report);
            }
        }
    }
    setGalleryList(data,exist){
        patientDesensitization(data.gallery_list)
        parseImageListToLocal(data.gallery_list,'url')
        if(data.gallery_list.length<this.vm.systemConfig.consultationImageShowNum){
            data.gallery_index = -1
        }
        this.vm.$store.commit('conversationList/initGalleryObj',data);
    }

    postNotification(type){
        if (!document.hasFocus()) {
            var notification = new Notification(this.vm.lang.app_name,{
                body:this.vm.lang.notification_body[type],
                icon:'static/resource_pc/images/new_logo.png'
            });
        }
    }
    messageNotify(type='new_message'){
        if (this.notifying||window.livingStatus === 2) {
            //去除重复通知 ,直播中声音去掉
            return ;
        }
        document.querySelector('#message_notify').play()
        if (Tool.ifAppClientType(window.clientType)) {
            window.CWorkstationCommunicationMng.clientNotice({})
        }else{
            if (window.Notification) {
                if (Notification.permission=='granted') {
                    this.postNotification(type)
                } else if (Notification.permission!='denied') {
                    if (!this.applyingPermission) {
                        this.vm.$MessageBox.confirm(this.vm.lang.notification_reqeust_tip).then(action => {
                            Notification.requestPermission((permission)=>{
                                if (permission=='granted') {
                                    this.postNotification(type)
                                }
                                this.applyingPermission=false;
                            })
                        });

                        this.applyingPermission=true;
                    }
                }
            }
        }
    }
    checkMessageListDuplicate(messageList,cid){
        let chatMessageList = this.vm.$store.state.conversationList[cid].chatMessageList;
        let newMessageList = [];
        for (let message of messageList) {
            if (message.gmsg_id && message.gmsg_id != 0) {
                let isDuplicate = chatMessageList.some(chatMessage => message.gmsg_id == chatMessage.gmsg_id);
                if (isDuplicate) {
                    console.log("ignore message %%%%%%%%%%");
                    continue;
                }
            }
            newMessageList.push(message);
        }
        return newMessageList;
    }
    setSayChatMessage(omessageList,cid){
        console.log('setSayChatMessage',omessageList)
        const messageList = this.checkMessageListDuplicate(omessageList,cid)
        // const messageList = omessageList;
        if (messageList.length) {
            if (this.vm.$store.state.conversationList[cid]&&!this.vm.$store.state.conversationList[cid].preferences.is_mute) {
                this.messageNotify('new_message')
                this.notifying=true
            }

        }
        for(let message of messageList){
            this.vm.$store.commit('chatList/addUnread',{
                group_id:cid
            })
            if(Tool.checkAppClient('Cef')){
                let liveRoom = getLiveRoomObj();
                if (liveRoom) {
                    liveRoom.updateUnReadMsgCount()
                }

            }
            message.original_msg_body = message.msg_body
            message.msg_body=this.vm.parseMessageBody(message.msg_body)
            patientDesensitization([message]);
            message.patientInfo=transferPatientInfo(message);
            message.sending=false;
            message.sendFail=false;
            message.downloading=false;
            parseImageListToLocal([message],'url')

            if(message.liveInfo){
                message.nickname = message.liveInfo.creator_name
            }
            if(message.sender_nickname){
                message.nickname = message.sender_nickname.nickname
            }
            const attendee = this.vm.$store.state.conversationList[cid]?.attendeeList['attendee_'+message.sender_id]
            if(attendee){
                message.nickname = (attendee.alias_name ? attendee.alias_name : attendee.nickname) || message.nickname
            }
            if (message.msg_type==this.vm.systemConfig.msg_type.AI_ANALYZE) {
                if (message.ai_analyze&&message.ai_analyze.messages) {
                    parseImageListToLocal(message.ai_analyze.messages,'url')
                    let ignoreConsultationImages=false
                    if (this.vm.$store.state.conversationList[cid]&&(this.vm.systemConfig.msg_type.AI_ANALYZE==this.vm.$store.state.conversationList[cid].service_type)) {
                        //AI分析图片不放入总图像列表里
                        //AI分析图片放入总图像列表里
                        //ignoreConsultationImages=true
                    }
                    for(let item of message.ai_analyze.messages){
                        let obj=Object.assign({},item,true)
                        pushImageToList(obj,ignoreConsultationImages)
                    }
                }
            }
            if (message.msg_type==this.vm.systemConfig.msg_type.Text) {
                if(Array.isArray(message.mentionList)){
                    if(message.mentionList.includes(this.vm.user.uid)||message.mentionList.includes('all')){
                        this.vm.$store.commit('chatList/setMention',message)
                        this.messageNotify('new_message')
                        this.notifying=true
                    }
                }
            }
            if (message.protocol_guid) {
                //消息存在iworks信息
                if (message.iworks_protocol) {
                    this.vm.setIworksProtocol(message.iworks_protocol)
                }
                if (message.iworks_protocol_execution) {
                    this.vm.setIworksProtocol(message.iworks_protocol_execution)
                }
                setIworksInfoToMsg(message);
            }
            this.vm.$store.commit('chatList/addMessageNoSort',message)
            pushImageToList(message);
            this.addChatMessageTask(message, false);
            this.vm.$root.eventBus.$emit('updateExamImageListIfNeed',message)
        }
        this.notifying=false
        if(window.vm.$store.state.conversationList[cid].is_loaded_history_list){
            const res =  {
                list:messageList,
                cid:cid,
                type:'append',
                is_localdb_msg: 0
            }
            this.vm.$store.commit('conversationList/setChatMessage',res)
        }

    }
    addChatMessageTask(message, is_history){
        console.log('addChatMessageTask:','0**----------------------')
        //DCM自动下载任务
        console.log(message)
        console.log(message.msg_type)
        console.log(this.vm.systemConfig.msg_type.EXAM_IMAGES)
        let g_auto_download = this.vm.globalParams.auto_download;
        if (is_history || !g_auto_download.enable) {
            return;
        }
        if (message && this.vm.user && message.sender_id == this.vm.user.uid) {
            return;
        }
        var task = null;
        if (this.vm.systemConfig.msg_type.Frame == message.msg_type) {
            var img_encode_type = message.img_encode_type? message.img_encode_type.toUpperCase() : '';
            var to_download = true;
            if ('DCM' == img_encode_type && g_auto_download.Frame_DCM) {
                to_download = true;
            } else if ('DCM' != img_encode_type && g_auto_download.Frame) {
                to_download = true;
            }

            if (to_download) {
                task = {
                    type: this.vm.systemConfig.gallery_image_mode.Frame,
                    img_id: message.img_id,
                    img_list:[],
                    creator_id: message.creator_id.toString(),
                    cid: message.group_id.toString(),
                    user_id: this.vm.user.id.toString(),

                    patient_name: message.patient_name,
                    patient_sex: message.patient_sex,
                    patient_age: message.patient_age,
                    patient_age_unit: message.patient_age_unit,
                    patient_id: message.patient_id,
                    patient_series_datetime:message.patient_series_datetime,

                    exam_type:message.exam_type,
                    exam_id:message.exam_id,
                };

                var url = message.url.substr(0, message.url.lastIndexOf('/') + 1);
                task.img_list.push(url + "SingleFrame." + message.img_encode_type);
                if (message.img_has_gesture_video) {
                    task.img_list.push(url + "CameraFrame." + message.img_encode_type);
                }
            }
        } else if (this.vm.systemConfig.msg_type.Cine == message.msg_type) {
            var img_encode_type = message.img_encode_type? message.img_encode_type.toUpperCase() : '';
            var to_download = false;
            if ('DCM' == img_encode_type && g_auto_download.Cine_DCM) {
                to_download = true;
            } else if ('DCM' != img_encode_type && g_auto_download.Cine) {
                to_download = true;
            }

            if (to_download) {
                task = {
                    type: this.vm.systemConfig.gallery_image_mode.Cine,
                    img_id: message.img_id,
                    creator_id: message.creator_id.toString(),
                    cid: message.group_id.toString(),
                    user_id: this.vm.user.id.toString(),

                    img_list:[],

                    patient_name: message.patient_name,
                    patient_sex: message.patient_sex,
                    patient_age: message.patient_age,
                    patient_age_unit: message.patient_age_unit,
                    patient_id: message.patient_id,
                    patient_series_datetime:message.patient_series_datetime,

                    exam_type:message.exam_type,
                    exam_id:message.exam_id,
                };

                var url = message.url.substr(0, message.url.lastIndexOf('/') + 1);
                task.img_list.push(url + "DeviceVideo." + message.img_encode_type);
                if (message.img_has_gesture_video) {
                    task.img_list.push(url + "GestureVideo." + message.img_encode_type);
                }
            }
        } else if (this.vm.systemConfig.msg_type.File == message.msg_type || this.vm.systemConfig.msg_type.Image == message.msg_type) {
            // 下载所有类型文件
            console.log('addChatMessageTask:','1**----------------------')
            var to_download = true;
            if (to_download) {
                task = {
                    type:  message.msg_type,
                    img_id: message.img_id,
                    creator_id: message.creator_id.toString(),
                    cid: message.group_id.toString(),
                    user_id: this.vm.user.id.toString(),
                    img_list:[],
                };
                task.img_list.push(message.url);
            }
            console.log('addChatMessageTask:','2**----------------------')
            console.log(task)

        }  else if (this.vm.systemConfig.msg_type.EXAM_IMAGES == message.msg_type) {
            // 下载所有类型文件
            console.log('addChatMessageTask:','1**----------------------')
            if (message.url && message.url.length > 0) {
                var to_download = true;
                if (to_download ) {
                    task = {
                        type: message.msg_type,
                        img_id: message.img_id,
                        creator_id: message.creator_id.toString(),
                        cid: message.group_id.toString(),
                        user_id: this.vm.user.id.toString(),
                        img_list:[],
                        patient_name: message.patient_name,
                        patient_sex: message.patient_sex,
                        patient_age: message.patient_age,
                        patient_age_unit: message.patient_age_unit,
                        patient_id: message.patient_id,
                        patient_series_datetime:message.patient_series_datetime,

                        exam_type:message.exam_type,
                        exam_id:message.exam_id,
                    };
                    // message.realUrl = getRealUrl(message)

                    for(let image of message.resourceList){
                        if ( image.id == message.resource_id) {
                            if (image.resource_type == 1) {
                                //MEDIC_IMAGE
                                message.realUrl=message.url.replace("thumbnail.jpg","SingleFrame.jpg");
                                if ('DCM' == message.img_encode_type) {
                                    message.attachmentUrl=message.url.replace("thumbnail.jpg","SingleFrame.DCM");
                                }
                                if ('dcm' == message.img_encode_type) {
                                    message.attachmentUrl=message.url.replace("thumbnail.jpg","SingleFrame.dcm");
                                }
                                if ('PDF' == message.img_encode_type) {
                                    message.attachmentUrl=message.url.replace("thumbnail.jpg","SingleFrame.PDF");
                                }
                                if ('pdf' == message.img_encode_type) {
                                    message.attachmentUrl=message.url.replace("thumbnail.jpg","SingleFrame.pdf");
                                }
                                if ('jpg' == message.img_encode_type) {
                                    message.attachmentUrl=message.url.replace("thumbnail.jpg","SingleFrame.jpg");
                                }
                                if ('png' == message.img_encode_type) {
                                    message.attachmentUrl=message.url.replace("thumbnail.jpg","SingleFrame.jpg");
                                }
                            } else if (image.resource_type == 13) {//产科ai文件
                                // if ('ai' == message.img_encode_type.toLowerCase()) {
                                //     message.attachmentUrl=message.url.replace("/thumbnail.jpg",".ai");
                                // }
                                message.attachmentUrl=message.url.replace("/thumbnail.jpg",".ai");
                            } else if (image.resource_type == 2) {
                                //MEDIC_VIDEO
                                if ('DCM' == message.img_encode_type) {
                                    message.attachmentUrl=message.url.replace("thumbnail.jpg","DeviceVideo.DCM");
                                }
                                if ('dcm' == message.img_encode_type) {
                                    message.attachmentUrl=message.url.replace("thumbnail.jpg","DeviceVideo.dcm");
                                }
                                if ('mp4' == message.img_encode_type) {
                                    message.attachmentUrl=message.url.replace("thumbnail.jpg","DeviceVideo.mp4");
                                }
                                if ('avi' == message.img_encode_type) {
                                    message.attachmentUrl=message.url.replace("thumbnail.jpg","DeviceVideo.avi");
                                }
                            }else{
                                message.realUrl=message.url.replace("thumbnail.jpg","SingleFrame"+message.img_encode_type);
                            }
                        }
                    }
                    // console.error(message.dicomUrl)
                    // task.img_list.push(message.realUrl)
                    if (message.attachmentUrl && message.attachmentUrl.length > 0) {
                        task.img_list.push(message.attachmentUrl)
                    }
                    if (message.realUrl && message.realUrl.length > 0) {
                        task.img_list.push(message.realUrl)
                    }
                    if (message.file_id){
                        task.img_id = message.file_id
                    }
                }
            }
            console.log('addChatMessageTask:','2**----------------------')
            console.log(task)

        }else if (this.vm.systemConfig.msg_type.OBAI == message.msg_type) {
            var to_download = true;
            if (to_download) {
                task = {
                    type: this.vm.systemConfig.gallery_image_mode.OBAI,
                    img_id: message.img_id,
                    img_list:[],
                    creator_id: message.creator_id.toString(),
                    cid: message.group_id.toString(),
                    user_id: this.vm.user.id.toString(),

                    patient_name: message.patient_name,
                    patient_sex: message.patient_sex,
                    patient_age: message.patient_age,
                    patient_age_unit: message.patient_age_unit,
                    patient_id: message.patient_id,
                    patient_series_datetime:message.patient_series_datetime,

                    exam_type:message.exam_type,
                    exam_id:message.exam_id,
                };
                var url = message.url.substr(0, message.url.lastIndexOf('/'));
                task.img_list.push(url + ".ai");
            }
        }

        if (!task) {
            return;
        }

        task.task_id = new Date().getTime() + "_" + this.task_index;
        this.task_index++;

        //通知App
        window.CWorkstationCommunicationMng.addDownloadTasks({list:[task]});
    }
    /**
     * 清理资源
     */
    destroy() {
        this.vm = null
    }
}

export default ConversationManager

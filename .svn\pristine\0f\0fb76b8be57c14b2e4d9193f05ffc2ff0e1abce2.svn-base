<template>
    <transition name="fade">
    	<div class="search_page second_level_page">
            <div class="container">
                <div class="search_box">
                    <van-search
                        autofocus
                        v-model="keyword"
                        background="#00c59d"
                        show-action
                        clearable
                        :placeholder="lang.search_input_key"
                        @cancel="cancelIcloudSearch"
                        @input="handleKeywordInput"
                    >
                    </van-search>
                    <div class="van-search-list">
                        <div class="category-card" v-if="isContainsChat">
                            <p class="category-name">{{ lang.recent_chat_text }}</p>
                            <ul class="search-list">
                                <li
                                    :class="['list-item', { needLine: hasMoreContainsChat }]"
                                    v-for="(item, i) in relatedChatList"
                                    :key="i"
                                    @click.stop="openChat(item, 3)"
                                >
                                    <mr-avatar
                                        :url="getLocalAvatar(item)"
                                        :origin_url="item.avatar"
                                        :showOnlineState="false"
                                        :key="item.avatar"
                                    ></mr-avatar>
                                    <span class="nickname">
                                        <span class="nickname_wrap" v-html="item.subject"> </span>
                                    </span>
                                </li>
                            </ul>
                            <div class="more-option" @click.stop="openMore(3)" v-if="hasMoreContainsChat">
                                <i class="iconfont svg_icon_search icon-magnifier"></i>
                                <span>{{ `${lang.more_text} ${lang.recent_chat_text}` }}</span>
                                <i class="iconfont svg_icon_right icon-entry"></i>
                            </div>
                        </div>
                        <div class="category-card" v-if="isContainsFriend">
                            <p class="category-name">{{ lang.contact_text }}</p>
                            <ul class="search-list">
                                <li
                                    :class="['list-item', { needLine: hasMoreContainsFriend }]"
                                    v-for="(item, i) in relatedFriendList"
                                    :key="i"
                                    @click.stop="openChat(item)"
                                >
                                    <mr-avatar
                                        :url="getLocalAvatar(item)"
                                        :origin_url="item.avatar"
                                        :showOnlineState="false"
                                        :key="item.avatar"
                                    ></mr-avatar>
                                    <span class="nickname">
                                        <span class="nickname_wrap" v-html="item.nickname"> </span>
                                    </span>
                                </li>
                            </ul>
                            <div class="more-option" @click.stop="openMore(1)" v-if="hasMoreContainsFriend">
                                <i class="iconfont svg_icon_search icon-magnifier"></i>
                                <span>{{ `${lang.more_text} ${lang.contact_text}` }}</span>
                                <i class="iconfont svg_icon_right icon-entry"></i>
                            </div>
                        </div>
                        <div class="category-card" v-if="isContainsGroup">
                            <p class="category-name">{{ lang.group_chat_text }}</p>
                            <ul class="search-list">
                                <li
                                    :class="['list-item', { needLine: hasMoreContainsGroup }]"
                                    v-for="(item, i) in relatedGroupList"
                                    :key="i"
                                    @click.stop="openChat(item)"
                                >
                                    <mr-avatar
                                        :url="getLocalAvatar(item)"
                                        :origin_url="item.avatar"
                                        :showOnlineState="false"
                                        :key="item.avatar"
                                    ></mr-avatar>
                                    <span class="nickname">
                                        <span class="nickname_wrap" v-html="item.subject"> </span>
                                    </span>
                                </li>
                            </ul>
                            <div class="more-option" @click.stop="openMore(2)" v-if="hasMoreContainsGroup">
                                <i class="iconfont svg_icon_search icon-magnifier"></i>
                                <span>{{ `${lang.more_text} ${lang.group_chat_text}` }}</span>
                                <i class="iconfont svg_icon_right icon-entry"></i>
                            </div>
                        </div>
                        <div class="category-card" v-if="isContainsGroupset">
                            <p class="category-name">{{ lang.groupset_text }}</p>
                            <ul class="search-list">
                                <li
                                    :class="['list-item', { needLine: hasMoreContainsGroupset }]"
                                    v-for="(item, i) in relatedGroupsetList"
                                    :key="i"
                                    @click.stop="openChat(item)"
                                >
                                    <mr-avatar
                                        :url="getLocalAvatar(item)"
                                        :origin_url="item.avatar"
                                        :showOnlineState="false"
                                        :key="item.avatar"
                                    ></mr-avatar>
                                    <span class="nickname">
                                        <span class="nickname_wrap" v-html="item.subject"> </span>
                                    </span>
                                </li>
                            </ul>
                            <div class="more-option" @click.stop="openMore(4)" v-if="hasMoreContainsGroupset">
                                <i class="iconfont svg_icon_search icon-magnifier"></i>
                                <span>{{ `${lang.more_text} ${lang.groupset_text}` }}</span>
                                <i class="iconfont svg_icon_right icon-entry"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <router-view></router-view>
    	</div>
	</transition>
</template>
<script>
import base from '../lib/base'
import { Search } from 'vant';
import groupsetTool from '../lib/groupsetTool'
import {getLocalAvatar} from '../lib/common_base'
export default {
    mixins: [base,groupsetTool],
    name: 'icloudSearch',
    components:{
        VanSearch: Search
    },
    data(){
        return {
            getLocalAvatar,
            keyword:'',
            link:'',
            // relatedFriendList: [],
            // relatedGroupList: [],
            // relatedChatList: [],
            // relatedGroupsetList: [],
            beforeKeyword:'',
            friends: [],
            groups:[],
            chats:[],
            groupsets:[],
            filterTimer: null
        }
    },
    computed:{
        relatedFriendList(){
            return this.$store.state.relatedSearchList.relatedFriendList
        },
        relatedGroupList(){
            return this.$store.state.relatedSearchList.relatedGroupList
        },
        relatedChatList(){
            return this.$store.state.relatedSearchList.relatedChatList
        },
        relatedGroupsetList(){
            return this.$store.state.relatedSearchList.relatedGroupsetList
        },
        groupList() {
            return this.$store.state.groupList
        },
        friendList() {
            return this.$store.state.friendList.list
        },
        chatList(){
            return this.$store.state.chatList.list
        },
        groupsetList() {
            return this.$store.state.groupset.list
        },
        isContainsFriend() {
            return this.relatedFriendList.length > 0
        },
        isContainsGroup() {
            return this.relatedGroupList.length > 0
    	},
        isContainsChat() {
            return this.relatedChatList.length > 0
        },
        isContainsGroupset() {
            return this.functionsStatus.groupset&&this.relatedGroupsetList.length > 0
        },
        hasMoreContainsChat() {
            return this.relatedChatList.length >= 3
        },
        hasMoreContainsFriend() {
            return this.relatedFriendList.length >= 3
        },
        hasMoreContainsGroup() {
            return this.relatedGroupList.length >= 3
        },
        hasMoreContainsGroupset() {
            return this.relatedGroupsetList.length >= 3
        },
        isMyGroupSetInit() {
            return this.$store.state.groupset.isMyGroupSetInit
        },
        isManagerGroupSetInit(){
            return this.$store.state.groupset.isManagerGroupSetInit
        }
    },
    mounted() {
        this.$nextTick(() => {
            // setTimeout(() => {
            //     input.addEventListener('click', () => {
            //         input.focus()
            //     })
            //     input.click()
            // },200)
            this.initGroupsetData();
        });
    },
    beforeDestroy() {},
    methods: {
        cancelIcloudSearch() {
            this.clearAllSearchData();
            this.$router.back();
        },
        handleKeywordInput() {
            this.filterTimer && clearTimeout(this.filterTimer)
            // 防抖处理
            this.filterTimer = setTimeout(() => {
                const keyword = this.keyword.trim()
                if(keyword) {
                    if(keyword !== this.beforeKeyword) {
                        const keyword = this.keyword
                        this.beforeKeyword = keyword
                        this.searchFriends(keyword)
                        this.searchGroups(keyword)
                        this.searchChatList(keyword, this.chatList)
                        this.searchGroupsetList(keyword)
                        this.commitAllRelatedListToStore()
                    }else{
                        // 值与之前一样，不做任何操作
                    }

                }else{
                    this.beforeKeyword = ''
                    this.clearAllSearchData()
                }
            }, 300)
        },
        searchFriends(keyword) {
            this.friends = []
            const re = new RegExp(`${keyword}`, 'ig')
            const keywordLength = keyword.length

            for(const item of this.friendList) {
                // 深拷贝一份再操作，避免破坏源对象结构
                let itemCopy = {...item}
                let nickname = itemCopy.alias||itemCopy.nickname
                if(re.test(nickname)) { // 正则匹配
                    const index = nickname.search(re) // 找到匹配到的字符的第一个索引
                    const subStr = nickname.substring(index, index+keywordLength) // 从原昵称切割
                    re.lastIndex = 0
                    itemCopy.nickname = nickname.replace(subStr, `<span class="lighten">${subStr}</span>`)
                    itemCopy.level = 1
                    itemCopy.category = 1
                    if(this.friends.length < 3) {
                        this.friends.push(itemCopy)
                    }else{
                        return
                    }
                }
            }
        },
        searchGroups(keyword) {
            const that = this
            this.groups = []
            const re = new RegExp(`${keyword}`, 'ig')
            const keywordLength = keyword.length
            for(const item of this.groupList) {
                // 深拷贝一份再操作，避免破坏源对象结构
                let itemCopy = {...item}
                let subject = itemCopy.subject
                if(re.test(subject)) {
                    const index = subject.search(re) // 找到匹配到的字符的第一个索引
                    const subStr = subject.substring(index, index+keywordLength) // 从原昵称切割
                    re.lastIndex = 0
                    console.log(index, itemCopy.subject, subStr)
                    itemCopy.subject = subject.replace(subStr, `<span class="lighten">${subStr}</span>`)
                    itemCopy.level = 1
                    itemCopy.category = 2
                    // 最多只展示3条数据
                    if(this.groups.length < 3) {
                        this.groups.push(itemCopy)
                    }else{
                        return
                    }
                }
            }
        },
        isGroupMember(group, groupList) {
            // debugger
            if(group.type !== 2) { // 不是群，直接false
                return false
            }
            for(const item of groupList) {
                if(item.id == group.cid) {
                    return true
                }
            }
            return false
        },
        isFriend(person, friendList) {
            // debugger
            if(person.type !== 1) { // 不是单聊，直接false
                return false
            }
            for(const item of friendList) {
                if(item.id == person.fid) {
                    return true
                }
            }
            return false
        },
        pushItemToRelatedChatList(regexp, chat, keywordLength, groupset_id) {
        /*
            regexp: 字符串匹配正则
        */
            const isGroupMember = this.isGroupMember(chat, this.groupList)
            const isFriend = this.isFriend(chat, this.friendList)
            if(isGroupMember || isFriend) {
                let itemCopy = {...chat}
                let subject = itemCopy.subject
                if(regexp.test(subject)) {
                    const index = subject.search(regexp) // 找到匹配到的字符的第一个索引
                    const subStr = subject.substring(index, index+keywordLength) // 从原昵称切割
                    regexp.lastIndex = 0

                    itemCopy.subject = subject.replace(subStr, `<span class="lighten">${subStr}</span>`)
                    itemCopy.level = 1
                    itemCopy.category = 3
                    if(groupset_id) {
                        itemCopy.groupset_id = groupset_id
                    }
                    // 最多只展示3条数据
                    if(this.chats.length < 3) {
                        this.chats.push(itemCopy)
                    }else{
                        return
                    }
                }
            }

        },
        searchChatList(keyword, chatList, filter) {
            const re = new RegExp(`${keyword}`, 'ig')
            const keywordLength = keyword.length
            let filterObj = filter || {}
            this.chats = []

            for(const item of chatList) {
                // 如果是群落，深度遍历
                if(item.type === 3 && item.list.length > 0) {
                    for(const chat of item.list) {
                        if(chat.cid in filterObj){ // 去重
                            continue
                        }
                        filterObj[chat.cid] = ''
                        if(chat.type === 3 && item.list.length > 0) { // 如果还是群落，则继续深度遍历
                            this.searchChatList(keyword, chat.list, filterObj)
                        }else{
                            this.pushItemToRelatedChatList(re, chat, keywordLength, item.id)
                        }
                    }
                }else{
                    // 单聊或群聊，判断自己是否属于该群群成员
                    if(item.cid in filterObj){ // 去重
                        continue
                    }
                    filterObj[item.cid] = ""
                    this.pushItemToRelatedChatList(re, item, keywordLength)
                }

            }
        },
        searchGroupsetList(keyword) {
        	this.groupsets = []
            const re = new RegExp(`${keyword}`, 'ig')
            const keywordLength = keyword.length

            for(const item of this.groupsetList) {
                // 深拷贝一份再操作，避免破坏源对象结构
                let itemCopy = {...item}
                let subject = itemCopy.subject
                if(re.test(subject)) {
                    const index = subject.search(re) // 找到匹配到的字符的第一个索引
                    const subStr = subject.substring(index, index+keywordLength) // 从原昵称切割
                    re.lastIndex = 0

                    itemCopy.subject = subject.replace(subStr, `<span class="lighten">${subStr}</span>`)
                    itemCopy.level = 1
                    itemCopy.category = 4
                    // 最多只展示3条数据
                    if(this.groupsets.length < 3) {
                        this.groupsets.push(itemCopy)
                    }else{
                        return
                    }
                }
            }
        },
        clearAllSearchData() {
            this.friends = []
            this.groups = []
            this.chats = []
            this.groupsets = []
            this.commitAllRelatedListToStore()
        },
        clearAllLevelSearchData() {
            let commitData = {
                relatedFriendList: [],
                relatedGroupList: [],
                relatedChatList: [],
                relatedGroupsetList: [],
                relatedMoreFriendList: [],
                relatedMoreGroupList:[],
                relatedMoreChatList:[],
                relatedMoreGroupsetList:[]
            }
            this.$store.commit('relatedSearchList/updateRelatedSearchList', commitData)
        },
        openChat(item, extra_type) {
            // this.$router.back()
            console.log("#######item",item)
            if(extra_type === 3){ // 特殊处理最近聊天
                if(item.type == 2) { // 群
                    item.id = item.cid
                }else{               // 好友
                    item.id = item.fid
                }

            }
            this.clearAllLevelSearchData()
            if(item.type==3){
                this.openGroupset(item)
            } else if(item.type==2) {
                setTimeout(() => {
                    this.openConversation(item.id, 2)   // replace的方式打开会话
                },50)
            }else {
                this.$router.back()
                setTimeout(() => {
                    this.openConversation(item.id, 3)
                },50)
            }
        },
        openMore(type){
        	this.$router.push(`/index/icloud_search/icloud_search_more?keyword=${this.keyword}&type=${type}`)
        },
        initGroupsetData() {
            if(!this.isMyGroupSetInit) {
                window.main_screen.getGroupsetList({},(data)=>{
                    console.log('data',data)
                    if (data.error_code==0) {
                        let list=data.data;
                        list.forEach(item=>{
                            item.type=this.systemConfig.ConversationConfig.type.GroupSet;
                        })
                        this.$store.commit('groupset/initGroupsetList',data.data);
                    }
                })
            }
            if(!this.isManagerGroupSetInit){
                window.main_screen.getManagerGroupsetList({},(data)=>{
                    console.log(data,'getManagerGroupsetList')
                    if (data.error_code==0) {
                        let list=data.data;
                        list.forEach(item=>{
                            item.type=this.systemConfig.ConversationConfig.type.GroupSet;
                        })
                        this.$store.commit('groupset/initGroupsetManagerList',data.data);
                    }
                })
            }
        },
        openGroupset(groupset) {
            this.$router.replace(`/index/groupsets/detail/${groupset.id}`)
        },
        commitAllRelatedListToStore(){
            let commitData = {
                relatedFriendList: this.friends,
                relatedGroupList: this.groups,
                relatedChatList: this.chats,
                relatedGroupsetList: this.groupsets
            }
            console.log("########commitData:", commitData)
            this.$store.commit('relatedSearchList/updateRelatedSearchList', commitData)
        },
    }
}

</script>
<style lang="scss">
.search_page{
    .container{
        box-sizing:border-box;
//         height: calc(100% - 2.95rem);
        .search_box {
            overflow: auto;
            .category-card {
                box-sizing: border-box;
                background:#fff;
                padding: 0.8rem 0rem 0 0.8rem;
                box-shadow: 0 0 0.1rem #ccc;
                margin-bottom: .5rem;
                // 分类名
                .category-name {
                    font-size: 0.63rem;
                    height: 1.2rem;
                    color: rgba(0, 0, 0, 0.5);
                    margin-bottom:.2rem;
                    position:relative;
                    &::after{
                        bottom:0rem;
                        position: absolute;
                        content: '';
                        display: block;
                        width: 100%;
                        height: 1px;
                        background: black;
                        opacity: .1;
                    }
                }
                // 查询结果列表(一个类别：如联系人)
                .search-list {
                    display: flex;
                    flex-direction: column;
                    .list-item {
                        position:relative;
                        padding: .6rem 0;
                        display: flex;
                        img {
                            width: 2rem;
                            height: 2rem;
                            border-radius: 100%;
                            vertical-align: middle;
                        }
                        .nickname {
                            flex: 1;
                            display:inline-block;
                            height: 2rem;
                            line-height: 2rem;
                            font-size:.85rem;
                            vertical-align: middle;
                            margin-left: .6rem;
                            .nickname_wrap {
                                display:inline-block;
                                width: 13rem;
                                overflow:hidden;
                                white-space:nowrap;
                                text-overflow:ellipsis;
                            }

                            &::after {
                                position: absolute;
                                bottom: 0;
                                content: '';
                                display: block;
                                height: 1px;
                                width: calc(100% - 2.6rem);
                                background:#000;
                                opacity:.1;
                            }
                        }
                        // 最后一条特殊处理
                        &:nth-last-child(1) {
                            .nickname{
                                &::after {
                                    display:none;
                                }
                            }
                        }
                    }
                    .needLine {
                        &:nth-last-child(1) {
                            &::after{
                                position: absolute;
                                content: '';
                                display: block;
                                bottom: 0;
                                height: 1px;
                                width: 100%;
                                background:#000;
                                opacity:.1;
                            }
                        }
                    }
                }
                // 查看更多
                .more-option {
                    display:flex;
                    padding:.7rem 0;
                    align-items:center;
                    height: .6rem;
                    span,i {
                        vertical-align:middle;
                    }

                    span {
                        flex: 1;
                        margin-left:.3rem;
                        font-size: .63rem;
                    }
                    .svg_icon_search {
                        fill: #586b95;
                        color: #586b95;
                        // width: 0.7rem;
                        // height: 0.6rem;
                    }
                    .svg_icon_right {
                        margin-right: .6rem;
                        fill: rgba(0, 0, 0, .4);
                        color: rgba(0, 0, 0, .4);
                        font-size:2.5rem ;
                    transform: scale(0.25);
                    }
                    span,.svg_icon_search {
                        color: #586b95;
                    }

                }
            }

            .van-search-list {
                overflow-y: auto;
                overflow-x: hidden;
                height: auto;
            }
        }
    }

    .lighten {
        color: green;
    }
}

</style>

<template>
    <div>
        <el-dialog
        class="map_picker_modal"
        title="map-picker"
        :visible="show"
        :append-to-body="true"
        width="800px"
        height="600px"
        :modal="false"
        :before-close="closePickerModal"
        >
            <p v-show="level!=0" @click="backLevel" class="back_level">返回上一级</p>
            <div id="map_chart_dom"></div>
        </el-dialog>
    </div>
</template>
<script>
import * as echarts from 'echarts'
import provinceMap from '../lib/china-province-map.js'
import cityMap from '../lib/china-main-city-map.js'
export default {
    mixins: [],
    name: 'mapPicker',
    components: {},
    props:['show','callback'],
    computed:{
    },
    data(){
        return {
            provinceCenters:{},
            cityCenters:{},
            areaCenters:{},
            mapQueue:[],
            mapCharts:{},
            level:0,
            special:['北京','天津','上海','重庆','香港','澳门','台湾']
        }
    },

    mounted(){
        this.$nextTick(()=>{
            this.init()
        })
    },
    methods:{
        init(){
            window.mui.getJSON('static/resource_pc/map/china.json',(json)=>{
                echarts.registerMap('中国',json)
                for(let feature of json.features){
                    this.provinceCenters[feature.properties.name]=feature.properties.cp||feature.properties.center
                }
                this.mapCharts = echarts.init(document.getElementById("map_chart_dom"))
                this.mapQueue.push('中国')
                this.renderMap();
                this.mapCharts.on('click',(params)=>{
                    if (params.componentType!='geo') {
                        return
                    }
                    if (this.level==2) {
                        //点击区县
                        this.pickMap(params)
                    } else if (this.level==0) {
                        this.drawProvince(params)
                    } else if (this.level==1) {
                        let province=this.mapQueue[1]
                        if (this.special.indexOf(province) >= 0) {
                            //点击区县
                            this.pickMap(params)
                        }else{
                            this.drawCity(params)
                        }
                    }
                })
            })
        },
        renderMap(){
            let map=this.mapQueue[this.mapQueue.length-1]
            let center;
            if (this.level==1) {
                center=this.provinceCenters[map]
            } else if (this.level==2) {
                center=this.cityCenters[map]
            }
            if (!center) {
                if (this.level==0) {
                    this.setMapOption(map,center)
                    return;
                }
                let parentMap=this.mapQueue[this.mapQueue.length-2]
                this.downloadMap(parentMap,this.renderMap);
                // this.mapQueue.push(map)
            }else{
                this.downloadMap(map,()=>{
                    this.setMapOption(map,center)
                })
            }
        },
        setMapOption(map,center){
            let option={}
            option.title={
                text:map,
                textStyle:{
                    color:'#e9f0f6',
                    fontSize:30
                },
                left:'center',
                top:10
            }
            option.visualMap={
                type:'piecewise',
                splitNumber:1,
                pieces:[
                    {
                        min:0,
                        color:'#65e1fd'
                    }
                ],
                show:false,
            }
            option.geo={
                center:center,
                map:map,
                roam:true,
                label: {
                    normal: {
                        show: false,
                        textStyle: {
                            color: "#fff",
                            fontSize: 12
                        }
                    },
                    emphasis: {
                        show: true,
                        textStyle: {
                            color: "#fff",
                            fontSize: 16
                        }
                    }
                },
                itemStyle: {
                    normal: {
                        areaColor: "#0f1e3b",
                        borderColor: "#374663",
                        borderWidth: 1,
                        shadowBlur: 0
                    },
                    emphasis: {
                        areaColor: "#42abb9"
                    }
                },
            }
            this.mapCharts.setOption(option);
        },
        downloadMap(map,cb){
            let str='',level=0;
            if (provinceMap[map]) {
                str=`static/resource_pc/map/json/province/${provinceMap[map]}.json`;
                level=1
            }
            if (cityMap[map]) {
                str=`static/resource_pc/map/json/citys/${cityMap[map]}.json`;
                level=2;
            }
            window.mui.getJSON(str,(json)=>{
                echarts.registerMap(map,json)
                let obj={}
                for(let feature of json.features){
                    obj[feature.properties.name]=feature.properties.cp||feature.properties.center
                }
                if (level==1) {
                    this.cityCenters=obj
                } else if (level==2) {
                    this.areaCenters=obj
                }
                cb&&cb()
            })
        },
        drawProvince(params){
            this.level=1;
            this.mapQueue.push(params.name);
            this.renderMap()
        },
        drawCity(params){
            this.level=2;
            this.mapQueue.push(params.name);
            this.renderMap()
        },
        backLevel(){
            this.mapQueue.pop();
            this.level=this.mapQueue.length-1;
            this.renderMap()
        },
        closePickerModal(){
            this.$emit('update:show', false);
        },
        submit(){
            this.callback&&this.callback()
            this.$emit('update:show', false);
        },
        pickMap(params){
            let that=this;
            let center
            if (this.level==1) {
                //点击直辖市的区县
                center=this.cityCenters[params.name]
            } else if (this.level==2) {
                //点击省市的区县
                center=this.areaCenters[params.name]
            }
            if (!center) {
                this.$message.error('该地区暂不支持定位，请联系管理员升级地图数据包！')
                return;
            }
            let region=this.mapQueue.join('-')+'-'+params.name
            this.callback&&this.callback(region,center)
            this.$emit('update:show', false);
        }
    }
}
</script>
<style lang="scss">
.map_picker_modal{
    #map_chart_dom{
        background:#0c162f;
        width:100%;
        height:100%;
    }
    .back_level{
        position:absolute;
        z-index: 2;
        color: #fff;
        left: 20px;
        top: 10px;
        cursor: pointer;
        user-select: none;
    }
}
</style>

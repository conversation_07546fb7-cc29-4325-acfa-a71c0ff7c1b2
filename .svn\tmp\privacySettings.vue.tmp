<template>
    <transition name="slide">
        <div class="privacy_settings_page second_level_page">
            <mrHeader>
                <template #title>
                    {{ lang.ultrasync_privacy_protocol }}
                </template>
            </mrHeader>
            <div class="privacy_settings_container">
                <!-- 隐私协议版本历史 -->
                <div class="version_history_section">
                    <div class="section_title">{{ lang.privacy_policy_historical_versions }}</div>
                    <div class="version_list">
                        <div
                            v-for="(version, index) in privacyVersions"
                            :key="version.version"
                            class="version_item"
                            @click="viewPrivacyVersion(version)"
                        >
                            <div class="version_info">
                                <span class="version_number">
                                    {{version.version}}
                                    <span v-if="index === 0" class="latest_tag">{{ lang.latest_version }}</span>
                                </span>
                                <span class="version_date">{{version.date}}</span>
                            </div>
                            <van-icon name="arrow" />
                        </div>
                    </div>
                </div>

                <!-- 撤销隐私协议同意按钮 - 固定在底部 -->
                <div class="revoke_section" v-if="shouldShowRevokeButton">
                    <van-button
                        type="default"
                        class="revoke_button"
                        block
                        @click="showRevokeConfirm"
                    >
                        {{ lang.revocation_privacy_agreement_consent }}
                    </van-button>
                </div>
            </div>
        </div>
    </transition>
</template>

<script>
import base from '../lib/base'
import Tool from '@/common/tool'
import { Icon, Button } from "vant";

export default {
    mixins: [base],
    name: 'privacySettings',
    components: {
        VanIcon: Icon,
        VanButton: Button,
    },
    data() {
        return {
            privacyVersions: [],
        }
    },
    computed: {
        // 判断是否显示撤销按钮，login路由下不显示
        shouldShowRevokeButton() {
            return !this.$route.path.includes('/login/');
        },
    },
    created() {
        // 初始化隐私协议版本历史
        this.initPrivacyVersions();
    },
    methods: {
        // 初始化隐私协议版本历史
        initPrivacyVersions() {
            const isCNEnvironment = process.env.VUE_APP_PROJECT_NOV === "CN";
            if (isCNEnvironment) {
                this.privacyVersions = [
                    {
                        version: '2.0',
                        date: '2025-01-07',
                        env: 'CN'
                    },
                    {
                        version: '1.0',
                        date: '2021-11-01',
                        env: 'CN'
                    }
                ];
            } else {
                this.privacyVersions = [
                    {
                        version: '2.0',
                        date: '2025-07-30',
                        env: 'CE'
                    },
                    {
                        version: '1.0',
                        date: '2024-06-27',
                        env: 'CE'
                    },
                    {
                        version: '2.0',
                        date: '2025-07-30',
                        env: 'CE'
                    }
                ];
            }
        },

        // 查看特定版本的隐私协议
        viewPrivacyVersion(version) {
            const server_type = this.$store.state.systemConfig.server_type;
            let host = server_type.protocol + server_type.host + server_type.port;
            if (host.indexOf("localhost") > -1 || host.indexOf("192.168") > -1) {
                host = `https://${Tool.getHostConfig().dev}`;
            }
            const url = host + `/privacyPolicyPage/${version.env}/pravicyPolicy${version.env}_${version.version}.html`;

            // 在新窗口中打开特定版本的隐私协议
            Tool.openLinkByDefaultBrowser(url);
        },

        // 显示撤销确认对话框
        showRevokeConfirm() {
            Tool.openMobileDialog({
                message: this.lang.sure_revoke_privacy_tips,
                showRejectButton: true,
                confirm: () => {
                    this.revokePrivacyAgreement();
                },
                reject: () => {
                    // 用户取消操作
                }
            });
        },

        // 撤销隐私协议同意
        revokePrivacyAgreement() {
            try {
                // 调用隐私协议组件中的方法来撤销同意
                this.setPrivacyStatus(0);

                // 延迟执行退出登录，直接调用minePage的logout eventBus
                setTimeout(() => {
                    this.$root.eventBus.$emit('logout');
                }, 500);
            } catch (error) {
                console.error('撤销隐私协议失败:', error);
            }
        },

        // 设置隐私协议状态
        setPrivacyStatus(status) {
            let serverType = localStorage.getItem('serverType') || '云++';
            let privacyStatus = JSON.parse(localStorage.getItem('isAgreePrivacyPolicy') || "{}");

            // 从envConfig中获取版本号
            let privacy_version = this.$store.state.systemConfig.envConfig && this.$store.state.systemConfig.envConfig.privacy_agreement_version;

            // 如果同意隐私协议，记录版本号；如果不同意，记录0
            if (status) {
                privacyStatus[serverType] = privacy_version;
            } else {
                privacyStatus[serverType] = '';
            }

            localStorage.setItem('isAgreePrivacyPolicy', JSON.stringify(privacyStatus));
            // 向客户端通知隐私协议状态，传递实际的版本号
            if (window.CWorkstationCommunicationMng && window.CWorkstationCommunicationMng.setPrivacyPolicyStatus) {
                window.CWorkstationCommunicationMng.setPrivacyPolicyStatus({
                    status: status ? 1 : 0,
                    version: privacy_version
                });
            }
        },
    },
}
</script>

<style lang="scss" scoped>
.privacy_settings_page {
    height: 100%;
    display: flex;
    flex-direction: column;
    background: #f7f8fa;

    .privacy_settings_container {
        flex: 1;
        display: flex;
        flex-direction: column;
        padding: 0.6rem;

        .version_history_section {
            flex: 1;
            background: #fff;
            border-radius: 0.4rem;
            overflow: hidden;
            border: 1px solid #e8eaed;
            margin-bottom: 1rem;

            .section_title {
                padding: 1rem 1.2rem 0.8rem;
                font-size: 0.8rem;
                font-weight: 500;
                color: #5f6368;
                background: #fafbfc;
                border-bottom: 1px solid #e8eaed;
            }

            .version_list {
                padding: 0;

                .version_item {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding: 1rem 1.2rem;
                    border-bottom: 1px solid #f0f0f0;
                    cursor: pointer;
                    transition: background-color 0.2s ease;

                    &:hover {
                        background: #f8f9fa;
                    }

                    &:last-child {
                        border-bottom: none;
                    }

                    .version_info {
                        display: flex;
                        flex-direction: column;
                        flex: 1;

                        .version_number {
                            font-size: 0.85rem;
                            font-weight: 500;
                            color: #202124;
                            margin-bottom: 0.3rem;
                            display: flex;
                            align-items: center;
                            gap: 0.5rem;

                            .latest_tag {
                                background: #34a853;
                                color: #fff;
                                font-weight: 400;
                                font-size: 0.65rem;
                                padding: 0.1rem 0.4rem;
                                border-radius: 0.2rem;
                            }
                        }

                        .version_date {
                            font-size: 0.7rem;
                            color: #5f6368;
                            font-weight: 400;
                        }
                    }

                    .van-icon {
                        color: #9aa0a6;
                        font-size: 0.8rem;
                        transition: color 0.2s ease;
                    }

                    &:hover .van-icon {
                        color: #5f6368;
                    }
                }
            }
        }

        .revoke_section {
            flex-shrink: 0;
            padding: 0;
            background: transparent;

            .revoke_button {
                background: #fff;
                border: 1px solid #dadce0;
                color: #5f6368;
                font-size: 0.75rem;
                font-weight: 500;
                height: 2.4rem;
                border-radius: 0.4rem;
                transition: all 0.2s ease;

                &:hover {
                    background: #f8f9fa;
                    border-color: #dadce0;
                    color: #202124;
                }

                &:active {
                    background: #f1f3f4;
                    border-color: #dadce0;
                }
            }
        }
    }
}

// 简化过渡动画
.slide-enter-active,
.slide-leave-active {
    transition: all 0.2s ease;
}

.slide-enter-from {
    opacity: 0;
    transform: translateX(0.5rem);
}

.slide-leave-to {
    opacity: 0;
    transform: translateX(-0.5rem);
}
</style>

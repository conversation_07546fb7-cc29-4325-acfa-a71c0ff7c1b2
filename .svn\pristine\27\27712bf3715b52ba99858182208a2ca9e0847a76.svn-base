<template>
    <div>
        <!-- <el-dialog
            class="edit_group_manage"
            :title="title"
            :visible="true"
            :close-on-click-modal="false"
            :append-to-body="true"
            width="40%"
            :modal="false"
            :before-close="back"
        > -->
        <CommonDialog
            class="edit_group_manage"
            :title="title"
            :show.sync="visible"
            :close-on-click-modal="false"
            :append-to-body="true"
            width="40%"
            :modal="false"
            @closed="back"
            :footShow="false"
        >
            <div class="list">
                <ContactSelectList :options="checkOption" v-model="groupUser"></ContactSelectList>
            </div>
            <div class="clearfix">
                <el-button type="primary" size="medium" class="fr" :disabled="!enable" @click="submit">{{lang.confirm_txt}}</el-button>
            </div>
        <!-- </el-dialog> -->
         </CommonDialog>       
    </div>
</template>
<script>
import base from "../lib/base";
import ContactSelectList from "../components/contactSelectList.vue";
import CommonDialog from "../MRComponents/commonDialog.vue";

export default {
    mixins: [base],
    name: "transferGroup",
    components: {
        ContactSelectList,
        CommonDialog
    },
    data() {
        return {
            visible: false,
            cid: this.$route.params.cid,
            type:this.$route.params.type,
            groupUser:[],
        };
    },
    computed: {
        title(){
            if (this.type == 1) {
                return this.lang.add_group_manager;
            }else{
                return this.lang.delete_group_manager;
            }
        },
        conversation() {
            return this.conversationList[this.cid] || {};
        },
        attendeeList() {
            return this.conversation.attendeeList;
        },
        attendeeArray() {
            let arr = [];
            for (let key in this.attendeeList) {
                if (this.attendeeList[key].attendeeState != 0 && this.attendeeList[key].userid != this.user.id) {
                    arr.push(this.attendeeList[key]);
                }
            }
            return arr;
        },
        remarkMap(){
            return this.$store.state.friendList.remarkMap;
        },
        checkOption(){
            const arr=[]
            let list = this.attendeeArray
            if (this.type == 1) {
                for(let item of list){
                    let option={}
                    option.name=this.remarkMap[item.userid]||item.nickname
                    option.id=item.userid
                    option.avatar=item.avatar
                    option.disabled=false
                    if (item.role > this.systemConfig.groupRole.normal ) {
                        option.disabled=true;
                    }
                    arr.push(option)
                }
            }else{
                for(let item of list){
                    if (item.role === this.systemConfig.groupRole.manager) {
                        let option={}
                        option.name=this.remarkMap[item.userid]||item.nickname
                        option.id=item.userid
                        option.avatar=item.avatar
                        option.disabled=false
                        arr.push(option)
                    }
                }
            }
            return arr;
        },
        enable(){
            return this.groupUser.length>0
        },
    },
    mounted() {
        this.$nextTick(() => {
            this.visible = true;
        });
    },
    methods: {
        submit(){
            if (this.enable) {
                if (this.type == 1) {
                    window.main_screen.conversation_list[this.cid].addGroupManager({
                        userIdList:this.groupUser
                    },(result)=>{
                        if (result.error_code === 0) {
                            this.back();
                        }else{
                            this.$message.error(result.error_msg);
                        }
                    })
                }else{
                    window.main_screen.conversation_list[this.cid].deleteGroupManager({
                        userIdList:this.groupUser
                    },(result)=>{
                        if (result.error_code === 0) {
                            this.back();
                        }else{
                            this.$message.error(result.error_msg);
                        }
                    })
                }
            }
        }
    },
};
</script>
<style lang="scss">
.edit_group_manage {
    .el-dialog {
    }
    .el-dialog__body {
        display: flex;
        flex-direction: column;
        .group_manage_container {
            height: 100%;
        }
        .list{
            flex:1;
            overflow: auto;
            overflow: hidden;
            padding-bottom: 50px;
        }
        .submit_btn{
            position: absolute;
            right: 10px;
            bottom: 10px;
        }
    }
}
</style>
